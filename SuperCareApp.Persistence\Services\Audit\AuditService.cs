using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services;

/// <summary>
/// Service for audit log operations
/// </summary>
public class AuditService : IAuditService
{
    private readonly ApplicationDbContext _context;

    public AuditService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<AuditLog>> GetEntityAuditLogsAsync(
        string entityType,
        Guid entityId,
        CancellationToken cancellationToken = default
    )
    {
        return await _context
            .Set<AuditLog>()
            .Where(a => a.EntityType == entityType && a.EntityId == entityId)
            .OrderByDescending(a => a.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<(List<AuditLog> AuditLogs, int TotalCount)> GetUserAuditLogsAsync(
        Guid userId,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default
    )
    {
        var query = _context
            .Set<AuditLog>()
            .Where(a => a.UserId == userId)
            .OrderByDescending(a => a.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);

        var auditLogs = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (auditLogs, totalCount);
    }

    public async Task<(List<AuditLog> AuditLogs, int TotalCount)> GetAuditLogsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        string? entityType = null,
        string? action = null,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default
    )
    {
        var query = _context
            .Set<AuditLog>()
            .Where(a => a.Timestamp >= startDate && a.Timestamp <= endDate);

        if (!string.IsNullOrEmpty(entityType))
        {
            query = query.Where(a => a.EntityType == entityType);
        }

        if (!string.IsNullOrEmpty(action))
        {
            query = query.Where(a => a.Action == action);
        }

        query = query.OrderByDescending(a => a.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);

        var auditLogs = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (auditLogs, totalCount);
    }

    public async Task<AuditStatistics> GetAuditStatisticsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default
    )
    {
        var utcStartDate = startDate.ToUniversalTime();
        var utcEndDate = endDate.ToUniversalTime();

        var auditLogs = await _context
            .AuditLogs.Where(a => a.Timestamp >= utcStartDate && a.Timestamp <= utcEndDate) // Query with UTC dates
            .ToListAsync(cancellationToken);

        var statistics = new AuditStatistics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalOperations = auditLogs.Count,
            InsertOperations = auditLogs.Count(a => a.Action == "INSERT"),
            UpdateOperations = auditLogs.Count(a => a.Action == "UPDATE"),
            DeleteOperations = auditLogs.Count(a => a.Action == "DELETE"),
            OperationsByEntityType = auditLogs
                .GroupBy(a => a.EntityType)
                .ToDictionary(g => g.Key, g => g.Count()),
            OperationsByUser = auditLogs
                .GroupBy(a => a.UserId)
                .ToDictionary(g => g.Key, g => g.Count()),
        };

        return statistics;
    }
}
