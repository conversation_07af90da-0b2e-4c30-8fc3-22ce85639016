﻿namespace SuperCareApp.Application.Common.Models.Bookings;

/// <summary>
/// Parameters for filtering and pagination of leave lists
/// </summary>
public class LeaveListParams
{
    private const int MaxPageSize = 50;
    private int _pageSize = 10;

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
    }

    /// <summary>
    /// Filter by provider ID
    /// </summary>
    public Guid? ProviderId { get; set; }

    /// <summary>
    /// Filter by start date (inclusive)
    /// </summary>
    public DateTime? StartDateFrom { get; set; }

    /// <summary>
    /// Filter by start date (inclusive)
    /// </summary>
    public DateTime? StartDateTo { get; set; }

    /// <summary>
    /// Filter by end date (inclusive)
    /// </summary>
    public DateTime? EndDateFrom { get; set; }

    /// <summary>
    /// Filter by end date (inclusive)
    /// </summary>
    public DateTime? EndDateTo { get; set; }

    /// <summary>
    /// Filter by date range (leaves that overlap with this range)
    /// </summary>
    public DateTime? DateFrom { get; set; }

    /// <summary>
    /// Filter by date range (leaves that overlap with this range)
    /// </summary>
    public DateTime? DateTo { get; set; }

    /// <summary>
    /// Sort by field name
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// Sort in descending order if true
    /// </summary>
    public bool SortDescending { get; set; } = false;
}
