namespace SuperCareApp.Application.Common.Models.Provider
{
    /// <summary>
    /// Parameters for filtering and pagination of care provider lists
    /// </summary>
    public class CareProviderListParams
    {
        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        public int PageNumber { get; set; } = 1;

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        public string? SortBy { get; set; }

        public bool SortDescending { get; set; } = true;
    }
}
