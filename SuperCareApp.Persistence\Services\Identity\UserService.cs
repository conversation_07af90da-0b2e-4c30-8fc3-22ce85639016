﻿namespace SuperCareApp.Persistence.Services.Identity;

public class UserService : IUserService
{
    private readonly UserManager<ApplicationUser> _userManager;

    public UserService(UserManager<ApplicationUser> userManager)
    {
        _userManager = userManager;
    }

    public async Task<Result<ApplicationUser?>> GetByIdAsync(Guid id)
    {
        var user = await _userManager.FindByIdAsync(id.ToString());
        return user == null
            ? Result<ApplicationUser?>.Failure(Error.NotFound("User not found"))
            : Result<ApplicationUser?>.Success(user);
    }
}
