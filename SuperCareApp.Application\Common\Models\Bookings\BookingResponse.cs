﻿using System.Text.Json.Serialization;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class BookingResponse
    {
        public Guid BookingId { get; set; }
        public string Duration { get; set; }
        public string? Description { get; set; }
        public string Status { get; set; }
        public string ServiceType { get; set; }
        public decimal TotalAmount { get; set; }
        public Client Clients { get; set; }
        public CareProvider CareProviders { get; set; }

        // New property
        [JsonPropertyName("bookingSlots")]
        public List<BookingWindowResponse> BookingWindows { get; set; }

        public class Client
        {
            public string Name { get; set; } // Concatenated FirstName and LastName
            public string? PhoneNumber { get; set; }
            public string Email { get; set; }
            public string? ProfilePictureUrl { get; set; }
            public Location Location { get; set; }
        }

        public class Location
        {
            public string StreetAddress { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Country { get; set; }
            public string PostalCode { get; set; }
            public decimal? Longitude { get; set; }
            public decimal? Latitude { get; set; }
        }

        public class CareProvider
        {
            [JsonPropertyName("Providerid")]
            public Guid ProviderId { get; set; }
            public string Name { get; set; } // Concatenated FirstName and LastName
            public string PhoneNumber { get; set; }
            public string Email { get; set; }

            [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
            public string? ProfilePictureUrl { get; set; }
            public int? YearsOfExperience { get; set; }
            public decimal Rating { get; set; }
        }
    }
}
