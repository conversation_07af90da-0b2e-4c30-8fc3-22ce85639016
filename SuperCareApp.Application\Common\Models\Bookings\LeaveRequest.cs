﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    /// <summary>
    /// Request model for creating or updating a leave
    /// </summary>
    public class LeaveRequest
    {
        /// <summary>
        /// The start date of the leave
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// The end date of the leave
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Optional reason for the leave
        /// </summary>
        public string? Reason { get; set; }
    }

    public class LeaveRequestValidator : AbstractValidator<LeaveRequest>
    {
        public LeaveRequestValidator()
        {
            // --- Date Validations ---
            RuleFor(x => x.StartDate)
                .NotEmpty()
                .WithMessage("Start date is required.")
                // Ensure the leave doesn't start in the past.
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Start date cannot be in the past.");

            RuleFor(x => x.EndDate).NotEmpty().WithMessage("End date is required.");

            // --- Cross-field Date Validation ---
            RuleFor(x => x.EndDate)
                // End date must be on or after the start date to allow for single-day leave.
                .GreaterThanOrEqualTo(x => x.StartDate)
                .WithMessage("End date must be on or after the start date.");

            // --- Leave Duration Validation ---
            RuleFor(x => x)
                .Must(x => (x.EndDate - x.StartDate).TotalDays <= 365)
                .WithMessage("Leave period cannot exceed 365 days")
                .When(x => x.StartDate != default && x.EndDate != default);

            // --- Optional Fields Validation ---
            RuleFor(x => x.Reason)
                // Set a reasonable length limit on the reason field.
                .MaximumLength(500)
                .WithMessage("Reason cannot exceed 500 characters.");
        }
    }
}
