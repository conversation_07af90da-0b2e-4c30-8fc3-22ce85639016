﻿using PhoneNumbers;

namespace SuperCareApp.Domain.Entities.ValueObjects;

public static class PhoneNumber
{
    private static readonly PhoneNumberUtil _phoneUtil = PhoneNumberUtil.GetInstance();

    public static string Create(string rawNumber, string defaultRegion = "US")
    {
        if (string.IsNullOrWhiteSpace(rawNumber))
            throw new ArgumentException("Phone number cannot be empty");

        try
        {
            var parsed = _phoneUtil.Parse(rawNumber, defaultRegion);

            if (!_phoneUtil.IsValidNumber(parsed))
                throw new ArgumentException("Invalid phone number.");

            return _phoneUtil.Format(parsed, PhoneNumberFormat.E164);
        }
        catch (NumberParseException ex)
        {
            throw new FormatException("Invalid phone number format.", ex);
        }
    }
}
