﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Repositories
{
    /// <summary>
    /// Repository implementation that adapts RepositoryBase to IRepository
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TContext">The database context type</typeparam>
    public class Repository<TEntity, TContext>
        : RepositoryBase<TEntity, TContext>,
            IRepository<TEntity>
        where TEntity : BaseEntity
        where TContext : DbContext
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">The database context</param>
        public Repository(TContext context)
            : base(context) { }

        /// <summary>
        /// Gets all entities
        /// </summary>
        public async Task<Result<IEnumerable<TEntity>>> GetAllAsync(
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Get all entities and filter out deleted ones
                var entities = (await ListAllAsync()).Where(e => !e.IsDeleted);
                return Result.Success<IEnumerable<TEntity>>(entities);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<TEntity>>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Gets entities by filter
        /// </summary>
        public async Task<Result<IEnumerable<TEntity>>> GetByFilterAsync(
            Expression<Func<TEntity, bool>> filter,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Combine the filter with a check for non-deleted entities
                Expression<Func<TEntity, bool>> combinedFilter = e =>
                    !e.IsDeleted && filter.Compile()(e);
                var entities = await FindAsync(combinedFilter);
                return Result.Success<IEnumerable<TEntity>>(entities);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<TEntity>>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Gets entity by id
        /// </summary>
        public async Task<Result<TEntity>> GetByIdAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Find the entity by ID
                var entity = await GetByIdAsync((object)id);

                // Check if entity exists and is not deleted
                if (entity == null || entity.IsDeleted)
                {
                    return Result.Failure<TEntity>(
                        Error.NotFound($"Entity with ID {id} not found")
                    );
                }

                return Result.Success(entity);
            }
            catch (Exception ex)
            {
                return Result.Failure<TEntity>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Adds a new entity
        /// </summary>
        public async Task<Result<TEntity>> AddAsync(
            TEntity entity,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var addedEntity = await base.AddAsync(entity);
                return Result.Success(addedEntity);
            }
            catch (Exception ex)
            {
                return Result.Failure<TEntity>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        public async Task<Result<TEntity>> UpdateAsync(
            TEntity entity,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var updatedEntity = await base.UpdateAsync(entity);
                return Result.Success(updatedEntity);
            }
            catch (Exception ex)
            {
                return Result.Failure<TEntity>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Deletes an entity by id
        /// </summary>
        public async Task<Result> DeleteAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entityResult = await GetByIdAsync(id, cancellationToken);

                if (entityResult.IsFailure)
                {
                    return Result.Failure(entityResult.Error);
                }

                await DeleteAsync(entityResult.Value);
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Soft deletes an entity by id
        /// </summary>
        public async Task<Result> SoftDeleteAsync(
            Guid id,
            Guid deletedBy,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entityResult = await GetByIdAsync(id, cancellationToken);

                if (entityResult.IsFailure)
                {
                    return Result.Failure(entityResult.Error);
                }

                var entity = entityResult.Value;
                entity.IsDeleted = true;
                entity.DeletedBy = deletedBy;
                entity.DeletedAt = DateTime.UtcNow;

                await UpdateAsync(entity, cancellationToken);
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Checks if an entity exists
        /// </summary>
        public async Task<Result<bool>> ExistsAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Check if entity exists and is not deleted
                var exists = await CheckExistsAsync(e => e.Id == id && !e.IsDeleted);
                return Result.Success(exists);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Gets a queryable for advanced operations
        /// </summary>
        public IQueryable<TEntity> AsQueryable()
        {
            // Filter out deleted entities by default
            return Context.Set<TEntity>().Where(e => !e.IsDeleted).AsQueryable();
        }
    }
}
