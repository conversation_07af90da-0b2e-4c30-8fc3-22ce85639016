﻿using Microsoft.AspNetCore.Identity;
#nullable disable
namespace SuperCareApp.Domain.Identity
{
    public class ApplicationUserToken : IdentityUserToken<Guid>
    {
        public string Token { get; set; }
        public string TokenType { get; set; }
        public DateTime ExpiryDate { get; set; }
        public bool IsExpired { get; set; }
        public bool IsRevoked { get; set; }
        public Guid UserId { get; set; }

        public Guid LoginSessionId { get; set; }

        // Navigation property
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
