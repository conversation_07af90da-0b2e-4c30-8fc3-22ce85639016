using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings;

public class UpdateBookingWithWindowsRequest
{
    public string? SpecialInstructions { get; set; }
    public List<BookingWindowRequest> BookingWindows { get; set; } = new();
}

public class UpdateBookingWithWindowsRequestValidator
    : AbstractValidator<UpdateBookingWithWindowsRequest>
{
    public UpdateBookingWithWindowsRequestValidator()
    {
        RuleFor(x => x.BookingWindows)
            .NotEmpty()
            .WithMessage("At least one booking window is required.")
            .Must(HaveNoOverlappingWindows)
            .WithMessage(
                "Booking windows within the same request must not overlap on the same date."
            );

        RuleForEach(x => x.BookingWindows).SetValidator(new BookingWindowRequestValidator());

        RuleFor(x => x.SpecialInstructions)
            .MaximumLength(500)
            .WithMessage("Special instructions cannot exceed 500 characters.");
    }

    private bool HaveNoOverlappingWindows(List<BookingWindowRequest> windows)
    {
        if (windows == null || windows.Count <= 1)
            return true;

        var windowsByDate = windows.GroupBy(w => w.Date.Date);

        foreach (var dayGroup in windowsByDate)
        {
            var sortedWindows = dayGroup.OrderBy(w => w.StartTime).ToList();
            for (int i = 1; i < sortedWindows.Count; i++)
            {
                if (sortedWindows[i].StartTime < sortedWindows[i - 1].EndTime)
                {
                    return false;
                }
            }
        }

        return true;
    }
}
