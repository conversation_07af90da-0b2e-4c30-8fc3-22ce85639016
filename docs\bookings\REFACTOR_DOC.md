# Design Document

## Overview

The booking system refactoring will transform the current monolithic service-based architecture into a clean CQRS implementation with proper separation of concerns. The design focuses on extracting business logic into specialized services, implementing unified command/query handlers, and optimizing data access patterns.

The solution will maintain backward compatibility while providing a more maintainable, testable, and performant booking system. The architecture will follow Clean Architecture principles with clear boundaries between domain, application, and infrastructure layers.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
│              BookingsController (Refactored)                │
├─────────────────────────────────────────────────────────────┤
│              Application Layer (CQRS Handlers)              │
│  Commands: Create, Update, Cancel, Delete, UpdateStatus     │
│  Queries: GetBookings, GetBookingById, GetStatistics       │
├─────────────────────────────────────────────────────────────┤
│              Application Services (Specialized)             │
│  IBookingValidationService, IBookingCalculationService     │
│  IBookingAvailabilityService                               │
├─────────────────────────────────────────────────────────────┤
│                   Domain Layer (Enhanced)                   │
│  Booking (Enhanced), BookingWindow, BookingStatus (Fixed)   │
└─────────────────────────────────────────────────────────────┘
```

### Integration Points

1. **Existing Infrastructure**: Leverages current DbContext and repository patterns
2. **Authentication**: Uses existing ICurrentUserService for user context
3. **Validation**: Integrates with existing FluentValidation infrastructure
4. **Logging**: Maintains current structured logging patterns
5. **Error Handling**: Uses existing Result<T> pattern for consistent error handling

## Components and Interfaces

### Domain Layer Enhancements

#### Enhanced Booking Entity

```csharp
public class Booking : BaseEntity
{
    // Existing properties
    public Guid ClientId { get; set; }
    public Guid ProviderId { get; set; }
    public Guid CategoryId { get; set; }
    public int? WorkingHours { get; set; }
    public string? SpecialInstructions { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal PlatformFee { get; set; }
    public decimal ProviderAmount { get; set; }

    // Enhanced navigation properties
    public ApplicationUser Client { get; set; } = null!;
    public CareProviderProfile Provider { get; set; } = null!;
    public CareCategory Category { get; set; } = null!;
    public ICollection<BookingStatus> StatusHistory { get; set; } = new List<BookingStatus>();
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
    public ICollection<Review> Reviews { get; set; } = new List<Review>();
    public ICollection<TrackingSession> TrackingSessions { get; set; } = new List<TrackingSession>();
    public ICollection<BookingWindow> BookingWindows { get; set; } = new List<BookingWindow>();

    // Computed properties
    public BookingStatus? CurrentStatus => StatusHistory
        .OrderByDescending(s => s.CreatedAt)
        .FirstOrDefault();
    
    public BookingStatusType CurrentStatusType => CurrentStatus?.Status ?? BookingStatusType.Requested;
    
    public bool CanBeModified => CurrentStatusType.CanBeModified();
    public bool CanBeCancelled => CurrentStatusType.CanBeCancelled();
    public bool IsActive => CurrentStatusType.IsActive();
    
    public DateTime StartDate => BookingWindows.Min(w => w.Date.ToDateTime(w.StartTime));
    public DateTime EndDate => BookingWindows.Max(w => w.Date.ToDateTime(w.EndTime));
    public TimeSpan TotalDuration => BookingWindows.Sum(w => w.Duration);

    // Business methods
    public Result AddStatus(BookingStatusType status, Guid userId, string? notes = null)
    {
        var validation = ValidateStatusTransition(CurrentStatusType, status);
        if (!validation.IsSuccess) return validation;
        
        if (status.RequiresNotes() && string.IsNullOrWhiteSpace(notes))
            return Result.Failure(Error.Validation($"Status {status} requires notes"));

        StatusHistory.Add(new BookingStatus
        {
            BookingId = Id,
            Status = status,
            CreatedBy = userId,
            Notes = notes,
            CreatedAt = DateTime.UtcNow
        });
        
        return Result.Success();
    }
    
    public Result UpdateBookingWindows(List<BookingWindowRequest> newWindows, Guid userId)
    {
        // Validate new windows
        var validation = ValidateBookingWindows(newWindows);
        if (!validation.IsSuccess) return validation;
        
        // Clear existing windows
        BookingWindows.Clear();
        
        // Add new windows
        foreach (var window in newWindows)
        {
            BookingWindows.Add(new BookingWindow
            {
                BookingId = Id,
                Date = DateOnly.FromDateTime(window.Date),
                StartTime = window.StartTime,
                EndTime = window.EndTime,
                DaySpecialInstructions = window.DaySpecialInstructions,
                CreatedBy = userId,
                CreatedAt = DateTime.UtcNow
            });
        }
        
        // Recalculate working hours
        WorkingHours = (int)Math.Ceiling(TotalDuration.TotalHours);
        
        return Result.Success();
    }
    
    private Result ValidateStatusTransition(BookingStatusType from, BookingStatusType to)
    {
        var validTransitions = GetValidTransitions(from);
        if (!validTransitions.Contains(to))
            return Result.Failure(Error.BusinessRule($"Cannot transition from {from} to {to}"));
        
        return Result.Success();
    }
    
    private static List<BookingStatusType> GetValidTransitions(BookingStatusType from) => from switch
    {
        BookingStatusType.Requested => [BookingStatusType.Accepted, BookingStatusType.Rejected, BookingStatusType.Cancelled],
        BookingStatusType.Accepted => [BookingStatusType.Confirmed, BookingStatusType.Cancelled, BookingStatusType.Modified],
        BookingStatusType.Confirmed => [BookingStatusType.InProgress, BookingStatusType.Cancelled, BookingStatusType.NoShow],
        BookingStatusType.InProgress => [BookingStatusType.Completed, BookingStatusType.Cancelled],
        BookingStatusType.Modified => [BookingStatusType.Accepted, BookingStatusType.Cancelled],
        _ => []
    };
    
    private static Result ValidateBookingWindows(List<BookingWindowRequest> windows)
    {
        if (!windows.Any())
            return Result.Failure(Error.Validation("At least one booking window is required"));
        
        // Check for overlaps within same day
        var windowsByDate = windows.GroupBy(w => w.Date.Date);
        foreach (var dayGroup in windowsByDate)
        {
            var sortedWindows = dayGroup.OrderBy(w => w.StartTime).ToList();
            for (int i = 1; i < sortedWindows.Count; i++)
            {
                if (sortedWindows[i].StartTime < sortedWindows[i - 1].EndTime)
                    return Result.Failure(Error.Validation($"Booking windows overlap on {dayGroup.Key:yyyy-MM-dd}"));
            }
        }
        
        return Result.Success();
    }
}
```

#### Fixed BookingStatus Entity

```csharp
public class BookingStatus : BaseEntity
{
    public Guid BookingId { get; set; }
    public BookingStatusType Status { get; set; }
    public new Guid CreatedBy { get; set; } // Use 'new' to hide base property
    public string? Notes { get; set; }

    // Navigation properties
    public Booking Booking { get; set; } = null!;
    public ApplicationUser CreatedByUser { get; set; } = null!;
}
```

#### Enhanced BookingWindow Entity

```csharp
public class BookingWindow : BaseEntity
{
    // Existing properties
    public Guid BookingId { get; set; }
    public DateOnly Date { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public decimal? DailyRate { get; set; }
    public string? DaySpecialInstructions { get; set; }
    public int? DurationMinutes { get; set; }

    // Navigation properties
    public Booking Booking { get; set; } = null!;

    // Enhanced computed properties
    public bool IsValid => StartTime < EndTime && Date >= DateOnly.FromDateTime(DateTime.Today);
    public TimeSpan Duration => EndTime - StartTime;
    public double DurationHours => Duration.TotalHours;
    public DateTime StartDateTime => Date.ToDateTime(StartTime);
    public DateTime EndDateTime => Date.ToDateTime(EndTime);

    // Business methods
    public bool ConflictsWith(BookingWindow other) =>
        Date == other.Date && StartTime < other.EndTime && EndTime > other.StartTime;

    public bool IsWithinTimeRange(TimeOnly start, TimeOnly end) =>
        StartTime >= start && EndTime <= end;

    public bool IsInFuture() => 
        Date > DateOnly.FromDateTime(DateTime.Today) || 
        (Date == DateOnly.FromDateTime(DateTime.Today) && StartTime > TimeOnly.FromDateTime(DateTime.Now));
    
    public Result ValidateTimeRange()
    {
        if (StartTime >= EndTime)
            return Result.Failure(Error.Validation("End time must be after start time"));
        
        if (Duration < TimeSpan.FromMinutes(30))
            return Result.Failure(Error.Validation("Booking duration must be at least 30 minutes"));
        
        if (!IsInFuture())
            return Result.Failure(Error.Validation("Booking must be scheduled for a future time"));
        
        return Result.Success();
    }
}
```

#### Enhanced BookingStatusType Extensions

```csharp
public static class BookingStatusTypeExtensions
{
    public static bool CanBeModified(this BookingStatusType status) =>
        status is BookingStatusType.Requested or BookingStatusType.Accepted or 
                  BookingStatusType.Confirmed or BookingStatusType.Modified;
    
    public static bool CanBeCancelled(this BookingStatusType status) =>
        status is not (BookingStatusType.Completed or BookingStatusType.Cancelled or 
                      BookingStatusType.NoShow or BookingStatusType.Rejected);
    
    public static bool IsActive(this BookingStatusType status) =>
        status is BookingStatusType.Requested or BookingStatusType.Accepted or 
                  BookingStatusType.InProgress or BookingStatusType.Confirmed or
                  BookingStatusType.Modified;
    
    public static bool IsFinal(this BookingStatusType status) =>
        status is BookingStatusType.Completed or BookingStatusType.Cancelled or 
                  BookingStatusType.NoShow or BookingStatusType.Rejected;
    
    public static bool RequiresNotes(this BookingStatusType status) =>
        status is BookingStatusType.Cancelled or BookingStatusType.Rejected or 
                  BookingStatusType.NoShow;
}
```

### Application Layer Services

#### Booking Validation Service

```csharp
public interface IBookingValidationService
{
    Task<Result<BookingEntities>> ValidateBookingEntitiesAsync(Guid clientId, Guid providerId, Guid categoryId, CancellationToken ct = default);
    Result ValidateBookingWindows(List<BookingWindowRequest> windows);
    Task<Result> ValidateProviderAvailabilityAsync(Guid providerId, List<BookingWindowRequest> windows, CancellationToken ct = default);
    Task<Result> ValidateBookingConflictsAsync(Guid providerId, List<BookingWindowRequest> windows, Guid? excludeBookingId = null, CancellationToken ct = default);
    Result ValidateStatusTransition(BookingStatusType? from, BookingStatusType to);
    Result ValidateUserAuthorization(Guid userId, Guid clientId, Guid providerId);
}

public record BookingEntities(
    ApplicationUser Client,
    CareProviderProfile Provider,
    CareCategory Category
);

internal class BookingValidationService : IBookingValidationService
{
    private readonly ApplicationDbContext _context;
    private readonly IBookingAvailabilityService _availabilityService;

    public BookingValidationService(ApplicationDbContext context, IBookingAvailabilityService availabilityService)
    {
        _context = context;
        _availabilityService = availabilityService;
    }

    public async Task<Result<BookingEntities>> ValidateBookingEntitiesAsync(Guid clientId, Guid providerId, Guid categoryId, CancellationToken ct = default)
    {
        var client = await _context.Users.FirstOrDefaultAsync(u => u.Id == clientId, ct);
        if (client == null)
            return Result.Failure<BookingEntities>(Error.NotFound("Client not found"));

        var provider = await _context.CareProviderProfiles
            .Include(p => p.Availabilities)
            .ThenInclude(a => a.AvailabilitySlots)
            .FirstOrDefaultAsync(p => p.Id == providerId, ct);
        if (provider == null)
            return Result.Failure<BookingEntities>(Error.NotFound("Provider not found"));

        var category = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == categoryId, ct);
        if (category == null)
            return Result.Failure<BookingEntities>(Error.NotFound("Category not found"));

        return Result.Success(new BookingEntities(client, provider, category));
    }

    public Result ValidateBookingWindows(List<BookingWindowRequest> windows)
    {
        if (!windows.Any())
            return Result.Failure(Error.Validation("At least one booking window is required"));

        // Validate each window individually
        foreach (var window in windows)
        {
            if (window.StartTime >= window.EndTime)
                return Result.Failure(Error.Validation($"Invalid time range on {window.Date:yyyy-MM-dd}"));
            
            if ((window.EndTime - window.StartTime) < TimeSpan.FromMinutes(30))
                return Result.Failure(Error.Validation($"Minimum booking duration is 30 minutes on {window.Date:yyyy-MM-dd}"));
        }

        // Check for overlaps within same day
        var windowsByDate = windows.GroupBy(w => w.Date.Date);
        foreach (var dayGroup in windowsByDate)
        {
            var sortedWindows = dayGroup.OrderBy(w => w.StartTime).ToList();
            for (int i = 1; i < sortedWindows.Count; i++)
            {
                if (sortedWindows[i].StartTime < sortedWindows[i - 1].EndTime)
                    return Result.Failure(Error.Validation($"Booking windows overlap on {dayGroup.Key:yyyy-MM-dd}"));
            }
        }

        return Result.Success();
    }

    public async Task<Result> ValidateProviderAvailabilityAsync(Guid providerId, List<BookingWindowRequest> windows, CancellationToken ct = default)
    {
        foreach (var window in windows)
        {
            var availabilityResult = await _availabilityService.ValidateSlotAvailabilityAsync(providerId, window, ct);
            if (!availabilityResult.IsSuccess)
                return availabilityResult;
        }

        return Result.Success();
    }

    public async Task<Result> ValidateBookingConflictsAsync(Guid providerId, List<BookingWindowRequest> windows, Guid? excludeBookingId = null, CancellationToken ct = default)
    {
        var conflicts = await _availabilityService.FindBookingConflictsAsync(providerId, windows, excludeBookingId, ct);
        
        if (conflicts.Any())
        {
            var conflict = conflicts.First();
            return Result.Failure(Error.Conflict($"Provider has conflicting booking on {conflict.Date:yyyy-MM-dd} from {conflict.StartTime:hh\\:mm} to {conflict.EndTime:hh\\:mm}"));
        }

        return Result.Success();
    }

    public Result ValidateStatusTransition(BookingStatusType? from, BookingStatusType to)
    {
        // Implementation moved from Booking entity for reuse
        var validTransitions = GetValidTransitions(from ?? BookingStatusType.Requested);
        if (!validTransitions.Contains(to))
            return Result.Failure(Error.BusinessRule($"Cannot transition from {from} to {to}"));
        
        return Result.Success();
    }

    public Result ValidateUserAuthorization(Guid userId, Guid clientId, Guid providerId)
    {
        if (userId != clientId && userId != providerId)
            return Result.Failure(Error.Unauthorized("User is not authorized for this booking"));
        
        return Result.Success();
    }

    private static List<BookingStatusType> GetValidTransitions(BookingStatusType from) => from switch
    {
        BookingStatusType.Requested => [BookingStatusType.Accepted, BookingStatusType.Rejected, BookingStatusType.Cancelled],
        BookingStatusType.Accepted => [BookingStatusType.Confirmed, BookingStatusType.Cancelled, BookingStatusType.Modified],
        BookingStatusType.Confirmed => [BookingStatusType.InProgress, BookingStatusType.Cancelled, BookingStatusType.NoShow],
        BookingStatusType.InProgress => [BookingStatusType.Completed, BookingStatusType.Cancelled],
        BookingStatusType.Modified => [BookingStatusType.Accepted, BookingStatusType.Cancelled],
        _ => []
    };
}
```

#### Booking Calculation Service

```csharp
public interface IBookingCalculationService
{
    Task<BookingAmounts> CalculateBookingAmountsAsync(Guid providerId, Guid categoryId, List<BookingWindowRequest> windows, CancellationToken ct = default);
    TimeSpan CalculateTotalDuration(List<BookingWindowRequest> windows);
    int CalculateTotalHours(List<BookingWindowRequest> windows);
    decimal CalculateHourlyTotal(decimal hourlyRate, List<BookingWindowRequest> windows);
}

public record BookingAmounts(
    decimal TotalAmount,
    decimal PlatformFee,
    decimal ProviderAmount,
    decimal HourlyRate,
    double TotalHours,
    int TotalDays
);

internal class BookingCalculationService : IBookingCalculationService
{
    private readonly ApplicationDbContext _context;

    public BookingCalculationService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<BookingAmounts> CalculateBookingAmountsAsync(Guid providerId, Guid categoryId, List<BookingWindowRequest> windows, CancellationToken ct = default)
    {
        var provider = await _context.CareProviderProfiles.FirstOrDefaultAsync(p => p.Id == providerId, ct);
        var category = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == categoryId, ct);
        
        if (provider == null) throw new InvalidOperationException("Provider not found");
        if (category == null) throw new InvalidOperationException("Category not found");

        var totalHours = CalculateTotalDuration(windows).TotalHours;
        var totalDays = windows.Select(w => w.Date.Date).Distinct().Count();
        var hourlyRate = provider.HourlyRate ?? 0m;
        
        var providerAmount = hourlyRate * (decimal)totalHours;
        var platformFee = category.PlatformFee;
        var totalAmount = providerAmount + (providerAmount * platformFee);

        return new BookingAmounts(
            TotalAmount: totalAmount,
            PlatformFee: platformFee,
            ProviderAmount: providerAmount,
            HourlyRate: hourlyRate,
            TotalHours: totalHours,
            TotalDays: totalDays
        );
    }

    public TimeSpan CalculateTotalDuration(List<BookingWindowRequest> windows) =>
        TimeSpan.FromTicks(windows.Sum(w => (w.EndTime - w.StartTime).Ticks));

    public int CalculateTotalHours(List<BookingWindowRequest> windows) =>
        (int)Math.Ceiling(CalculateTotalDuration(windows).TotalHours);

    public decimal CalculateHourlyTotal(decimal hourlyRate, List<BookingWindowRequest> windows) =>
        hourlyRate * (decimal)CalculateTotalDuration(windows).TotalHours;
}
```

#### Booking Availability Service

```csharp
public interface IBookingAvailabilityService
{
    Task<List<Interval<TimeOnly>>> GetAvailableSlotsForDateAsync(Guid providerId, DateOnly date, CancellationToken ct = default);
    Task<Result> ValidateSlotAvailabilityAsync(Guid providerId, BookingWindowRequest window, CancellationToken ct = default);
    Task<List<BookingConflict>> FindBookingConflictsAsync(Guid providerId, List<BookingWindowRequest> windows, Guid? excludeBookingId = null, CancellationToken ct = default);
}

public record BookingConflict(
    Guid BookingId,
    DateOnly Date,
    TimeOnly StartTime,
    TimeOnly EndTime,
    BookingStatusType Status
);

internal class BookingAvailabilityService : IBookingAvailabilityService
{
    private readonly ApplicationDbContext _context;
    private readonly TimeSpan _minimumSlotDuration = TimeSpan.FromMinutes(30);

    public BookingAvailabilityService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Interval<TimeOnly>>> GetAvailableSlotsForDateAsync(Guid providerId, DateOnly date, CancellationToken ct = default)
    {
        // Implementation similar to existing BookingManagementService.GetAvailableSlotsForDateAsync
        // but optimized and focused on availability only
        var provider = await _context.CareProviderProfiles
            .Include(p => p.Availabilities)
            .ThenInclude(a => a.AvailabilitySlots)
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == providerId, ct);

        if (provider == null) return [];

        var dayOfWeek = date.DayOfWeek.ToString();
        var dailyAvailability = provider.Availabilities.FirstOrDefault(a =>
            a.DayOfWeek.Equals(dayOfWeek, StringComparison.OrdinalIgnoreCase) && a.IsAvailable);

        if (dailyAvailability == null) return [];

        var baseSlots = dailyAvailability.AvailabilitySlots
            .Select(slot => new Interval<TimeOnly>(slot.StartTime, slot.EndTime))
            .ToList();

        if (!baseSlots.Any()) return [];

        // Get blockages (leaves and existing bookings)
        var blockages = await GetBlockagesForDateAsync(providerId, date, ct);
        
        // Calculate available slots
        var availableSlots = new List<Interval<TimeOnly>>();
        foreach (var slot in IntervalUtils.Merge(baseSlots))
        {
            var remaining = IntervalUtils.Subtract(slot, blockages);
            availableSlots.AddRange(remaining);
        }

        return availableSlots
            .Where(slot => (slot.End - slot.Start) >= _minimumSlotDuration)
            .OrderBy(slot => slot.Start)
            .ToList();
    }

    public async Task<Result> ValidateSlotAvailabilityAsync(Guid providerId, BookingWindowRequest window, CancellationToken ct = default)
    {
        var dateOnly = DateOnly.FromDateTime(window.Date);
        var requestedSlot = new Interval<TimeOnly>(window.StartTime, window.EndTime);
        
        var availableSlots = await GetAvailableSlotsForDateAsync(providerId, dateOnly, ct);
        
        if (!availableSlots.Any(slot => IntervalUtils.IsContainedWithin(requestedSlot, slot)))
            return Result.Failure(Error.Conflict($"Requested time slot on {window.Date:yyyy-MM-dd} is not available"));
        
        return Result.Success();
    }

    public async Task<List<BookingConflict>> FindBookingConflictsAsync(Guid providerId, List<BookingWindowRequest> windows, Guid? excludeBookingId = null, CancellationToken ct = default)
    {
        var activeStatuses = new[]
        {
            BookingStatusType.Requested,
            BookingStatusType.Accepted,
            BookingStatusType.InProgress,
            BookingStatusType.Confirmed
        };

        var requestedDates = windows.Select(w => DateOnly.FromDateTime(w.Date)).Distinct().ToList();

        var existingWindows = await _context.Bookings
            .Where(b => b.ProviderId == providerId && 
                       (excludeBookingId == null || b.Id != excludeBookingId) &&
                       activeStatuses.Contains(b.CurrentStatus!.Status))
            .SelectMany(b => b.BookingWindows)
            .Where(bw => requestedDates.Contains(bw.Date))
            .Select(bw => new BookingConflict(
                bw.BookingId,
                bw.Date,
                bw.StartTime,
                bw.EndTime,
                bw.Booking.CurrentStatus!.Status))
            .ToListAsync(ct);

        var conflicts = new List<BookingConflict>();

        foreach (var window in windows)
        {
            var windowDate = DateOnly.FromDateTime(window.Date);
            var conflictingWindows = existingWindows
                .Where(existing => existing.Date == windowDate)
                .Where(existing => window.StartTime < existing.EndTime && window.EndTime > existing.StartTime);

            conflicts.AddRange(conflictingWindows);
        }

        return conflicts;
    }

    private async Task<List<Interval<TimeOnly>>> GetBlockagesForDateAsync(Guid providerId, DateOnly date, CancellationToken ct)
    {
        var blockages = new List<Interval<TimeOnly>>();

        // Get leaves
        var rangeStart = date.ToDateTime(TimeOnly.MinValue);
        var rangeEnd = date.ToDateTime(TimeOnly.MaxValue);

        var leaves = await _context.Leaves
            .Where(l => l.ProviderId == providerId && 
                       l.StartDate <= rangeEnd && 
                       l.EndDate >= rangeStart)
            .AsNoTracking()
            .ToListAsync(ct);

        foreach (var leave in leaves)
        {
            var leaveStart = DateOnly.FromDateTime(leave.StartDate);
            var leaveEnd = DateOnly.FromDateTime(leave.EndDate);
            
            var start = leaveStart < date ? TimeOnly.MinValue : TimeOnly.FromDateTime(leave.StartDate);
            var end = leaveEnd > date ? TimeOnly.MaxValue : TimeOnly.FromDateTime(leave.EndDate);
            
            blockages.Add(new Interval<TimeOnly>(start, end));
        }

        // Get existing bookings
        var activeStatuses = new[]
        {
            BookingStatusType.Confirmed,
            BookingStatusType.InProgress
        };

        var bookings = await _context.Bookings
            .Include(b => b.BookingWindows)
            .Where(b => b.ProviderId == providerId && 
                       activeStatuses.Contains(b.CurrentStatus!.Status) &&
                       b.BookingWindows.Any(w => w.Date == date))
            .AsNoTracking()
            .ToListAsync(ct);

        foreach (var booking in bookings)
        {
            foreach (var window in booking.BookingWindows.Where(w => w.Date == date))
            {
                blockages.Add(new Interval<TimeOnly>(window.StartTime, window.EndTime));
            }
        }

        return IntervalUtils.Merge(blockages);
    }
}
```

### Request and Response Objects

#### Request Objects

```csharp
public record CreateBookingRequest(
    Guid ProviderId,
    Guid CategoryId,
    List<BookingWindowRequest> BookingWindows,
    string? SpecialInstructions
);

public record BookingWindowRequest(
    DateTime Date,
    TimeOnly StartTime,
    TimeOnly EndTime,
    string? DaySpecialInstructions = null
);

public record UpdateBookingRequest(
    Guid CategoryId,
    List<BookingWindowRequest> BookingWindows,
    string? SpecialInstructions
);

public record UpdateBookingStatusRequest(
    BookingStatusType Status,
    string? Notes = null
);

public record CancelBookingRequest(
    string? Reason = null,
    bool NotifyOtherParty = true
);

public record BookingFilters(
    BookingStatusType? Status = null,
    Guid? ProviderId = null,
    Guid? ClientId = null,
    Guid? CategoryId = null,
    DateTime? StartDate = null,
    DateTime? EndDate = null,
    string? SearchTerm = null
);

public record PaginationParams(
    int PageNumber = 1,
    int PageSize = 20
);
```

#### Response Objects

```csharp
public record CreateBookingResponse(
    Guid BookingId,
    BookingStatusType Status,
    decimal TotalAmount,
    decimal PlatformFee,
    decimal ProviderAmount,
    int TotalHours,
    int TotalDays,
    List<BookingWindowResponse> BookingWindows,
    DateTime CreatedAt
);

public record UpdateBookingResponse(
    Guid BookingId,
    BookingStatusType Status,
    decimal TotalAmount,
    decimal PlatformFee,
    decimal ProviderAmount,
    int TotalHours,
    List<BookingWindowResponse> BookingWindows,
    DateTime UpdatedAt,
    string? SpecialInstructions
);

public record BookingWindowResponse(
    Guid BookingWindowId,
    DateTime Date,
    TimeSpan StartTime,
    TimeSpan EndTime,
    string? DaySpecialInstructions = null,
    BookingWindowStatus Status = BookingWindowStatus.Active
);

public record BookingListItem(
    Guid BookingId,
    string ClientName,
    string ProviderName,
    string ServiceType,
    BookingStatusType Status,
    DateTime StartDate,
    DateTime EndDate,
    decimal TotalAmount,
    int WindowCount,
    DateTime CreatedAt
);

public record BookingDetails(
    Guid BookingId,
    ClientInfo Client,
    ProviderInfo Provider,
    ServiceInfo Service,
    BookingStatusType CurrentStatus,
    List<BookingStatusHistory> StatusHistory,
    List<BookingWindowDetails> Windows,
    PaymentInfo Payment,
    decimal TotalAmount,
    decimal PlatformFee,
    decimal ProviderAmount,
    int TotalHours,
    string? SpecialInstructions,
    DateTime CreatedAt,
    DateTime? UpdatedAt
);

public record ClientInfo(
    Guid ClientId,
    string Name,
    string Email,
    string PhoneNumber,
    AddressInfo? Address
);

public record ProviderInfo(
    Guid ProviderId,
    string Name,
    string Email,
    string PhoneNumber,
    int YearsOfExperience,
    decimal Rating,
    decimal HourlyRate
);

public record ServiceInfo(
    Guid CategoryId,
    string Name,
    string Description,
    decimal PlatformFee
);

public record BookingStatusHistory(
    Guid StatusId,
    BookingStatusType Status,
    string StatusDescription,
    string? Notes,
    string CreatedByName,
    DateTime CreatedAt
);

public record BookingWindowDetails(
    Guid WindowId,
    DateTime Date,
    TimeOnly StartTime,
    TimeOnly EndTime,
    TimeSpan Duration,
    string? DaySpecialInstructions,
    BookingWindowStatus Status
);

public record PaymentInfo(
    decimal TotalPaid,
    decimal OutstandingAmount,
    List<PaymentDetails> Payments
);

public record PaymentDetails(
    Guid PaymentId,
    decimal Amount,
    PaymentStatus Status,
    DateTime CreatedAt
);

public record AddressInfo(
    string StreetAddress,
    string City,
    string State,
    string PostalCode,
    decimal? Latitude,
    decimal? Longitude
);

public record PagedList<T>(
    List<T> Items,
    int TotalCount,
    int PageNumber,
    int PageSize,
    int TotalPages
)
{
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
```

### Enhanced Domain Model

#### BookingWindow Status Management

```csharp
public enum BookingWindowStatus
{
    Active,
    Cancelled,
    Modified,
    Completed
}

public class BookingWindow : BaseEntity
{
    // Existing properties...
    public BookingWindowStatus Status { get; set; } = BookingWindowStatus.Active;
    public Guid? ReplacedByWindowId { get; set; } // For tracking modifications
    public string? CancellationReason { get; set; }
    public DateTime? CancelledAt { get; set; }
    public Guid? CancelledBy { get; set; }

    // Navigation properties
    public Booking Booking { get; set; } = null!;
    public BookingWindow? ReplacedByWindow { get; set; }
    public ApplicationUser? CancelledByUser { get; set; }

    // Enhanced computed properties
    public bool IsActive => Status == BookingWindowStatus.Active;
    public bool IsCancelled => Status == BookingWindowStatus.Cancelled;
    public bool CanBeCancelled => Status == BookingWindowStatus.Active && IsInFuture();
    public bool CanBeModified => Status == BookingWindowStatus.Active && IsInFuture();

    // Business methods
    public Result Cancel(Guid userId, string? reason = null)
    {
        if (!CanBeCancelled)
            return Result.Failure(Error.BusinessRule("Booking window cannot be cancelled"));

        Status = BookingWindowStatus.Cancelled;
        CancellationReason = reason;
        CancelledAt = DateTime.UtcNow;
        CancelledBy = userId;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = userId;

        return Result.Success();
    }

    public Result MarkAsReplaced(Guid replacementWindowId, Guid userId)
    {
        if (!CanBeModified)
            return Result.Failure(Error.BusinessRule("Booking window cannot be modified"));

        Status = BookingWindowStatus.Modified;
        ReplacedByWindowId = replacementWindowId;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = userId;

        return Result.Success();
    }
}

public static class BookingWindowStatusExtensions
{
    public static string GetDescription(this BookingWindowStatus status) => status switch
    {
        BookingWindowStatus.Active => "Active",
        BookingWindowStatus.Cancelled => "Cancelled",
        BookingWindowStatus.Modified => "Modified",
        BookingWindowStatus.Completed => "Completed",
        _ => "Unknown"
    };
}
```

### Command and Query Handlers

#### Create Booking Command

```csharp
public record CreateBookingCommand(
    Guid UserId,
    CreateBookingRequest Request
) : ICommand<Result<CreateBookingResponse>>;

public record CreateBookingResponse(
    Guid BookingId,
    BookingStatusType Status,
    decimal TotalAmount,
    decimal PlatformFee,
    decimal ProviderAmount,
    int TotalHours,
    int TotalDays,
    List<BookingWindowResponse> BookingWindows,
    DateTime CreatedAt
);

internal sealed class CreateBookingCommandHandler : ICommandHandler<CreateBookingCommand, Result<CreateBookingResponse>>
{
    private readonly ApplicationDbContext _context;
    private readonly IBookingValidationService _validationService;
    private readonly IBookingCalculationService _calculationService;
    private readonly ILogger<CreateBookingCommandHandler> _logger;

    public CreateBookingCommandHandler(
        ApplicationDbContext context,
        IBookingValidationService validationService,
        IBookingCalculationService calculationService,
        ILogger<CreateBookingCommandHandler> logger)
    {
        _context = context;
        _validationService = validationService;
        _calculationService = calculationService;
        _logger = logger;
    }

    public async Task<Result<CreateBookingResponse>> Handle(CreateBookingCommand command, CancellationToken ct)
    {
        try
        {
            var (userId, request) = command;

            // 1. Validate entities exist
            var entitiesResult = await _validationService.ValidateBookingEntitiesAsync(
                userId, request.ProviderId, request.CategoryId, ct);
            if (!entitiesResult.IsSuccess)
                return Result.Failure<CreateBookingResponse>(entitiesResult.Error);

            var entities = entitiesResult.Value;

            // 2. Validate booking windows
            var windowsValidation = _validationService.ValidateBookingWindows(request.BookingWindows);
            if (!windowsValidation.IsSuccess)
                return Result.Failure<CreateBookingResponse>(windowsValidation.Error);

            // 3. Validate provider availability
            var availabilityValidation = await _validationService.ValidateProviderAvailabilityAsync(
                request.ProviderId, request.BookingWindows, ct);
            if (!availabilityValidation.IsSuccess)
                return Result.Failure<CreateBookingResponse>(availabilityValidation.Error);

            // 4. Check for booking conflicts
            var conflictValidation = await _validationService.ValidateBookingConflictsAsync(
                request.ProviderId, request.BookingWindows, null, ct);
            if (!conflictValidation.IsSuccess)
                return Result.Failure<CreateBookingResponse>(conflictValidation.Error);

            // 5. Calculate amounts
            var amounts = await _calculationService.CalculateBookingAmountsAsync(
                request.ProviderId, request.CategoryId, request.BookingWindows, ct);

            // 6. Create booking in transaction
            await using var transaction = await _context.Database.BeginTransactionAsync(ct);
            try
            {
                var booking = await CreateBookingEntityAsync(userId, request, amounts, ct);
                await _context.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);

                var response = CreateResponse(booking, amounts);
                
                _logger.LogInformation("Booking {BookingId} created successfully for client {ClientId} and provider {ProviderId}",
                    booking.Id, userId, request.ProviderId);

                return Result.Success(response);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating booking for user {UserId}", command.UserId);
            return Result.Failure<CreateBookingResponse>(Error.Internal("Error creating booking"));
        }
    }

    private async Task<Booking> CreateBookingEntityAsync(Guid userId, CreateBookingRequest request, BookingAmounts amounts, CancellationToken ct)
    {
        var booking = new Booking
        {
            ClientId = userId,
            ProviderId = request.ProviderId,
            CategoryId = request.CategoryId,
            TotalAmount = amounts.TotalAmount,
            PlatformFee = amounts.PlatformFee,
            ProviderAmount = amounts.ProviderAmount,
            SpecialInstructions = request.SpecialInstructions,
            WorkingHours = amounts.TotalHours,
            CreatedBy = userId,
            CreatedAt = DateTime.UtcNow
        };

        // Add initial status
        booking.StatusHistory.Add(new BookingStatus
        {
            BookingId = booking.Id,
            Status = BookingStatusType.Requested,
            CreatedBy = userId,
            Notes = "Booking created",
            CreatedAt = DateTime.UtcNow
        });

        // Add booking windows
        foreach (var window in request.BookingWindows)
        {
            booking.BookingWindows.Add(new BookingWindow
            {
                BookingId = booking.Id,
                Date = DateOnly.FromDateTime(window.Date),
                StartTime = window.StartTime,
                EndTime = window.EndTime,
                DaySpecialInstructions = window.DaySpecialInstructions,
                CreatedBy = userId,
                CreatedAt = DateTime.UtcNow
            });
        }

        await _context.Bookings.AddAsync(booking, ct);
        return booking;
    }

    private static CreateBookingResponse CreateResponse(Booking booking, BookingAmounts amounts)
    {
        return new CreateBookingResponse(
            BookingId: booking.Id,
            Status: booking.CurrentStatusType,
            TotalAmount: amounts.TotalAmount,
            PlatformFee: amounts.PlatformFee,
            ProviderAmount: amounts.ProviderAmount,
            TotalHours: amounts.TotalHours,
            TotalDays: amounts.TotalDays,
            BookingWindows: booking.BookingWindows.Select(w => new BookingWindowResponse
            {
                BookingWindowId = w.Id,
                Date = w.StartDateTime,
                StartTime = w.StartTime.ToTimeSpan(),
                EndTime = w.EndTime.ToTimeSpan(),
                DaySpecialInstructions = w.DaySpecialInstructions
            }).ToList(),
            CreatedAt: booking.CreatedAt
        );
    }
}
```

#### Update Booking Command

```csharp
public record UpdateBookingCommand(
    Guid UserId,
    Guid BookingId,
    UpdateBookingRequest Request
) : ICommand<Result<UpdateBookingResponse>>;

internal sealed class UpdateBookingCommandHandler : ICommandHandler<UpdateBookingCommand, Result<UpdateBookingResponse>>
{
    private readonly ApplicationDbContext _context;
    private readonly IBookingValidationService _validationService;
    private readonly IBookingCalculationService _calculationService;
    private readonly ILogger<UpdateBookingCommandHandler> _logger;

    public UpdateBookingCommandHandler(
        ApplicationDbContext context,
        IBookingValidationService validationService,
        IBookingCalculationService calculationService,
        ILogger<UpdateBookingCommandHandler> logger)
    {
        _context = context;
        _validationService = validationService;
        _calculationService = calculationService;
        _logger = logger;
    }

    public async Task<Result<UpdateBookingResponse>> Handle(UpdateBookingCommand command, CancellationToken ct)
    {
        try
        {
            var (userId, bookingId, request) = command;

            // 1. Load existing booking
            var booking = await _context.Bookings
                .Include(b => b.BookingWindows)
                .Include(b => b.StatusHistory)
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, ct);

            if (booking == null)
                return Result.Failure<UpdateBookingResponse>(Error.NotFound("Booking not found"));

            // 2. Validate user authorization
            var authValidation = _validationService.ValidateUserAuthorization(userId, booking.ClientId, booking.ProviderId);
            if (!authValidation.IsSuccess)
                return Result.Failure<UpdateBookingResponse>(authValidation.Error);

            // 3. Validate booking can be modified
            if (!booking.CanBeModified)
                return Result.Failure<UpdateBookingResponse>(Error.BusinessRule($"Booking with status {booking.CurrentStatusType} cannot be modified"));

            // 4. Validate new booking windows
            var windowsValidation = _validationService.ValidateBookingWindows(request.BookingWindows);
            if (!windowsValidation.IsSuccess)
                return Result.Failure<UpdateBookingResponse>(windowsValidation.Error);

            // 5. Validate provider availability for new windows
            var availabilityValidation = await _validationService.ValidateProviderAvailabilityAsync(
                booking.ProviderId, request.BookingWindows, ct);
            if (!availabilityValidation.IsSuccess)
                return Result.Failure<UpdateBookingResponse>(availabilityValidation.Error);

            // 6. Check for conflicts (excluding current booking)
            var conflictValidation = await _validationService.ValidateBookingConflictsAsync(
                booking.ProviderId, request.BookingWindows, bookingId, ct);
            if (!conflictValidation.IsSuccess)
                return Result.Failure<UpdateBookingResponse>(conflictValidation.Error);

            // 7. Calculate new amounts
            var amounts = await _calculationService.CalculateBookingAmountsAsync(
                booking.ProviderId, request.CategoryId, request.BookingWindows, ct);

            // 8. Update booking in transaction
            await using var transaction = await _context.Database.BeginTransactionAsync(ct);
            try
            {
                // Update booking windows
                var windowsUpdateResult = booking.UpdateBookingWindows(request.BookingWindows, userId);
                if (!windowsUpdateResult.IsSuccess)
                    return Result.Failure<UpdateBookingResponse>(windowsUpdateResult.Error);

                // Update booking properties
                booking.CategoryId = request.CategoryId;
                booking.SpecialInstructions = request.SpecialInstructions;
                booking.TotalAmount = amounts.TotalAmount;
                booking.PlatformFee = amounts.PlatformFee;
                booking.ProviderAmount = amounts.ProviderAmount;
                booking.WorkingHours = amounts.TotalHours;
                booking.UpdatedAt = DateTime.UtcNow;
                booking.UpdatedBy = userId;

                // Add status history
                var statusResult = booking.AddStatus(BookingStatusType.Modified, userId, "Booking updated");
                if (!statusResult.IsSuccess)
                    return Result.Failure<UpdateBookingResponse>(statusResult.Error);

                await _context.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);

                var response = CreateUpdateResponse(booking, amounts);
                
                _logger.LogInformation("Booking {BookingId} updated successfully by user {UserId}", bookingId, userId);
                
                return Result.Success(response);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating booking {BookingId} for user {UserId}", command.BookingId, command.UserId);
            return Result.Failure<UpdateBookingResponse>(Error.Internal("Error updating booking"));
        }
    }

    private static UpdateBookingResponse CreateUpdateResponse(Booking booking, BookingAmounts amounts)
    {
        return new UpdateBookingResponse(
            BookingId: booking.Id,
            Status: booking.CurrentStatusType,
            TotalAmount: amounts.TotalAmount,
            PlatformFee: amounts.PlatformFee,
            ProviderAmount: amounts.ProviderAmount,
            TotalHours: amounts.TotalHours,
            BookingWindows: booking.BookingWindows.Select(w => new BookingWindowResponse(
                w.Id,
                w.StartDateTime,
                w.StartTime.ToTimeSpan(),
                w.EndTime.ToTimeSpan(),
                w.DaySpecialInstructions,
                w.Status
            )).ToList(),
            UpdatedAt: booking.UpdatedAt ?? DateTime.UtcNow,
            SpecialInstructions: booking.SpecialInstructions
        );
    }
}
```

#### Update Booking Status Command

```csharp
public record UpdateBookingStatusCommand(
    Guid UserId,
    Guid BookingId,
    BookingStatusType Status,
    string? Notes = null
) : ICommand<Result>;

internal sealed class UpdateBookingStatusCommandHandler : ICommandHandler<UpdateBookingStatusCommand, Result>
{
    private readonly ApplicationDbContext _context;
    private readonly IBookingValidationService _validationService;
    private readonly ILogger<UpdateBookingStatusCommandHandler> _logger;

    public UpdateBookingStatusCommandHandler(
        ApplicationDbContext context,
        IBookingValidationService validationService,
        ILogger<UpdateBookingStatusCommandHandler> logger)
    {
        _context = context;
        _validationService = validationService;
        _logger = logger;
    }

    public async Task<Result> Handle(UpdateBookingStatusCommand command, CancellationToken ct)
    {
        try
        {
            var (userId, bookingId, status, notes) = command;

            // 1. Load booking with status history
            var booking = await _context.Bookings
                .Include(b => b.StatusHistory)
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, ct);

            if (booking == null)
                return Result.Failure(Error.NotFound("Booking not found"));

            // 2. Validate user authorization
            var authValidation = _validationService.ValidateUserAuthorization(userId, booking.ClientId, booking.ProviderId);
            if (!authValidation.IsSuccess)
                return authValidation;

            // 3. Validate status transition
            var transitionValidation = _validationService.ValidateStatusTransition(booking.CurrentStatusType, status);
            if (!transitionValidation.IsSuccess)
                return transitionValidation;

            // 4. Add new status
            var statusResult = booking.AddStatus(status, userId, notes);
            if (!statusResult.IsSuccess)
                return statusResult;

            // 5. Save changes
            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("Booking {BookingId} status updated to {Status} by user {UserId}", 
                bookingId, status, userId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating booking status for booking {BookingId}", command.BookingId);
            return Result.Failure(Error.Internal("Error updating booking status"));
        }
    }
}
```

#### Cancel Booking Command

```csharp
public record CancelBookingCommand(
    Guid UserId,
    Guid BookingId,
    string? Reason = null,
    bool NotifyOtherParty = true
) : ICommand<Result>;

internal sealed class CancelBookingCommandHandler : ICommandHandler<CancelBookingCommand, Result>
{
    private readonly ApplicationDbContext _context;
    private readonly IBookingValidationService _validationService;
    private readonly ILogger<CancelBookingCommandHandler> _logger;

    public CancelBookingCommandHandler(
        ApplicationDbContext context,
        IBookingValidationService validationService,
        ILogger<CancelBookingCommandHandler> logger)
    {
        _context = context;
        _validationService = validationService;
        _logger = logger;
    }

    public async Task<Result> Handle(CancelBookingCommand command, CancellationToken ct)
    {
        try
        {
            var (userId, bookingId, reason, notifyOtherParty) = command;

            // 1. Load booking with status history and windows
            var booking = await _context.Bookings
                .Include(b => b.StatusHistory)
                .Include(b => b.BookingWindows)
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, ct);

            if (booking == null)
                return Result.Failure(Error.NotFound("Booking not found"));

            // 2. Validate user authorization
            var authValidation = _validationService.ValidateUserAuthorization(userId, booking.ClientId, booking.ProviderId);
            if (!authValidation.IsSuccess)
                return authValidation;

            // 3. Validate booking can be cancelled
            if (!booking.CanBeCancelled)
                return Result.Failure(Error.BusinessRule($"Booking with status {booking.CurrentStatusType} cannot be cancelled"));

            // 4. Cancel all active booking windows
            foreach (var window in booking.BookingWindows.Where(w => w.CanBeCancelled))
            {
                var cancelResult = window.Cancel(userId, reason);
                if (!cancelResult.IsSuccess)
                    return cancelResult;
            }

            // 5. Add cancellation status
            var statusResult = booking.AddStatus(BookingStatusType.Cancelled, userId, reason ?? "Booking cancelled");
            if (!statusResult.IsSuccess)
                return statusResult;

            // 6. Save changes
            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("Booking {BookingId} cancelled by user {UserId}. Reason: {Reason}", 
                bookingId, userId, reason ?? "No reason provided");

            // TODO: Implement notification logic if notifyOtherParty is true

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling booking {BookingId}", command.BookingId);
            return Result.Failure(Error.Internal("Error cancelling booking"));
        }
    }
}
```

### Read Models and Query Handlers

#### Get All Bookings Query

```csharp
public record GetAllBookingsQuery(
    Guid? UserId,
    BookingFilters Filters,
    PaginationParams Pagination
) : IQuery<Result<PagedList<BookingListItem>>>;

internal sealed class GetAllBookingsQueryHandler : IQueryHandler<GetAllBookingsQuery, Result<PagedList<BookingListItem>>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<GetAllBookingsQueryHandler> _logger;

    public GetAllBookingsQueryHandler(ApplicationDbContext context, ILogger<GetAllBookingsQueryHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Result<PagedList<BookingListItem>>> Handle(GetAllBookingsQuery query, CancellationToken ct)
    {
        try
        {
            var baseQuery = _context.Bookings
                .AsNoTracking()
                .AsSplitQuery()
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserProfile)
                .Include(b => b.Provider)
                    .ThenInclude(p => p.User)
                    .ThenInclude(u => u.UserProfile)
                .Include(b => b.Category)
                .Include(b => b.StatusHistory)
                .Include(b => b.BookingWindows)
                .Where(b => !b.IsDeleted);

            // Apply filters
            baseQuery = ApplyFilters(baseQuery, query.Filters, query.UserId);

            // Get total count efficiently
            var totalCount = await baseQuery.CountAsync(ct);

            if (totalCount == 0)
            {
                return Result.Success(new PagedList<BookingListItem>(
                    [], 0, query.Pagination.PageNumber, query.Pagination.PageSize));
            }

            // Calculate pagination
            var totalPages = (int)Math.Ceiling((double)totalCount / query.Pagination.PageSize);

            // Apply pagination and projection
            var items = await baseQuery
                .OrderByDescending(b => b.CreatedAt)
                .Skip((query.Pagination.PageNumber - 1) * query.Pagination.PageSize)
                .Take(query.Pagination.PageSize)
                .Select(b => new BookingListItem(
                    b.Id,
                    $"{b.Client.UserProfile!.FirstName} {b.Client.UserProfile.LastName}".Trim(),
                    $"{b.Provider.User.UserProfile!.FirstName} {b.Provider.User.UserProfile.LastName}".Trim(),
                    b.Category.Name,
                    b.StatusHistory.OrderByDescending(s => s.CreatedAt).First().Status,
                    b.BookingWindows.Where(w => w.Status == BookingWindowStatus.Active).Min(w => w.Date.ToDateTime(w.StartTime)),
                    b.BookingWindows.Where(w => w.Status == BookingWindowStatus.Active).Max(w => w.Date.ToDateTime(w.EndTime)),
                    b.TotalAmount,
                    b.BookingWindows.Count(w => w.Status == BookingWindowStatus.Active),
                    b.CreatedAt
                ))
                .ToListAsync(ct);

            var pagedList = new PagedList<BookingListItem>(
                items, totalCount, query.Pagination.PageNumber, query.Pagination.PageSize);

            return Result.Success(pagedList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving bookings with filters {@Filters}", query.Filters);
            return Result.Failure<PagedList<BookingListItem>>(Error.Internal("Error retrieving bookings"));
        }
    }

    private static IQueryable<Booking> ApplyFilters(IQueryable<Booking> query, BookingFilters filters, Guid? userId)
    {
        // User-specific filtering
        if (userId.HasValue)
        {
            query = query.Where(b => b.ClientId == userId || b.ProviderId == userId);
        }

        // Status filtering
        if (filters.Status.HasValue)
        {
            query = query.Where(b => b.StatusHistory
                .OrderByDescending(s => s.CreatedAt)
                .First().Status == filters.Status);
        }

        // Provider filtering
        if (filters.ProviderId.HasValue)
        {
            query = query.Where(b => b.ProviderId == filters.ProviderId);
        }

        // Client filtering
        if (filters.ClientId.HasValue)
        {
            query = query.Where(b => b.ClientId == filters.ClientId);
        }

        // Category filtering
        if (filters.CategoryId.HasValue)
        {
            query = query.Where(b => b.CategoryId == filters.CategoryId);
        }

        // Date range filtering
        if (filters.StartDate.HasValue)
        {
            var startDate = DateOnly.FromDateTime(filters.StartDate.Value);
            query = query.Where(b => b.BookingWindows
                .Any(w => w.Date >= startDate && w.Status == BookingWindowStatus.Active));
        }

        if (filters.EndDate.HasValue)
        {
            var endDate = DateOnly.FromDateTime(filters.EndDate.Value);
            query = query.Where(b => b.BookingWindows
                .Any(w => w.Date <= endDate && w.Status == BookingWindowStatus.Active));
        }

        // Search term filtering
        if (!string.IsNullOrWhiteSpace(filters.SearchTerm))
        {
            var searchTerm = filters.SearchTerm.ToLower();
            query = query.Where(b => 
                b.Client.UserProfile!.FirstName.ToLower().Contains(searchTerm) ||
                b.Client.UserProfile.LastName.ToLower().Contains(searchTerm) ||
                b.Provider.User.UserProfile!.FirstName.ToLower().Contains(searchTerm) ||
                b.Provider.User.UserProfile.LastName.ToLower().Contains(searchTerm) ||
                b.Category.Name.ToLower().Contains(searchTerm) ||
                (b.SpecialInstructions != null && b.SpecialInstructions.ToLower().Contains(searchTerm)));
        }

        return query;
    }
}
```

#### Get Booking By ID Query

```csharp
public record GetBookingByIdQuery(
    Guid BookingId,
    Guid? UserId = null
) : IQuery<Result<BookingDetails>>;

internal sealed class GetBookingByIdQueryHandler : IQueryHandler<GetBookingByIdQuery, Result<BookingDetails>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<GetBookingByIdQueryHandler> _logger;

    public GetBookingByIdQueryHandler(ApplicationDbContext context, ILogger<GetBookingByIdQueryHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Result<BookingDetails>> Handle(GetBookingByIdQuery query, CancellationToken ct)
    {
        try
        {
            var booking = await _context.Bookings
                .AsNoTracking()
                .AsSplitQuery()
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserProfile)
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserAddresses.Where(ua => !ua.IsDeleted && ua.IsPrimary))
                    .ThenInclude(ua => ua.Address)
                .Include(b => b.Provider)
                    .ThenInclude(p => p.User)
                    .ThenInclude(u => u.UserProfile)
                .Include(b => b.Category)
                .Include(b => b.StatusHistory)
                    .ThenInclude(s => s.CreatedByUser)
                    .ThenInclude(u => u.UserProfile)
                .Include(b => b.BookingWindows)
                .Include(b => b.Payments.Where(p => !p.IsDeleted))
                .Where(b => b.Id == query.BookingId && !b.IsDeleted)
                .FirstOrDefaultAsync(ct);

            if (booking == null)
                return Result.Failure<BookingDetails>(Error.NotFound("Booking not found"));

            // Validate user authorization if UserId is provided
            if (query.UserId.HasValue && 
                query.UserId != booking.ClientId && 
                query.UserId != booking.ProviderId)
            {
                return Result.Failure<BookingDetails>(Error.Unauthorized("User is not authorized to view this booking"));
            }

            var bookingDetails = CreateBookingDetails(booking);
            return Result.Success(bookingDetails);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving booking {BookingId}", query.BookingId);
            return Result.Failure<BookingDetails>(Error.Internal("Error retrieving booking"));
        }
    }

    private static BookingDetails CreateBookingDetails(Booking booking)
    {
        var primaryAddress = booking.Client.UserAddresses?.FirstOrDefault(ua => ua.IsPrimary && !ua.IsDeleted);

        var clientInfo = new ClientInfo(
            booking.ClientId,
            $"{booking.Client.UserProfile?.FirstName} {booking.Client.UserProfile?.LastName}".Trim(),
            booking.Client.Email ?? string.Empty,
            booking.Client.PhoneNumber ?? string.Empty,
            primaryAddress != null ? new AddressInfo(
                primaryAddress.Address?.StreetAddress ?? string.Empty,
                primaryAddress.Address?.City ?? string.Empty,
                primaryAddress.Address?.State ?? string.Empty,
                primaryAddress.Address?.PostalCode ?? string.Empty,
                primaryAddress.Address?.Latitude,
                primaryAddress.Address?.Longitude
            ) : null
        );

        var providerInfo = new ProviderInfo(
            booking.ProviderId,
            $"{booking.Provider.User.UserProfile?.FirstName} {booking.Provider.User.UserProfile?.LastName}".Trim(),
            booking.Provider.User.Email ?? string.Empty,
            booking.Provider.User.PhoneNumber ?? string.Empty,
            booking.Provider.YearsExperience ?? 0,
            booking.Provider.Rating ?? 0,
            booking.Provider.HourlyRate ?? 0
        );

        var serviceInfo = new ServiceInfo(
            booking.CategoryId,
            booking.Category.Name,
            booking.Category.Description ?? string.Empty,
            booking.Category.PlatformFee
        );

        var statusHistory = booking.StatusHistory
            .OrderByDescending(s => s.CreatedAt)
            .Select(s => new BookingStatusHistory(
                s.Id,
                s.Status,
                s.Status.GetDescription(),
                s.Notes,
                $"{s.CreatedByUser?.UserProfile?.FirstName} {s.CreatedByUser?.UserProfile?.LastName}".Trim(),
                s.CreatedAt
            ))
            .ToList();

        var windows = booking.BookingWindows
            .OrderBy(w => w.Date)
            .ThenBy(w => w.StartTime)
            .Select(w => new BookingWindowDetails(
                w.Id,
                w.StartDateTime,
                w.StartTime,
                w.EndTime,
                w.Duration,
                w.DaySpecialInstructions,
                w.Status
            ))
            .ToList();

        var totalPaid = booking.Payments.Where(p => p.Status == PaymentStatus.Completed).Sum(p => p.Amount);
        var outstandingAmount = booking.TotalAmount - totalPaid;

        var paymentDetails = booking.Payments
            .OrderByDescending(p => p.CreatedAt)
            .Select(p => new PaymentDetails(
                p.Id,
                p.Amount,
                p.Status,
                p.CreatedAt
            ))
            .ToList();

        var paymentInfo = new PaymentInfo(totalPaid, outstandingAmount, paymentDetails);

        var currentStatus = statusHistory.FirstOrDefault()?.Status ?? BookingStatusType.Requested;

        return new BookingDetails(
            booking.Id,
            clientInfo,
            providerInfo,
            serviceInfo,
            currentStatus,
            statusHistory,
            windows,
            paymentInfo,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.ProviderAmount,
            booking.WorkingHours ?? 0,
            booking.SpecialInstructions,
            booking.CreatedAt,
            booking.UpdatedAt
        );
    }
}
```

## Error Handling

### Centralized Error Types

```csharp
public static class BookingErrors
{
    public static Error BookingNotFound(Guid bookingId) => 
        Error.NotFound($"Booking {bookingId} not found");
    
    public static Error InvalidStatusTransition(BookingStatusType from, BookingStatusType to) => 
        Error.BusinessRule($"Cannot transition booking status from {from} to {to}");
    
    public static Error BookingWindowOverlap(DateTime date) => 
        Error.Validation($"Booking windows overlap on {date:yyyy-MM-dd}");
    
    public static Error ProviderNotAvailable(DateTime date, TimeOnly startTime, TimeOnly endTime) => 
        Error.Conflict($"Provider is not available on {date:yyyy-MM-dd} from {startTime:hh\\:mm} to {endTime:hh\\:mm}");
    
    public static Error BookingConflict(DateTime date, TimeOnly startTime, TimeOnly endTime) => 
        Error.Conflict($"Provider has conflicting booking on {date:yyyy-MM-dd} from {startTime:hh\\:mm} to {endTime:hh\\:mm}");
    
    public static Error UnauthorizedBookingAccess() => 
        Error.Unauthorized("User is not authorized to access this booking");
    
    public static Error BookingCannotBeModified(BookingStatusType status) => 
        Error.BusinessRule($"Booking with status {status} cannot be modified");
    
    public static Error StatusRequiresNotes(BookingStatusType status) => 
        Error.Validation($"Status {status} requires notes");
}
```

## Testing Strategy

### Unit Tests

1. **Domain Entity Tests**
   - Booking business methods
   - Status transition validation
   - BookingWindow validation and calculations

2. **Service Tests**
   - BookingValidationService with various scenarios
   - BookingCalculationService with different rate structures
   - BookingAvailabilityService with complex availability patterns

3. **Command Handler Tests**
   - All success and failure paths
   - Transaction rollback scenarios
   - Validation integration

4. **Query Handler Tests**
   - Filtering and pagination
   - Projection accuracy
   - Performance with large datasets

### Integration Tests

1. **End-to-End Booking Workflows**
   - Complete booking creation process
   - Multi-day booking scenarios
   - Status transition workflows

2. **Database Integration**
   - Entity Framework configuration
   - Query performance
   - Transaction handling

3. **API Integration**
   - Controller endpoint behavior
   - Request/response mapping
   - Error handling

This design provides a solid foundation for a maintainable, testable, and performant booking system that properly implements CQRS patterns while handling complex multi-day booking scenarios elegantly.