# Domain and Integration Events Guide

## Overview

This guide explains how to use Domain Events and Integration Events in the SuperCareApp using our custom Mediator implementation. This documentation is designed for junior developers and includes practical examples.

## Table of Contents

1. [Understanding Events](#understanding-events)
2. [Domain Events](#domain-events)
3. [Integration Events](#integration-events)
4. [Setting Up the Mediator](#setting-up-the-mediator)
5. [Creating and Publishing Events](#creating-and-publishing-events)
6. [Event Handlers](#event-handlers)
7. [Best Practices](#best-practices)
8. [Common Patterns](#common-patterns)
9. [Troubleshooting](#troubleshooting)

## Understanding Events

### What are Events?

Events represent something that has happened in your application. They are used to decouple different parts of your system and enable reactive programming.

**Domain Events**: Events that occur within your business domain (e.g., "User registered", "Booking created")
**Integration Events**: Events that cross system boundaries and may be consumed by external systems

### Event Flow

```
Action → Domain Event → Event Handler(s) → Side Effects
```

## Domain Events

Domain events implement the `IDomainEvent` interface and are used for in-process communication within your application.

### Creating a Domain Event

```csharp
using SuperCareApp.Application.Common.Interfaces;

namespace SuperCareApp.Application.Events.Domain
{
    public class UserRegisteredDomainEvent : IDomainEvent
    {
        public Guid UserId { get; }
        public string Email { get; }
        public DateTime RegisteredAt { get; }

        public UserRegisteredDomainEvent(Guid userId, string email, DateTime registeredAt)
        {
            UserId = userId;
            Email = email;
            RegisteredAt = registeredAt;
        }
    }
}
```

### Creating a Domain Event Handler

```csharp
using SuperCareApp.Application.Common.Interfaces;
using Microsoft.Extensions.Logging;

namespace SuperCareApp.Application.Events.Handlers
{
    public class UserRegisteredDomainEventHandler : IDomainEventHandler<UserRegisteredDomainEvent>
    {
        private readonly ILogger<UserRegisteredDomainEventHandler> _logger;
        private readonly IEmailService _emailService;

        public UserRegisteredDomainEventHandler(
            ILogger<UserRegisteredDomainEventHandler> logger,
            IEmailService emailService)
        {
            _logger = logger;
            _emailService = emailService;
        }

        public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Handling user registration for user {UserId}", notification.UserId);

            // Send welcome email
            await _emailService.SendWelcomeEmailAsync(notification.Email, cancellationToken);

            // Log the event
            _logger.LogInformation("Welcome email sent to {Email}", notification.Email);
        }
    }
}
```

### Publishing Domain Events

```csharp
using SuperCareApp.Application.Common.Interfaces.Mediator;

public class UserService
{
    private readonly IMediator _mediator;

    public UserService(IMediator mediator)
    {
        _mediator = mediator;
    }

    public async Task RegisterUserAsync(string email, CancellationToken cancellationToken)
    {
        // Create user logic here...
        var userId = Guid.NewGuid();
        var registeredAt = DateTime.UtcNow;

        // Publish domain event
        var domainEvent = new UserRegisteredDomainEvent(userId, email, registeredAt);
        await _mediator.Publish(domainEvent, cancellationToken);
    }
}
```

## Integration Events

Integration events implement the `IIntegrationEvent` interface and are used for cross-system communication.

### Creating an Integration Event

```csharp
using SuperCareApp.Application.Common.Interfaces;
using System.Text.Json;

namespace SuperCareApp.Application.Events.Integration
{
    public class BookingCreatedIntegrationEvent : IIntegrationEvent
    {
        public Guid Id { get; }
        public DateTime CreatedAt { get; }
        public string EventType => "BookingCreated";
        public string EventName => "booking.created";
        public string Data { get; }

        // Event-specific properties
        public Guid BookingId { get; }
        public Guid ProviderId { get; }
        public Guid ClientId { get; }
        public DateTime BookingDate { get; }

        public BookingCreatedIntegrationEvent(
            Guid bookingId, 
            Guid providerId, 
            Guid clientId, 
            DateTime bookingDate)
        {
            Id = Guid.NewGuid();
            CreatedAt = DateTime.UtcNow;
            BookingId = bookingId;
            ProviderId = providerId;
            ClientId = clientId;
            BookingDate = bookingDate;

            // Serialize event data
            var eventData = new
            {
                BookingId = bookingId,
                ProviderId = providerId,
                ClientId = clientId,
                BookingDate = bookingDate
            };
            Data = JsonSerializer.Serialize(eventData);
        }
    }
}
```

### Creating an Integration Event Handler

```csharp
using SuperCareApp.Application.Common.Interfaces;
using Microsoft.Extensions.Logging;

namespace SuperCareApp.Application.Events.Handlers
{
    public class BookingCreatedIntegrationEventHandler : IIntegrationEventHandler<BookingCreatedIntegrationEvent>
    {
        private readonly ILogger<BookingCreatedIntegrationEventHandler> _logger;
        private readonly INotificationService _notificationService;
        private readonly IExternalApiService _externalApiService;

        public BookingCreatedIntegrationEventHandler(
            ILogger<BookingCreatedIntegrationEventHandler> logger,
            INotificationService notificationService,
            IExternalApiService externalApiService)
        {
            _logger = logger;
            _notificationService = notificationService;
            _externalApiService = externalApiService;
        }

        public async Task Handle(BookingCreatedIntegrationEvent notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing booking created integration event for booking {BookingId}", 
                notification.BookingId);

            try
            {
                // Send notification to mobile app
                await _notificationService.SendBookingNotificationAsync(
                    notification.ProviderId, 
                    notification.ClientId, 
                    cancellationToken);

                // Notify external calendar system
                await _externalApiService.SyncBookingToCalendarAsync(
                    notification.BookingId, 
                    cancellationToken);

                _logger.LogInformation("Successfully processed booking created event for {BookingId}", 
                    notification.BookingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process booking created event for {BookingId}", 
                    notification.BookingId);
                throw;
            }
        }
    }
}
```

## Setting Up the Mediator

### In Program.cs or Startup.cs

```csharp
using SuperCareApp.Persistence.Extensions;
using System.Reflection;

// Register mediator with assemblies to scan for handlers
builder.Services.AddMediator(
    Assembly.GetExecutingAssembly(),                    // Current assembly
    typeof(UserRegisteredDomainEvent).Assembly,        // Application assembly
    typeof(ApplicationDbContext).Assembly              // Persistence assembly
);
```

### Adding Pipeline Behaviors (Optional)

```csharp
// Add logging behavior
builder.Services.AddPipelineBehavior<LoggingBehavior>();

// Add validation behavior
builder.Services.AddPipelineBehavior<ValidationBehavior>();
```

## Creating and Publishing Events

### In Domain Entities

```csharp
public class Booking : BaseEntity
{
    public Guid ProviderId { get; private set; }
    public Guid ClientId { get; private set; }
    public DateTime BookingDate { get; private set; }
    public BookingStatus Status { get; private set; }

    public static async Task<Booking> CreateAsync(
        Guid providerId, 
        Guid clientId, 
        DateTime bookingDate,
        IMediator mediator)
    {
        var booking = new Booking
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            ClientId = clientId,
            BookingDate = bookingDate,
            Status = BookingStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        // Publish domain event
        var domainEvent = new BookingCreatedDomainEvent(booking.Id, providerId, clientId, bookingDate);
        await mediator.Publish(domainEvent);

        // Publish integration event
        var integrationEvent = new BookingCreatedIntegrationEvent(booking.Id, providerId, clientId, bookingDate);
        await mediator.Publish(integrationEvent);

        return booking;
    }
}
```

### In Application Services

```csharp
public class BookingService
{
    private readonly IMediator _mediator;
    private readonly IBookingRepository _bookingRepository;

    public BookingService(IMediator mediator, IBookingRepository bookingRepository)
    {
        _mediator = mediator;
        _bookingRepository = bookingRepository;
    }

    public async Task<Guid> CreateBookingAsync(CreateBookingRequest request, CancellationToken cancellationToken)
    {
        // Create booking
        var booking = await Booking.CreateAsync(
            request.ProviderId, 
            request.ClientId, 
            request.BookingDate,
            _mediator);

        // Save to database
        await _bookingRepository.AddAsync(booking, cancellationToken);
        await _bookingRepository.SaveChangesAsync(cancellationToken);

        return booking.Id;
    }
}
```

## Event Handlers

### Multiple Handlers for Same Event

You can have multiple handlers for the same event. They will all be executed in parallel:

```csharp
// First handler - sends email
public class UserRegisteredEmailHandler : IDomainEventHandler<UserRegisteredDomainEvent>
{
    private readonly IEmailService _emailService;

    public UserRegisteredEmailHandler(IEmailService emailService)
    {
        _emailService = emailService;
    }

    public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        await _emailService.SendWelcomeEmailAsync(notification.Email, cancellationToken);
    }
}

// Second handler - creates user profile
public class UserRegisteredProfileHandler : IDomainEventHandler<UserRegisteredDomainEvent>
{
    private readonly IUserProfileService _userProfileService;

    public UserRegisteredProfileHandler(IUserProfileService userProfileService)
    {
        _userProfileService = userProfileService;
    }

    public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        await _userProfileService.CreateDefaultProfileAsync(notification.UserId, cancellationToken);
    }
}

// Third handler - logs analytics
public class UserRegisteredAnalyticsHandler : IDomainEventHandler<UserRegisteredDomainEvent>
{
    private readonly IAnalyticsService _analyticsService;

    public UserRegisteredAnalyticsHandler(IAnalyticsService analyticsService)
    {
        _analyticsService = analyticsService;
    }

    public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        await _analyticsService.TrackUserRegistrationAsync(notification.UserId, cancellationToken);
    }
}
```

### Error Handling in Event Handlers

```csharp
public class RobustEventHandler : IDomainEventHandler<UserRegisteredDomainEvent>
{
    private readonly ILogger<RobustEventHandler> _logger;
    private readonly IEmailService _emailService;

    public RobustEventHandler(ILogger<RobustEventHandler> logger, IEmailService emailService)
    {
        _logger = logger;
        _emailService = emailService;
    }

    public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing user registration event for {UserId}", notification.UserId);

            await _emailService.SendWelcomeEmailAsync(notification.Email, cancellationToken);

            _logger.LogInformation("Successfully processed user registration event for {UserId}", notification.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process user registration event for {UserId}", notification.UserId);

            // Decide whether to rethrow or handle gracefully
            // For critical operations, rethrow to stop the pipeline
            // For non-critical operations, log and continue

            // Example: Email failure shouldn't stop user registration
            // So we log the error but don't rethrow
        }
    }
}
```

## Best Practices

### 1. Event Naming Conventions

```csharp
// ✅ Good - Past tense, describes what happened
public class UserRegisteredDomainEvent : IDomainEvent { }
public class BookingCancelledDomainEvent : IDomainEvent { }
public class PaymentProcessedDomainEvent : IDomainEvent { }

// ❌ Bad - Present tense, describes what should happen
public class RegisterUserDomainEvent : IDomainEvent { }
public class CancelBookingDomainEvent : IDomainEvent { }
```

### 2. Event Data

```csharp
// ✅ Good - Include relevant data
public class BookingCreatedDomainEvent : IDomainEvent
{
    public Guid BookingId { get; }
    public Guid ProviderId { get; }
    public Guid ClientId { get; }
    public DateTime BookingDate { get; }
    public decimal Amount { get; }

    // Constructor...
}

// ❌ Bad - Only include ID, forcing handlers to query database
public class BookingCreatedDomainEvent : IDomainEvent
{
    public Guid BookingId { get; }

    // Constructor...
}
```

### 3. Handler Responsibilities

```csharp
// ✅ Good - Single responsibility
public class SendWelcomeEmailHandler : IDomainEventHandler<UserRegisteredDomainEvent>
{
    public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        // Only responsible for sending welcome email
        await _emailService.SendWelcomeEmailAsync(notification.Email, cancellationToken);
    }
}

// ❌ Bad - Multiple responsibilities
public class UserRegisteredHandler : IDomainEventHandler<UserRegisteredDomainEvent>
{
    public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
    {
        // Too many responsibilities in one handler
        await _emailService.SendWelcomeEmailAsync(notification.Email, cancellationToken);
        await _analyticsService.TrackRegistrationAsync(notification.UserId, cancellationToken);
        await _profileService.CreateProfileAsync(notification.UserId, cancellationToken);
        await _notificationService.SendPushNotificationAsync(notification.UserId, cancellationToken);
    }
}
```

### 4. Event Immutability

```csharp
// ✅ Good - Immutable event
public class UserRegisteredDomainEvent : IDomainEvent
{
    public Guid UserId { get; }
    public string Email { get; }
    public DateTime RegisteredAt { get; }

    public UserRegisteredDomainEvent(Guid userId, string email, DateTime registeredAt)
    {
        UserId = userId;
        Email = email;
        RegisteredAt = registeredAt;
    }
}

// ❌ Bad - Mutable event
public class UserRegisteredDomainEvent : IDomainEvent
{
    public Guid UserId { get; set; }
    public string Email { get; set; }
    public DateTime RegisteredAt { get; set; }
}
```

## Common Patterns

### 1. Event Sourcing Pattern

```csharp
public abstract class AggregateRoot : BaseEntity
{
    private readonly List<IDomainEvent> _domainEvents = new();

    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}

public class Booking : AggregateRoot
{
    public void ConfirmBooking()
    {
        Status = BookingStatus.Confirmed;
        AddDomainEvent(new BookingConfirmedDomainEvent(Id, ProviderId, ClientId));
    }
}
```

### 2. Saga Pattern with Events

```csharp
public class BookingProcessingSaga :
    IDomainEventHandler<BookingCreatedDomainEvent>,
    IDomainEventHandler<PaymentProcessedDomainEvent>,
    IDomainEventHandler<ProviderNotifiedDomainEvent>
{
    private readonly IMediator _mediator;
    private readonly IBookingRepository _bookingRepository;

    public async Task Handle(BookingCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        // Step 1: Process payment
        var paymentEvent = new ProcessPaymentDomainEvent(notification.BookingId, notification.Amount);
        await _mediator.Publish(paymentEvent, cancellationToken);
    }

    public async Task Handle(PaymentProcessedDomainEvent notification, CancellationToken cancellationToken)
    {
        // Step 2: Notify provider
        var notifyEvent = new NotifyProviderDomainEvent(notification.BookingId, notification.ProviderId);
        await _mediator.Publish(notifyEvent, cancellationToken);
    }

    public async Task Handle(ProviderNotifiedDomainEvent notification, CancellationToken cancellationToken)
    {
        // Step 3: Complete booking process
        var booking = await _bookingRepository.GetByIdAsync(notification.BookingId, cancellationToken);
        booking.CompleteProcessing();
        await _bookingRepository.SaveChangesAsync(cancellationToken);
    }
}
```

### 3. Event Replay Pattern

```csharp
public class EventReplayService
{
    private readonly IMediator _mediator;
    private readonly IEventStore _eventStore;

    public async Task ReplayEventsAsync(DateTime fromDate, CancellationToken cancellationToken)
    {
        var events = await _eventStore.GetEventsFromDateAsync(fromDate, cancellationToken);

        foreach (var eventData in events)
        {
            var domainEvent = DeserializeEvent(eventData);
            await _mediator.Publish(domainEvent, cancellationToken);
        }
    }
}
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Handler Not Found

**Problem**: `No handlers found for notification {NotificationType}`

**Solution**:
- Ensure your handler implements the correct interface
- Verify the handler is registered in DI container
- Check that the assembly containing the handler is included in `AddMediator()`

```csharp
// ✅ Correct handler registration
builder.Services.AddMediator(
    typeof(UserRegisteredDomainEventHandler).Assembly  // Include this assembly
);
```

#### 2. Event Not Being Published

**Problem**: Event handlers are not being called

**Solution**:
- Verify you're calling `await _mediator.Publish(event, cancellationToken)`
- Check that the event implements `IDomainEvent` or `IIntegrationEvent`
- Ensure the mediator is properly injected

#### 3. Handler Exceptions

**Problem**: One handler fails and stops other handlers

**Solution**:
- Implement proper error handling in handlers
- Consider using try-catch blocks for non-critical operations
- Use logging to track handler execution

```csharp
public async Task Handle(UserRegisteredDomainEvent notification, CancellationToken cancellationToken)
{
    try
    {
        // Critical operation - let it throw if it fails
        await _userService.CreateUserProfileAsync(notification.UserId, cancellationToken);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to create user profile for {UserId}", notification.UserId);
        throw; // Re-throw for critical operations
    }

    try
    {
        // Non-critical operation - don't let it stop the process
        await _emailService.SendWelcomeEmailAsync(notification.Email, cancellationToken);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to send welcome email to {Email}", notification.Email);
        // Don't re-throw for non-critical operations
    }
}
```

#### 4. Performance Issues

**Problem**: Too many handlers causing slow performance

**Solution**:
- Consider using background processing for non-critical handlers
- Implement async processing patterns
- Use performance monitoring to identify bottlenecks

### Debugging Tips

1. **Enable Debug Logging**: The mediator logs debug information about event publishing
2. **Use Breakpoints**: Set breakpoints in your handlers to verify they're being called
3. **Check DI Registration**: Verify all handlers are properly registered in the DI container
4. **Monitor Performance**: Use Application Insights or similar tools to monitor event processing times

### Testing Events

```csharp
[Test]
public async Task Should_Send_Welcome_Email_When_User_Registers()
{
    // Arrange
    var mockEmailService = new Mock<IEmailService>();
    var handler = new UserRegisteredEmailHandler(mockEmailService.Object);
    var domainEvent = new UserRegisteredDomainEvent(Guid.NewGuid(), "<EMAIL>", DateTime.UtcNow);

    // Act
    await handler.Handle(domainEvent, CancellationToken.None);

    // Assert
    mockEmailService.Verify(x => x.SendWelcomeEmailAsync("<EMAIL>", It.IsAny<CancellationToken>()), Times.Once);
}
```

---

## Summary

This guide covered:
- Creating and publishing domain and integration events
- Implementing event handlers with proper error handling
- Best practices for event-driven architecture
- Common patterns and troubleshooting tips

Remember: Events are powerful tools for building loosely coupled, maintainable applications. Start simple and gradually add complexity as your application grows.
```
