﻿using Swashbuckle.AspNetCore.Annotations;

namespace SuperCareApp.Application.Common.Models.Identity;

public class ProfileQueryParams
{
    [SwaggerParameter("Page number (1-based)", Required = false)]
    public int PageNumber { get; set; } = 1;

    [SwaggerParameter("Number of items per page (max 50)", Required = false)]
    public int PageSize { get; set; } = 10;

    [SwaggerParameter("Field to sort by (name, phoneNumber, gender, etc.)", Required = false)]
    public string? SortBy { get; set; }

    [SwaggerParameter(
        "Sort direction (true for descending, false for ascending)",
        Required = false
    )]
    public bool SortDescending { get; set; }

    [SwaggerParameter("Search term to filter profiles", Required = false)]
    public string? SearchTerm { get; set; }

    [SwaggerParameter("Date for availability filter (YYYY-MM-DD)", Required = false)]
    public string? Date { get; set; }

    [SwaggerParameter("Start time for availability filter (HH:mm)", Required = false)]
    public TimeOnly? StartTime { get; set; }

    [SwaggerParameter("End time for availability filter (HH:mm)", Required = false)]
    public TimeOnly? EndTime { get; set; }

    [SwaggerParameter(
        "Care category IDs to filter care providers (comma-separated list)",
        Required = false
    )]
    public List<Guid>? CategoryIds { get; set; }

    [SwaggerParameter("Minimum years of experience", Required = false)]
    public int? MinExperience { get; set; }

    [SwaggerParameter("Maximum years of experience", Required = false)]
    public int? MaxExperience { get; set; }

    [SwaggerParameter("Latitude for location filter", Required = false)]
    public double? LocationLat { get; set; }

    [SwaggerParameter("Longitude for location filter", Required = false)]
    public double? LocationLong { get; set; }

    [SwaggerParameter("Distance radius in kilometers", Required = false)]
    public double? DistanceRadius { get; set; }

    [SwaggerParameter("Genders to filter care providers (comma-separated list)", Required = false)]
    public List<string>? Genders { get; set; }

    [SwaggerParameter("Minimum age", Required = false)]
    public int? MinAge { get; set; }

    [SwaggerParameter("Maximum age", Required = false)]
    public int? MaxAge { get; set; }

    [SwaggerParameter("Minimum hourly rate", Required = false)]
    public decimal? MinPrice { get; set; }

    [SwaggerParameter("Maximum hourly rate", Required = false)]
    public decimal? MaxPrice { get; set; }
}
