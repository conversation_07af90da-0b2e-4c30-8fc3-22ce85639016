using System.Reflection;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using super_care_app.Models.Doc;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Swagger operation filter to add response examples to specific endpoints
    /// </summary>
    public class SwaggerResponseExamplesFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Get controller and action names
            var controllerName = context.MethodInfo?.DeclaringType?.Name.Replace("Controller", "");
            var actionName = context.MethodInfo?.Name;

            if (string.IsNullOrEmpty(controllerName) || string.IsNullOrEmpty(actionName))
                return;

            // Add examples based on controller and action
            AddResponseExamples(operation, controllerName, actionName);

            // Add examples from SwaggerResponseExample attributes
            AddExamplesFromAttributes(operation, context);
        }

        private void AddResponseExamples(
            OpenApiOperation operation,
            string controllerName,
            string actionName
        )
        {
            // Auth controller examples
            if (controllerName == "Auth")
            {
                if (actionName == "Login")
                {
                    AddSuccessExample(operation, ApiResponseExamples.LoginSuccessExample());
                    AddValidationErrorExample(operation);
                    AddUnauthorizedErrorExample(operation);
                }
                else if (actionName == "Register")
                {
                    AddSuccessExample(operation, ApiResponseExamples.RegisterSuccessExample());
                    AddValidationErrorExample(operation);
                }
                else if (actionName == "RefreshToken")
                {
                    AddSuccessExample(operation, ApiResponseExamples.LoginSuccessExample());
                    AddUnauthorizedErrorExample(operation);
                }
            }
            // User controller examples
            else if (controllerName == "User")
            {
                if (actionName == "GetUser" || actionName == "GetUserById")
                {
                    AddSuccessExample(operation, ApiResponseExamples.UserProfileSuccessExample());
                    AddNotFoundErrorExample(operation);
                    AddUnauthorizedErrorExample(operation);
                }
                else if (actionName == "UpdateUser")
                {
                    AddSuccessExample(operation, ApiResponseExamples.UserProfileSuccessExample());
                    AddValidationErrorExample(operation);
                    AddNotFoundErrorExample(operation);
                    AddUnauthorizedErrorExample(operation);
                }
            }
            // Account controller examples
            else if (controllerName == "Account")
            {
                switch (actionName)
                {
                    case "GetProfiles":
                        AddSuccessExample(operation, AccountExamples.GetAllAccountsExample());
                        break;
                    case "GetProfile":
                        AddSuccessExample(operation, AccountExamples.GetSingleAccountResponse());
                        break;
                    case "GetAddresses":
                        AddSuccessExample(operation, AccountExamples.GetAddresses());
                        break;
                    case "GetAddress":
                        AddSuccessExample(operation, AccountExamples.GetSingleAddress());
                        break;
                    case "GetDocuments":
                        AddSuccessExample(
                            operation,
                            AccountExamples.GetDocumentsByUserIdResponse()
                        );
                        break;
                    case "GetDocument":
                        AddSuccessExample(operation, AccountExamples.GetDocumentByUserIdResponse());
                        break;
                }
            }
            // Bookings controller examples
            else if (controllerName == "Bookings")
            {
                if (actionName == "GetProviderAvailability")
                {
                    AddSuccessExample(operation, BookingExamples.GetProviderAvailabilityExample());
                }
                else if (actionName == "GetBookingById")
                {
                    AddSuccessExample(operation, BookingExamples.GetBookingByIdExample());
                }
                else if (actionName == "GetAllBookings")
                {
                    AddSuccessExample(operation, BookingExamples.GetAllBookingsExample());
                }
                else if (actionName == "GetProviderLeaves")
                {
                    AddSuccessExample(operation, BookingExamples.GetProviderLeavesExample());
                }
                else if (actionName == "GetMyLeaves")
                {
                    AddSuccessExample(operation, BookingExamples.GetMyLeavesExample());
                }
            }
            // CareProvider controller examples
            else if (controllerName == "CareProvider")
            {
                if (actionName == "GetAllProviders")
                {
                    AddSuccessExample(operation, CareProviderExamples.GetAllProvidersExample());
                    AddInternalServerErrorExample(operation);
                }
                else if (actionName == "GetPagedProviders")
                {
                    AddSuccessExample(operation, CareProviderExamples.GetPagedProvidersExample());
                    AddValidationErrorExample(operation);
                    AddInternalServerErrorExample(operation);
                }
                else if (actionName == "GetProviderById")
                {
                    AddSuccessExample(operation, CareProviderExamples.GetProviderByIdExample());
                    AddNotFoundErrorExample(operation);
                    AddInternalServerErrorExample(operation);
                }
                else if (actionName == "CreateProviderProfile")
                {
                    AddSuccessExample(
                        operation,
                        CareProviderExamples.CreateProviderProfileExample()
                    );
                    AddValidationErrorExample(operation);
                    AddUnauthorizedErrorExample(operation);
                    AddInternalServerErrorExample(operation);
                }
                else if (actionName == "UpdateProviderProfile")
                {
                    AddSuccessExample(
                        operation,
                        CareProviderExamples.UpdateProviderProfileExample()
                    );
                    AddValidationErrorExample(operation);
                    AddNotFoundErrorExample(operation);
                    AddUnauthorizedErrorExample(operation);
                    AddInternalServerErrorExample(operation);
                }
            }
            // Add generic examples for all endpoints
            else
            {
                // Add generic examples for common status codes
                AddGenericExamples(operation);
            }
        }

        private void AddSuccessExample(OpenApiOperation operation, string example)
        {
            // Check for 200 OK response
            if (operation.Responses.ContainsKey("200"))
            {
                var response = operation.Responses["200"];
                if (response.Content.ContainsKey("application/json"))
                {
                    response.Content["application/json"].Examples["Success"] = new OpenApiExample
                    {
                        Summary = "Success Response",
                        Description = "Example of a successful response",
                        Value = new OpenApiString(example),
                    };
                }
            }

            // Check for 201 Created response
            if (operation.Responses.ContainsKey("201"))
            {
                var response = operation.Responses["201"];
                if (response.Content.ContainsKey("application/json"))
                {
                    response.Content["application/json"].Examples["Success"] = new OpenApiExample
                    {
                        Summary = "Success Response",
                        Description = "Example of a successful creation response",
                        Value = new OpenApiString(example),
                    };
                }
            }
        }

        private void AddValidationErrorExample(OpenApiOperation operation)
        {
            if (!operation.Responses.ContainsKey("400"))
                return;

            var response = operation.Responses["400"];
            if (response.Content.ContainsKey("application/json"))
            {
                response.Content["application/json"].Examples["ValidationError"] =
                    new OpenApiExample
                    {
                        Summary = "Validation Error",
                        Description = "Example of a validation error response",
                        Value = new OpenApiString(ApiResponseExamples.ValidationErrorExample()),
                    };
            }
        }

        private void AddNotFoundErrorExample(OpenApiOperation operation)
        {
            if (!operation.Responses.ContainsKey("404"))
                return;

            var response = operation.Responses["404"];
            if (response.Content.ContainsKey("application/json"))
            {
                response.Content["application/json"].Examples["NotFound"] = new OpenApiExample
                {
                    Summary = "Not Found Error",
                    Description = "Example of a not found error response",
                    Value = new OpenApiString(ApiResponseExamples.NotFoundErrorExample()),
                };
            }
        }

        private void AddUnauthorizedErrorExample(OpenApiOperation operation)
        {
            if (!operation.Responses.ContainsKey("401"))
                return;

            var response = operation.Responses["401"];
            if (response.Content.ContainsKey("application/json"))
            {
                response.Content["application/json"].Examples["Unauthorized"] = new OpenApiExample
                {
                    Summary = "Unauthorized Error",
                    Description = "Example of an unauthorized error response",
                    Value = new OpenApiString(ApiResponseExamples.UnauthorizedErrorExample()),
                };
            }
        }

        private void AddForbiddenErrorExample(OpenApiOperation operation)
        {
            if (!operation.Responses.ContainsKey("403"))
                return;

            var response = operation.Responses["403"];
            if (response.Content.ContainsKey("application/json"))
            {
                response.Content["application/json"].Examples["Forbidden"] = new OpenApiExample
                {
                    Summary = "Forbidden Error",
                    Description = "Example of a forbidden error response",
                    Value = new OpenApiString(ApiResponseExamples.ForbiddenErrorExample()),
                };
            }
        }

        private void AddInternalServerErrorExample(OpenApiOperation operation)
        {
            if (!operation.Responses.ContainsKey("500"))
                return;

            var response = operation.Responses["500"];
            if (response.Content.ContainsKey("application/json"))
            {
                response.Content["application/json"].Examples["InternalServerError"] =
                    new OpenApiExample
                    {
                        Summary = "Internal Server Error",
                        Description = "Example of an internal server error response",
                        Value = new OpenApiString(ApiResponseExamples.InternalServerErrorExample()),
                    };
            }
        }

        private void AddGenericExamples(OpenApiOperation operation)
        {
            // Add generic examples for common status codes
            if (operation.Responses.ContainsKey("400"))
                AddValidationErrorExample(operation);

            if (operation.Responses.ContainsKey("401"))
                AddUnauthorizedErrorExample(operation);

            if (operation.Responses.ContainsKey("403"))
                AddForbiddenErrorExample(operation);

            if (operation.Responses.ContainsKey("404"))
                AddNotFoundErrorExample(operation);

            if (operation.Responses.ContainsKey("500"))
                AddInternalServerErrorExample(operation);
        }

        private void AddExamplesFromAttributes(
            OpenApiOperation operation,
            OperationFilterContext context
        )
        {
            // Get SwaggerResponseExample attributes
            var swaggerResponseExampleAttributes = context
                .MethodInfo.GetCustomAttributes(true)
                .OfType<SwaggerResponseExampleAttribute>();

            foreach (var attribute in swaggerResponseExampleAttributes)
            {
                var statusCode = attribute.StatusCode.ToString();
                var exampleType = attribute.ExamplesType;

                if (!operation.Responses.ContainsKey(statusCode))
                    continue;

                // Try to find example methods in the example type
                var exampleMethods = exampleType
                    .GetMethods(BindingFlags.Public | BindingFlags.Static)
                    .Where(m => m.ReturnType == typeof(string) && m.GetParameters().Length == 0);

                foreach (var method in exampleMethods)
                {
                    try
                    {
                        // Invoke the example method to get the example string
                        var example = method.Invoke(null, null) as string;
                        if (string.IsNullOrEmpty(example))
                            continue;

                        var response = operation.Responses[statusCode];
                        if (!response.Content.ContainsKey("application/json"))
                            continue;

                        // Add the example to the response
                        response.Content["application/json"].Examples[method.Name] =
                            new OpenApiExample
                            {
                                Summary = method.Name,
                                Description = $"Example from {exampleType.Name}.{method.Name}",
                                Value = new OpenApiString(example),
                            };
                    }
                    catch
                    {
                        // Ignore errors when invoking example methods
                    }
                }
            }
        }
    }
}
