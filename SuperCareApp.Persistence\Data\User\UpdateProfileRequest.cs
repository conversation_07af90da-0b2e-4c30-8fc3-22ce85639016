﻿using System.ComponentModel.DataAnnotations;

namespace SuperCareApp.Persistence.Data.User
{
    public class UpdateProfileRequest
    {
        // Foreign key to ApplicationUser
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? ProfilePicture { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public string? Country { get; set; }
        public string? Preferences { get; set; } // Stored as JSON string
    }
}
