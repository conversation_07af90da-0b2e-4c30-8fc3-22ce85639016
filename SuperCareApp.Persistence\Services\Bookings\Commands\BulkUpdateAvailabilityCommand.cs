﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

public record BulkUpdateAvailabilityCommand(
    Guid ProviderId,
    List<(string DayOfWeek, bool IsAvailable, List<AvailabilitySlot> Slots)> AvailabilityUpdates,
    int BufferDuration,
    bool? ProvidesRecurringBooking,
    int? WorkingHoursPerDay
) : ICommand<Result>;

public class BulkUpdateAvailabilityCommandHandler
    : ICommandHandler<BulkUpdateAvailabilityCommand, Result>
{
    private readonly ApplicationDbContext _context;
    private readonly IDateTimeProvider _dateTimeProvider;

    public BulkUpdateAvailabilityCommandHandler(
        ApplicationDbContext context,
        IDateTimeProvider dateTimeProvider
    )
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _dateTimeProvider =
            dateTimeProvider ?? throw new ArgumentNullException(nameof(dateTimeProvider));
    }

    public async Task<Result> Handle(BulkUpdateAvailabilityCommand request, CancellationToken ct)
    {
        // 1. Ensure provider exists
        var providerExists = await _context.CareProviderProfiles.AnyAsync(
            p => p.Id == request.ProviderId && !p.IsDeleted,
            ct
        );

        if (!providerExists)
            return Result.Failure(Error.NotFound("Provider not found."));

        // 2. Load existing availabilities for the provider
        var existingAvails = await _context
            .Availabilities.Include(a => a.AvailabilitySlots)
            .Where(a => a.ProviderId == request.ProviderId)
            .ToListAsync(ct);

        // 3. Apply changes
        foreach (var update in request.AvailabilityUpdates)
        {
            var existing = existingAvails.FirstOrDefault(a =>
                a.DayOfWeek.Equals(update.DayOfWeek, StringComparison.OrdinalIgnoreCase)
            );

            if (existing is null)
            {
                // Create new availability
                var newAvail = new Availability
                {
                    ProviderId = request.ProviderId,
                    DayOfWeek = update.DayOfWeek,
                    IsAvailable = update.IsAvailable,
                    AvailabilitySlots = update.IsAvailable
                        ? update
                            .Slots.Select(s => new AvailabilitySlot
                            {
                                StartTime = s.StartTime,
                                EndTime = s.EndTime,
                            })
                            .ToList()
                        : new List<AvailabilitySlot>(), // empty when not available
                };
                _context.Availabilities.Add(newAvail);
            }
            else
            {
                // Update existing availability
                existing.IsAvailable = update.IsAvailable;

                // Only touch slots when provider IS available
                if (update.IsAvailable)
                {
                    // Validate slot integrity
                    foreach (var slot in update.Slots)
                    {
                        if (slot.EndTime <= slot.StartTime)
                            return Result.Failure(
                                Error.BadRequest(
                                    $"Invalid slot: {slot.StartTime}-{slot.EndTime} on {update.DayOfWeek}."
                                )
                            );
                    }

                    // Replace slots
                    _context.AvailabilitySlots.RemoveRange(existing.AvailabilitySlots);
                    existing.AvailabilitySlots = update
                        .Slots.Select(s => new AvailabilitySlot
                        {
                            AvailabilityId = existing.Id,
                            StartTime = s.StartTime,
                            EndTime = s.EndTime,
                        })
                        .ToList();
                }
                // If IsAvailable == false, slots remain untouched (business rule)
            }
        }

        // 4. Update provider-level metadata
        var provider = await _context.CareProviderProfiles.SingleAsync(
            p => p.Id == request.ProviderId,
            ct
        );

        if (request.BufferDuration > 0)
            provider.BufferDuration = request.BufferDuration;

        if (request.WorkingHoursPerDay.HasValue)
            provider.WorkingHours = request.WorkingHoursPerDay.Value;

        provider.UpdatedAt = _dateTimeProvider.UtcNow;

        // 5. Persist atomically
        await _context.SaveChangesAsync(ct);

        return Result.Success();
    }
}
