﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Persistence
{
    /// <summary>
    /// Unit of work interface for transaction management
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Gets a repository for an entity
        /// </summary>
        IRepository<TEntity> Repository<TEntity>()
            where TEntity : BaseEntity;

        /// <summary>
        /// Begins a transaction
        /// </summary>
        Task BeginTransactionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Commits the transaction
        /// </summary>
        Task<Result> CommitTransactionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back the transaction
        /// </summary>
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves all changes made in this context to the database
        /// </summary>
        Task<Result<int>> SaveChangesAsync(CancellationToken cancellationToken = default);
    }
}
