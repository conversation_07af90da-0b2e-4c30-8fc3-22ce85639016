using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Bookings;

public interface IAvailabilityService
{
    // Availability management
    Task<Result<Guid>> AddAvailabilityAsync(
        Guid userId,
        string DayOfWeek,
        bool IsAvailable,
        List<AvailabilitySlot> Slots
    );

    Task<Result> UpdateAvailabilityAsync(
        Guid userId,
        Guid availabilityId,
        bool IsAvailable,
        List<AvailabilitySlot> Slots
    );

    Task<Result> DeleteAvailabilityAsync(Guid userId, Guid availabilityId);

    /// <summary>
    /// Bulk updates multiple availability records in a single transaction
    /// </summary>
    /// <param name="userId">The ID of the user performing the update</param>
    /// <param name="availabilityUpdates">List of availability records to update with their new values</param>
    /// <returns>Success or failure result</returns>
    Task<Result> BulkUpdateAvailabilityAsync(
        Guid userId,
        List<(
            Guid AvailabilityId,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )> availabilityUpdates
    );

    /// <summary>
    /// Bulk updates multiple availability records by day of week in a single transaction
    /// </summary>
    /// <param name="providerId">The ID of the provider performing the update</param>
    /// <param name="availabilityUpdates">List of availability records to update with their new values</param>
    /// <returns>Success or failure result</returns>
    Task<Result> BulkUpdateAvailabilityByDayOfWeekAsync(
        Guid providerId,
        List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )> availabilityUpdates,
        int BufferDuration,
        bool? ProvidesRecurringBooking,
        int? WorkingHoursPerDay
    );

    Task<Result<IEnumerable<AvailabilityResponse>>> GetProviderAvailabilityAsync(Guid providerId);
    Task<Result<IEnumerable<AvailabilityResponse>>> GetMyAvailabilityAsync(Guid userId);

    Task<Result<AvailabilityResponse>> GetProviderAvailabilityForDateAsync(
        Guid providerId,
        DateTime date
    );

    Task<Result<AvailabilityTemplateResponse>> GetProviderAvailabilityTemplateAsync(
        Guid providerId,
        DateTime? checkDate = null
    );

    Task<Result> UpdateAvailabilityStatusAsync(Guid userId, Guid availabilityId, bool isAvailable);

    Task<Result<IEnumerable<TimeSlotResponse>>> GetAvailableTimeSlotsAsync(
        Guid providerId,
        DateTime date,
        int durationMinutes = 60
    );

    Task<Result<EnhancedAvailabilityResponse>> GetProviderEnhancedAvailabilityAsync(
        Guid providerId,
        int monthsAhead = 3
    );

    // Leave management
    Task<Result<Guid>> AddLeaveAsync(Guid providerId, LeaveRequest request);
    Task<Result> UpdateLeaveAsync(Guid providerId, Guid leaveId, LeaveRequest request);
    Task<Result> DeleteLeaveAsync(Guid providerId, Guid leaveId);
    Task<Result<LeaveResponse>> GetLeaveAsync(Guid providerId, Guid leaveId);
    Task<Result<IEnumerable<LeaveResponse>>> GetMyLeavesAsync(Guid userId);
    Task<Result<IEnumerable<LeaveResponse>>> GetProviderLeavesAsync(Guid providerId);

    Task<
        Result<(IEnumerable<LeaveResponse> Items, PaginationMetadata Pagination)>
    > GetAllLeavesAsync(LeaveListParams parameters);
}
