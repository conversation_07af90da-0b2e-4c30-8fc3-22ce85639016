﻿using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Parameters for filtering and pagination of approval request lists
    /// </summary>
    public class ApprovalRequestListParams
    {
        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        /// <summary>
        /// Filter by approval type (string representation of ApprovalType enum)
        /// </summary>
        public string? ApprovalType { get; set; }

        /// <summary>
        /// Filter by approval status (null = pending, true = approved, false = rejected)
        /// </summary>
        public bool? IsApproved { get; set; }

        /// <summary>
        /// Filter by user ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Search term for user email or name
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Sort by field name
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort direction (true = descending, false = ascending)
        /// </summary>
        public bool SortDescending { get; set; } = true;
    }
}
