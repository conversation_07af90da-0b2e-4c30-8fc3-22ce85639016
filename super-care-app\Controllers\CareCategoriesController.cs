﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Persistence.Services.Categories.Commands;
using SuperCareApp.Persistence.Services.Categories.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace super_care_app.Controllers
{
    /// <summary>
    /// Controller for managing care categories
    /// </summary>
    public class CareCategoriesController : BaseController
    {
        private readonly IMediator _mediator;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<CareCategoriesController> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CareCategoriesController(
            IMediator mediator,
            ICurrentUserService currentUserService,
            ILogger<CareCategoriesController> logger
        )
        {
            _mediator = mediator;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        /// <summary>
        /// Gets care categories with optional pagination
        /// </summary>
        /// <param name="pageNumber">Page number (1-based, optional for pagination)</param>
        /// <param name="pageSize">Number of items per page (optional for pagination, max: 100)</param>
        /// <param name="includeInactive">Whether to include inactive categories</param>
        /// <param name="providerId">Add provider Id to include provider specific result alongside</param>
        /// <returns>List or paginated list of care categories</returns>
        [HttpGet(ApiRoutes.Categories.GetAll)]
        [Authorize]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<IEnumerable<CareCategoryResponse>>)
        )]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(PaginatedResponseModel<IEnumerable<CareCategoryResponse>>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets care categories with optional pagination",
            Description = "Returns a list of care categories. If pageNumber and pageSize are provided, returns paginated results.",
            OperationId = "Categories_GetAll",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> GetCategories(
            [FromQuery] int? pageNumber = null,
            [FromQuery] int? pageSize = null,
            [FromQuery] bool includeInactive = false,
            [FromQuery] Guid? providerId = null
        )
        {
            // If pagination parameters are provided, return paginated results
            if (pageNumber.HasValue && pageSize.HasValue)
            {
                var paginatedQuery = new GetPaginatedCategoriesQuery(
                    pageNumber.Value,
                    pageSize.Value,
                    includeInactive
                );
                var paginatedResult = await _mediator.Send(paginatedQuery);

                if (paginatedResult.IsFailure)
                {
                    return ErrorResponseFromError<PagedCategoryList>(paginatedResult.Error);
                }

                return this.Paginated(
                    paginatedResult.Value.Categories,
                    paginatedResult.Value.ToMetadata(),
                    "Care categories retrieved successfully"
                );
            }

            // Otherwise, return all categories
            var query = new GetAllCategoriesQuery(includeInactive, providerId);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<IEnumerable<CareCategoryResponse>>(result.Error);
            }

            return SuccessResponse(result.Value, "Care categories retrieved successfully");
        }

        /// <summary>
        /// Gets a care category by ID
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <returns>The care category</returns>
        [HttpGet(ApiRoutes.Categories.GetById)]
        [Authorize]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareCategoryResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets a care category by ID",
            Description = "Returns a specific care category by its ID",
            OperationId = "Categories_GetById",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> GetCategoryById(Guid id)
        {
            var query = new GetCategoryByIdQuery(id);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareCategoryResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Care category retrieved successfully");
        }

        /// <summary>
        /// Gets care categories for a specific provider
        /// </summary>
        /// <param name="providerId">The provider ID</param>
        /// <returns>List of care categories for the provider</returns>
        [HttpGet(ApiRoutes.Categories.GetByProviderId)]
        [Authorize]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<IEnumerable<CareCategoryResponse>>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets care categories for a provider",
            Description = "Returns a list of care categories associated with a specific provider",
            OperationId = "Categories_GetByProviderId",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> GetCategoriesByProviderId(Guid providerId)
        {
            var query = new GetCategoriesByProviderIdQuery(providerId);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<IEnumerable<CareCategoryResponse>>(result.Error);
            }

            return SuccessResponse(result.Value, "Care categories retrieved successfully");
        }

        /// <summary>
        /// Creates a new care category
        /// </summary>
        /// <param name="request">The create category request</param>
        /// <returns>The created care category</returns>
        [HttpPost(ApiRoutes.Categories.Create)]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(
            StatusCodes.Status201Created,
            Type = typeof(ApiResponseModel<CareCategoryResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status409Conflict,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Creates a new care category",
            Description = "Creates a new care category with the provided details",
            OperationId = "Categories_Create",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> CreateCategory(
            [FromBody] CreateCareCategoryRequest request
        )
        {
            // In a real application, get the user ID from the authenticated user
            var userId = _currentUserService.UserId ?? Guid.Empty;
            if (userId == Guid.Empty)
            {
                return UnauthorizedResponse<CareCategoryResponse>("User is not authenticated");
            }

            var command = new CreateCareCategoryCommand(request, userId);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareCategoryResponse>(result.Error);
            }

            return CreatedAtAction(
                nameof(GetCategoryById),
                new { id = result.Value.Id },
                new ApiResponseModel<CareCategoryResponse>(
                    ApiResponseStatusEnum.Success,
                    "Care category created successfully",
                    result.Value
                )
            );
        }

        /// <summary>
        /// Updates an existing care category
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <param name="request">The update category request</param>
        /// <returns>The updated care category</returns>
        [HttpPut(ApiRoutes.Categories.Update)]
        [Authorize]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareCategoryResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status409Conflict,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Updates a care category",
            Description = "Updates an existing care category with the provided details",
            OperationId = "Categories_Update",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> UpdateCategory(
            Guid id,
            [FromBody] UpdateCareCategoryRequest request
        )
        {
            // In a real application, get the user ID from the authenticated user
            var userId = _currentUserService.UserId ?? Guid.Empty;
            if (userId == Guid.Empty)
            {
                return UnauthorizedResponse<CareCategoryResponse>("User is not authenticated");
            }

            var command = new UpdateCareCategoryCommand(id, request, userId);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareCategoryResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Care category updated successfully");
        }

        [HttpPut(ApiRoutes.Categories.UpdateByProviderId)]
        [Authorize]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareCategoryResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status409Conflict,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Updates a care category",
            Description = "Updates an existing care category with the provided details for a care provider",
            OperationId = "Categories_UpdateForProvider",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> UpdateCategoryForProvider(
            [FromRoute] Guid providerId,
            [FromRoute] Guid id,
            [FromBody] UpdateCareCategoryForProviderRequest request
        )
        {
            var command = new UpdateCareProviderCategoryCommand(providerId, id, request);
            // Validate the command using FluentValidation or similar;
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareCategoryResponse>(result.Error);
            }

            var genericObjectResponse = new GenericObjectResponse(
                "Care category updated successfully",
                Guid.NewGuid()
            );

            return SuccessResponse(genericObjectResponse, "Care category updated successfully");
        }

        /// <summary>
        /// Deletes a care category
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <returns>Success or failure result</returns>
        [HttpDelete(ApiRoutes.Categories.Delete)]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Deletes a care category",
            Description = "Deletes a care category by its ID",
            OperationId = "Categories_Delete",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> DeleteCategory(Guid id)
        {
            // In a real application, get the user ID from the authenticated user
            var userId = _currentUserService.UserId ?? Guid.Empty;
            if (userId == Guid.Empty)
            {
                return UnauthorizedResponse<object>("User is not authenticated");
            }

            var command = new DeleteCareCategoryCommand(id, userId);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<object>(result.Error);
            }

            return NoContent();
        }

        /// <summary>
        /// Activates a care category
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <returns>The activated care category</returns>
        [HttpPost(ApiRoutes.Categories.Activate)]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareCategoryResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Activates a care category",
            Description = "Activates a care category by its ID",
            OperationId = "Categories_Activate",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> ActivateCategory(Guid id)
        {
            // In a real application, get the user ID from the authenticated user
            var userId = _currentUserService.UserId ?? Guid.Empty;
            if (userId == Guid.Empty)
            {
                return UnauthorizedResponse<CareCategoryResponse>("User is not authenticated");
            }

            var command = new ActivateCareCategoryCommand(id, userId);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareCategoryResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Care category activated successfully");
        }

        /// <summary>
        /// Deactivates a care category
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <returns>The deactivated care category</returns>
        [HttpPost(ApiRoutes.Categories.Deactivate)]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareCategoryResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Deactivates a care category",
            Description = "Deactivates a care category by its ID",
            OperationId = "Categories_Deactivate",
            Tags = new[] { "Categories" }
        )]
        public async Task<IActionResult> DeactivateCategory(Guid id)
        {
            // In a real application, get the user ID from the authenticated user
            var userId = _currentUserService.UserId ?? Guid.Empty;
            if (userId == Guid.Empty)
            {
                return UnauthorizedResponse<CareCategoryResponse>("User is not authenticated");
            }

            var command = new DeactivateCareCategoryCommand(id, userId);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareCategoryResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Care category deactivated successfully");
        }
    }
}
