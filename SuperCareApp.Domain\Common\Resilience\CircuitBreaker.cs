﻿using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Domain.Common.Resilience
{
    /// <summary>
    /// Circuit breaker state
    /// </summary>
    public enum CircuitState
    {
        Closed, // Normal operation, requests are allowed
        Open, // Circuit is broken, requests are not allowed
        HalfOpen // Testing if the circuit can be closed again
        ,
    }

    /// <summary>
    /// Implements the Circuit Breaker pattern to prevent repeated calls to failing services
    /// </summary>
    public class CircuitBreaker
    {
        private readonly int _failureThreshold;
        private readonly TimeSpan _resetTimeout;
        private readonly object _stateLock = new object();

        private CircuitState _state;
        private int _failureCount;
        private DateTime _lastFailureTime;

        public CircuitState State => _state;

        public CircuitBreaker(int failureThreshold = 5, TimeSpan? resetTimeout = null)
        {
            _failureThreshold = failureThreshold;
            _resetTimeout = resetTimeout ?? TimeSpan.FromSeconds(30);
            _state = CircuitState.Closed;
            _failureCount = 0;
            _lastFailureTime = DateTime.MinValue;
        }

        /// <summary>
        /// Executes an operation through the circuit breaker
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result<T>> ExecuteAsync<T>(Func<Task<Result<T>>> operation)
        {
            if (!CanExecute())
            {
                return Result.Failure<T>(
                    Error.ExternalService("Circuit is open. Too many failures detected.")
                );
            }

            try
            {
                var result = await operation();

                if (result.IsSuccess)
                {
                    Reset();
                }
                else
                {
                    RecordFailure();
                }

                return result;
            }
            catch (Exception ex)
            {
                RecordFailure();
                return Result.Failure<T>(Error.ExternalService(ex.Message));
            }
        }

        /// <summary>
        /// Executes an operation through the circuit breaker
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result> ExecuteAsync(Func<Task<Result>> operation)
        {
            if (!CanExecute())
            {
                return Result.Failure(
                    Error.ExternalService("Circuit is open. Too many failures detected.")
                );
            }

            try
            {
                var result = await operation();

                if (result.IsSuccess)
                {
                    Reset();
                }
                else
                {
                    RecordFailure();
                }

                return result;
            }
            catch (Exception ex)
            {
                RecordFailure();
                return Result.Failure(Error.ExternalService(ex.Message));
            }
        }

        /// <summary>
        /// Executes an operation through the circuit breaker
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation or throws an exception</returns>
        public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation)
        {
            if (!CanExecute())
            {
                throw new InvalidOperationException("Circuit is open. Too many failures detected.");
            }

            try
            {
                var result = await operation();
                Reset();
                return result;
            }
            catch (Exception)
            {
                RecordFailure();
                throw;
            }
        }

        private bool CanExecute()
        {
            lock (_stateLock)
            {
                switch (_state)
                {
                    case CircuitState.Closed:
                        return true;

                    case CircuitState.Open:
                        // Check if the timeout has elapsed to transition to half-open
                        if (DateTime.UtcNow - _lastFailureTime >= _resetTimeout)
                        {
                            _state = CircuitState.HalfOpen;
                            return true;
                        }
                        return false;

                    case CircuitState.HalfOpen:
                        return true;

                    default:
                        return false;
                }
            }
        }

        private void RecordFailure()
        {
            lock (_stateLock)
            {
                _lastFailureTime = DateTime.UtcNow;

                switch (_state)
                {
                    case CircuitState.HalfOpen:
                        // If we're testing the circuit and a failure occurs, open the circuit again
                        _state = CircuitState.Open;
                        break;

                    case CircuitState.Closed:
                        _failureCount++;
                        if (_failureCount >= _failureThreshold)
                        {
                            _state = CircuitState.Open;
                        }
                        break;
                }
            }
        }

        private void Reset()
        {
            lock (_stateLock)
            {
                _state = CircuitState.Closed;
                _failureCount = 0;
            }
        }
    }
}
