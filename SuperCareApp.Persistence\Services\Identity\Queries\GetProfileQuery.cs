using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Identity.Queries;

public record GetProfileQuery(
    Guid UserId,
    UserType UserType,
    ProfileListParams? PaginationParams = null
) : IQuery<Result<PagedProfileList>>;

internal sealed class GetProfileQueryHandler
    : IQueryHandler<GetProfileQuery, Result<PagedProfileList>>
{
    private readonly IUserProfileService _userProfileService;
    private readonly ICareProviderProfileService _careProviderProfileService;
    private readonly ApplicationDbContext _context;
    private readonly IFileStorageService _fileStorageService;

    public GetProfileQueryHandler(
        IUserProfileService userProfileService,
        ICareProviderProfileService careProviderProfileService,
        ApplicationDbContext context,
        IFileStorageService fileStorageService
    )
    {
        _userProfileService = userProfileService;
        _careProviderProfileService = careProviderProfileService;
        _context = context;
        _fileStorageService = fileStorageService;
    }

    public async Task<Result<PagedProfileList>> Handle(
        GetProfileQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Initialize pagination parameters if not provided
            var paginationParams = request.PaginationParams ?? new ProfileListParams();

            // Ensure valid pagination parameters
            if (paginationParams.PageNumber < 1)
            {
                return Result.Failure<PagedProfileList>(
                    Error.BadRequest("Page number should be at least 1")
                );
            }

            switch (request.UserType)
            {
                case UserType.Client:
                    return await GetClientProfilesAsync(
                        request.UserId,
                        paginationParams,
                        cancellationToken
                    );

                case UserType.CareProvider:
                    return await GetCareProviderProfilesAsync(
                        request.UserId,
                        paginationParams,
                        cancellationToken
                    );

                default:
                    return Result.Failure<PagedProfileList>(
                        Error.BadRequest("Unsupported user type")
                    );
            }
        }
        catch (Exception ex)
        {
            return Result.Failure<PagedProfileList>(
                Error.Internal($"Error retrieving profile: {ex.Message}")
            );
        }
    }

    private async Task<Result<PagedProfileList>> GetClientProfilesAsync(
        Guid userId,
        ProfileListParams parameters,
        CancellationToken cancellationToken
    )
    {
        // For a specific user, we'll only have one profile, but we'll use the same pattern
        // for consistency with other paginated endpoints

        // Check if the user profile exists and is not deleted
        var userProfile = await _userProfileService.GetByUserIdAsync(userId);
        if (userProfile.IsFailure || userProfile.Value == null)
        {
            return Result.Failure<PagedProfileList>(
                Error.NotFound("User profile not found or has been deleted")
            );
        }

        // Check if the profile is deleted
        if (userProfile.Value.IsDeleted)
        {
            return Result.Failure<PagedProfileList>(
                Error.NotFound("User profile has been deleted")
            );
        }

        var profileResponse = MapToProfileResponse(userProfile.Value);

        // Create a paged result with just one item
        var result = new PagedProfileList
        {
            Profiles = new List<ProfileResponse> { profileResponse },
            PageNumber = parameters.PageNumber,
            PageSize = parameters.PageSize,
            TotalCount = 1,
            TotalPages = 1,
        };

        return Result.Success(result);
    }

    private async Task<Result<PagedProfileList>> GetCareProviderProfilesAsync(
        Guid userId,
        ProfileListParams parameters,
        CancellationToken cancellationToken
    )
    {
        // For a specific user, we'll only have one profile, but we'll use the same pattern
        // for consistency with other paginated endpoints

        // Check if the care provider profile exists, is not deleted, and is verified
        var providerProfile = await _careProviderProfileService.GetByUserIdAsync(userId);
        if (providerProfile.IsFailure || providerProfile.Value == null)
        {
            return Result.Failure<PagedProfileList>(
                Error.NotFound(
                    "Care provider profile not found, has been deleted, or is pending verification"
                )
            );
        }

        // Double-check verification status and deleted flag
        if (providerProfile.Value.IsDeleted)
        {
            return Result.Failure<PagedProfileList>(
                Error.NotFound("Care provider profile has been deleted")
            );
        }

        if (providerProfile.Value.VerificationStatus == Domain.Enums.VerificationStatus.Pending)
        {
            return Result.Failure<PagedProfileList>(
                Error.BadRequest("Care provider profile is pending verification")
            );
        }

        var profileResponse = MapToProfileResponse(providerProfile.Value);

        // Create a paged result with just one item
        var result = new PagedProfileList
        {
            Profiles = new List<ProfileResponse> { profileResponse },
            PageNumber = parameters.PageNumber,
            PageSize = parameters.PageSize,
            TotalCount = 1,
            TotalPages = 1,
        };

        return Result.Success(result);
    }

    private ProfileResponse MapToProfileResponse(UserProfile profile)
    {
        // Get the file URL if the image path exists
        string? profilePictureUrl = null;
        if (!string.IsNullOrEmpty(profile.ImagePath))
        {
            // Use the file storage service to get the URL from the relative path
            profilePictureUrl = _fileStorageService.GetFileUrl(profile.ImagePath);
        }

        return new ProfileResponse
        {
            Id = profile.Id,
            UserId = profile.ApplicationUserId,
            FirstName = profile.FirstName ?? string.Empty,
            LastName = profile.LastName ?? string.Empty,
            PhoneNumber = profile.PhoneNumber ?? string.Empty,
            Gender = profile.Gender ?? string.Empty,
            DateOfBirth = profile.DateOfBirth,
            YearsExperience = 0, // Default for client
            ProfilePictureUrl = profilePictureUrl,
            Country = profile.Country,
        };
    }

    private ProfileResponse MapToProfileResponse(CareProviderProfile profile)
    {
        // Try to get user profile data for name and other common fields
        var userProfile = _context
            .UserProfiles.AsNoTracking()
            .FirstOrDefault(up => up.ApplicationUserId == profile.UserId);

        string name = string.Empty;
        string phoneNumber = string.Empty;
        string gender = string.Empty;
        DateTime? dateOfBirth = null;
        string? profilePictureUrl = null;

        if (userProfile != null)
        {
            name = $"{userProfile.FirstName} {userProfile.LastName}".Trim();
            phoneNumber = userProfile.PhoneNumber ?? string.Empty;
            gender = userProfile.Gender ?? string.Empty;
            dateOfBirth = userProfile.DateOfBirth;

            // Get the file URL if the image path exists
            if (!string.IsNullOrEmpty(userProfile.ImagePath))
            {
                // Use the file storage service to get the URL from the relative path
                profilePictureUrl = _fileStorageService.GetFileUrl(userProfile.ImagePath);
            }
        }

        return new ProfileResponse
        {
            Id = profile.Id,
            UserId = profile.UserId,
            ProviderId = profile.Id, // Include the CareProviderProfile.Id
            FirstName = profile.User.UserProfile.FirstName ?? string.Empty,
            LastName = profile.User.UserProfile.LastName ?? string.Empty,
            PhoneNumber = phoneNumber,
            Gender = gender,
            DateOfBirth = dateOfBirth,
            YearsExperience = profile.YearsExperience,
            ProfilePictureUrl = profilePictureUrl,
        };
    }
}
