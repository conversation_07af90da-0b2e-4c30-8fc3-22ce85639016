﻿using System.Net;
using System.Text.Json;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Exceptions;

namespace super_care_app.Middleware
{
    /// <summary>
    /// Middleware to handle exceptions globally and return standardized error responses
    /// </summary>
    public class GlobalExceptionHandlerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionHandlerMiddleware> _logger;
        private readonly IWebHostEnvironment _env;
        private static readonly JsonSerializerOptions _jsonOptions =
            new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };

        public GlobalExceptionHandlerMiddleware(
            RequestDelegate next,
            ILogger<GlobalExceptionHandlerMiddleware> logger,
            IWebHostEnvironment env
        )
        {
            _next = next;
            _logger = logger;
            _env = env;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            ApiResponseStatusEnum status;
            int statusCode;
            string message;
            object? payload = null;

            switch (exception)
            {
                case EntityNotFoundException ex:
                    status = ApiResponseStatusEnum.NotFound;
                    statusCode = (int)HttpStatusCode.NotFound;
                    message = ex.Message;
                    break;

                case ValidationException ex:
                    status = ApiResponseStatusEnum.BadRequest;
                    statusCode = (int)HttpStatusCode.BadRequest;
                    message = ex.Message;
                    payload = ex.Errors; // Include validation errors in the payload
                    break;

                case BadRequestException ex:
                    status = ApiResponseStatusEnum.BadRequest;
                    statusCode = (int)HttpStatusCode.BadRequest;
                    message = ex.Message;
                    break;

                case UnauthorizedException ex:
                    status = ApiResponseStatusEnum.Unauthorized;
                    statusCode = (int)HttpStatusCode.Unauthorized;
                    message = ex.Message;
                    break;

                case ForbiddenException ex:
                    status = ApiResponseStatusEnum.Forbidden;
                    statusCode = (int)HttpStatusCode.Forbidden;
                    message = ex.Message;
                    break;

                case ConflictException ex:
                    status = ApiResponseStatusEnum.Error;
                    statusCode = (int)HttpStatusCode.Conflict;
                    message = ex.Message;
                    break;

                case ExternalServiceException ex:
                    status = ApiResponseStatusEnum.Error;
                    statusCode = (int)HttpStatusCode.BadGateway;
                    message = ex.Message;
                    break;

                case SuperCareException ex:
                    status = ApiResponseStatusEnum.Error;
                    statusCode = (int)HttpStatusCode.InternalServerError;
                    message = ex.Message;
                    break;

                default:
                    status = ApiResponseStatusEnum.InternalServerError;
                    statusCode = (int)HttpStatusCode.InternalServerError;
                    message = _env.IsDevelopment()
                        ? exception.Message
                        : "An unexpected error occurred. Please try again later.";
                    break;
            }

            context.Response.StatusCode = statusCode;

            var response = new ApiResponseModel<object>(status, message, payload);

            var json = JsonSerializer.Serialize(response, _jsonOptions);
            await context.Response.WriteAsync(json);
        }
    }

    /// <summary>
    /// Extension method to add the global exception handler middleware to the application pipeline
    /// </summary>
    public static class GlobalExceptionHandlerMiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandler(this IApplicationBuilder app)
        {
            return app.UseMiddleware<GlobalExceptionHandlerMiddleware>();
        }
    }
}
