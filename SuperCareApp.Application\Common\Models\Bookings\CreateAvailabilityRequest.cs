using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class CreateAvailabilityRequest
    {
        public string DayOfWeek { get; set; }
        public bool IsAvailable { get; set; } = true;
        public List<CreateAvailabilitySlotRequest> Slots { get; set; } =
            new List<CreateAvailabilitySlotRequest>();
    }

    public class CreateAvailabilitySlotRequest
    {
        public TimeOnly StartTime { get; set; }
        public TimeOnly EndTime { get; set; }
    }

    public class CreateAvailabilityRequestValidator : AbstractValidator<CreateAvailabilityRequest>
    {
        public CreateAvailabilityRequestValidator()
        {
            // Rule for DayOfWeek: Must not be empty and must be a valid day.
            RuleFor(x => x.DayOfWeek)
                .NotEmpty()
                .WithMessage("Day of week is required.")
                .Must(BeAValidDayOfWeek)
                .WithMessage("Please provide a valid day of the week (e.g., Monday).");

            // Rule for IsAvailable: Cannot be null.
            RuleFor(x => x.IsAvailable).NotNull().WithMessage("Availability status is required.");

            // Conditional validation based on the value of IsAvailable.
            When(
                x => x.IsAvailable,
                () =>
                {
                    // If available, there must be at least one slot.
                    RuleFor(x => x.Slots)
                        .NotEmpty()
                        .WithMessage(
                            "At least one availability slot is required when set to available."
                        );

                    // Validate each slot in the list.
                    RuleForEach(x => x.Slots)
                        .SetValidator(new CreateAvailabilitySlotRequestValidator());

                    // Check for overlapping slots.
                    RuleFor(x => x.Slots)
                        .Must(NotHaveOverlappingSlots)
                        .WithMessage("Availability slots cannot overlap.");
                }
            );

            When(
                x => !x.IsAvailable,
                () =>
                {
                    // If not available, slots must be empty.
                    RuleFor(x => x.Slots)
                        .Empty()
                        .WithMessage("Slots must be empty when not available.");
                }
            );
        }

        /// <summary>
        /// Checks if the provided string is a valid day of the week.
        /// </summary>
        private bool BeAValidDayOfWeek(string day)
        {
            var validDays = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Sunday",
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
            };
            return validDays.Contains(day);
        }

        /// <summary>
        /// Checks if there are any overlapping time slots in the list.
        /// </summary>
        private bool NotHaveOverlappingSlots(List<CreateAvailabilitySlotRequest> slots)
        {
            if (slots == null || !slots.Any())
            {
                return true;
            }

            var orderedSlots = slots.OrderBy(s => s.StartTime).ToList();

            for (int i = 0; i < orderedSlots.Count - 1; i++)
            {
                // If the end time of the current slot is after the start time of the next slot, they overlap.
                if (orderedSlots[i].EndTime > orderedSlots[i + 1].StartTime)
                {
                    return false;
                }
            }

            return true;
        }
    }

    public class CreateAvailabilitySlotRequestValidator
        : AbstractValidator<CreateAvailabilitySlotRequest>
    {
        public CreateAvailabilitySlotRequestValidator()
        {
            // StartTime and EndTime are required.
            RuleFor(x => x.StartTime).NotEmpty().WithMessage("Start time is required.");

            RuleFor(x => x.EndTime).NotEmpty().WithMessage("End time is required.");

            // EndTime must be after StartTime.
            RuleFor(x => x.EndTime)
                .GreaterThan(x => x.StartTime)
                .WithMessage("End time must be after start time.");
        }
    }
}
