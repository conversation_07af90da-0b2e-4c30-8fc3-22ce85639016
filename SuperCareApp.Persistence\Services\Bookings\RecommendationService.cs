using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Bookings;

public class RecommendationService : IRecommendationService
{
    private readonly IRecommendationEngine _recommendationEngine;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RecommendationService> _logger;
    private readonly SemaphoreSlim _initializationSemaphore = new(1, 1);
    private bool _isInitialized = false;

    public RecommendationService(
        IRecommendationEngine recommendationEngine,
        IUnitOfWork unitOfWork,
        ILogger<RecommendationService> logger
    )
    {
        _recommendationEngine = recommendationEngine;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task InitializeEngineAsync(CancellationToken cancellationToken = default)
    {
        await _initializationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_isInitialized)
                return;

            _logger.LogInformation("Initializing recommendation engine...");

            var careProviderRepository = _unitOfWork.Repository<CareProviderProfile>();
            var result = await careProviderRepository.GetAllAsync();

            if (!result.IsSuccess)
            {
                _logger.LogError("Failed to retrieve care providers: {Error}", result.Error);
                throw new InvalidOperationException(
                    $"Failed to initialize recommendation engine: {result.Error}"
                );
            }

            var careProviders = result.Value.ToList();
            foreach (var provider in careProviders)
            {
                _recommendationEngine.AddCareProfessional(provider);
            }

            _isInitialized = true;
            _logger.LogInformation(
                "Recommendation engine initialized with {Count} providers",
                careProviders.Count
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize recommendation engine");
            throw;
        }
        finally
        {
            _initializationSemaphore.Release();
        }
    }

    public async Task<List<CareProviderProfile>> GetRecommendedProvidersAsync(
        double clientLat,
        double clientLon,
        string? requiredService,
        TimeOnly? requestedStart,
        TimeOnly? requestedEnd,
        double searchRadiusKm = 5,
        int topN = 10,
        CancellationToken cancellationToken = default
    )
    {
        await EnsureInitializedAsync(cancellationToken);

        return _recommendationEngine.Recommend(
            clientLat,
            clientLon,
            requiredService,
            requestedStart,
            requestedEnd,
            searchRadiusKm,
            topN
        );
    }

    public async Task RefreshProviderDataAsync(CareProviderProfile provider)
    {
        await EnsureInitializedAsync();

        _recommendationEngine.UpdateCareProfessional(provider);
        _logger.LogDebug("Updated provider {ProviderId} in recommendation engine", provider.Id);
    }

    public async Task RemoveProviderAsync(Guid providerId)
    {
        await EnsureInitializedAsync();

        _recommendationEngine.RemoveCareProfessional(providerId);
        _logger.LogDebug("Removed provider {ProviderId} from recommendation engine", providerId);
    }

    public List<CareProviderProfile> GetRecommendations(
        double clientLat,
        double clientLon,
        string requiredService,
        TimeOnly requestedStart,
        TimeOnly requestedEnd
    )
    {
        // For synchronous method, we need to ensure initialization happened
        // This is not ideal but maintains backward compatibility
        if (!_isInitialized)
        {
            throw new InvalidOperationException(
                "Recommendation engine not initialized. Call InitializeEngineAsync first."
            );
        }

        return _recommendationEngine.Recommend(
            clientLat,
            clientLon,
            requiredService,
            requestedStart,
            requestedEnd
        );
    }

    private async Task EnsureInitializedAsync(CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
        {
            await InitializeEngineAsync(cancellationToken);
        }
    }

    public void Dispose()
    {
        _initializationSemaphore?.Dispose();
    }
}
