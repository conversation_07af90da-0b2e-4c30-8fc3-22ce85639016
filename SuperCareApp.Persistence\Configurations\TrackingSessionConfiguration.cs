﻿// SuperCareApp.Persistence.Configurations.TrackingSessionConfiguration.cs
// Updated 2025-07-14 – aligns with per-BookingWindow tracking & new enum

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Configurations;

public class TrackingSessionConfiguration : IEntityTypeConfiguration<TrackingSession>
{
    public void Configure(EntityTypeBuilder<TrackingSession> builder)
    {
        builder.HasKey(ts => ts.Id);
        builder.Property(ts => ts.Id).ValueGeneratedOnAdd();

        /* 1️⃣  BookingWindow relationship (replaces BookingId) */
        builder.Property(ts => ts.BookingWindowId).IsRequired();

        builder
            .HasOne(ts => ts.BookingWindow)
            .WithMany(bw => bw.TrackingSessions)
            .HasForeignKey(ts => ts.BookingWindowId)
            .OnDelete(DeleteBehavior.Restrict); // safer – delete window = manual clean-up

        /* 2️⃣  Provider stays unchanged */
        builder.Property(ts => ts.ProviderId).IsRequired();
        builder
            .HasOne(ts => ts.Provider)
            .WithMany()
            .HasForeignKey(ts => ts.ProviderId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(ts => ts.StartTime).IsRequired().HasColumnType("timestamp with time zone");

        builder.Property(ts => ts.EndTime).HasColumnType("timestamp with time zone");

        builder.Property(ts => ts.PausedAt).HasColumnType("timestamp with time zone");

        builder.Property(ts => ts.Status).IsRequired().HasConversion<string>().HasMaxLength(10);

        builder.Property(ts => ts.TrackingData).HasColumnType("jsonb");
        builder.Property(ts => ts.Notes).HasMaxLength(1000);
        builder
            .Property(ts => ts.TotalPausedDuration)
            .IsRequired()
            .HasColumnType("interval")
            .HasDefaultValueSql("interval '0 seconds'");

        builder.Property(ts => ts.TotalHours).HasPrecision(18, 2);

        builder
            .HasIndex(ts => ts.BookingWindowId)
            .HasDatabaseName("IX_TrackingSessions_BookingWindowId");

        builder.HasIndex(ts => ts.ProviderId).HasDatabaseName("IX_TrackingSessions_ProviderId");

        builder
            .HasIndex(ts => new { ts.ProviderId, ts.Status })
            .HasDatabaseName("IX_TrackingSessions_Provider_Status")
            .HasFilter("\"status\" <> 'Stopped'"); // fast lookup for active sessions

        builder.HasIndex(ts => ts.StartTime).HasDatabaseName("IX_TrackingSessions_StartTime");

        builder.Property(ts => ts.CreatedAt).IsRequired().HasColumnType("timestamp with time zone");

        builder.Property(ts => ts.UpdatedAt).HasColumnType("timestamp with time zone");

        builder.Property(ts => ts.DeletedAt).HasColumnType("timestamp with time zone");

        builder.Property(ts => ts.IsDeleted).IsRequired().HasDefaultValue(false);

        builder.HasQueryFilter(ts => !ts.IsDeleted);

        builder.ToTable(t =>
        {
            t.HasCheckConstraint(
                "CK_TrackingSessions_EndAfterStart",
                "\"end_time\" IS NULL OR \"end_time\" > \"start_time\""
            );
        });

        builder.ToTable(t =>
        {
            t.HasCheckConstraint(
                "CK_TrackingSessions_EndAfterStart",
                "\"end_time\" IS NULL OR \"end_time\" > \"start_time\""
            );

            t.HasCheckConstraint(
                "CK_TrackingSessions_NonNegativeTotalHours",
                "\"total_hours\" IS NULL OR \"total_hours\" >= 0"
            );
        });
    }
}
