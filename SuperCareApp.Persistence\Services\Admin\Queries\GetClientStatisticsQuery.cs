using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Admin;

namespace SuperCareApp.Persistence.Services.Admin.Queries;

public record GetClientStatisticsQuery : IQuery<Result<ClientStatisticsResponse>>;

internal sealed class GetClientStatisticsQueryHandler
    : I<PERSON>ueryHandler<GetClientStatisticsQuery, Result<ClientStatisticsResponse>>
{
    private readonly ApplicationDbContext _context;

    public GetClientStatisticsQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<ClientStatisticsResponse>> Handle(
        GetClientStatisticsQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Active clients = Users who are not deleted and not care providers
            var activeClients = _context
                .Users.Include(u => u.UserProfile)
                .ThenInclude(up => up.Documents)
                .Include(u => u.UserAddresses)
                .Where(u => !u.IsDeleted);

            // Count all active clients
            var totalClients = await activeClients.CountAsync(cancellationToken);

            // Count email confirmed
            var emailConfirmed = await activeClients
                .Where(u => u.EmailVerified)
                .CountAsync(cancellationToken);

            // Count phone confirmed (via UserProfile join)
            var phoneConfirmed = await activeClients
                .Join(_context.UserProfiles, u => u.Id, up => up.ApplicationUserId, (u, up) => up)
                .Where(up => up.PhoneNumber != null)
                .CountAsync(cancellationToken);

            var completeProfiles = await activeClients
                .Join(
                    _context.UserProfiles,
                    u => u.Id,
                    up => up.ApplicationUserId,
                    (u, up) => new { u, up }
                )
                .Where(x =>
                    x.up.PhoneNumber != null
                    && x.u.EmailVerified
                    && x.up.FirstName != null
                    && x.up.LastName != null
                    && x.up.DateOfBirth != null
                    && x.up.ImagePath != null
                    && x.up.Documents.Any()
                    && x.up.User.UserAddresses.Any()
                )
                .CountAsync(cancellationToken);

            // Build response
            var response = new ClientStatisticsResponse
            {
                TotalClients = totalClients,
                EmailConfirmed = emailConfirmed,
                PhoneConfirmed = phoneConfirmed,
                CompleteProfiles = completeProfiles,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            // Log exception here if possible
            return Result.Failure<ClientStatisticsResponse>(
                Error.Internal($"Error retrieving client statistics: {ex.Message}")
            );
        }
    }
}
