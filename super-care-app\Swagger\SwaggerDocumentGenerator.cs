using System.Reflection;
using Asp.Versioning.ApiExplorer;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Configures the Swagger generation options
    /// </summary>
    public class SwaggerDocumentGenerator : IConfigureOptions<SwaggerGenOptions>
    {
        private readonly IApiVersionDescriptionProvider _provider;

        /// <summary>
        /// Initializes a new instance of the <see cref="SwaggerDocumentGenerator"/> class
        /// </summary>
        /// <param name="provider">The API version description provider</param>
        public SwaggerDocumentGenerator(IApiVersionDescriptionProvider provider)
        {
            _provider = provider;
        }

        /// <summary>
        /// Configures the Swagger generation options
        /// </summary>
        /// <param name="options">The Swagger generation options</param>
        public void Configure(SwaggerGenOptions options)
        {
            // Add a swagger document for each discovered API version
            foreach (var description in _provider.ApiVersionDescriptions)
            {
                options.SwaggerDoc(description.GroupName, CreateInfoForApiVersion(description));
            }

            // Add XML comments
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                options.IncludeXmlComments(xmlPath);
            }

            // Add Email/Password Authentication
            options.AddSecurityDefinition(
                "EmailPassword",
                new OpenApiSecurityScheme
                {
                    Description =
                        "Enter your email and password to authenticate.\r\n\r\n"
                        + "Email: [Your email address]\r\n\r\n"
                        + "Password: [Your password]",
                    Type = SecuritySchemeType.Http,
                    In = ParameterLocation.Header,
                    Name = "Authorization",
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                }
            );

            options.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "EmailPassword",
                            },
                        },
                        Array.Empty<string>()
                    },
                }
            );

            // Add custom filters
            options.OperationFilter<SwaggerExampleFilter>();
            options.OperationFilter<SwaggerResponseExamplesFilter>();
            options.OperationFilter<SwaggerExamplesOperationFilter>(); // Add our new filter
            options.OperationFilter<AuthorizeCheckOperationFilter>();
            options.OperationFilter<FormFileOperationFilter>();
            options.DocumentFilter<SwaggerModelExampleFilter>();
            options.SchemaFilter<SwaggerEnumSchemaFilter>();
            options.SchemaFilter<SwaggerDefaultValuesFilter>();

            // Configure file upload handling
            options.MapType<IFormFile>(
                () => new OpenApiSchema { Type = "string", Format = "binary" }
            );

            // Enable annotations
            options.EnableAnnotations();

            // Customize schema IDs to use the full type name
            options.CustomSchemaIds(type => type.FullName);
        }

        /// <summary>
        /// Creates the OpenAPI info for the specified API version
        /// </summary>
        /// <param name="description">The API version description</param>
        /// <returns>The OpenAPI info</returns>
        private static OpenApiInfo CreateInfoForApiVersion(ApiVersionDescription description)
        {
            var info = new OpenApiInfo
            {
                Title = "SuperCare API",
                Version = description.ApiVersion.ToString(),
                Description = "API for SuperCare application",
                Contact = new OpenApiContact
                {
                    Name = "SuperCare Team",
                    Email = "<EMAIL>",
                },
                License = new OpenApiLicense
                {
                    Name = "MIT",
                    Url = new Uri("https://opensource.org/licenses/MIT"),
                },
            };

            if (description.IsDeprecated)
            {
                info.Description += " This API version has been deprecated.";
            }

            return info;
        }
    }
}
