﻿namespace SuperCareApp.Application.Common.Settings;

/// <summary>
/// Represents a generic interval with a start and an end point.
/// This is an immutable value object.
/// </summary>
/// <typeparam name="T">The type of the interval points, must be comparable (e.g., DateTime, TimeOnly, int).</typeparam>
public record Interval<T>(T Start, T End)
    where T : IComparable<T>
{
    /// <summary>
    /// Validates that the start of the interval is not after its end.
    /// Throws an ArgumentException if the interval is invalid.
    /// </summary>
    public void Validate()
    {
        if (Start.CompareTo(End) > 0)
        {
            throw new ArgumentException("The start of the interval cannot be after its end.");
        }
    }
}

/// <summary>
/// A static utility class for performing calculations on intervals (ranges).
/// These methods are generic and can work with any comparable type like DateTime, TimeOnly, or numbers.
/// </summary>
public static class IntervalUtils
{
    #region Overlap and Containment Checks

    /// <summary>
    /// Checks if two intervals overlap.
    /// Intervals are considered overlapping if they share at least one point in common.
    /// e.g., [9:00, 10:00] and [9:30, 10:30] overlap.
    /// e.g., [9:00, 10:00] and [10:00, 11:00] do NOT overlap (they are adjacent).
    /// </summary>
    public static bool DoOverlap<T>(Interval<T> a, Interval<T> b)
        where T : IComparable<T>
    {
        // Overlap exists if A doesn't completely finish before B starts,
        // AND B doesn't completely finish before A starts.
        return a.Start.CompareTo(b.End) < 0 && b.Start.CompareTo(a.End) < 0;
    }

    /// <summary>
    /// Checks if two intervals are adjacent (touching at the boundary) or overlapping.
    /// e.g., [9:00, 10:00] and [10:00, 11:00] are adjacent/overlapping.
    /// Useful for determining if intervals can be merged.
    /// </summary>
    public static bool AreAdjacentOrOverlapping<T>(Interval<T> a, Interval<T> b)
        where T : IComparable<T>
    {
        return a.Start.CompareTo(b.End) <= 0 && b.Start.CompareTo(a.End) <= 0;
    }

    /// <summary>
    /// Checks if the 'inner' interval is completely contained within the 'outer' interval.
    /// The boundaries can be inclusive.
    /// </summary>
    public static bool IsContainedWithin<T>(Interval<T> inner, Interval<T> outer)
        where T : IComparable<T>
    {
        return outer.Start.CompareTo(inner.Start) <= 0 && inner.End.CompareTo(outer.End) <= 0;
    }

    #endregion

    #region Interval Manipulation

    /// <summary>
    /// Merges a collection of overlapping or adjacent intervals into the minimum number of distinct intervals.
    /// </summary>
    /// <param name="intervals">A collection of intervals to merge.</param>
    /// <returns>A new list of merged intervals, sorted by start time.</returns>
    public static List<Interval<T>> Merge<T>(IEnumerable<Interval<T>> intervals)
        where T : IComparable<T>
    {
        var sortedIntervals = intervals.OrderBy(i => i.Start).ToList();
        if (sortedIntervals.Count <= 1)
        {
            return sortedIntervals;
        }

        var merged = new List<Interval<T>>();
        var currentMerge = sortedIntervals[0];

        for (int i = 1; i < sortedIntervals.Count; i++)
        {
            var next = sortedIntervals[i];
            if (AreAdjacentOrOverlapping(currentMerge, next))
            {
                // Merge by extending the end of the current merge interval
                var newEnd = currentMerge.End.CompareTo(next.End) > 0 ? currentMerge.End : next.End;
                currentMerge = new Interval<T>(currentMerge.Start, newEnd);
            }
            else
            {
                // No overlap, finalize the current merge and start a new one
                merged.Add(currentMerge);
                currentMerge = next;
            }
        }

        merged.Add(currentMerge); // Add the last merged interval
        return merged;
    }

    /// <summary>
    /// Subtracts a list of intervals from a single source interval.
    /// This is useful for calculating available time by removing leaves or existing bookings from an availability slot.
    /// </summary>
    /// <param name="source">The main interval (e.g., a provider's work day).</param>
    /// <param name="subtractions">A list of intervals to remove (e.g., breaks, appointments).</param>
    /// <returns>A list of resulting intervals after the subtractions.</returns>
    public static List<Interval<T>> Subtract<T>(
        Interval<T> source,
        IEnumerable<Interval<T>> subtractions
    )
        where T : IComparable<T>
    {
        // First, merge the subtractions to handle their overlaps and simplify the logic
        var mergedSubtractions = Merge(subtractions);

        var results = new List<Interval<T>>();
        var currentTime = source.Start;

        foreach (var sub in mergedSubtractions)
        {
            // Ignore subtractions that don't overlap with the source at all
            if (sub.End.CompareTo(source.Start) <= 0 || sub.Start.CompareTo(source.End) >= 0)
            {
                continue;
            }

            // If there's a gap between the current time and the start of the subtraction, it's a valid resulting interval
            if (currentTime.CompareTo(sub.Start) < 0)
            {
                results.Add(new Interval<T>(currentTime, sub.Start));
            }

            // Move the current time pointer to the end of the subtraction, effectively "jumping" over it
            if (currentTime.CompareTo(sub.End) < 0)
            {
                currentTime = sub.End;
            }
        }

        // If there's any time left in the source interval after the last subtraction, add it as a final result
        if (currentTime.CompareTo(source.End) < 0)
        {
            results.Add(new Interval<T>(currentTime, source.End));
        }

        return results;
    }

    #endregion
}
