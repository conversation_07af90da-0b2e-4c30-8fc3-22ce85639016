using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class OtpCode
    {
        public Guid Id { get; set; }
        public Guid ApplicationUserId { get; set; }
        public string Code { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsUsed { get; set; }

        // Navigation property
        public ApplicationUser User { get; set; }
    }
}
