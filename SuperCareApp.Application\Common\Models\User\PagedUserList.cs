﻿namespace SuperCareApp.Application.Common.Models.User
{
    /// <summary>
    /// Paged list of users
    /// </summary>
    public class PagedUserList
    {
        public List<UserDto> Users { get; set; } = new List<UserDto>();
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
