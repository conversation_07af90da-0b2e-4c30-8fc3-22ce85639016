# Recommendation Engine Implementation

This document explains how to implement and use the R-Tree based recommendation engine for suggesting care providers.

## 1. Dependency Injection

The `RTreeRecommendationEngine` is the concrete implementation of the `IRecommendationEngine` interface. To make it available for dependency injection, you need to register it in the `SuperCareApp.Persistence/DependencyInjection.cs` file.

```csharp
// In SuperCareApp.Persistence/DependencyInjection.cs

using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Persistence.Services.Bookings;

// ... other using statements

public static class DependencyInjection
{
    public static IServiceCollection AddPersistence(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // ... other service registrations

        services.AddSingleton<IRecommendationEngine, RTreeRecommendationEngine>();

        return services;
    }
}
```

## 2. Using the Recommendation Engine

Once registered, you can inject the `IRecommendationEngine` into any service where you need to get recommendations for care providers.

### Example: A Recommendation Service

Here is an example of how you might use the recommendation engine in a service.

```csharp
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities;

public class RecommendationService
{
    private readonly IRecommendationEngine _recommendationEngine;
    private readonly IUnitOfWork _unitOfWork; // Assuming you have a Unit of Work

    public RecommendationService(IRecommendationEngine recommendationEngine, IUnitOfWork unitOfWork)
    {
        _recommendationEngine = recommendationEngine;
        _unitOfWork = unitOfWork;
        InitializeEngine();
    }

    private void InitializeEngine()
    {
        var careProviders = _unitOfWork.CareProviderProfiles.GetAll(); // Or a more specific query
        foreach (var provider in careProviders)
        {
            _recommendationEngine.AddCareProfessional(provider);
        }
    }

    public List<CareProviderProfile> GetRecommendations(
        double clientLat,
        double clientLon,
        string requiredService,
        TimeOnly requestedStart,
        TimeOnly requestedEnd)
    {
        return _recommendationEngine.Recommend(clientLat, clientLon, requiredService, requestedStart, requestedEnd);
    }
}
```

### Key Points

*   **Initialization:** The `RTreeRecommendationEngine` needs to be populated with care provider data. This should be done when the application starts or when the data changes significantly. The example above shows a simple initialization in the service's constructor.
*   **Data Loading:** In a real-world application, you would likely load the care provider data from the database and add it to the engine.
*   **Recommendations:** The `Recommend` method takes the client's location, the required service, and the requested time slot to return a list of suitable care providers.
