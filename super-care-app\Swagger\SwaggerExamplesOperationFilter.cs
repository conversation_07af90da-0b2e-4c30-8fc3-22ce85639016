using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using super_care_app.Models.Doc;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Operation filter to add examples to Swagger responses
    /// </summary>
    public class SwaggerExamplesOperationFilter : IOperationFilter
    {
        /// <summary>
        /// Applies the filter to the specified operation
        /// </summary>
        /// <param name="operation">The operation to apply the filter to</param>
        /// <param name="context">The current operation filter context</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Get controller and action names
            var controllerName = context.MethodInfo?.DeclaringType?.Name.Replace("Controller", "");
            var actionName = context.MethodInfo?.Name;

            if (string.IsNullOrEmpty(controllerName) || string.IsNullOrEmpty(actionName))
                return;

            // Add examples based on controller and action
            if (controllerName == "CareCategories")
            {
                if (actionName == "GetAllCategories" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetAllCategoriesExample",
                        CareCategoryExamples.GetAllCategoriesExample()
                    );
                }
                else if (actionName == "GetCategoryById" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetCareCategoryExample",
                        CareCategoryExamples.GetCareCategoryExample()
                    );
                }
                else if (actionName == "CreateCategory" && operation.Responses.ContainsKey("201"))
                {
                    AddResponseExample(
                        operation,
                        "201",
                        "CreateCareCategoryExample",
                        CareCategoryExamples.CreateCareCategoryExample()
                    );
                }
            }
            else if (controllerName == "Bookings")
            {
                if (actionName == "GetAvailableTimeSlots" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetAvailableTimeSlotsExample",
                        BookingExamples.GetAvailableTimeSlotsExample()
                    );
                }
                else if (
                    actionName == "GetAllAvailabilities"
                    && operation.Responses.ContainsKey("200")
                )
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetAllAvailabilitiesExample",
                        BookingExamples.GetAllAvailabilitiesExample()
                    );
                }
                else if (actionName == "IsDateAvailable" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "IsDateAvailableExample",
                        BookingExamples.IsDateAvailableExample()
                    );
                }
                else if (actionName == "CreateLeave" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "CreateLeaveExample",
                        BookingExamples.CreateLeaveExample()
                    );
                }
                else if (actionName == "GetLeave" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetLeaveExample",
                        BookingExamples.GetLeaveExample()
                    );
                }
                else if (actionName == "GetAllLeaves" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetAllLeavesExample",
                        BookingExamples.GetAllLeavesExample()
                    );
                }
            }
            else if (controllerName == "Certifications")
            {
                // Add examples for certification endpoints if needed
            }
            else if (controllerName == "Admin")
            {
                if (actionName == "GetUserProfiles" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetUserProfilesExample",
                        AdminExamples.GetUserProfilesExample()
                    );
                }
            }
            else if (controllerName == "Documents" || controllerName == "Account")
            {
                if (actionName == "UploadDocument" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "UploadDocumentExample",
                        DocumentExamples.UploadDocumentExample()
                    );
                }
                else if (actionName == "GetDocumentById" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetDocumentExample",
                        DocumentExamples.GetDocumentExample()
                    );
                }
                else if (
                    (actionName == "GetAllDocuments" || actionName == "GetAllDocumentsCached")
                    && operation.Responses.ContainsKey("200")
                )
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "GetAllDocumentsExample",
                        DocumentExamples.GetAllDocumentsExample()
                    );
                }
                else if (actionName == "UpdateDocument" && operation.Responses.ContainsKey("200"))
                {
                    AddResponseExample(
                        operation,
                        "200",
                        "UpdateDocumentExample",
                        DocumentExamples.UpdateDocumentExample()
                    );
                }
            }
        }

        /// <summary>
        /// Adds a response example to the specified operation
        /// </summary>
        /// <param name="operation">The operation to add the example to</param>
        /// <param name="statusCode">The HTTP status code</param>
        /// <param name="name">The name of the example</param>
        /// <param name="example">The example content</param>
        private static void AddResponseExample(
            OpenApiOperation operation,
            string statusCode,
            string name,
            string example
        )
        {
            if (!operation.Responses.TryGetValue(statusCode, out var response))
                return;

            if (!response.Content.TryGetValue("application/json", out var content))
                return;

            content.Examples[name] = new OpenApiExample
            {
                Summary = name,
                Value = new OpenApiString(example),
            };
        }
    }
}
