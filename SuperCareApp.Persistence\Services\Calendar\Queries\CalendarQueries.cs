using SuperCareApp.Application.Common.Interfaces.Calendar;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Calendar;

namespace SuperCareApp.Persistence.Services.Calendar.Queries
{
    // Monthly Calendar Query
    public record GetMonthlyCalendarQuery(Guid UserId, int Year, int Month)
        : IQuery<Result<MonthlyCalendarResponse>>;

    public class GetMonthlyCalendarQueryHandler
        : IQueryHandler<GetMonthlyCalendarQuery, Result<MonthlyCalendarResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetMonthlyCalendarQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<MonthlyCalendarResponse>> Handle(
            GetMonthlyCalendarQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetMonthlyCalendarAsync(
                request.UserId,
                request.Year,
                request.Month
            );
        }
    }

    // Calendar Range Query
    public record GetCalendarRangeQuery(Guid ProviderId, CalendarRangeParams Parameters)
        : IQuery<Result<CalendarRangeResponse>>;

    public class GetCalendarRangeQueryHandler
        : IQueryHandler<GetCalendarRangeQuery, Result<CalendarRangeResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetCalendarRangeQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<CalendarRangeResponse>> Handle(
            GetCalendarRangeQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetCalendarRangeAsync(
                request.ProviderId,
                request.Parameters
            );
        }
    }

    // Day Availability Query
    public record GetDayAvailabilityQuery(Guid ProviderId, DateTime Date)
        : IQuery<Result<CalendarDayResponse>>;

    public class GetDayAvailabilityQueryHandler
        : IQueryHandler<GetDayAvailabilityQuery, Result<CalendarDayResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetDayAvailabilityQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<CalendarDayResponse>> Handle(
            GetDayAvailabilityQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetDayAvailabilityAsync(request.ProviderId, request.Date);
        }
    }

    // Next Available Slots Query
    public record GetNextAvailableSlotsQuery(Guid ProviderId, NextAvailableSlotsParams Parameters)
        : IQuery<Result<NextAvailableSlotsResponse>>;

    public class GetNextAvailableSlotsQueryHandler
        : IQueryHandler<GetNextAvailableSlotsQuery, Result<NextAvailableSlotsResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetNextAvailableSlotsQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<NextAvailableSlotsResponse>> Handle(
            GetNextAvailableSlotsQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetNextAvailableSlotsAsync(
                request.ProviderId,
                request.Parameters
            );
        }
    }

    // Check Availability Query
    public record CheckAvailabilityQuery(Guid ProviderId, CheckAvailabilityParams Parameters)
        : IQuery<Result<CheckAvailabilityResponse>>;

    public class CheckAvailabilityQueryHandler
        : IQueryHandler<CheckAvailabilityQuery, Result<CheckAvailabilityResponse>>
    {
        private readonly ICalendarService _calendarService;

        public CheckAvailabilityQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<CheckAvailabilityResponse>> Handle(
            CheckAvailabilityQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.CheckAvailabilityAsync(
                request.ProviderId,
                request.Parameters
            );
        }
    }

    // Calendar Summary Query
    public record GetCalendarSummaryQuery(Guid ProviderId, int Year, int Month)
        : IQuery<Result<MonthlyCalendarResponse>>;

    public class GetCalendarSummaryQueryHandler
        : IQueryHandler<GetCalendarSummaryQuery, Result<MonthlyCalendarResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetCalendarSummaryQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<MonthlyCalendarResponse>> Handle(
            GetCalendarSummaryQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetCalendarSummaryAsync(
                request.ProviderId,
                request.Year,
                request.Month
            );
        }
    }

    // Available Providers Query
    public record GetAvailableProvidersQuery(AvailableProvidersParams Parameters)
        : IQuery<Result<AvailableProvidersResponse>>;

    public class GetAvailableProvidersQueryHandler
        : IQueryHandler<GetAvailableProvidersQuery, Result<AvailableProvidersResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetAvailableProvidersQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<AvailableProvidersResponse>> Handle(
            GetAvailableProvidersQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetAvailableProvidersAsync(request.Parameters);
        }
    }

    // Filtered Calendar Query
    public record GetFilteredCalendarQuery(
        Guid ProviderId,
        int Year,
        int Month,
        FilteredCalendarParams Filters
    ) : IQuery<Result<MonthlyCalendarResponse>>;

    public class GetFilteredCalendarQueryHandler
        : IQueryHandler<GetFilteredCalendarQuery, Result<MonthlyCalendarResponse>>
    {
        private readonly ICalendarService _calendarService;

        public GetFilteredCalendarQueryHandler(ICalendarService calendarService)
        {
            _calendarService = calendarService;
        }

        public async Task<Result<MonthlyCalendarResponse>> Handle(
            GetFilteredCalendarQuery request,
            CancellationToken cancellationToken
        )
        {
            return await _calendarService.GetFilteredCalendarAsync(
                request.ProviderId,
                request.Year,
                request.Month,
                request.Filters
            );
        }
    }
}
