using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Shared.Utility;
using Swashbuckle.AspNetCore.Annotations;

namespace super_care_app.Controllers;

/// <summary>
/// Controller for audit log operations
/// </summary>
[Authorize(Roles = "Admin")]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(
    StatusCodes.Status500InternalServerError,
    Type = typeof(ApiResponseModel<object>)
)]
public class AuditController : BaseController
{
    private readonly IAuditService _auditService;
    private readonly ILogger<AuditController> _logger;

    public AuditController(IAuditService auditService, ILogger<AuditController> logger)
    {
        _auditService = auditService;
        _logger = logger;
    }

    /// <summary>
    /// Gets audit logs for a specific entity
    /// </summary>
    /// <param name="entityType">The entity type name</param>
    /// <param name="entityId">The entity ID</param>
    /// <returns>List of audit logs for the entity</returns>
    [HttpGet(ApiRoutes.Admin.GetEntityAuditLogs)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<AuditLog>>)
    )]
    [SwaggerOperation(
        Summary = "Gets audit logs for a specific entity",
        Description = "Retrieves a list of audit logs for a single entity, identified by its type and ID.",
        OperationId = "Audit_GetEntityAuditLogs",
        Tags = new[] { "Admin - Audit Logs" }
    )]
    public async Task<IActionResult> GetEntityAuditLogs(
        [FromRoute] string entityType,
        [FromRoute] Guid entityId
    )
    {
        try
        {
            var auditLogs = await _auditService.GetEntityAuditLogsAsync(entityType, entityId);
            return SuccessResponse(
                auditLogs,
                $"Audit logs retrieved successfully for {entityType} with ID {entityId}"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity audit logs: {Error}", ex.Message);
            return InternalServerErrorResponse<List<AuditLog>>(
                "An error occurred while retrieving audit logs"
            );
        }
    }

    /// <summary>
    /// Gets audit logs for a specific user's actions
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <returns>Paginated list of audit logs for the user</returns>
    [HttpGet(ApiRoutes.Admin.GetUserAuditLogs)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets audit logs for a specific user",
        Description = "Retrieves a paginated list of actions performed by a specific user.",
        OperationId = "Audit_GetUserAuditLogs",
        Tags = new[] { "Admin - Audit Logs" }
    )]
    public async Task<IActionResult> GetUserAuditLogs(
        [FromRoute] Guid userId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10
    )
    {
        try
        {
            var (auditLogs, totalCount) = await _auditService.GetUserAuditLogsAsync(
                userId,
                pageNumber,
                pageSize
            );

            var response = new
            {
                AuditLogs = auditLogs,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
            };

            return SuccessResponse(
                response,
                $"User audit logs retrieved successfully for user {userId}"
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user audit logs: {Error}", ex.Message);
            return InternalServerErrorResponse<object>(
                "An error occurred while retrieving user audit logs"
            );
        }
    }

    /// <summary>
    /// Gets audit logs within a date range with optional filters
    /// </summary>
    /// <param name="startDate" example="2025-06-10T00:00:00Z">
    /// The start of the date range (e.g., beginning of a day in UTC).
    /// </param>
    /// <param name="endDate" example="2025-06-10T23:59:59Z">
    /// The end of the date range (e.g., end of the same day in UTC).
    /// </param>
    /// <param name="entityType">Optional entity type filter</param>
    /// <param name="action">Optional action filter (INSERT, UPDATE, DELETE)</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <returns>Paginated list of audit logs</returns>
    [HttpGet(ApiRoutes.Admin.GetAuditLogsByDateRange)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets audit logs by date range",
        Description = "Retrieves a paginated list of audit logs within a specified date range, with optional filtering by entity type and action.",
        OperationId = "Audit_GetAuditLogsByDateRange",
        Tags = new[] { "Admin - Audit Logs" }
    )]
    public async Task<IActionResult> GetAuditLogsByDateRange(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? entityType = null,
        [FromQuery] string? action = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10
    )
    {
        try
        {
            var (auditLogs, totalCount) = await _auditService.GetAuditLogsByDateRangeAsync(
                startDate,
                endDate,
                entityType,
                action,
                pageNumber,
                pageSize
            );

            var response = new
            {
                AuditLogs = auditLogs,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                Filters = new
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    EntityType = entityType,
                    Action = action,
                },
            };

            return SuccessResponse(response, "Audit logs retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit logs by date range: {Error}", ex.Message);
            return InternalServerErrorResponse<object>(
                "An error occurred while retrieving audit logs"
            );
        }
    }

    /// <summary>
    /// Gets audit statistics for reporting
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Audit statistics</returns>
    [HttpGet(ApiRoutes.Admin.GetAuditStatistics)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<AuditStatistics>)
    )]
    [SwaggerOperation(
        Summary = "Gets audit statistics",
        Description = "Retrieves statistics about audit activities (e.g., counts by action type) within a specified date range.",
        OperationId = "Audit_GetAuditStatistics",
        Tags = new[] { "Admin - Audit Logs" }
    )]
    public async Task<IActionResult> GetAuditStatistics(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate
    )
    {
        try
        {
            var statistics = await _auditService.GetAuditStatisticsAsync(startDate, endDate);
            return SuccessResponse(statistics, "Audit statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit statistics: {Error}", ex.Message);
            return InternalServerErrorResponse<AuditStatistics>(
                "An error occurred while retrieving audit statistics"
            );
        }
    }
}
