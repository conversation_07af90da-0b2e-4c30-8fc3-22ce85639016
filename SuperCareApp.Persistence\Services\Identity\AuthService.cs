using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.Otp;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Identity;

public class AuthService : IAuthService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly SignInManager<ApplicationUser> _userSignInManager;
    private readonly ApplicationDbContext _context;
    private readonly ITokenService _tokenService;
    private readonly IMailSender _mailSender;
    private readonly ILogger<AuthService> _logger;
    private readonly OtpSettings _otpSettings;

    public AuthService(
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        SignInManager<ApplicationUser> userSignInManager,
        ApplicationDbContext context,
        ITokenService tokenService,
        IMailSender mailSender,
        ILogger<AuthService> logger,
        IOptions<OtpSettings> otpSettings
    )
    {
        _userManager = userManager;
        _roleManager = roleManager;
        _userSignInManager = userSignInManager;
        _context = context;
        _tokenService = tokenService;
        _mailSender = mailSender;
        _logger = logger;
        _otpSettings = otpSettings.Value;
    }

    public async Task<Result<AuthResponse>> AuthenticateAsync(string email, string password)
    {
        try
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null || !await _userManager.CheckPasswordAsync(user, password))
            {
                _logger.LogWarning("Authentication failed for user: {Email}", email);
                return Result.Failure<AuthResponse>(Error.Validation("Invalid email or password."));
            }

            await _userSignInManager.SignInAsync(user, isPersistent: false);

            var roles = await _userManager.GetRolesAsync(user);
            var loginSessionId = Guid.NewGuid();
            var accessTokenResult = await _tokenService.GenerateAccessTokenAsync(
                user,
                loginSessionId
            );
            if (accessTokenResult.IsFailure)
            {
                _logger.LogError("Failed to generate access token for user: {Email}", email);
                return Result.Failure<AuthResponse>(accessTokenResult.Error);
            }

            var refreshTokenResult = await _tokenService.GenerateRefreshTokenAsync(
                user,
                accessTokenResult.Value,
                loginSessionId
            );
            if (refreshTokenResult.IsFailure)
            {
                _logger.LogError("Failed to generate refresh token for user: {Email}", email);
                return Result.Failure<AuthResponse>(refreshTokenResult.Error);
            }
            // Check if the user is a care provider and get verification status
            bool? isVerifiedByAdmin = null;

            var careProviderProfile = await _context
                .CareProviderProfiles.AsNoTracking()
                .FirstOrDefaultAsync(cp => cp.UserId == user.Id && !cp.IsDeleted);
            var careProviderProfileId = careProviderProfile?.Id;
            if (careProviderProfile != null)
            {
                // Set verification status
                isVerifiedByAdmin =
                    careProviderProfile.VerificationStatus
                    == Domain.Enums.VerificationStatus.Verified;

                _logger.LogInformation(
                    "Care provider {Email} verification status: {Status}",
                    email,
                    careProviderProfile.VerificationStatus
                );
            }

            var authResponse = new AuthResponse(
                accessTokenResult.Value,
                DateTime.UtcNow.AddHours(1),
                refreshTokenResult.Value,
                isVerifiedByAdmin,
                new AuthUserResponse(
                    user.Id.ToString(),
                    user.Email,
                    user.PhoneNumber,
                    careProviderProfileId.ToString()
                )
            );

            _logger.LogInformation("User {Email} authenticated successfully.", email);
            return Result.Success(authResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user: {Email}", email);
            return Result.Failure<AuthResponse>(
                Error.Internal("An error occurred during authentication.")
            );
        }
    }

    public async Task<Result<CreateUserProfileResponse>> CreateUserProfileAsync(
        string email,
        string? firstName,
        string? lastName,
        string? phoneNumber
    )
    {
        try
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                _logger.LogWarning("User not found for email: {Email}", email);
                return Result.Failure<CreateUserProfileResponse>(Error.NotFound("User not found."));
            }

            // Check if profile already exists
            var existingProfile = await _context
                .UserProfiles.AsNoTracking()
                .FirstOrDefaultAsync(p => p.ApplicationUserId == user.Id);

            var profile =
                existingProfile
                ?? new UserProfile
                {
                    ApplicationUserId = user.Id,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                };

            profile.FirstName = firstName;
            profile.LastName = lastName;
            profile.PhoneNumber = phoneNumber;

            if (existingProfile == null)
            {
                _context.UserProfiles.Add(profile);
            }
            else
            {
                _context.UserProfiles.Update(profile);
            }

            await _context.SaveChangesAsync();

            return Result.Success(
                new CreateUserProfileResponse("User profile created successfully.", profile.Id)
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user profile for {Email}", email);
            return Result.Failure<CreateUserProfileResponse>(
                Error.Internal("Failed to create user profile.")
            );
        }
    }

    public async Task<Result<OtpResponse>> GenerateOtpAsync(string email, string actionType)
    {
        try
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                _logger.LogWarning("User not found for OTP generation: {Email}", email);
                return Result.Failure<OtpResponse>(Error.NotFound("User not found."));
            }

            var otpCode = new OtpCode
            {
                ApplicationUserId = user.Id,
                Code = GenerateRandomOtp(),
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(10), // 10 minutes expiry
                IsUsed = false,
            };

            _context.OtpCodes.Add(otpCode);
            await _context.SaveChangesAsync();

            // ✅ Send OTP via email
            var subject = "Your One-Time Password (OTP)";
            var body =
                $@"
            <p>Hello,</p>
            <p>Your OTP code for {actionType} is: {otpCode.Code}</p>
            <p>This code will expire in 10 minutes.</p>
            <p>Please do not share it with anyone.</p>
            <br/>
            <p>Best regards,<br/>SuperCareApp Team</p>";

            var sendResult = await _mailSender.SendMail(email, subject, body);

            if (!sendResult.IsSuccess)
            {
                _logger.LogError("Failed to send OTP email to {Email}", email);
                return Result.Failure<OtpResponse>(Error.BadRequest("Failed to send OTP email."));
            }

            return Result.Success(new OtpResponse(otpCode.Code, DateTime.UtcNow.AddMinutes(10)));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating OTP for {Email}", email);
            return Result.Failure<OtpResponse>(Error.Internal("Failed to generate OTP."));
        }
    }

    private string GenerateRandomOtp()
    {
        const string chars = "**********";
        var random = new Random();
        return new string(
            Enumerable.Repeat(chars, 6).Select(s => s[random.Next(s.Length)]).ToArray()
        );
    }

    public async Task<Result<Guid>> RegisterAsync(
        string email,
        string password,
        string? phoneNumber,
        bool isCareProvider
    )
    {
        try
        {
            var user = new ApplicationUser
            {
                UserName = email,
                Email = email,
                EmailVerified = false,
                IsActive = true,
                PhoneNumber = phoneNumber,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
            };
            var response = await _userManager.CreateAsync(user, password);
            var userId = response.Succeeded ? user.Id : Guid.Empty;
            //Create Error DIctionary based on response
            var errors = new Dictionary<string, string[]>();
            if (response.Errors.Any())
            {
                foreach (var error in response.Errors)
                {
                    errors.Add(error.Code, new[] { error.Description });
                }
            }
            return response.Succeeded
                ? Result.Success(userId)
                : Result.Failure<Guid>(Error.Validation("Failed to register user", errors));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering user: {Email}", email);
            return Result.Failure<Guid>(Error.Internal("Failed to register user"));
        }
    }

    public async Task<Result> ResetPasswordAsync(string email, string password, string resetToken)
    {
        try
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return Result.Failure(Error.NotFound("User not found."));
            }

            var result = await _userManager.ResetPasswordAsync(user, resetToken, password);
            if (!result.Succeeded)
            {
                return Result.Failure(Error.Validation("Password reset failed."));
            }

            await _tokenService.RevokeAllTokensAsync(user.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for {Email}", email);
            return Result.Failure(Error.Internal("Failed to reset password."));
        }
    }

    public async Task<Result<UpdateuserProfileResponse>> UpdateUserProfileAsync(
        string email,
        UpdateUserProfileRequest request
    )
    {
        try
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return Result.Failure<UpdateuserProfileResponse>(Error.NotFound("User not found."));
            }

            var profile = await _context.UserProfiles.FirstOrDefaultAsync(p =>
                p.ApplicationUserId == user.Id
            );

            if (profile == null)
            {
                return Result.Failure<UpdateuserProfileResponse>(
                    Error.NotFound("User profile not found.")
                );
            }

            profile.FirstName = request.FirstName;
            profile.LastName = request.LastName;
            profile.PhoneNumber = request.PhoneNumber;
            profile.DateOfBirth = request.DateOfBirth;
            profile.Gender = request.Gender;
            profile.Country = request.Country;
            profile.UpdatedAt = DateTime.UtcNow;

            // Handle address separately if provided
            if (
                !string.IsNullOrWhiteSpace(request.Address)
                || !string.IsNullOrWhiteSpace(request.City)
                || !string.IsNullOrWhiteSpace(request.State)
                || !string.IsNullOrWhiteSpace(request.PostalCode)
            )
            {
                // Check if user already has a primary address
                var existingPrimaryAddress = await _context
                    .UserAddresses.Include(ua => ua.Address)
                    .FirstOrDefaultAsync(ua =>
                        ua.UserId == user.Id && ua.IsPrimary && !ua.IsDeleted
                    );

                if (existingPrimaryAddress != null)
                {
                    // Update existing primary address
                    var address = existingPrimaryAddress.Address;

                    if (!string.IsNullOrWhiteSpace(request.Address))
                        address.StreetAddress = request.Address;

                    if (!string.IsNullOrWhiteSpace(request.City))
                        address.City = request.City;

                    if (!string.IsNullOrWhiteSpace(request.State))
                        address.State = request.State;

                    if (!string.IsNullOrWhiteSpace(request.PostalCode))
                        address.PostalCode = request.PostalCode;

                    address.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    // Create new address
                    var address = new Domain.Entities.Address
                    {
                        StreetAddress = request.Address ?? string.Empty,
                        City = request.City ?? string.Empty,
                        State = request.State ?? string.Empty,
                        PostalCode = request.PostalCode ?? string.Empty,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    };

                    await _context.Addresses.AddAsync(address);

                    // Create user-address relationship
                    var userAddress = new UserAddress
                    {
                        UserId = user.Id,
                        AddressId = address.Id,
                        IsPrimary = true,
                        Label = "Home",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    };

                    await _context.UserAddresses.AddAsync(userAddress);
                }
            }

            _context.UserProfiles.Update(profile);
            await _context.SaveChangesAsync();

            return Result.Success(
                new UpdateuserProfileResponse("User profile updated successfully.", profile.Id)
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user profile for {Email}", email);
            return Result.Failure<UpdateuserProfileResponse>(
                Error.Internal("Failed to update user profile.")
            );
        }
    }

    public async Task<Result<ApplicationUser>> VerifyOtpAsync(string email, string otp)
    {
        try
        {
            var isDevelopment = _otpSettings.UseDefaultOtpForDevelopment;
            var user = await VeifyUserAsync(email);
            if (user == null)
            {
                return Result.Failure<ApplicationUser>(Error.NotFound("User not found."));
            }
            if (isDevelopment && otp == _otpSettings.DefaultOtp)
            {
                if (!user.EmailVerified)
                {
                    user.EmailConfirmed = true;
                    user.EmailVerified = true;
                    await _userManager.UpdateAsync(user);
                }
                return Result.Success(user);
            }

            if (!user.EmailVerified)
            {
                user.EmailConfirmed = true;
                user.EmailVerified = true;
                await _userManager.UpdateAsync(user);
            }
            var otpRecord = await _context
                .OtpCodes.AsNoTracking()
                .FirstOrDefaultAsync(o =>
                    o.ApplicationUserId == user.Id && o.Code == otp && !o.IsUsed
                );

            if (otpRecord == null)
            {
                return Result.Failure<ApplicationUser>(Error.Validation("Invalid or expired OTP."));
            }

            otpRecord.IsUsed = true;
            await _context.SaveChangesAsync();

            return Result.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying OTP for {Email}", email);
            return Result.Failure<ApplicationUser>(Error.Internal("Failed to verify OTP."));
        }
    }

    public async Task<Result<string>> VerifyOtpForgetPasswordAsync(string email, string otp)
    {
        try
        {
            var user = await VeifyUserAsync(email);
            if (user == null)
            {
                return Result.Failure<string>(Error.NotFound("User not found."));
            }
            var otpRecord = await _context
                .OtpCodes.AsNoTracking()
                .FirstOrDefaultAsync(o =>
                    o.ApplicationUserId == user.Id && o.Code == otp && !o.IsUsed
                );

            if (otpRecord == null)
            {
                return Result.Failure<string>(Error.Validation("Invalid or expired OTP."));
            }

            otpRecord.IsUsed = true;
            await _context.SaveChangesAsync();

            // Generate a password reset token
            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            return Result.Success(token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying OTP for password reset for {Email}", email);
            return Result.Failure<string>(Error.Internal("Failed to verify OTP."));
        }
    }

    private async Task<ApplicationUser> VeifyUserAsync(string email)
    {
        var user = await _userManager.FindByEmailAsync(email);
        if (user == null)
        {
            return new ApplicationUser();
        }

        if (!user.EmailVerified)
        {
            user.EmailVerified = true;
            await _userManager.UpdateAsync(user);
        }
        return user;
    }

    public async Task<Result> AddToRoleAsync(string userId, string role)
    {
        try
        {
            if (!await _roleManager.RoleExistsAsync(role))
            {
                _logger.LogError("Role {Role} not found", role);
                return Result.Failure(Error.NotFound("Role not found."));
            }
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                _logger.LogError("User with ID {UserId} not found", userId);
                return Result.Failure(Error.NotFound("User not found."));
            }

            if (await _userManager.IsInRoleAsync(user, role))
            {
                _logger.LogInformation("User {UserId} is already in role {Role}", userId, role);
                return Result.Success();
            }

            var result = await _userManager.AddToRoleAsync(user, role);
            if (!result.Succeeded)
            {
                _logger.LogError(
                    "Failed to add role {Role} to user {UserId}: {Errors}",
                    role,
                    userId,
                    string.Join(", ", result.Errors.Select(e => e.Description))
                );
                return Result.Failure(
                    Error.Validation(
                        $"Failed to add role: {string.Join(", ", result.Errors.Select(e => e.Description))}"
                    )
                );
            }

            _logger.LogInformation("Successfully added role {Role} to user {UserId}", role, userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "An error occurred while adding role {Role} to user {UserId}",
                role,
                userId
            );
            return Result.Failure(
                Error.Internal("An unexpected error occurred while adding the role.")
            );
        }
    }
}
