﻿namespace SuperCareApp.Domain.Common.Results
{
    /// <summary>
    /// Extension methods for the Result class
    /// </summary>
    public static class ResultExtensions
    {
        /// <summary>
        /// Maps a successful result to a new result with a different value
        /// </summary>
        public static Result<TOut> Map<TIn, TOut>(this Result<TIn> result, Func<TIn, TOut> mapFunc)
        {
            return result.IsSuccess
                ? Result.Success(mapFunc(result.Value))
                : Result.Failure<TOut>(result.Error);
        }

        /// <summary>
        /// Maps a successful result to a new result with a different value asynchronously
        /// </summary>
        public static async Task<Result<TOut>> MapAsync<TIn, TOut>(
            this Result<TIn> result,
            Func<TIn, Task<TOut>> mapFunc
        )
        {
            return result.IsSuccess
                ? Result.Success(await mapFunc(result.Value))
                : Result.Failure<TOut>(result.Error);
        }

        /// <summary>
        /// Maps a successful result to a new result with a different value asynchronously
        /// </summary>
        public static async Task<Result<TOut>> MapAsync<TIn, TOut>(
            this Task<Result<TIn>> resultTask,
            Func<TIn, TOut> mapFunc
        )
        {
            var result = await resultTask;
            return result.Map(mapFunc);
        }

        /// <summary>
        /// Maps a successful result to a new result with a different value asynchronously
        /// </summary>
        public static async Task<Result<TOut>> MapAsync<TIn, TOut>(
            this Task<Result<TIn>> resultTask,
            Func<TIn, Task<TOut>> mapFunc
        )
        {
            var result = await resultTask;
            return await result.MapAsync(mapFunc);
        }

        /// <summary>
        /// Binds a successful result to a new result
        /// </summary>
        public static Result<TOut> Bind<TIn, TOut>(
            this Result<TIn> result,
            Func<TIn, Result<TOut>> bindFunc
        )
        {
            return result.IsSuccess ? bindFunc(result.Value) : Result.Failure<TOut>(result.Error);
        }

        /// <summary>
        /// Binds a successful result to a new result asynchronously
        /// </summary>
        public static async Task<Result<TOut>> BindAsync<TIn, TOut>(
            this Result<TIn> result,
            Func<TIn, Task<Result<TOut>>> bindFunc
        )
        {
            return result.IsSuccess
                ? await bindFunc(result.Value)
                : Result.Failure<TOut>(result.Error);
        }

        /// <summary>
        /// Binds a successful result to a new result asynchronously
        /// </summary>
        public static async Task<Result<TOut>> BindAsync<TIn, TOut>(
            this Task<Result<TIn>> resultTask,
            Func<TIn, Result<TOut>> bindFunc
        )
        {
            var result = await resultTask;
            return result.Bind(bindFunc);
        }

        /// <summary>
        /// Binds a successful result to a new result asynchronously
        /// </summary>
        public static async Task<Result<TOut>> BindAsync<TIn, TOut>(
            this Task<Result<TIn>> resultTask,
            Func<TIn, Task<Result<TOut>>> bindFunc
        )
        {
            var result = await resultTask;
            return await result.BindAsync(bindFunc);
        }

        /// <summary>
        /// Executes an action if the result is successful
        /// </summary>
        public static Result<T> Tap<T>(this Result<T> result, Action<T> action)
        {
            if (result.IsSuccess)
            {
                action(result.Value);
            }

            return result;
        }

        /// <summary>
        /// Executes an action if the result is successful asynchronously
        /// </summary>
        public static async Task<Result<T>> TapAsync<T>(this Result<T> result, Func<T, Task> action)
        {
            if (result.IsSuccess)
            {
                await action(result.Value);
            }

            return result;
        }

        /// <summary>
        /// Executes an action if the result is successful asynchronously
        /// </summary>
        public static async Task<Result<T>> TapAsync<T>(
            this Task<Result<T>> resultTask,
            Action<T> action
        )
        {
            var result = await resultTask;
            return result.Tap(action);
        }

        /// <summary>
        /// Executes an action if the result is successful asynchronously
        /// </summary>
        public static async Task<Result<T>> TapAsync<T>(
            this Task<Result<T>> resultTask,
            Func<T, Task> action
        )
        {
            var result = await resultTask;
            return await result.TapAsync(action);
        }

        /// <summary>
        /// Executes an action if the result is a failure
        /// </summary>
        public static Result<T> TapError<T>(this Result<T> result, Action<Error> action)
        {
            if (result.IsFailure)
            {
                action(result.Error);
            }

            return result;
        }

        /// <summary>
        /// Executes an action if the result is a failure asynchronously
        /// </summary>
        public static async Task<Result<T>> TapErrorAsync<T>(
            this Result<T> result,
            Func<Error, Task> action
        )
        {
            if (result.IsFailure)
            {
                await action(result.Error);
            }

            return result;
        }

        /// <summary>
        /// Executes an action if the result is a failure asynchronously
        /// </summary>
        public static async Task<Result<T>> TapErrorAsync<T>(
            this Task<Result<T>> resultTask,
            Action<Error> action
        )
        {
            var result = await resultTask;
            return result.TapError(action);
        }

        /// <summary>
        /// Executes an action if the result is a failure asynchronously
        /// </summary>
        public static async Task<Result<T>> TapErrorAsync<T>(
            this Task<Result<T>> resultTask,
            Func<Error, Task> action
        )
        {
            var result = await resultTask;
            return await result.TapErrorAsync(action);
        }

        /// <summary>
        /// Matches the result to one of two functions based on success or failure
        /// </summary>
        public static TOut Match<TIn, TOut>(
            this Result<TIn> result,
            Func<TIn, TOut> onSuccess,
            Func<Error, TOut> onFailure
        )
        {
            return result.IsSuccess ? onSuccess(result.Value) : onFailure(result.Error);
        }

        /// <summary>
        /// Matches the result to one of two functions based on success or failure asynchronously
        /// </summary>
        public static async Task<TOut> MatchAsync<TIn, TOut>(
            this Result<TIn> result,
            Func<TIn, Task<TOut>> onSuccess,
            Func<Error, Task<TOut>> onFailure
        )
        {
            return result.IsSuccess ? await onSuccess(result.Value) : await onFailure(result.Error);
        }

        /// <summary>
        /// Matches the result to one of two functions based on success or failure asynchronously
        /// </summary>
        public static async Task<TOut> MatchAsync<TIn, TOut>(
            this Task<Result<TIn>> resultTask,
            Func<TIn, TOut> onSuccess,
            Func<Error, TOut> onFailure
        )
        {
            var result = await resultTask;
            return result.Match(onSuccess, onFailure);
        }

        /// <summary>
        /// Matches the result to one of two functions based on success or failure asynchronously
        /// </summary>
        public static async Task<TOut> MatchAsync<TIn, TOut>(
            this Task<Result<TIn>> resultTask,
            Func<TIn, Task<TOut>> onSuccess,
            Func<Error, Task<TOut>> onFailure
        )
        {
            var result = await resultTask;
            return await result.MatchAsync(onSuccess, onFailure);
        }
    }
}
