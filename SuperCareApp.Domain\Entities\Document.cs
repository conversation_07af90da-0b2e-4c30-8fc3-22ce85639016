﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities
{
    public class Document : BaseEntity
    {
        public Guid UserId { get; set; }
        public string? DocumentName { get; set; } = string.Empty;
        public string DocumentType { get; set; } = string.Empty;
        public string DocumentUrl { get; set; } = string.Empty;
        public string? Issuer { get; set; }
        public VerificationStatus VerificationStatus { get; set; }
        public DateTime UploadedAt { get; set; }
        public DateTime? VerifiedAt { get; set; }
        public Guid? VerifiedBy { get; set; }
        public string? RejectionReason { get; set; }

        // New properties for country-specific certifications
        public Country Country { get; set; } = Country.Other;
        public CertificationType? CertificationType { get; set; }
        public string? OtherCertificationType { get; set; }
        public string? CertificationNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;

        // Navigation properties
        public UserProfile UserProfile { get; set; } = null!;
    }
}
