using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.InMemory;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services;
using AvailabilityEntity = SuperCareApp.Domain.Entities.Availability;

namespace SuperCareApp.Persistence.Test.Services.BookingAvailabilityWorkflow;

public class BookingWorkflowHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BookingManagementService _service;
    private readonly Guid _providerId;
    private readonly Guid _clientId;
    private readonly Guid _categoryId;
    private readonly Guid _userId;

    public BookingWorkflowHandlerTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        _context = new ApplicationDbContext(options);
        _service = new BookingManagementService(_context);

        _providerId = Guid.NewGuid();
        _clientId = Guid.NewGuid();
        _categoryId = Guid.NewGuid();
        _userId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.CareProviderProfiles.Add(providerProfile);

        // Create availability for Monday
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Monday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Booking Confirmation Tests

    [Fact]
    public async Task HandleBookingAsync_ConfirmBooking_WithAvailableSlot_ShouldSucceed()
    {
        // Arrange
        var booking = CreateRequestedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var confirmRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Confirm,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act
        var result = await _service.HandleBookingAsync(confirmRequest);

        // Assert
        Assert.Equal(booking.booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.booking.Id);

        Assert.Equal(BookingStatusType.Confirmed, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_ConfirmBooking_WithUnavailableSlot_ShouldThrowException()
    {
        // Arrange
        var booking = CreateRequestedBooking();

        // Create a conflicting confirmed booking in the same time slot
        var conflictingBooking = CreateConfirmedBooking(
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime
        );

        _context.Bookings.AddRange(booking.booking, conflictingBooking.booking);
        _context.BookingStatuses.AddRange(booking.status, conflictingBooking.status);
        _context.BookingWindows.AddRange(booking.window, conflictingBooking.window);
        await _context.SaveChangesAsync();

        var confirmRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Confirm,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(confirmRequest)
        );

        Assert.Contains("no longer available", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_ConfirmBooking_WithNonRequestedStatus_ShouldThrowException()
    {
        // Arrange
        var booking = CreateConfirmedBooking(); // Already confirmed
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var confirmRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Confirm,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(confirmRequest)
        );

        Assert.Contains("not in Requested state", exception.Message);
    }

    #endregion

    #region Booking Cancellation Tests

    [Fact]
    public async Task HandleBookingAsync_CancelBooking_WithValidBooking_ShouldSucceed()
    {
        // Arrange
        var booking = CreateConfirmedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var cancelRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Cancel,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act
        var result = await _service.HandleBookingAsync(cancelRequest);

        // Assert
        Assert.Equal(booking.booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.booking.Id);

        Assert.Equal(BookingStatusType.Cancelled, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_CancelBooking_WithCompletedBooking_ShouldThrowException()
    {
        // Arrange
        var booking = CreateCompletedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var cancelRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Cancel,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(cancelRequest)
        );

        Assert.Contains("Booking is already completed or cancelled", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_CancelBooking_WithAlreadyCancelledBooking_ShouldThrowException()
    {
        // Arrange
        var booking = CreateCancelledBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var cancelRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Cancel,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(cancelRequest)
        );

        Assert.Contains("Booking is already completed or cancelled", exception.Message);
    }

    #endregion

    #region Booking Completion Tests

    [Fact]
    public async Task HandleBookingAsync_CompleteBooking_WithConfirmedBooking_ShouldSucceed()
    {
        // Arrange
        var booking = CreateConfirmedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var completeRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Complete,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act
        var result = await _service.HandleBookingAsync(completeRequest);

        // Assert
        Assert.Equal(booking.booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.booking.Id);

        Assert.Equal(BookingStatusType.Completed, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_CompleteBooking_WithRequestedBooking_ShouldThrowException()
    {
        // Arrange
        var booking = CreateRequestedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var completeRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Complete,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(completeRequest)
        );

        Assert.Contains("not in Confirmed state", exception.Message);
    }

    #endregion

    #region Multi-Day Booking Tests

    [Fact]
    public async Task HandleBookingAsync_ConfirmMultiDayBooking_WithAllSlotsAvailable_ShouldSucceed()
    {
        // Arrange
        var booking = CreateMultiDayRequestedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.AddRange(booking.windows);
        await _context.SaveChangesAsync();

        var confirmRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Confirm,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.windows[0].Date,
            booking.windows[0].StartTime,
            booking.windows[0].EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act
        var result = await _service.HandleBookingAsync(confirmRequest);

        // Assert
        Assert.Equal(booking.booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.booking.Id);

        Assert.Equal(BookingStatusType.Confirmed, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_ConfirmMultiDayBooking_WithOneSlotUnavailable_ShouldThrowException()
    {
        // Arrange
        var booking = CreateMultiDayRequestedBooking();

        // Create a conflicting booking for the second day
        var conflictingBooking = CreateConfirmedBooking(
            booking.windows[1].Date,
            booking.windows[1].StartTime,
            booking.windows[1].EndTime
        );

        _context.Bookings.AddRange(booking.booking, conflictingBooking.booking);
        _context.BookingStatuses.AddRange(booking.status, conflictingBooking.status);
        _context.BookingWindows.AddRange(
            booking.windows.Concat(new[] { conflictingBooking.window })
        );
        await _context.SaveChangesAsync();

        var confirmRequest = new BookingRequest(
            booking.booking.Id,
            BookingAction.Confirm,
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.windows[0].Date,
            booking.windows[0].StartTime,
            booking.windows[0].EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(confirmRequest)
        );

        Assert.Contains("no longer available", exception.Message);
    }

    #endregion

    #region Transaction and Error Handling Tests

    [Fact]
    public async Task HandleBookingAsync_WithNonExistentBooking_ShouldThrowException()
    {
        // Arrange
        var nonExistentBookingId = Guid.NewGuid();
        var confirmRequest = new BookingRequest(
            nonExistentBookingId,
            BookingAction.Confirm,
            _providerId,
            _clientId,
            _categoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            100,
            10,
            _userId
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.HandleBookingAsync(confirmRequest)
        );

        Assert.Contains("Booking not found", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_WithInvalidAction_ShouldThrowException()
    {
        // Arrange
        var booking = CreateRequestedBooking();
        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        var invalidRequest = new BookingRequest(
            booking.booking.Id,
            (BookingAction)999, // Invalid action
            booking.booking.ProviderId,
            booking.booking.ClientId,
            booking.booking.CategoryId,
            booking.window.Date,
            booking.window.StartTime,
            booking.window.EndTime,
            null,
            booking.booking.TotalAmount,
            booking.booking.PlatformFee,
            booking.booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _service.HandleBookingAsync(invalidRequest)
        );

        Assert.Contains("Invalid booking action", exception.Message);
    }

    #endregion

    #region Helper Methods

    private (Booking booking, BookingStatus status, BookingWindow window) CreateRequestedBooking()
    {
        return CreateBookingWithStatus(BookingStatusType.Requested);
    }

    private (Booking booking, BookingStatus status, BookingWindow window) CreateConfirmedBooking(
        DateOnly? date = null,
        TimeOnly? startTime = null,
        TimeOnly? endTime = null
    )
    {
        return CreateBookingWithStatus(BookingStatusType.Confirmed, date, startTime, endTime);
    }

    private (Booking booking, BookingStatus status, BookingWindow window) CreateCompletedBooking()
    {
        return CreateBookingWithStatus(BookingStatusType.Completed);
    }

    private (Booking booking, BookingStatus status, BookingWindow window) CreateCancelledBooking()
    {
        return CreateBookingWithStatus(BookingStatusType.Cancelled);
    }

    private (Booking booking, BookingStatus status, BookingWindow window) CreateBookingWithStatus(
        BookingStatusType statusType,
        DateOnly? date = null,
        TimeOnly? startTime = null,
        TimeOnly? endTime = null
    )
    {
        var bookingId = Guid.NewGuid();
        var bookingDate = date ?? new DateOnly(2025, 7, 21); // Monday
        var bookingStartTime = startTime ?? new TimeOnly(10, 0);
        var bookingEndTime = endTime ?? new TimeOnly(11, 0);

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 1,
            TotalAmount = 100,
            PlatformFee = 10,
            ProviderAmount = 90,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var status = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = statusType,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var window = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Date = bookingDate,
            StartTime = bookingStartTime,
            EndTime = bookingEndTime,
            DurationMinutes = (int)(bookingEndTime - bookingStartTime).TotalMinutes,
            DailyRate = 100,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        booking.Status = status;
        return (booking, status, window);
    }

    private (
        Booking booking,
        BookingStatus status,
        List<BookingWindow> windows
    ) CreateMultiDayRequestedBooking()
    {
        var bookingId = Guid.NewGuid();

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 2,
            TotalAmount = 200,
            PlatformFee = 20,
            ProviderAmount = 180,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var status = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Requested,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var windows = new List<BookingWindow>
        {
            new BookingWindow
            {
                Id = Guid.NewGuid(),
                BookingId = bookingId,
                Date = new DateOnly(2025, 7, 21), // Monday
                StartTime = new TimeOnly(10, 0),
                EndTime = new TimeOnly(11, 0),
                DurationMinutes = 60,
                DailyRate = 100,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _userId,
            },
            new BookingWindow
            {
                Id = Guid.NewGuid(),
                BookingId = bookingId,
                Date = new DateOnly(2025, 7, 28), // Next Monday
                StartTime = new TimeOnly(14, 0),
                EndTime = new TimeOnly(15, 0),
                DurationMinutes = 60,
                DailyRate = 100,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _userId,
            },
        };

        booking.Status = status;
        return (booking, status, windows);
    }

    #endregion
}
