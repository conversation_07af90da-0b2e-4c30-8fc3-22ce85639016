using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

/// <summary>
/// Query to get provider enhanced availability with monthly calendar view
/// </summary>
/// <param name="ProviderId">The provider ID</param>
/// <param name="MonthsAhead">Number of months ahead to check (default: 3)</param>
public record GetProviderEnhancedAvailabilityQuery(Guid ProviderId, int MonthsAhead = 3)
    : IQuery<Result<EnhancedAvailabilityResponse>>;

/// <summary>
/// Handler for GetProviderEnhancedAvailabilityQuery
/// </summary>
public class GetProviderEnhancedAvailabilityQueryHandler
    : IQueryHandler<GetProviderEnhancedAvailabilityQuery, Result<EnhancedAvailabilityResponse>>
{
    private readonly IAvailabilityService _availabilityService;
    private readonly ILogger<GetProviderEnhancedAvailabilityQueryHandler> _logger;

    public GetProviderEnhancedAvailabilityQueryHandler(
        IAvailabilityService availabilityService,
        ILogger<GetProviderEnhancedAvailabilityQueryHandler> logger
    )
    {
        _availabilityService = availabilityService;
        _logger = logger;
    }

    public async Task<Result<EnhancedAvailabilityResponse>> Handle(
        GetProviderEnhancedAvailabilityQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            return await _availabilityService.GetProviderEnhancedAvailabilityAsync(
                request.ProviderId,
                request.MonthsAhead
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetProviderEnhancedAvailabilityQuery");
            return Result.Failure<EnhancedAvailabilityResponse>(
                Error.Internal("Error retrieving provider enhanced availability")
            );
        }
    }
}
