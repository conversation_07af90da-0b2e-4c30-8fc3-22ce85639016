using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

/// <summary>
/// Entity Framework Core configuration for the <see cref="BookingWindow"/> entity.
/// This configuration is tailored for a PostgreSQL database.
/// </summary>
public class BookingWindowConfiguration : IEntityTypeConfiguration<BookingWindow>
{
    public void Configure(EntityTypeBuilder<BookingWindow> builder)
    {
        builder.HasKey(bw => bw.Id);

        // --- Property Configurations ---
        builder.Property(bw => bw.Id).IsRequired();

        // Use 'date' type for PostgresSQL for just the date part.
        builder.Property(bw => bw.Date).IsRequired().HasColumnType("date");

        // Use 'time' type for PostgresSQL for just the time part.
        builder.Property(bw => bw.StartTime).IsRequired().HasColumnType("time");

        builder.Property(bw => bw.EndTime).IsRequired().HasColumnType("time");

        //Map the enum property
        builder.Property(bw => bw.Status).HasConversion<string>();

        // Map the decimal property to a specific precision.
        builder.Property(bw => bw.DailyRate).HasColumnType("decimal(18,2)");

        builder.Property(bw => bw.DaySpecialInstructions).HasMaxLength(1000);
        builder
            .Property(bw => bw.DurationMinutes)
            .HasComputedColumnSql(
                @"EXTRACT(EPOCH FROM (""end_time"" - ""start_time"")) / 60",
                stored: true
            );
        builder
            .HasOne(bw => bw.Booking)
            .WithMany(b => b.BookingWindows)
            .HasForeignKey(bw => bw.BookingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasMany(bw => bw.TrackingSessions)
            .WithOne(ts => ts.BookingWindow)
            .HasForeignKey(ts => ts.BookingWindowId)
            .OnDelete(DeleteBehavior.Restrict); // safer – delete window = manual clean-up

        builder.HasIndex(bw => bw.Date).HasDatabaseName("IX_BookingWindows_Date");

        builder
            .HasIndex(bw => new { bw.BookingId, bw.Date })
            .IsUnique()
            .HasDatabaseName("IX_BookingWindows_BookingId_Date");

        builder.HasIndex(bw => bw.StartTime).HasDatabaseName("IX_BookingWindows_StartTime");

        builder.HasIndex(bw => bw.EndTime).HasDatabaseName("IX_BookingWindows_EndTime");

        builder.ToTable(tb =>
            tb.HasCheckConstraint("CK_BookingWindows_ValidTimes", @"""end_time"" > ""start_time""")
        );
    }
}
