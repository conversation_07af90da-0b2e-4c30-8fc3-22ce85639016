using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings.Queries;
using AvailabilityEntity = SuperCareApp.Domain.Entities.Availability;

namespace SuperCareApp.Persistence.Test.Services.BookingAvailabilityWorkflow;

public class QueryHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly Guid _providerId;
    private readonly Guid _userId;

    public QueryHandlerTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _providerId = Guid.NewGuid();
        _userId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.CareProviderProfiles.Add(providerProfile);

        // Create availability for Monday
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Monday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region GetProviderAvailabilityForDateQuery Tests

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithAvailableProvider_ShouldReturnAvailableSlots()
    {
        // Arrange
        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var query = new GetProviderAvailabilityForDateQuery(_providerId, new DateOnly(2025, 7, 21)); // Monday

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Available);
        Assert.Single(result.Value.Slots);
        Assert.Equal("09:00", result.Value.Slots.First().StartTime);
        Assert.Equal("17:00", result.Value.Slots.First().EndTime);
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithNonExistentProvider_ShouldReturnUnavailable()
    {
        // Arrange
        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var nonExistentProviderId = Guid.NewGuid();
        var query = new GetProviderAvailabilityForDateQuery(
            nonExistentProviderId,
            new DateOnly(2025, 7, 21)
        );

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithProviderOnLeave_ShouldReturnUnavailable()
    {
        // Arrange
        var testDate = new DateOnly(2025, 7, 21); // Monday

        // Add leave for the test date
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = testDate.ToDateTime(TimeOnly.MinValue),
            EndDate = testDate.ToDateTime(TimeOnly.MaxValue),
            Reason = "Sick leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var query = new GetProviderAvailabilityForDateQuery(_providerId, testDate);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithProviderNotAvailableOnWeekday_ShouldReturnUnavailable()
    {
        // Arrange
        // Set provider as not available on Monday
        var availability = await _context.Availabilities.FirstAsync(a =>
            a.ProviderId == _providerId && a.DayOfWeek == "Monday"
        );
        availability.IsAvailable = false;
        await _context.SaveChangesAsync();

        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var query = new GetProviderAvailabilityForDateQuery(_providerId, new DateOnly(2025, 7, 21)); // Monday

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithExistingBooking_ShouldApplyBuffer()
    {
        // Arrange
        var testDate = new DateOnly(2025, 7, 21); // Monday

        // Create a confirmed booking
        var booking = new Booking
        {
            Id = Guid.NewGuid(),
            ClientId = Guid.NewGuid(),
            ProviderId = _providerId,
            CategoryId = Guid.NewGuid(),
            WorkingHours = 1,
            TotalAmount = 100,
            PlatformFee = 10,
            ProviderAmount = 90,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var bookingWindow = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = booking.Id,
            Date = testDate,
            StartTime = new TimeOnly(12, 0),
            EndTime = new TimeOnly(13, 0),
            DurationMinutes = 60,
            DailyRate = 100,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Bookings.Add(booking);
        _context.BookingWindows.Add(bookingWindow);
        await _context.SaveChangesAsync();

        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var query = new GetProviderAvailabilityForDateQuery(_providerId, testDate);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Available);
        Assert.Equal(2, result.Value.Slots.Count);

        // First slot: 9:00 AM - 11:30 AM (12:00 PM - 30 min buffer)
        Assert.Equal("09:00", result.Value.Slots[0].StartTime);
        Assert.Equal("11:30", result.Value.Slots[0].EndTime);

        // Second slot: 1:30 PM - 5:00 PM (1:00 PM + 30 min buffer)
        Assert.Equal("13:30", result.Value.Slots[1].StartTime);
        Assert.Equal("17:00", result.Value.Slots[1].EndTime);
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithComplexScenario_ShouldHandleCorrectly()
    {
        // Arrange
        var testDate = new DateOnly(2025, 7, 23); // Wednesday

        // Create availability for Wednesday (8 AM - 6 PM)
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Wednesday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(8, 0),
            EndTime = new TimeOnly(18, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);

        // Add multiple bookings
        var booking1 = CreateBooking(testDate, new TimeOnly(10, 0), new TimeOnly(11, 0));
        var booking2 = CreateBooking(testDate, new TimeOnly(14, 0), new TimeOnly(15, 0));

        _context.Bookings.AddRange(booking1.booking, booking2.booking);
        _context.BookingWindows.AddRange(booking1.window, booking2.window);
        await _context.SaveChangesAsync();

        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var query = new GetProviderAvailabilityForDateQuery(_providerId, testDate);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Available);
        Assert.Equal(3, result.Value.Slots.Count);

        // First slot: 8:00 AM - 9:30 AM (10:00 AM - 30 min buffer)
        Assert.Equal("08:00", result.Value.Slots[0].StartTime);
        Assert.Equal("09:30", result.Value.Slots[0].EndTime);

        // Second slot: 11:30 AM - 1:30 PM (between bookings with buffers)
        Assert.Equal("11:30", result.Value.Slots[1].StartTime);
        Assert.Equal("13:30", result.Value.Slots[1].EndTime);

        // Third slot: 3:30 PM - 6:00 PM (3:00 PM + 30 min buffer)
        Assert.Equal("15:30", result.Value.Slots[2].StartTime);
        Assert.Equal("18:00", result.Value.Slots[2].EndTime);
    }

    // [Fact]
    // public async Task GetProviderAvailabilityForDateQuery_WithPartialLeave_ShouldHandleCorrectly()
    // {
    //     // Arrange
    //     var testDate = new DateOnly(2025, 7, 24); // Thursday

    //     // Create availability for Thursday
    //     var availability = new AvailabilityEntity
    //     {
    //         Id = Guid.NewGuid(),
    //         ProviderId = _providerId,
    //         DayOfWeek = "Thursday",
    //         IsAvailable = true,
    //         CreatedAt = DateTime.UtcNow,
    //         CreatedBy = _userId,
    //     };

    //     var availabilitySlot = new AvailabilitySlot
    //     {
    //         Id = Guid.NewGuid(),
    //         AvailabilityId = availability.Id,
    //         StartTime = new TimeOnly(9, 0),
    //         EndTime = new TimeOnly(17, 0),
    //     };

    //     _context.Availabilities.Add(availability);
    //     _context.AvailabilitySlots.Add(availabilitySlot);

    //     // Add partial day leave (1:00 PM - 3:00 PM)
    //     var leave = new Leave
    //     {
    //         Id = Guid.NewGuid(),
    //         ProviderId = _providerId,
    //         StartDate = testDate.ToDateTime(new TimeOnly(13, 0)),
    //         EndDate = testDate.ToDateTime(new TimeOnly(15, 0)),
    //         Reason = "Doctor appointment",
    //         CreatedAt = DateTime.UtcNow,
    //         CreatedBy = _userId,
    //     };

    //     _context.Leaves.Add(leave);
    //     await _context.SaveChangesAsync();

    //     var handler = new GetProviderAvailabilityForDateQueryHandler(
    //         _context,
    //         NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
    //     );
    //     var query = new GetProviderAvailabilityForDateQuery(_providerId, testDate);

    //     // Act
    //     var result = await handler.Handle(query, CancellationToken.None);

    //     // Assert
    //     Assert.True(result.IsSuccess);
    //     Assert.True(result.Value.Available);
    //     Assert.Equal(2, result.Value.Slots.Count);

    //     // Morning slot: 9:00 AM - 1:00 PM
    //     Assert.Equal("09:00", result.Value.Slots[0].StartTime);
    //     Assert.Equal("13:00", result.Value.Slots[0].EndTime);

    //     // Afternoon slot: 3:00 PM - 5:00 PM
    //     Assert.Equal("15:00", result.Value.Slots[1].StartTime);
    //     Assert.Equal("17:00", result.Value.Slots[1].EndTime);
    // }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task GetProviderAvailabilityForDateQuery_WithDatabaseError_ShouldReturnFailure()
    {
        // Arrange
        await _context.DisposeAsync(); // Dispose context to simulate database error

        var handler = new GetProviderAvailabilityForDateQueryHandler(
            _context,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );
        var query = new GetProviderAvailabilityForDateQuery(_providerId, new DateOnly(2025, 7, 21));

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("internal error", result.Error.Message.ToLower());
    }

    #endregion

    #region Helper Methods

    private (Booking booking, BookingWindow window) CreateBooking(
        DateOnly date,
        TimeOnly startTime,
        TimeOnly endTime
    )
    {
        var bookingId = Guid.NewGuid();

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = Guid.NewGuid(),
            ProviderId = _providerId,
            CategoryId = Guid.NewGuid(),
            WorkingHours = 1,
            TotalAmount = 100,
            PlatformFee = 10,
            ProviderAmount = 90,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var window = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Date = date,
            StartTime = startTime,
            EndTime = endTime,
            DurationMinutes = (int)(endTime - startTime).TotalMinutes,
            DailyRate = 100,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        return (booking, window);
    }

    #endregion
}
