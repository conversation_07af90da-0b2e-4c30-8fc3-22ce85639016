﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Persistence.Configurations
{
    public class ApplicationUserConfiguration : IEntityTypeConfiguration<ApplicationUser>
    {
        public void Configure(EntityTypeBuilder<ApplicationUser> builder)
        {
            builder.Property(u => u.AuthProvider).HasMaxLength(50);

            builder.Property(u => u.AuthProviderId).HasMaxLength(256);

            builder.Property(u => u.EmailVerified).IsRequired().HasDefaultValue(false);

            builder.Property(u => u.IsActive).IsRequired().HasDefaultValue(true);

            // Audit properties
            builder.Property(u => u.CreatedAt).IsRequired();

            builder.Property(u => u.CreatedBy).IsRequired();

            builder.Property(u => u.IsDeleted).IsRequired().HasDefaultValue(false);

            // Relationships
            builder
                .HasOne(u => u.UserProfile)
                .WithOne(p => p.User)
                .HasForeignKey<Domain.Entities.UserProfile>(p => p.ApplicationUserId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
