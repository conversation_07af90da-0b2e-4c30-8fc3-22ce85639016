namespace SuperCareApp.Application.Common.Interfaces.Mediator;

/// <summary>
/// Defines a mediator for handling requests and notifications passed through the application.
/// </summary>
public interface IMediator
{
    /// <summary>
    /// Sends a request to a single handler.
    /// </summary>
    /// <typeparam name="TResponse">Response type.</typeparam>
    /// <param name="request">Request object.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>Response from the request.</returns>
    Task<TResponse> Send<TResponse>(
        IRequest<TResponse> request,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Sends a request to a single handler without expecting a response.
    /// </summary>
    /// <param name="request">Request object.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    Task Send(IRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publishes a notification to multiple handlers.
    /// </summary>
    /// <param name="notification">Notification object.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    Task Publish(INotification notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publishes an async notification to multiple handlers.
    /// </summary>
    /// <param name="notification">Notification object.</param>
    /// <param name="cancellationToken">Optional cancellation token.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    Task Publish(IAsyncNotification notification, CancellationToken cancellationToken = default);
}
