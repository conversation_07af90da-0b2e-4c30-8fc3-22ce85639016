using FluentValidation;
using SuperCareApp.Domain.Common.Results;

namespace super_care_app.Shared.Utility;

public class RequestValidator
{
    public async Task<Result> ValidateAsync<T>(T request, AbstractValidator<T> validator)
        where T : class
    {
        var result = await validator.ValidateAsync(request);

        if (result.IsValid)
            return Result.Success();

        var errorMessages = result
            .Errors.GroupBy(e => e.PropertyName)
            .ToDictionary(g => g.Key, g => g.Select(e => e.ErrorMessage).ToArray());

        return Result.Failure(Error.Validation("Validation failed", errorMessages));
    }
}
