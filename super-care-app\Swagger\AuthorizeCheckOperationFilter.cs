using System.Reflection;
using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Operation filter to add the authorization requirement to endpoints that have the [Authorize] attribute
    /// </summary>
    public class AuthorizeCheckOperationFilter : IOperationFilter
    {
        /// <summary>
        /// Applies the filter to the specified operation using the given context.
        /// </summary>
        /// <param name="operation">The operation to apply the filter to.</param>
        /// <param name="context">The current operation filter context.</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Check if the endpoint has [AllowAnonymous]
            var allowAnonymous = context
                .MethodInfo.GetCustomAttributes(true)
                .OfType<AllowAnonymousAttribute>()
                .Any();
            if (allowAnonymous)
                return;

            // Check if the controller has [AllowAnonymous]
            allowAnonymous =
                context
                    .MethodInfo.DeclaringType?.GetCustomAttributes(true)
                    .OfType<AllowAnonymousAttribute>()
                    .Any() ?? false;
            if (allowAnonymous)
                return;

            // Check if the endpoint or controller has [Authorize]
            var hasAuthorize = context
                .MethodInfo.GetCustomAttributes(true)
                .OfType<AuthorizeAttribute>()
                .Any();
            if (!hasAuthorize)
            {
                hasAuthorize =
                    context
                        .MethodInfo.DeclaringType?.GetCustomAttributes(true)
                        .OfType<AuthorizeAttribute>()
                        .Any() ?? false;
            }

            if (hasAuthorize)
            {
                // Initialize the security requirements if they don't exist
                operation.Security ??= new List<OpenApiSecurityRequirement>();

                // Add Email/Password authentication requirement
                var securityScheme = new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "EmailPassword",
                    },
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                };

                operation.Security.Add(
                    new OpenApiSecurityRequirement { { securityScheme, Array.Empty<string>() } }
                );
            }
        }
    }
}
