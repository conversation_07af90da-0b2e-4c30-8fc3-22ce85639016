using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.UnitOfWork;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Integration tests that verify the complete flow from service to database
/// for Icon and Color field functionality
/// </summary>
public class CareCategoryIntegrationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CareCategoryService _service;

    public CareCategoryIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        var repository = new CareCategoryRepository(_context);
        var unitOfWork = new UnitOfWork.UnitOfWork(_context);
        _service = new CareCategoryService(
            repository,
            _context,
            unitOfWork,
            NullLogger<CareCategoryService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task CompleteWorkflow_CreateUpdateGetDelete_WithIconAndColor_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();

        // 1. Create category with Icon and Color
        var createRequest = new CreateCareCategoryRequest
        {
            Name = "Integration Test Category",
            Description = "Integration test description",
            IsActive = true,
            PlatformFee = 25.50m,
            Icon = "fas fa-integration",
            Color = "#123456",
        };

        var createResult = await _service.CreateCategoryAsync(createRequest, userId);
        Assert.True(createResult.IsSuccess);
        Assert.Equal(createRequest.Icon, createResult.Value.Icon);
        Assert.Equal(createRequest.Color, createResult.Value.Color);

        var categoryId = createResult.Value.Id;

        // 2. Verify in database
        var dbCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == categoryId);
        Assert.NotNull(dbCategory);
        Assert.Equal(createRequest.Icon, dbCategory.Icon);
        Assert.Equal(createRequest.Color, dbCategory.Color);

        // 3. Get by ID and verify Icon/Color are returned
        var getResult = await _service.GetCategoryByIdAsync(categoryId);
        Assert.True(getResult.IsSuccess);
        Assert.Equal(createRequest.Icon, getResult.Value.Icon);
        Assert.Equal(createRequest.Color, getResult.Value.Color);

        // 4. Update Icon and Color
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Integration Category",
            Description = "Updated description",
            Icon = "fas fa-updated",
            Color = "#ABCDEF",
        };

        var updateResult = await _service.UpdateCategoryAsync(categoryId, updateRequest, userId);
        Assert.True(updateResult.IsSuccess);
        Assert.Equal(updateRequest.Icon, updateResult.Value.Icon);
        Assert.Equal(updateRequest.Color, updateResult.Value.Color);

        // 5. Verify update in database
        var updatedDbCategory = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == categoryId
        );
        Assert.NotNull(updatedDbCategory);
        Assert.Equal(updateRequest.Icon, updatedDbCategory.Icon);
        Assert.Equal(updateRequest.Color, updatedDbCategory.Color);

        // 6. Get all categories and verify Icon/Color are included
        var getAllResult = await _service.GetAllCategoriesAsync();
        Assert.True(getAllResult.IsSuccess);
        var category = getAllResult.Value.First(c => c.Id == categoryId);
        Assert.Equal(updateRequest.Icon, category.Icon);
        Assert.Equal(updateRequest.Color, category.Color);

        // 7. Delete category
        var deleteResult = await _service.DeleteCategoryAsync(categoryId, userId);
        Assert.True(deleteResult.IsSuccess);

        // 8. Verify soft delete (category should still exist but marked as deleted)
        var deletedDbCategory = await _context
            .CareCategories.IgnoreQueryFilters()
            .FirstOrDefaultAsync(c => c.Id == categoryId);
        Assert.NotNull(deletedDbCategory);
        Assert.True(deletedDbCategory.IsDeleted);
        // Icon and Color should still be preserved even after soft delete
        Assert.Equal(updateRequest.Icon, deletedDbCategory.Icon);
        Assert.Equal(updateRequest.Color, deletedDbCategory.Color);
    }

    [Fact]
    public async Task ProviderCategoryWorkflow_WithIconAndColor_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        // 1. Create category with Icon and Color
        var createRequest = new CreateCareCategoryRequest
        {
            Name = "Provider Test Category",
            Description = "Provider test description",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = "fas fa-provider-test",
            Color = "#FF6B35",
        };

        var createResult = await _service.CreateCategoryAsync(createRequest, userId);
        Assert.True(createResult.IsSuccess);
        var categoryId = createResult.Value.Id;

        // 2. Create provider-category relationship
        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = categoryId,
            HourlyRate = 75.00m,
            ExperienceYears = 8,
            ProviderSpecificDescription = "Provider specific description for this category",
        };

        _context.CareProviderCategories.Add(providerCategory);
        await _context.SaveChangesAsync();

        // 3. Get categories by provider ID and verify Icon/Color are included
        var providerCategoriesResult = await _service.GetCategoriesByProviderIdAsync(providerId);
        Assert.True(providerCategoriesResult.IsSuccess);
        Assert.Single(providerCategoriesResult.Value);

        var providerCategoryResponse = providerCategoriesResult.Value.First();
        Assert.Equal(createRequest.Icon, providerCategoryResponse.Icon);
        Assert.Equal(createRequest.Color, providerCategoryResponse.Color);
        Assert.Equal(75.00m, providerCategoryResponse.HourlyRate);
        Assert.Equal(8, providerCategoryResponse.ExperienceYears);
        Assert.Equal(
            "Provider specific description for this category",
            providerCategoryResponse.Description
        );
    }

    [Fact]
    public async Task PaginatedResults_WithIconAndColor_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();

        // Create multiple categories with different Icons and Colors
        var categories = new[]
        {
            new CreateCareCategoryRequest
            {
                Name = "Category Alpha",
                IsActive = true,
                PlatformFee = 10.00m,
                Icon = "fas fa-alpha",
                Color = "#FF0000",
            },
            new CreateCareCategoryRequest
            {
                Name = "Category Beta",
                IsActive = true,
                PlatformFee = 15.00m,
                Icon = "fas fa-beta",
                Color = "#00FF00",
            },
            new CreateCareCategoryRequest
            {
                Name = "Category Gamma",
                IsActive = true,
                PlatformFee = 20.00m,
                Icon = "fas fa-gamma",
                Color = "#0000FF",
            },
            new CreateCareCategoryRequest
            {
                Name = "Category Delta",
                IsActive = false,
                PlatformFee = 25.00m,
                Icon = "fas fa-delta",
                Color = "#FFFF00",
            },
        };

        // Create all categories
        var createdCategories = new List<CareCategoryResponse>();
        foreach (var categoryRequest in categories)
        {
            var result = await _service.CreateCategoryAsync(categoryRequest, userId);
            Assert.True(result.IsSuccess);
            createdCategories.Add(result.Value);
        }

        // Test paginated results (page 1, size 2)
        var paginatedResult = await _service.GetPaginatedCategoriesAsync(
            pageNumber: 1,
            pageSize: 2,
            includeInactive: true
        );

        Assert.True(paginatedResult.IsSuccess);
        Assert.Equal(4, paginatedResult.Value.TotalCount);
        Assert.Equal(2, paginatedResult.Value.TotalPages);
        Assert.Equal(2, paginatedResult.Value.Categories.Count);

        // Verify first page contains Icon and Color
        var firstPageCategories = paginatedResult.Value.Categories;
        foreach (var category in firstPageCategories)
        {
            Assert.NotNull(category.Icon);
            Assert.NotNull(category.Color);
            Assert.StartsWith("fas fa-", category.Icon);
            Assert.StartsWith("#", category.Color);
        }

        // Test paginated results (page 2, size 2)
        var secondPageResult = await _service.GetPaginatedCategoriesAsync(
            pageNumber: 2,
            pageSize: 2,
            includeInactive: true
        );

        Assert.True(secondPageResult.IsSuccess);
        Assert.Equal(2, secondPageResult.Value.Categories.Count);

        // Verify second page contains Icon and Color
        var secondPageCategories = secondPageResult.Value.Categories;
        foreach (var category in secondPageCategories)
        {
            Assert.NotNull(category.Icon);
            Assert.NotNull(category.Color);
            Assert.StartsWith("fas fa-", category.Icon);
            Assert.StartsWith("#", category.Color);
        }
    }
}
