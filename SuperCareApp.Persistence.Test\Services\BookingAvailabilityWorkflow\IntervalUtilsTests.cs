using SuperCareApp.Application.Common.Settings;

namespace SuperCareApp.Persistence.Test.Services.BookingAvailabilityWorkflow;

public class IntervalUtilsTests
{
    #region Overlap and Containment Tests

    [Fact]
    public void DoOverlap_WithOverlappingIntervals_ShouldReturnTrue()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(12, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(10, 0), new TimeOnly(13, 0));

        // Act
        var result = IntervalUtils.DoOverlap(interval1, interval2);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void DoOverlap_WithNonOverlappingIntervals_ShouldReturnFalse()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(10, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(11, 0), new TimeOnly(12, 0));

        // Act
        var result = IntervalUtils.DoOverlap(interval1, interval2);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void DoOverlap_WithAdjacentIntervals_ShouldReturnFalse()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(10, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(10, 0), new TimeOnly(11, 0));

        // Act
        var result = IntervalUtils.DoOverlap(interval1, interval2);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void AreAdjacentOrOverlapping_WithAdjacentIntervals_ShouldReturnTrue()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(10, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(10, 0), new TimeOnly(11, 0));

        // Act
        var result = IntervalUtils.AreAdjacentOrOverlapping(interval1, interval2);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void AreAdjacentOrOverlapping_WithOverlappingIntervals_ShouldReturnTrue()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(11, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(10, 0), new TimeOnly(12, 0));

        // Act
        var result = IntervalUtils.AreAdjacentOrOverlapping(interval1, interval2);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void AreAdjacentOrOverlapping_WithSeparateIntervals_ShouldReturnFalse()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(10, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(11, 0), new TimeOnly(12, 0));

        // Act
        var result = IntervalUtils.AreAdjacentOrOverlapping(interval1, interval2);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsContainedWithin_WithContainedInterval_ShouldReturnTrue()
    {
        // Arrange
        var inner = new Interval<TimeOnly>(new TimeOnly(10, 0), new TimeOnly(11, 0));
        var outer = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(12, 0));

        // Act
        var result = IntervalUtils.IsContainedWithin(inner, outer);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsContainedWithin_WithEqualIntervals_ShouldReturnTrue()
    {
        // Arrange
        var interval1 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(12, 0));
        var interval2 = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(12, 0));

        // Act
        var result = IntervalUtils.IsContainedWithin(interval1, interval2);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsContainedWithin_WithNonContainedInterval_ShouldReturnFalse()
    {
        // Arrange
        var inner = new Interval<TimeOnly>(new TimeOnly(8, 0), new TimeOnly(11, 0));
        var outer = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(12, 0));

        // Act
        var result = IntervalUtils.IsContainedWithin(inner, outer);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Merge Tests

    [Fact]
    public void Merge_WithOverlappingIntervals_ShouldMergeCorrectly()
    {
        // Arrange
        var intervals = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(9, 0), new TimeOnly(11, 0)),
            new(new TimeOnly(10, 0), new TimeOnly(12, 0)),
            new(new TimeOnly(14, 0), new TimeOnly(16, 0)),
        };

        // Act
        var result = IntervalUtils.Merge(intervals);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(12, 0), result[0].End);
        Assert.Equal(new TimeOnly(14, 0), result[1].Start);
        Assert.Equal(new TimeOnly(16, 0), result[1].End);
    }

    [Fact]
    public void Merge_WithAdjacentIntervals_ShouldMergeCorrectly()
    {
        // Arrange
        var intervals = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(9, 0), new TimeOnly(10, 0)),
            new(new TimeOnly(10, 0), new TimeOnly(11, 0)),
            new(new TimeOnly(11, 0), new TimeOnly(12, 0)),
        };

        // Act
        var result = IntervalUtils.Merge(intervals);

        // Assert
        Assert.Single(result);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(12, 0), result[0].End);
    }

    [Fact]
    public void Merge_WithNonOverlappingIntervals_ShouldReturnSameIntervals()
    {
        // Arrange
        var intervals = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(9, 0), new TimeOnly(10, 0)),
            new(new TimeOnly(11, 0), new TimeOnly(12, 0)),
            new(new TimeOnly(13, 0), new TimeOnly(14, 0)),
        };

        // Act
        var result = IntervalUtils.Merge(intervals);

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Equal(intervals[0], result[0]);
        Assert.Equal(intervals[1], result[1]);
        Assert.Equal(intervals[2], result[2]);
    }

    [Fact]
    public void Merge_WithEmptyList_ShouldReturnEmptyList()
    {
        // Arrange
        var intervals = new List<Interval<TimeOnly>>();

        // Act
        var result = IntervalUtils.Merge(intervals);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void Merge_WithSingleInterval_ShouldReturnSameInterval()
    {
        // Arrange
        var intervals = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(9, 0), new TimeOnly(12, 0)),
        };

        // Act
        var result = IntervalUtils.Merge(intervals);

        // Assert
        Assert.Single(result);
        Assert.Equal(intervals[0], result[0]);
    }

    [Fact]
    public void Merge_WithUnsortedIntervals_ShouldSortAndMerge()
    {
        // Arrange
        var intervals = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(14, 0), new TimeOnly(16, 0)),
            new(new TimeOnly(9, 0), new TimeOnly(11, 0)),
            new(new TimeOnly(10, 0), new TimeOnly(12, 0)),
        };

        // Act
        var result = IntervalUtils.Merge(intervals);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(12, 0), result[0].End);
        Assert.Equal(new TimeOnly(14, 0), result[1].Start);
        Assert.Equal(new TimeOnly(16, 0), result[1].End);
    }

    #endregion

    #region Subtract Tests

    [Fact]
    public void Subtract_WithNonOverlappingSubtraction_ShouldReturnOriginalInterval()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(18, 0), new TimeOnly(19, 0)),
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Single(result);
        Assert.Equal(source, result[0]);
    }

    [Fact]
    public void Subtract_WithCompleteOverlap_ShouldReturnEmptyList()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(8, 0), new TimeOnly(18, 0)),
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void Subtract_WithPartialOverlapAtStart_ShouldReturnRemainingInterval()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(8, 0), new TimeOnly(12, 0)),
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Single(result);
        Assert.Equal(new TimeOnly(12, 0), result[0].Start);
        Assert.Equal(new TimeOnly(17, 0), result[0].End);
    }

    [Fact]
    public void Subtract_WithPartialOverlapAtEnd_ShouldReturnRemainingInterval()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(14, 0), new TimeOnly(18, 0)),
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Single(result);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(14, 0), result[0].End);
    }

    [Fact]
    public void Subtract_WithMiddleSubtraction_ShouldReturnTwoIntervals()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(12, 0), new TimeOnly(14, 0)),
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(12, 0), result[0].End);
        Assert.Equal(new TimeOnly(14, 0), result[1].Start);
        Assert.Equal(new TimeOnly(17, 0), result[1].End);
    }

    [Fact]
    public void Subtract_WithMultipleSubtractions_ShouldHandleCorrectly()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(10, 0), new TimeOnly(11, 0)),
            new(new TimeOnly(13, 0), new TimeOnly(14, 0)),
            new(new TimeOnly(15, 0), new TimeOnly(16, 0)),
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Equal(4, result.Count);

        // First remaining interval: 9:00 - 10:00
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(10, 0), result[0].End);

        // Second remaining interval: 11:00 - 13:00
        Assert.Equal(new TimeOnly(11, 0), result[1].Start);
        Assert.Equal(new TimeOnly(13, 0), result[1].End);

        // Third remaining interval: 14:00 - 15:00
        Assert.Equal(new TimeOnly(14, 0), result[2].Start);
        Assert.Equal(new TimeOnly(15, 0), result[2].End);

        // Fourth remaining interval: 16:00 - 17:00
        Assert.Equal(new TimeOnly(16, 0), result[3].Start);
        Assert.Equal(new TimeOnly(17, 0), result[3].End);
    }

    [Fact]
    public void Subtract_WithOverlappingSubtractions_ShouldMergeAndSubtract()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(10, 0), new TimeOnly(12, 0)),
            new(new TimeOnly(11, 0), new TimeOnly(14, 0)), // Overlaps with previous
        };

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Equal(2, result.Count);

        // First remaining interval: 9:00 - 10:00
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(10, 0), result[0].End);

        // Second remaining interval: 14:00 - 17:00
        Assert.Equal(new TimeOnly(14, 0), result[1].Start);
        Assert.Equal(new TimeOnly(17, 0), result[1].End);
    }

    [Fact]
    public void Subtract_WithEmptySubtractionList_ShouldReturnOriginalInterval()
    {
        // Arrange
        var source = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));
        var subtractions = new List<Interval<TimeOnly>>();

        // Act
        var result = IntervalUtils.Subtract(source, subtractions);

        // Assert
        Assert.Single(result);
        Assert.Equal(source, result[0]);
    }

    #endregion

    #region Validation Tests

    [Fact]
    public void Interval_WithValidRange_ShouldNotThrow()
    {
        // Arrange & Act
        var interval = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));

        // Assert - No exception should be thrown
        interval.Validate();
    }

    [Fact]
    public void Interval_WithInvalidRange_ShouldThrowException()
    {
        // Arrange
        var interval = new Interval<TimeOnly>(new TimeOnly(17, 0), new TimeOnly(9, 0));

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => interval.Validate());
        Assert.Contains("start of the interval cannot be after its end", exception.Message);
    }

    [Fact]
    public void Interval_WithEqualStartAndEnd_ShouldNotThrow()
    {
        // Arrange
        var interval = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(9, 0));

        // Act & Assert - No exception should be thrown
        interval.Validate();
    }

    #endregion

    #region Real-World Scenario Tests

    [Fact]
    public void BookingAvailabilityScenario_WithBufferTime_ShouldCalculateCorrectly()
    {
        // Arrange - Provider available 9:00 AM - 5:00 PM
        var providerAvailability = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));

        // Existing booking: 12:00 PM - 1:00 PM with 30-minute buffer
        var existingBooking = new Interval<TimeOnly>(new TimeOnly(12, 0), new TimeOnly(13, 0));
        var bufferTime = TimeSpan.FromMinutes(30);

        var bookingWithBuffer = new Interval<TimeOnly>(
            existingBooking.Start.Add(-bufferTime),
            existingBooking.End.Add(bufferTime)
        );

        var blockages = new List<Interval<TimeOnly>> { bookingWithBuffer };

        // Act
        var availableSlots = IntervalUtils.Subtract(providerAvailability, blockages);

        // Assert
        Assert.Equal(2, availableSlots.Count);

        // Morning slot: 9:00 AM - 11:30 AM
        Assert.Equal(new TimeOnly(9, 0), availableSlots[0].Start);
        Assert.Equal(new TimeOnly(11, 30), availableSlots[0].End);

        // Afternoon slot: 1:30 PM - 5:00 PM
        Assert.Equal(new TimeOnly(13, 30), availableSlots[1].Start);
        Assert.Equal(new TimeOnly(17, 0), availableSlots[1].End);
    }

    [Fact]
    public void LeaveManagementScenario_WithPartialDayLeave_ShouldCalculateCorrectly()
    {
        // Arrange - Provider available 9:00 AM - 5:00 PM
        var providerAvailability = new Interval<TimeOnly>(new TimeOnly(9, 0), new TimeOnly(17, 0));

        // Leave period: 1:00 PM - 3:00 PM (doctor appointment)
        var leaveInterval = new Interval<TimeOnly>(new TimeOnly(13, 0), new TimeOnly(15, 0));
        var leaves = new List<Interval<TimeOnly>> { leaveInterval };

        // Act
        var availableSlots = IntervalUtils.Subtract(providerAvailability, leaves);

        // Assert
        Assert.Equal(2, availableSlots.Count);

        // Morning slot: 9:00 AM - 1:00 PM
        Assert.Equal(new TimeOnly(9, 0), availableSlots[0].Start);
        Assert.Equal(new TimeOnly(13, 0), availableSlots[0].End);

        // Afternoon slot: 3:00 PM - 5:00 PM
        Assert.Equal(new TimeOnly(15, 0), availableSlots[1].Start);
        Assert.Equal(new TimeOnly(17, 0), availableSlots[1].End);
    }

    [Fact]
    public void ComplexScenario_WithMultipleBookingsAndLeave_ShouldCalculateCorrectly()
    {
        // Arrange - Provider available 8:00 AM - 6:00 PM
        var providerAvailability = new Interval<TimeOnly>(new TimeOnly(8, 0), new TimeOnly(18, 0));

        // Multiple blockages:
        // 1. Booking: 10:00 AM - 11:00 AM (with 30-min buffer)
        // 2. Leave: 1:00 PM - 2:00 PM
        // 3. Booking: 4:00 PM - 5:00 PM (with 30-min buffer)
        var blockages = new List<Interval<TimeOnly>>
        {
            new(new TimeOnly(9, 30), new TimeOnly(11, 30)), // Booking 1 with buffer
            new(new TimeOnly(13, 0), new TimeOnly(14, 0)), // Leave
            new(new TimeOnly(15, 30), new TimeOnly(17, 30)), // Booking 2 with buffer
        };

        // Act
        var availableSlots = IntervalUtils.Subtract(providerAvailability, blockages);

        // Assert
        Assert.Equal(4, availableSlots.Count);

        // Early morning: 8:00 AM - 9:30 AM
        Assert.Equal(new TimeOnly(8, 0), availableSlots[0].Start);
        Assert.Equal(new TimeOnly(9, 30), availableSlots[0].End);

        // Late morning: 11:30 AM - 1:00 PM
        Assert.Equal(new TimeOnly(11, 30), availableSlots[1].Start);
        Assert.Equal(new TimeOnly(13, 0), availableSlots[1].End);

        // Early afternoon: 2:00 PM - 3:30 PM
        Assert.Equal(new TimeOnly(14, 0), availableSlots[2].Start);
        Assert.Equal(new TimeOnly(15, 30), availableSlots[2].End);

        // Late afternoon: 5:30 PM - 6:00 PM
        Assert.Equal(new TimeOnly(17, 30), availableSlots[3].Start);
        Assert.Equal(new TimeOnly(18, 0), availableSlots[3].End);
    }

    #endregion
}
