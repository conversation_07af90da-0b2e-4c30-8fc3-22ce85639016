﻿using SuperCareApp.Domain.Common;

namespace SuperCareApp.Domain.Entities
{
    public class CareCategory : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public decimal PlatformFee { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }

        // Navigation properties
        public ICollection<CareProviderCategory> CareProviderCategories { get; set; } =
            new List<CareProviderCategory>();
    }
}
