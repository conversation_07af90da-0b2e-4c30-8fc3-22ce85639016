﻿using System.Text.Json;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Application.Shared.Utility;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Example response models for CareCategory API documentation
    /// </summary>
    public static class CareCategoryExamples
    {
        // Reusable JsonSerializerOptions
        private static readonly JsonSerializerOptions _jsonOptions = new() { WriteIndented = true };

        /// <summary>
        /// Gets an example of a successful create care category response
        /// </summary>
        public static string CreateCareCategoryExample()
        {
            var response = new ApiResponseModel<CareCategoryResponse>(
                ApiResponseStatusEnum.Success,
                "Care category created successfully",
                new CareCategoryResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Name = "Elderly Care",
                    Description = "Specialized care for elderly patients",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = null,
                }
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful get care category response
        /// </summary>
        public static string GetCareCategoryExample()
        {
            var response = new ApiResponseModel<CareCategoryResponse>(
                ApiResponseStatusEnum.Success,
                "Care category retrieved successfully",
                new CareCategoryResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Name = "Elderly Care",
                    Description = "Specialized care for elderly patients",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5),
                }
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful get all care categories response
        /// </summary>
        public static string GetAllCategoriesExample()
        {
            var categories = new List<CareCategoryResponse>
            {
                new CareCategoryResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Name = "Elderly Care",
                    Description = "Specialized care for elderly patients",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5),
                },
                new CareCategoryResponse
                {
                    Id = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    Name = "Child Care",
                    Description = "Care for children and infants",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    UpdatedAt = null,
                },
                new CareCategoryResponse
                {
                    Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Name = "Special Needs Care",
                    Description = "Care for individuals with special needs",
                    IsActive = false,
                    CreatedAt = DateTime.UtcNow.AddDays(-10),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2),
                },
            };

            var response = new ApiResponseModel<IEnumerable<CareCategoryResponse>>(
                ApiResponseStatusEnum.Success,
                "Care categories retrieved successfully",
                categories
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example request for creating a care category
        /// </summary>
        public static string CreateCareCategoryRequestExample()
        {
            var request = new CreateCareCategoryRequest
            {
                Name = "Elderly Care",
                Description = "Specialized care for elderly patients",
                IsActive = false,
                HourlyRate = 25.50m,
            };

            return JsonSerializer.Serialize(request, _jsonOptions);
        }

        /// <summary>
        /// Gets an example request for updating a care category
        /// </summary>
        public static string UpdateCareCategoryRequestExample()
        {
            var request = new UpdateCareCategoryRequest
            {
                Name = "Updated Elderly Care",
                Description = "Updated specialized care for elderly patients",
                IsActive = true,
            };

            return JsonSerializer.Serialize(request, _jsonOptions);
        }

        /// <summary>
        /// Gets an example request for bulk updating care categories
        /// </summary>
        public static string BulkUpdateCareCategoriesRequestExample()
        {
            var request = new BulkUpdateCareCategoriesRequest
            {
                Categories = new List<CareCategoryUpdateItem>
                {
                    new CareCategoryUpdateItem
                    {
                        Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                        Name = "Updated Elderly Care",
                        Description = "Enhanced specialized care for elderly patients",
                        PlatformFee = 5.123456m,
                    },
                    new CareCategoryUpdateItem
                    {
                        Id = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                        IsActive = false,
                        HourlyRate = 32.50m,
                    },
                    new CareCategoryUpdateItem
                    {
                        Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                        IsActive = true,
                        PlatformFee = 7.654321m,
                    },
                },
            };

            return JsonSerializer.Serialize(request, _jsonOptions);
        }

        /// <summary>
        /// Gets an example response for bulk updating care categories
        /// </summary>
        public static string BulkUpdateCareCategoriesResponseExample()
        {
            var updatedCategories = new List<CareCategoryResponse>
            {
                new CareCategoryResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Name = "Updated Elderly Care",
                    Description = "Enhanced specialized care for elderly patients",
                    IsActive = true,
                    PlatformFee = 5.00m,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow,
                },
                new CareCategoryResponse
                {
                    Id = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    Name = "Child Care",
                    Description = "Care for children and infants",
                    IsActive = false,
                    PlatformFee = 4.50m,
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    UpdatedAt = DateTime.UtcNow,
                },
                new CareCategoryResponse
                {
                    Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Name = "Special Needs Care",
                    Description = "Care for individuals with special needs",
                    IsActive = true,
                    PlatformFee = 7.50m,
                    CreatedAt = DateTime.UtcNow.AddDays(-10),
                    UpdatedAt = DateTime.UtcNow,
                },
            };

            var response = new ApiResponseModel<IEnumerable<CareCategoryResponse>>(
                ApiResponseStatusEnum.Success,
                "Successfully updated 3 care categories",
                updatedCategories
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }
    }
}
