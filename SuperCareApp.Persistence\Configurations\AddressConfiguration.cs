﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class AddressConfiguration : IEntityTypeConfiguration<Address>
    {
        public void Configure(EntityTypeBuilder<Address> builder)
        {
            builder.HasKey(a => a.Id);

            builder.Property(a => a.StreetAddress).HasMaxLength(256);

            builder.Property(a => a.City).HasMaxLength(100);

            builder.Property(a => a.State).HasMaxLength(100);

            builder.Property(a => a.PostalCode).HasMaxLength(20);

            builder.Property(a => a.Latitude).HasColumnType("decimal(10,7)");

            builder.Property(a => a.Longitude).HasColumnType("decimal(10,7)");
        }
    }
}
