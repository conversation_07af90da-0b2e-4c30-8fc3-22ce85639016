﻿using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Interfaces.Admin
{
    public interface IApprovalService
    {
        Task<Result<IEnumerable<Approval>>> GetPendingApprovalsAsync();
        Task<Result<IEnumerable<Approval>>> GetApprovalsByUserIdAsync(Guid userId);
        Task<Result<IEnumerable<Approval>>> GetApprovalsByTypeAsync(ApprovalType type);
        Task<Result<Approval>> GetApprovalByIdAsync(Guid approvalId);
        Task<Result<Approval>> CreateApprovalAsync(
            Guid userId,
            ApprovalType type,
            string? approvalData = null,
            Guid? relatedEntityId = null
        );
        Task<Result<Approval>> ApproveAsync(Guid approvalId, Guid adminId, string? notes = null);
        Task<Result<Approval>> RejectAsync(
            Guid approvalId,
            Guid adminId,
            string rejectionReason,
            string? notes = null
        );
    }
}
