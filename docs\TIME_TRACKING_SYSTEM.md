# Time Tracking System Documentation

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [Use Cases](#use-cases)
- [Implementation Details](#implementation-details)
- [Security & Authorization](#security--authorization)
- [Error Handling](#error-handling)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## Overview

The Time Tracking System is a comprehensive solution that allows care providers to track their working hours during bookings with real-time location data, pause/resume functionality, and automatic time calculations. The system follows Clean Architecture principles with CQRS pattern implementation.

### Key Features

- **Real-time Time Tracking**: Start, pause, resume, and end tracking sessions
- **Location Tracking**: Store GPS coordinates and location data in JSON format
- **Automatic Calculations**: Calculate total hours, active duration, and pause time
- **Session Management**: Handle multiple sessions per booking with proper validation
- **Audit Trail**: Complete audit logging for all tracking operations
- **Role-based Access**: Secure access control based on user roles and ownership

### Business Value

- **Accurate Billing**: Precise time tracking for fair compensation
- **Transparency**: Clients can view tracking sessions for their bookings
- **Compliance**: Audit trail for regulatory requirements
- **Efficiency**: Automated time calculations reduce manual errors
- **Safety**: Location tracking for provider safety and verification

## Architecture

The system follows Clean Architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
├─────────────────────────────────────────────────────────────┤
│                Application Layer (DTOs/Interfaces)          │
├─────────────────────────────────────────────────────────────┤
│              Persistence Layer (Services/Repositories)      │
├─────────────────────────────────────────────────────────────┤
│                   Domain Layer (Entities/Enums)            │
└─────────────────────────────────────────────────────────────┘
```

### CQRS Implementation

- **Commands**: StartTrackingSession, UpdateTrackingSession, EndTrackingSession, PauseTrackingSession, ResumeTrackingSession
- **Queries**: GetTrackingSession, GetTrackingSessionsByBooking, GetTrackingSessionsByProvider

### Key Components

1. **TrackingSession Entity**: Core domain model with business logic
2. **TrackingSessionService**: Business logic implementation
3. **TrackingSessionsController**: RESTful API endpoints
4. **Command/Query Handlers**: CQRS pattern implementation
5. **Repository Pattern**: Data access abstraction

## Database Schema

### TrackingSessions Table

| Column | Type | Description | Constraints |
|--------|------|-------------|-------------|
| Id | GUID | Primary key | NOT NULL, PK |
| BookingId | GUID | Foreign key to Bookings | NOT NULL, FK |
| ProviderId | GUID | Foreign key to Users | NOT NULL, FK |
| StartTime | TIMESTAMP | Session start time | NOT NULL |
| EndTime | TIMESTAMP | Session end time | NULL, > StartTime |
| Status | VARCHAR(20) | Session status enum | NOT NULL |
| TrackingData | JSONB | Location data | NULL |
| Notes | VARCHAR(1000) | Session notes | NULL |
| TotalHours | DECIMAL(18,2) | Calculated hours | NULL, >= 0 |
| PausedAt | TIMESTAMP | Last pause time | NULL |
| TotalPausedDuration | INTERVAL | Total pause time | NULL |
| CreatedAt | TIMESTAMP | Audit: Created time | NOT NULL |
| UpdatedAt | TIMESTAMP | Audit: Updated time | NULL |
| DeletedAt | TIMESTAMP | Audit: Deleted time | NULL |
| IsDeleted | BOOLEAN | Soft delete flag | NOT NULL, DEFAULT FALSE |

### Indexes

```sql
-- Performance optimization indexes
CREATE INDEX IX_TrackingSessions_BookingId ON TrackingSessions(BookingId);
CREATE INDEX IX_TrackingSessions_ProviderId ON TrackingSessions(ProviderId);
CREATE INDEX IX_TrackingSessions_BookingId_Status ON TrackingSessions(BookingId, Status);
CREATE INDEX IX_TrackingSessions_StartTime ON TrackingSessions(StartTime);
```

### Relationships

- **TrackingSession** → **Booking** (Many-to-One)
- **TrackingSession** → **ApplicationUser** (Many-to-One, Provider)
- **Booking** → **TrackingSession** (One-to-Many)

## API Endpoints

### Base URL
```
/api/v1/tracking-sessions
```

### Authentication
All endpoints require JWT authentication. Role-specific endpoints require appropriate roles.

### Endpoints Overview

| Method | Endpoint | Description | Role Required |
|--------|----------|-------------|---------------|
| POST | `/bookings/{bookingId}/start` | Start tracking session | CareProvider |
| PUT | `/{sessionId}` | Update tracking session | CareProvider |
| POST | `/{sessionId}/end` | End tracking session | CareProvider |
| POST | `/{sessionId}/pause` | Pause tracking session | CareProvider |
| POST | `/{sessionId}/resume` | Resume tracking session | CareProvider |
| GET | `/{sessionId}` | Get tracking session | Authenticated |
| GET | `/bookings/{bookingId}` | Get sessions by booking | Authenticated |
| GET | `/providers/{providerId}` | Get sessions by provider | Authenticated |
| GET | `/my-sessions` | Get current user's sessions | CareProvider |

### Detailed Endpoint Documentation

#### 1. Start Tracking Session

**POST** `/api/v1/tracking-sessions/bookings/{bookingId}/start`

Starts a new tracking session for a booking.

**Authorization**: CareProvider role required

**Request Body**:
```json
{
  "notes": "Starting care session",
  "initialLocationData": {
    "latitude": 51.5074,
    "longitude": -0.1278,
    "timestamp": "2024-01-15T10:00:00Z",
    "accuracy": 5.0,
    "address": "123 Main St, London"
  }
}
```

**Response**:
```json
{
  "apiResponseId": "guid",
  "status": "success",
  "statusCode": 200,
  "message": "Tracking session started successfully",
  "payload": {
    "id": "session-guid",
    "bookingId": "booking-guid",
    "providerId": "provider-guid",
    "startTime": "2024-01-15T10:00:00Z",
    "endTime": null,
    "status": "Active",
    "notes": "Starting care session",
    "totalHours": null,
    "pausedAt": null,
    "totalPausedDuration": null,
    "trackingData": {...},
    "isActive": true,
    "isCompleted": false,
    "duration": null,
    "providerName": "John Doe"
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

**Error Responses**:
- `400 Bad Request`: Invalid booking ID or active session already exists
- `403 Forbidden`: Provider not assigned to this booking
- `404 Not Found`: Booking not found

#### 2. Update Tracking Session

**PUT** `/api/v1/tracking-sessions/{sessionId}`

Updates an active tracking session with new location data or notes.

**Authorization**: CareProvider role required (session owner only)

**Request Body**:
```json
{
  "status": "Active",
  "notes": "Updated notes",
  "locationData": {
    "latitude": 51.5075,
    "longitude": -0.1279,
    "timestamp": "2024-01-15T10:30:00Z",
    "accuracy": 3.0
  }
}
```

**Response**: Same structure as start session response with updated data.

#### 3. End Tracking Session

**POST** `/api/v1/tracking-sessions/{sessionId}/end`

Ends an active tracking session and calculates total hours.

**Authorization**: CareProvider role required (session owner only)

**Request Body**:
```json
{
  "notes": "Session completed successfully",
  "finalLocationData": {
    "latitude": 51.5074,
    "longitude": -0.1278,
    "timestamp": "2024-01-15T14:00:00Z",
    "accuracy": 4.0
  }
}
```

**Response**: Session response with calculated `totalHours` and `status: "Completed"`.

#### 4. Pause Tracking Session

**POST** `/api/v1/tracking-sessions/{sessionId}/pause`

Pauses an active tracking session.

**Authorization**: CareProvider role required (session owner only)

**Response**: Session response with `status: "Paused"` and `pausedAt` timestamp.

#### 5. Resume Tracking Session

**POST** `/api/v1/tracking-sessions/{sessionId}/resume`

Resumes a paused tracking session.

**Authorization**: CareProvider role required (session owner only)

**Response**: Session response with `status: "Active"` and updated `totalPausedDuration`.

#### 6. Get Tracking Session

**GET** `/api/v1/tracking-sessions/{sessionId}`

Retrieves a specific tracking session by ID.

**Authorization**: Authenticated (session owner, booking client, or admin)

**Response**: Complete session details including location data.

#### 7. Get Sessions by Booking

**GET** `/api/v1/tracking-sessions/bookings/{bookingId}`

Retrieves all tracking sessions for a specific booking.

**Authorization**: Authenticated (booking participants or admin)

**Response**:
```json
{
  "apiResponseId": "guid",
  "status": "success",
  "statusCode": 200,
  "message": "Tracking sessions retrieved successfully",
  "payload": [
    {
      "id": "session-guid",
      "bookingId": "booking-guid",
      "startTime": "2024-01-15T10:00:00Z",
      "endTime": "2024-01-15T14:00:00Z",
      "status": "Completed",
      "totalHours": 4.0,
      "duration": "04:00:00",
      "providerName": "John Doe"
    }
  ],
  "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 8. Get Sessions by Provider

**GET** `/api/v1/tracking-sessions/providers/{providerId}`

Retrieves tracking sessions for a specific provider with pagination and filtering.

**Authorization**: Authenticated (provider themselves or admin)

**Query Parameters**:
- `startDate` (optional): Filter by start date (YYYY-MM-DD)
- `endDate` (optional): Filter by end date (YYYY-MM-DD)
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Page size (default: 10, max: 50)

**Response**: Paginated list of session summaries.

#### 9. Get My Sessions

**GET** `/api/v1/tracking-sessions/my-sessions`

Retrieves tracking sessions for the current authenticated provider.

**Authorization**: CareProvider role required

**Query Parameters**: Same as Get Sessions by Provider

**Response**: Paginated list of current user's session summaries.

## Use Cases

### Use Case 1: Standard Care Session Tracking

**Scenario**: A care provider arrives at a client's location and needs to track their working time.

**Flow**:
1. Provider arrives at client location
2. Provider starts tracking session via mobile app
3. System records start time and initial location
4. Provider provides care services
5. Provider ends tracking session when leaving
6. System calculates total hours and stores final location
7. Client can view session details for transparency

**API Calls**:
```bash
# 1. Start session
POST /api/v1/tracking-sessions/bookings/{bookingId}/start
{
  "notes": "Arrived at client location",
  "initialLocationData": {
    "latitude": 51.5074,
    "longitude": -0.1278,
    "timestamp": "2024-01-15T09:00:00Z"
  }
}

# 2. End session
POST /api/v1/tracking-sessions/{sessionId}/end
{
  "notes": "Care session completed",
  "finalLocationData": {
    "latitude": 51.5074,
    "longitude": -0.1278,
    "timestamp": "2024-01-15T13:00:00Z"
  }
}
```

### Use Case 2: Session with Break

**Scenario**: A care provider needs to take a lunch break during a long care session.

**Flow**:
1. Provider starts tracking session
2. Provider pauses session for lunch break
3. System records pause time
4. Provider resumes session after break
5. System calculates pause duration
6. Provider ends session
7. System calculates active working time excluding break

**API Calls**:
```bash
# 1. Start session
POST /api/v1/tracking-sessions/bookings/{bookingId}/start

# 2. Pause for break
POST /api/v1/tracking-sessions/{sessionId}/pause

# 3. Resume after break
POST /api/v1/tracking-sessions/{sessionId}/resume

# 4. End session
POST /api/v1/tracking-sessions/{sessionId}/end
```

### Use Case 3: Location Updates During Session

**Scenario**: A care provider moves between different locations during a care session (e.g., accompanying client to appointments).

**Flow**:
1. Provider starts session at client's home
2. Provider updates location when moving to medical appointment
3. Provider updates location when returning home
4. Provider ends session
5. System maintains complete location history

**API Calls**:
```bash
# 1. Start at home
POST /api/v1/tracking-sessions/bookings/{bookingId}/start
{
  "initialLocationData": {
    "latitude": 51.5074,
    "longitude": -0.1278,
    "address": "Client's Home"
  }
}

# 2. Update location at medical center
PUT /api/v1/tracking-sessions/{sessionId}
{
  "locationData": {
    "latitude": 51.5155,
    "longitude": -0.1426,
    "address": "Medical Center"
  }
}

# 3. Update location back home
PUT /api/v1/tracking-sessions/{sessionId}
{
  "locationData": {
    "latitude": 51.5074,
    "longitude": -0.1278,
    "address": "Client's Home"
  }
}

# 4. End session
POST /api/v1/tracking-sessions/{sessionId}/end
```

### Use Case 4: Client Viewing Session History

**Scenario**: A client wants to review the tracking sessions for their recent bookings.

**Flow**:
1. Client logs into the application
2. Client navigates to booking details
3. System displays all tracking sessions for the booking
4. Client can view session duration, times, and status
5. Client gains transparency into care provider's time

**API Calls**:
```bash
# Get all sessions for a booking
GET /api/v1/tracking-sessions/bookings/{bookingId}
```

### Use Case 5: Provider Reviewing Work History

**Scenario**: A care provider wants to review their work history for timesheet purposes.

**Flow**:
1. Provider logs into the application
2. Provider navigates to work history section
3. Provider filters by date range
4. System displays paginated list of sessions
5. Provider can view total hours worked per day/week/month

**API Calls**:
```bash
# Get provider's sessions with date filter
GET /api/v1/tracking-sessions/my-sessions?startDate=2024-01-01&endDate=2024-01-31&page=1&pageSize=20
```

### Use Case 6: Admin Monitoring

**Scenario**: An administrator needs to monitor tracking sessions for compliance and quality assurance.

**Flow**:
1. Admin logs into admin dashboard
2. Admin searches for specific provider or booking
3. System displays detailed session information
4. Admin can verify location data and time accuracy
5. Admin can identify any irregularities or issues

**API Calls**:
```bash
# Get specific session details
GET /api/v1/tracking-sessions/{sessionId}

# Get all sessions for a provider
GET /api/v1/tracking-sessions/providers/{providerId}?startDate=2024-01-01
```

### Use Case 7: Emergency Session Cancellation

**Scenario**: A care session needs to be cancelled due to an emergency.

**Flow**:
1. Provider starts tracking session normally
2. Emergency situation arises
3. Provider updates session with emergency notes
4. Provider ends session early
5. System records actual time worked
6. Session marked as completed with emergency notes

**API Calls**:
```bash
# Update with emergency notes
PUT /api/v1/tracking-sessions/{sessionId}
{
  "notes": "Session ended early due to client emergency - ambulance called"
}

# End session early
POST /api/v1/tracking-sessions/{sessionId}/end
{
  "notes": "Emergency session termination"
}
```

## Implementation Details

### Domain Layer

#### TrackingSessionStatus Enum
```csharp
public enum TrackingSessionStatus
{
    Active,     // Session is currently running
    Paused,     // Session is temporarily paused
    Completed,  // Session finished normally
    Cancelled   // Session was cancelled
}
```

#### TrackingSession Entity
```csharp
public class TrackingSession : BaseEntity
{
    public Guid BookingId { get; set; }
    public Guid ProviderId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TrackingSessionStatus Status { get; set; }
    public string? TrackingData { get; set; } // JSON
    public string? Notes { get; set; }
    public decimal? TotalHours { get; set; }
    public DateTime? PausedAt { get; set; }
    public TimeSpan? TotalPausedDuration { get; set; }

    // Navigation properties
    public Booking Booking { get; set; }
    public ApplicationUser Provider { get; set; }

    // Computed properties
    public bool IsActive => Status.IsActive();
    public bool IsCompleted => Status == TrackingSessionStatus.Completed;
    public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    public TimeSpan? ActiveDuration => Duration.HasValue && TotalPausedDuration.HasValue 
        ? Duration.Value - TotalPausedDuration.Value : Duration;
}
```

### Application Layer

#### Service Interface
```csharp
public interface ITrackingSessionService
{
    Task<Result<TrackingSessionResponse>> StartTrackingSessionAsync(
        Guid bookingId, Guid providerId, string? notes = null, object? initialLocationData = null);
    
    Task<Result<TrackingSessionResponse>> UpdateTrackingSessionAsync(
        Guid sessionId, Guid providerId, UpdateTrackingSessionRequest request);
    
    Task<Result<TrackingSessionResponse>> EndTrackingSessionAsync(
        Guid sessionId, Guid providerId, string? notes = null, object? finalLocationData = null);
    
    // ... other methods
}
```

#### DTOs
```csharp
public class TrackingSessionResponse
{
    public Guid Id { get; set; }
    public Guid BookingId { get; set; }
    public Guid ProviderId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TrackingSessionStatus Status { get; set; }
    public string? Notes { get; set; }
    public decimal? TotalHours { get; set; }
    public object? TrackingData { get; set; }
    
    // Computed properties
    public bool IsActive { get; set; }
    public bool IsCompleted { get; set; }
    public TimeSpan? Duration { get; set; }
    public TimeSpan? ActiveDuration { get; set; }
}
```

### Persistence Layer

#### Entity Configuration
```csharp
public class TrackingSessionConfiguration : IEntityTypeConfiguration<TrackingSession>
{
    public void Configure(EntityTypeBuilder<TrackingSession> builder)
    {
        builder.ToTable("TrackingSessions");
        
        builder.Property(ts => ts.TrackingData)
            .HasColumnType("jsonb");
            
        builder.Property(ts => ts.TotalHours)
            .HasPrecision(18, 2);
            
        builder.HasCheckConstraint(
            "CK_TrackingSessions_EndTime_After_StartTime",
            "\"EndTime\" IS NULL OR \"EndTime\" > \"StartTime\"");
            
        // ... other configurations
    }
}
```

#### Service Implementation
The `TrackingSessionService` implements all business logic including:
- Session validation and authorization
- Time calculations with pause handling
- Location data management
- Status transitions
- Error handling with Result pattern

### API Layer

#### Controller Implementation
```csharp
[Authorize(Roles = "CareProvider")]
[HttpPost("bookings/{bookingId:guid}/start")]
public async Task<IActionResult> StartTrackingSession(
    [FromRoute] Guid bookingId,
    [FromBody] StartTrackingSessionRequest request)
{
    var providerId = _currentUserService.UserId ?? Guid.Empty;
    var command = new StartTrackingSessionCommand(bookingId, providerId, request.Notes, request.InitialLocationData);
    var result = await _mediator.Send(command);
    
    return result.IsFailure 
        ? ErrorResponseFromError<TrackingSessionResponse>(result.Error)
        : SuccessResponse(result.Value, "Tracking session started successfully");
}
```

## Security & Authorization

### Role-Based Access Control

| Role | Permissions |
|------|-------------|
| **CareProvider** | Start, update, pause, resume, end own sessions; View own sessions |
| **Client** | View sessions for their bookings (read-only) |
| **Admin** | Full access to all sessions; View all provider sessions |

### Resource-Level Security

1. **Session Ownership**: Providers can only manage their own sessions
2. **Booking Access**: Only booking participants can view sessions
3. **Data Isolation**: Users cannot access other users' data
4. **Admin Override**: Admins have full access for monitoring

### Authorization Implementation

```csharp
// Provider authorization check
if (session.ProviderId != providerId)
    return Result.Failure<TrackingSessionResponse>(
        Error.Unauthorized("Provider does not have access to this tracking session"));

// Booking access validation
if (booking.ClientId != userId && booking.ProviderId != userId && !User.IsInRole("Admin"))
    return Result.Failure<IEnumerable<TrackingSessionSummaryResponse>>(
        Error.Unauthorized("Access denied to this booking's tracking sessions"));
```

### Data Protection

1. **Location Data Encryption**: Sensitive location data stored securely
2. **Audit Logging**: All operations logged for compliance
3. **Soft Delete**: Data retention for regulatory requirements
4. **Input Validation**: All inputs validated and sanitized

## Error Handling

### Error Types

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `NotFound` | Session/Booking not found | 404 |
| `Unauthorized` | Access denied | 403 |
| `BadRequest` | Invalid request/business rule violation | 400 |
| `Conflict` | Active session already exists | 409 |
| `Internal` | Server error | 500 |

### Error Response Format

```json
{
  "apiResponseId": "guid",
  "status": "badRequest",
  "statusCode": 400,
  "message": "An active tracking session already exists for this booking",
  "payload": null,
  "timestamp": "2024-01-15T10:00:00Z"
}
```

### Common Error Scenarios

1. **Starting session when one already active**
   - Error: `BadRequest`
   - Message: "An active tracking session already exists for this booking"

2. **Updating completed session**
   - Error: `BadRequest`
   - Message: "Cannot update a completed or cancelled tracking session"

3. **Unauthorized access**
   - Error: `Unauthorized`
   - Message: "Provider does not have access to this tracking session"

4. **Invalid booking**
   - Error: `NotFound`
   - Message: "Booking not found"

## Testing

### Unit Tests

Test coverage should include:

1. **Domain Logic Tests**
   - TrackingSession entity computed properties
   - Status transition validation
   - Time calculation accuracy

2. **Service Tests**
   - Business rule validation
   - Authorization checks
   - Error handling scenarios

3. **Command/Query Handler Tests**
   - CQRS pattern implementation
   - Result pattern usage
   - Dependency injection

### Integration Tests

1. **API Endpoint Tests**
   - HTTP status codes
   - Response format validation
   - Authorization enforcement

2. **Database Tests**
   - Entity configuration
   - Constraint validation
   - Migration scripts

### Test Examples

```csharp
[Test]
public async Task StartTrackingSession_WhenActiveSessionExists_ShouldReturnBadRequest()
{
    // Arrange
    var bookingId = Guid.NewGuid();
    var providerId = Guid.NewGuid();
    
    // Create existing active session
    await CreateActiveSession(bookingId, providerId);
    
    // Act
    var result = await _service.StartTrackingSessionAsync(bookingId, providerId);
    
    // Assert
    Assert.That(result.IsFailure, Is.True);
    Assert.That(result.Error.Code, Is.EqualTo("BadRequest"));
}

[Test]
public async Task EndTrackingSession_ShouldCalculateTotalHours()
{
    // Arrange
    var session = await CreateActiveSession();
    var startTime = DateTime.UtcNow.AddHours(-4);
    var endTime = DateTime.UtcNow;
    
    // Act
    var result = await _service.EndTrackingSessionAsync(session.Id, session.ProviderId);
    
    // Assert
    Assert.That(result.IsSuccess, Is.True);
    Assert.That(result.Value.TotalHours, Is.EqualTo(4.0m).Within(0.1m));
}
```

## Deployment

### Database Migration

1. **Generate Migration**:
   ```bash
   dotnet ef migrations add AddTrackingSessionEnhancements --project SuperCareApp.Persistence --startup-project super-care-app
   ```

2. **Apply Migration**:
   ```bash
   dotnet ef database update --project SuperCareApp.Persistence --startup-project super-care-app
   ```

3. **Production Deployment**:
   ```bash
   # Generate SQL script for production
   dotnet ef migrations script --project SuperCareApp.Persistence --startup-project super-care-app --output migration.sql
   ```

### Configuration

1. **Connection String**: Ensure PostgreSQL connection string is configured
2. **JWT Settings**: Verify authentication configuration
3. **Role Configuration**: Ensure CareProvider role exists
4. **Indexes**: Verify performance indexes are created

### Monitoring

1. **Performance Metrics**:
   - API response times
   - Database query performance
   - Session creation/completion rates

2. **Business Metrics**:
   - Average session duration
   - Pause frequency
   - Location accuracy

3. **Error Monitoring**:
   - Failed session starts
   - Authorization failures
   - Data validation errors

## Troubleshooting

### Common Issues

#### 1. Session Won't Start

**Symptoms**: 400 Bad Request when starting session

**Possible Causes**:
- Active session already exists for booking
- Provider not assigned to booking
- Booking not found or inactive

**Solutions**:
```bash
# Check for existing active sessions
GET /api/v1/tracking-sessions/bookings/{bookingId}

# Verify booking assignment
GET /api/v1/bookings/{bookingId}

# End existing session if needed
POST /api/v1/tracking-sessions/{existingSessionId}/end
```

#### 2. Location Data Not Saving

**Symptoms**: Location data appears null in responses

**Possible Causes**:
- Invalid JSON format in location data
- Database JSONB column issues
- Serialization problems

**Solutions**:
```csharp
// Ensure proper JSON format
{
  "latitude": 51.5074,
  "longitude": -0.1278,
  "timestamp": "2024-01-15T10:00:00Z",
  "accuracy": 5.0
}

// Check database column type
ALTER TABLE "TrackingSessions" ALTER COLUMN "TrackingData" TYPE jsonb;
```

#### 3. Time Calculations Incorrect

**Symptoms**: TotalHours doesn't match expected duration

**Possible Causes**:
- Pause duration not calculated correctly
- Timezone issues
- Concurrent session updates

**Solutions**:
```csharp
// Verify pause calculation logic
var totalDuration = endTime - startTime;
var activeDuration = totalDuration - (totalPausedDuration ?? TimeSpan.Zero);
var totalHours = (decimal)activeDuration.TotalHours;

// Check for timezone consistency
// Ensure all times are in UTC
```

#### 4. Authorization Failures

**Symptoms**: 403 Forbidden responses

**Possible Causes**:
- User not in CareProvider role
- Session ownership mismatch
- JWT token issues

**Solutions**:
```bash
# Verify user roles
GET /api/v1/account/profile

# Check JWT token claims
# Ensure 'CareProvider' role is present

# Verify session ownership
GET /api/v1/tracking-sessions/{sessionId}
```

### Performance Issues

#### 1. Slow Query Performance

**Solutions**:
```sql
-- Ensure indexes exist
CREATE INDEX IF NOT EXISTS IX_TrackingSessions_BookingId ON "TrackingSessions"("BookingId");
CREATE INDEX IF NOT EXISTS IX_TrackingSessions_ProviderId ON "TrackingSessions"("ProviderId");

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM "TrackingSessions" WHERE "BookingId" = 'guid';
```

#### 2. High Memory Usage

**Solutions**:
- Implement pagination for large result sets
- Use projection for summary responses
- Cache frequently accessed data

### Data Integrity Issues

#### 1. Orphaned Sessions

**Detection**:
```sql
-- Find sessions without valid bookings
SELECT ts.* FROM "TrackingSessions" ts
LEFT JOIN "Bookings" b ON ts."BookingId" = b."Id"
WHERE b."Id" IS NULL;
```

**Resolution**:
```sql
-- Clean up orphaned sessions (use with caution)
DELETE FROM "TrackingSessions" 
WHERE "BookingId" NOT IN (SELECT "Id" FROM "Bookings");
```

#### 2. Inconsistent Status

**Detection**:
```sql
-- Find sessions with inconsistent status
SELECT * FROM "TrackingSessions"
WHERE ("Status" = 'Completed' AND "EndTime" IS NULL)
   OR ("Status" = 'Active' AND "EndTime" IS NOT NULL);
```

**Resolution**: Update status based on EndTime presence or fix EndTime values.

### Logging and Diagnostics

#### Enable Detailed Logging

```json
{
  "Logging": {
    "LogLevel": {
      "SuperCareApp.Persistence.Services.TrackingSessions": "Debug",
      "SuperCareApp.Controllers.TrackingSessionsController": "Debug"
    }
  }
}
```

#### Key Log Messages

- Session start/end events
- Authorization failures
- Business rule violations
- Performance warnings

### Support Contacts

For technical issues:
- **Development Team**: <EMAIL>
- **Database Issues**: <EMAIL>
- **Security Concerns**: <EMAIL>

---

*This documentation is maintained by the SuperCare Development Team. Last updated: January 2024*