﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record LogoutCommand(string Token) : ICommand<Result<LogoutResponse>>;

public sealed class LogoutCommandHandler : ICommandHandler<LogoutCommand, Result<LogoutResponse>>
{
    private readonly ICurrentUserService _currentUserService;
    private readonly ApplicationDbContext _dbContext;

    public LogoutCommandHandler(
        ICurrentUserService currentUserService,
        ApplicationDbContext dbContext
    )
    {
        _currentUserService = currentUserService;
        _dbContext = dbContext;
    }

    public async Task<Result<LogoutResponse>> Handle(
        LogoutCommand request,
        CancellationToken cancellationToken
    )
    {
        // Validate the specific access token exists and belongs to the current user
        var userToken = await _dbContext.UserTokens.FirstOrDefaultAsync(
            t =>
                t.Token == request.Token && !t.IsRevoked && !t.IsExpired && t.TokenType == "Access",
            cancellationToken
        );

        if (userToken == null)
        {
            return Result.Failure<LogoutResponse>(Error.Unauthorized("Invalid or expired token"));
        }

        // Revoke all tokens for the same login session
        var tokensToRevoke = await _dbContext
            .UserTokens.Where(t => t.LoginSessionId == userToken.LoginSessionId && !t.IsRevoked)
            .ToListAsync(cancellationToken);

        var now = DateTime.UtcNow;
        foreach (var token in tokensToRevoke)
        {
            token.IsRevoked = true;
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        _currentUserService.Clear();
        return Result.Success(new LogoutResponse("Logout successfully.", userToken.LoginSessionId));
    }
}
