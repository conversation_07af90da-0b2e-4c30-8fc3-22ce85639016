﻿using System.Net.Sockets;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Domain.Common.Resilience
{
    /// <summary>
    /// Provides retry functionality for operations that might fail temporarily
    /// </summary>
    public static class RetryPolicy
    {
        private static readonly Random _random = new Random();

        /// <summary>
        /// Executes an operation with exponential backoff retry logic
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxRetries">Maximum number of retries</param>
        /// <param name="retryableExceptions">Types of exceptions that should trigger a retry</param>
        /// <returns>The result of the operation</returns>
        public static async Task<Result<T>> ExecuteWithRetryAsync<T>(
            Func<Task<Result<T>>> operation,
            int maxRetries = 3,
            IEnumerable<Type>? retryableExceptions = null
        )
        {
            retryableExceptions ??= GetDefaultRetryableExceptions();

            int retryCount = 0;
            while (true)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    if (!ShouldRetry(ex, retryableExceptions) || retryCount >= maxRetries)
                    {
                        return Result.Failure<T>(Error.Internal(ex.Message));
                    }

                    retryCount++;
                    await Task.Delay(CalculateBackoffDelay(retryCount));
                }
            }
        }

        /// <summary>
        /// Executes an operation with exponential backoff retry logic
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxRetries">Maximum number of retries</param>
        /// <param name="retryableExceptions">Types of exceptions that should trigger a retry</param>
        /// <returns>The result of the operation</returns>
        public static async Task<Result> ExecuteWithRetryAsync(
            Func<Task<Result>> operation,
            int maxRetries = 3,
            IEnumerable<Type>? retryableExceptions = null
        )
        {
            retryableExceptions ??= GetDefaultRetryableExceptions();

            int retryCount = 0;
            while (true)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    if (!ShouldRetry(ex, retryableExceptions) || retryCount >= maxRetries)
                    {
                        return Result.Failure(Error.Internal(ex.Message));
                    }

                    retryCount++;
                    await Task.Delay(CalculateBackoffDelay(retryCount));
                }
            }
        }

        /// <summary>
        /// Executes an operation with exponential backoff retry logic
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxRetries">Maximum number of retries</param>
        /// <param name="retryableExceptions">Types of exceptions that should trigger a retry</param>
        /// <returns>The result of the operation</returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            IEnumerable<Type>? retryableExceptions = null
        )
        {
            retryableExceptions ??= GetDefaultRetryableExceptions();

            int retryCount = 0;
            while (true)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    if (!ShouldRetry(ex, retryableExceptions) || retryCount >= maxRetries)
                    {
                        throw;
                    }

                    retryCount++;
                    await Task.Delay(CalculateBackoffDelay(retryCount));
                }
            }
        }

        private static bool ShouldRetry(Exception exception, IEnumerable<Type> retryableExceptions)
        {
            return retryableExceptions.Any(exType =>
                exType.IsInstanceOfType(exception)
                || (
                    exception.InnerException != null
                    && exType.IsInstanceOfType(exception.InnerException)
                )
            );
        }

        private static int CalculateBackoffDelay(int retryCount)
        {
            // Exponential backoff with jitter: 2^retryCount * 100ms + random jitter
            int baseDelay = (int)Math.Pow(2, retryCount) * 100;
            int jitter = _random.Next(0, 100);
            return baseDelay + jitter;
        }

        private static IEnumerable<Type> GetDefaultRetryableExceptions()
        {
            return new List<Type>
            {
                typeof(SocketException),
                typeof(TimeoutException),
                typeof(IOException),
                typeof(HttpRequestException),
            };
        }
    }
}
