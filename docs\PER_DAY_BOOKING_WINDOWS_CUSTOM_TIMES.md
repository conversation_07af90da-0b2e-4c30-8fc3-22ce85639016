# Per-Day Booking Windows & Custom Times Implementation Plan

**Version**: 2.0
**Date**: January 2025
**Status**: Implementation Ready

---

## Overview

The current system applies a single custom time window (`StartTime` and `EndTime` in the `Booking` entity) for all days in a multi-day booking. This implementation introduces per-day booking windows, allowing each day to have its own custom start and end time through a new `BookingWindow` entity approach.

**Current Architecture Analysis:**

- **Domain**: `Booking` entity with single time window
- **Application**: `CreateBookingRequest` with single time slots
- **Persistence**: `BookingService` and `BookingManagementService`
- **API**: `BookingsController` with mediator pattern

**Solution**: Create a `BookingWindow` entity with one-to-many relationship to `Booking`, maintaining backward compatibility while enabling per-day customization.

---

## Implementation Phases

### Phase 1: Domain Layer Changes

**Objective**: Extend the domain to support per-day booking windows with proper validation.

**Files to Create/Modify**:

- **CREATE**: `SuperCareApp.Domain/Entities/BookingWindow.cs`
- **MODIFY**: `SuperCareApp.Domain/Entities/Booking.cs`
- **CREATE**: `SuperCareApp.Domain/Validation/BookingWindowValidators.cs`

**Tasks**:

- **Create BookingWindow entity**: New entity with `BookingId`, `Date`, `StartTime`, `EndTime`, `DailyRate`, `DaySpecialInstructions`, and validation methods
- **Update Booking entity**: Add `BookingWindows` collection, computed properties (`EarliestStartTime`, `LatestEndTime`, `TotalDurationMinutes`), and helper methods
- **Domain Validators**: Comprehensive validation for date continuity, time conflicts, and business rules

**Expected Outcome**: Domain model supports both single-day (backward compatible) and per-day window bookings with robust validation.

---

### Phase 2: Persistence Layer Changes

**Objective**: Ensure database and ORM support for per-day booking windows.

**Files to Create/Modify**:

- **CREATE**: `SuperCareApp.Persistence/Configurations/BookingWindowConfiguration.cs`
- **MODIFY**: `SuperCareApp.Persistence/Configurations/BookingConfiguration.cs`
- **MODIFY**: `SuperCareApp.Persistence/Services/Bookings/BookingService.cs`
- **MODIFY**: `SuperCareApp.Persistence/Services/Bookings/BookingManagementService.cs`

**Tasks**:

- **Entity Framework Configuration**: Configure `BookingWindow` entity with proper relationships, indexes, and constraints
- **BookingService Enhancement**: Add `CreateBookingWithWindowsAsync()` method with multi-day availability validation
- **Database Migration**: Create migration for `BookingWindows` table
- **Backward Compatibility**: Maintain existing `CreateBookingAsync()` method functionality

**Expected Outcome**: Database properly stores and retrieves booking windows with referential integrity and performance optimization.

---

### Phase 3: Application Layer Changes

**Objective**: Implement business logic and request/response models for per-day windows.

**Files to Create/Modify**:

- **CREATE**: `SuperCareApp.Application/Common/Models/Bookings/BookingWindowRequest.cs`
- **CREATE**: `SuperCareApp.Application/Common/Models/Bookings/BookingWindowResponse.cs`
- **MODIFY**: `SuperCareApp.Application/Common/Models/Bookings/CreateBookingRequest.cs`
- **MODIFY**: `SuperCareApp.Application/Common/Models/Bookings/BookingResponse.cs`
- **MODIFY**: `SuperCareApp.Application/Common/Interfaces/Bookings/IBookingService.cs`

**Detailed Changes**:

#### Request Models

```csharp
// BookingWindowRequest.cs
public class BookingWindowRequest {
    public DateOnly Date { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public decimal? DailyRate { get; set; }
    public string? DaySpecialInstructions { get; set; }
}

// CreateBookingRequest.cs
public class CreateBookingRequest {
    // Existing properties
    public Guid ProviderId { get; set; }
    public Guid CategoryId { get; set; }
    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public string? SpecialInstructions { get; set; }

    // Modified properties (made nullable for backward compatibility)
    public TimeOnly? StartTime { get; set; }
    public TimeOnly? EndTime { get; set; }

    // New property for per-day windows
    public List<BookingWindowRequest>? BookingWindows { get; set; }
}
```

#### Response Models

```csharp
// BookingWindowResponse.cs
public class BookingWindowResponse {
    public DateOnly Date { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public int DurationHours { get; set; }
    public decimal? DailyRate { get; set; }
    public string? DaySpecialInstructions { get; set; }
}

// BookingResponse.cs
public class BookingResponse {
    // Existing properties
    public Guid BookingId { get; set; }
    // ... other properties

    // New property
    public List<BookingWindowResponse>? BookingWindows { get; set; }
}
```

#### Service Interface

```csharp
public interface IBookingService {
    // Existing method
    Task<BookingResult> CreateBookingAsync(CreateBookingRequest request);

    // New method for window-based bookings
    Task<BookingResult> CreateBookingWithWindowsAsync(CreateBookingRequest request);
}
```

#### Validation

- Add FluentValidation rules to ensure:
  - `BookingWindows` covers all dates between start and end dates
  - No date overlaps in booking windows
  - Time windows are valid (start < end)
  - Daily rates are positive if provided
  - Backward compatibility: If `BookingWindows` is null, require `StartTime`/`EndTime`

**Expected Outcome**: Application layer processes both single-window and multi-window booking requests with comprehensive validation.

---

### Phase 4: API Layer Changes

**Objective**: Update API endpoints to support per-day booking windows.

**Files to Create/Modify**:

- **MODIFY**: `super-care-app/Controllers/BookingsController.cs`
- **CREATE**: Command/Query handlers for window operations
- **MODIFY**: API documentation and Swagger examples

**Detailed Changes**:

#### Controller Updates

```csharp
[ApiController]
[Route("api/[controller]")]
public class BookingsController : ControllerBase {
    private readonly IMediator _mediator;

    [HttpPost]
    public async Task<IActionResult> CreateBooking(CreateBookingRequest request) {
        // Determine request type based on presence of booking windows
        if (request.BookingWindows != null && request.BookingWindows.Any()) {
            var command = new CreateBookingWithWindowsCommand(request);
            var result = await _mediator.Send(command);
            return Ok(result);
        } else {
            // Handle legacy single-window request
            var command = new CreateBookingCommand(request);
            var result = await _mediator.Send(command);
            return Ok(result);
        }
    }
}
```

#### Command Handlers

```csharp
// CreateBookingWithWindowsCommand.cs
public class CreateBookingWithWindowsCommand : IRequest<BookingResult> {
    public CreateBookingRequest Request { get; }

    public CreateBookingWithWindowsCommand(CreateBookingRequest request) {
        Request = request;
    }
}

// CreateBookingWithWindowsCommandHandler.cs
public class CreateBookingWithWindowsCommandHandler : IRequestHandler<CreateBookingWithWindowsCommand, BookingResult> {
    private readonly IBookingService _bookingService;

    public async Task<BookingResult> Handle(CreateBookingWithWindowsCommand command, CancellationToken cancellationToken) {
        return await _bookingService.CreateBookingWithWindowsAsync(command.Request);
    }
}
```

#### API Versioning Strategy

- Maintain v1 endpoint for backward compatibility
- Introduce new endpoint in v2 for cleaner implementation:
  ```csharp
  [ApiVersion("2.0")]
  [HttpPost("v2")]
  public async Task<IActionResult> CreateBookingV2(CreateBookingRequest request) {
      // V2 implementation
  }
  ```

#### Documentation Updates

- Add Swagger examples for both request formats
- Document response structure differences between legacy and new formats
- Include validation error examples for invalid window configurations

**Expected Outcome**: API consumers can specify per-day custom times while maintaining backward compatibility.

---

### Phase 5: Testing and Quality Assurance

**Objective**: Comprehensive testing for new functionality and regression prevention.

**Files to Create**:

- **Unit Tests**: Domain entity and validator tests
- **Integration Tests**: End-to-end booking creation tests
- **API Tests**: Controller endpoint tests
- **Performance Tests**: Multi-day booking performance validation

**Tasks**:

- **Domain Tests**: Test `BookingWindow` validation, `Booking` computed properties
- **Service Tests**: Test availability validation for multiple windows
- **Controller Tests**: Test API endpoints with various window configurations
- **Backward Compatibility**: Ensure existing functionality remains intact

**Expected Outcome**: 95%+ test coverage with all tests passing and no regressions.

---

## Database Schema Changes

### New Table: BookingWindows

```sql
CREATE TABLE BookingWindows (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    BookingId UNIQUEIDENTIFIER NOT NULL,
    Date DATE NOT NULL,
    StartTime TIME NOT NULL,
    EndTime TIME NOT NULL,
    DailyRate DECIMAL(18,2) NULL,
    DaySpecialInstructions NVARCHAR(1000) NULL,
    DurationMinutes AS DATEDIFF(minute, StartTime, EndTime),
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    UpdatedAt DATETIME2 NULL,
    UpdatedBy UNIQUEIDENTIFIER NULL,
    IsDeleted BIT NOT NULL DEFAULT 0,
    DeletedAt DATETIME2 NULL,
    DeletedBy UNIQUEIDENTIFIER NULL,

    CONSTRAINT FK_BookingWindows_Booking
        FOREIGN KEY (BookingId) REFERENCES Bookings(Id) ON DELETE CASCADE,

    CONSTRAINT UQ_BookingWindows_BookingId_Date
        UNIQUE (BookingId, Date),

    CONSTRAINT CK_BookingWindows_ValidTimes
        CHECK (StartTime < EndTime)
);

CREATE INDEX IX_BookingWindows_Date ON BookingWindows(Date);
CREATE INDEX IX_BookingWindows_BookingId_Date ON BookingWindows(BookingId, Date);
```

### Modified Table: Bookings

```sql
-- Make existing time fields nullable for backward compatibility
ALTER TABLE Bookings ALTER COLUMN StartTime TIME NULL;
ALTER TABLE Bookings ALTER COLUMN EndTime TIME NULL;
```

---

## API Request/Response Examples

### Legacy Single-Window Request (Backward Compatible)

```json
{
  "providerId": "123e4567-e89b-12d3-a456-426614174000",
  "categoryId": "123e4567-e89b-12d3-a456-426614174001",
  "startDate": "2025-01-15",
  "endDate": "2025-01-17",
  "startTime": "09:00",
  "endTime": "17:00",
  "specialInstructions": "Multi-day care needed"
}
```

### New Per-Day Windows Request

```json
{
  "providerId": "123e4567-e89b-12d3-a456-426614174000",
  "categoryId": "123e4567-e89b-12d3-a456-426614174001",
  "startDate": "2025-01-15",
  "endDate": "2025-01-17",
  "bookingWindows": [
    {
      "date": "2025-01-15",
      "startTime": "09:00",
      "endTime": "17:00",
      "dailyRate": 200.0,
      "daySpecialInstructions": "Morning medication required"
    },
    {
      "date": "2025-01-16",
      "startTime": "10:00",
      "endTime": "16:00",
      "dailyRate": 180.0,
      "daySpecialInstructions": "Afternoon therapy session"
    },
    {
      "date": "2025-01-17",
      "startTime": "08:00",
      "endTime": "18:00",
      "dailyRate": 220.0,
      "daySpecialInstructions": "Extended care day"
    }
  ],
  "specialInstructions": "Overall booking instructions"
}
```

### Enhanced Response with Windows

```json
{
  "bookingId": "123e4567-e89b-12d3-a456-426614174002",
  "startDate": "2025-01-15",
  "endDate": "2025-01-17",
  "totalAmount": 600.0,
  "totalDurationHours": 22,
  "bookingWindows": [
    {
      "date": "2025-01-15",
      "startTime": "09:00",
      "endTime": "17:00",
      "durationHours": 8,
      "dailyRate": 200.0,
      "daySpecialInstructions": "Morning medication required"
    },
    {
      "date": "2025-01-16",
      "startTime": "10:00",
      "endTime": "16:00",
      "durationHours": 6,
      "dailyRate": 180.0,
      "daySpecialInstructions": "Afternoon therapy session"
    },
    {
      "date": "2025-01-17",
      "startTime": "08:00",
      "endTime": "18:00",
      "durationHours": 10,
      "dailyRate": 220.0,
      "daySpecialInstructions": "Extended care day"
    }
  ],
  "clients": {
    /* existing client data */
  },
  "careProviders": {
    /* existing provider data */
  }
}
```

---

## Implementation Strategy

### Incremental Development Approach

1. **Phase 1**: Domain layer foundation (entities, validators)
2. **Phase 2**: Persistence layer (configurations, services)
3. **Phase 3**: Application layer (models, interfaces)
4. **Phase 4**: API layer (controllers, endpoints)
5. **Phase 5**: Testing and validation

### Backward Compatibility Strategy

- Existing `StartTime`/`EndTime` fields remain functional
- Legacy API requests automatically converted to single-window format
- Gradual migration path for existing bookings
- Feature flags for controlled rollout

### Validation Strategy

- **Domain Level**: Entity validation and business rules
- **Application Level**: Request validation with FluentValidation
- **Database Level**: Constraints and foreign keys
- **API Level**: Input validation and error handling

---

## Success Criteria

### Functional Requirements

- ✅ Multi-day bookings accept per-day custom time windows
- ✅ Each day validates against provider availability independently
- ✅ Backward compatibility maintained for existing booking flows
- ✅ Clear error messages for invalid window configurations
- ✅ Support for different daily rates and instructions

### Performance Requirements

- ✅ Multi-day booking creation completes within 5 seconds (up to 30 days)
- ✅ Database queries optimized with proper indexing
- ✅ No performance degradation for existing single-day bookings
- ✅ Efficient availability validation across multiple days

### Quality Requirements

- ✅ 95%+ test coverage for new functionality
- ✅ Zero critical bugs in production
- ✅ Comprehensive API documentation
- ✅ Database referential integrity maintained

---

## Risk Mitigation

### High Risk Areas

1. **Data Migration**: Existing bookings with single time windows
   - **Mitigation**: Gradual migration with backward compatibility
2. **Performance Impact**: Multiple availability checks per booking
   - **Mitigation**: Optimized queries and caching strategies
3. **API Breaking Changes**: Modified request/response formats
   - **Mitigation**: API versioning and backward compatibility

### Rollback Strategy

- **Database**: Reversible migrations with data preservation
- **API**: Feature flags for immediate disable
- **Code**: Git branch strategy for quick rollback
- **Data**: Backup strategy before major changes

---

## Next Steps

1. **✅ Domain Layer**: Create `BookingWindow` entity and validators
2. **🔄 Persistence Layer**: Configure EF mappings and update services
3. **⏳ Application Layer**: Create request/response models
4. **⏳ API Layer**: Update controllers and endpoints
5. **⏳ Testing**: Comprehensive test suite
6. **⏳ Documentation**: Update API documentation
7. **⏳ Deployment**: Staged rollout with monitoring

---

## Conclusion

This implementation plan provides a comprehensive approach to introducing per-day booking windows while maintaining system stability and backward compatibility. The modular design ensures that changes can be implemented incrementally with proper testing and validation at each phase.

The solution addresses the core requirement of allowing different time windows for each day in a multi-day booking while preserving the existing functionality for single-day bookings and simple multi-day bookings with uniform time windows.
