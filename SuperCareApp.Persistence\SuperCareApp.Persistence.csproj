﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>SuperCareApp.Persistence</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.2" />
    <PackageReference Include="MailKit" Version="4.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="PeachPDF" Version="0.7.24" />
    <PackageReference Include="Razor.Templating.Core" Version="2.1.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.2" />
    <PackageReference Include="Twilio.AspNet.Core" Version="8.1.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SuperCareApp.Application\SuperCareApp.Application.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Services\UserService.new.cs" />
    <Compile Remove="Services\Identity\IdentityService.new.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Services\Documents\Commands\" />
    <Folder Include="Services\Documents\Queries\" />
    <Folder Include="Services\Shared\Handler\" />
  </ItemGroup>
</Project>
