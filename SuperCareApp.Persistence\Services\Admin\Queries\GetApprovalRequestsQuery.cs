﻿using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin.Queries
{
    public record GetApprovalRequestsQuery(ApprovalRequestListParams Parameters)
        : IRequest<Result<PagedApprovalRequestList>>;

    internal sealed class GetApprovalRequestsQueryHandler
        : IRequestHandler<GetApprovalRequestsQuery, Result<PagedApprovalRequestList>>
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<GetApprovalRequestsQueryHandler> _logger;

        public GetApprovalRequestsQueryHandler(
            ApplicationDbContext context,
            ILogger<GetApprovalRequestsQueryHandler> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Result<PagedApprovalRequestList>> Handle(
            GetApprovalRequestsQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var parameters = request.Parameters;

                // Start with a query for all approvals
                var query = _context
                    .Set<Approval>()
                    .Include(a => a.User)
                    .Include(a => a.Processor)
                    .AsNoTracking()
                    .Where(a => !a.IsDeleted)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrWhiteSpace(parameters.ApprovalType))
                {
                    // Try to parse the string to ApprovalType enum
                    if (
                        Enum.TryParse<ApprovalType>(
                            parameters.ApprovalType,
                            true,
                            out var approvalType
                        )
                    )
                    {
                        query = query.Where(a => a.ApprovalType == approvalType);
                    }
                }

                // Modified logic: Only apply IsApproved filter if explicitly specified
                if (parameters.IsApproved.HasValue)
                {
                    query = query.Where(a => a.IsApproved == parameters.IsApproved.Value);
                }
                // Removed the else block that defaulted to pending requests
                // Now it will fetch all requests (approved and pending) unless IsApproved is explicitly filtered

                if (parameters.UserId.HasValue)
                {
                    query = query.Where(a => a.UserId == parameters.UserId.Value);
                }

                if (!string.IsNullOrWhiteSpace(parameters.SearchTerm))
                {
                    var searchTerm = parameters.SearchTerm.ToLower();
                    query = query.Where(a =>
                        a.User.Email.ToLower().Contains(searchTerm)
                        || a.User.UserName.ToLower().Contains(searchTerm)
                        || a.User.UserProfile.FirstName.ToLower().Contains(searchTerm)
                        || a.User.UserProfile.LastName.ToLower().Contains(searchTerm)
                    );
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(parameters.SortBy))
                {
                    query = parameters.SortBy.ToLower() switch
                    {
                        "createdAt" => parameters.SortDescending
                            ? query.OrderByDescending(a => a.CreatedAt)
                            : query.OrderBy(a => a.CreatedAt),
                        "processedAt" => parameters.SortDescending
                            ? query.OrderByDescending(a => a.ProcessedAt)
                            : query.OrderBy(a => a.ProcessedAt),
                        "email" => parameters.SortDescending
                            ? query.OrderByDescending(a => a.User.Email)
                            : query.OrderBy(a => a.User.Email),
                        "name" => parameters.SortDescending
                            ? query
                                .OrderByDescending(a => a.User.UserProfile.LastName)
                                .ThenByDescending(a => a.User.UserProfile.FirstName)
                            : query
                                .OrderBy(a => a.User.UserProfile.LastName)
                                .ThenBy(a => a.User.UserProfile.FirstName),
                        "approvalType" => parameters.SortDescending
                            ? query.OrderByDescending(a => a.ApprovalType)
                            : query.OrderBy(a => a.ApprovalType),
                        "status" => parameters.SortDescending
                            ? query.OrderByDescending(a => a.IsApproved)
                            : query.OrderBy(a => a.IsApproved),
                        _ => parameters.SortDescending
                            ? query.OrderByDescending(a => a.CreatedAt)
                            : query.OrderBy(a => a.CreatedAt),
                    };
                }
                else
                {
                    // Default sorting by creation date (newest first)
                    query = query.OrderByDescending(a => a.CreatedAt);
                }

                // Get total count for pagination
                var totalCount = await query.CountAsync(cancellationToken);

                // Calculate pagination values
                var totalPages = (int)Math.Ceiling(totalCount / (double)parameters.PageSize);
                var skip = (parameters.PageNumber - 1) * parameters.PageSize;

                // Log the query before pagination
                _logger.LogInformation(
                    "Executing approval query with filters: ApprovalType={ApprovalType}, IsApproved={IsApproved}, UserId={UserId}",
                    parameters.ApprovalType,
                    parameters.IsApproved,
                    parameters.UserId
                );

                // Get the SQL query for debugging (EF Core 5.0+)
                var sql = query.ToQueryString();
                _logger.LogInformation("SQL Query: {Sql}", sql);

                // Apply pagination
                var pagedData = await query
                    .Skip(skip)
                    .Take(parameters.PageSize)
                    .ToListAsync(cancellationToken);

                _logger.LogInformation("Query returned {Count} results", pagedData.Count);

                // Log some details about the results
                foreach (var approval in pagedData.Take(3))
                {
                    _logger.LogInformation(
                        "Approval found: Id={Id}, UserId={UserId}, Type={Type}, IsApproved={IsApproved}",
                        approval.Id,
                        approval.UserId,
                        approval.ApprovalType,
                        approval.IsApproved
                    );
                }

                // Map to response model
                var requests = pagedData.Select(MapToApprovalRequestResponse).ToList();

                // Create paged result
                var result = new PagedApprovalRequestList
                {
                    Requests = requests,
                    PageNumber = parameters.PageNumber,
                    PageSize = parameters.PageSize,
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                };

                return Result.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving approval requests");
                return Result.Failure<PagedApprovalRequestList>(Error.Internal(ex.Message));
            }
        }

        private ApprovalRequestResponse MapToApprovalRequestResponse(Approval approval)
        {
            var user = approval.User;
            var userProfile = user?.UserProfile;

            return new ApprovalRequestResponse
            {
                Id = approval.Id,
                UserId = approval.UserId,
                Email = user?.Email ?? string.Empty,
                Name =
                    userProfile != null
                        ? $"{userProfile.FirstName} {userProfile.LastName}".Trim()
                        : user?.UserName ?? string.Empty,
                ApprovalTypeEnum = approval.ApprovalType,
                ApprovalTypeString = approval.ApprovalType.ToString(),
                ApprovalTypeDescription = approval.ApprovalType.GetDescription(),
                IsApproved = approval.IsApproved,
                RejectionReason = approval.RejectionReason,
                ApprovalData = approval.ApprovalData,
                RelatedEntityId = approval.RelatedEntityId,
                ProcessedBy = approval.ProcessedBy,
                ProcessedByName = approval.Processor?.UserName,
                ProcessedAt = approval.ProcessedAt,
                Notes = approval.Notes,
                CreatedAt = approval.CreatedAt,
            };
        }
    }
}
