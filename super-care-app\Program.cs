using super_care_app.Extensions;
using super_care_app.Shared.Configuration;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Data;
using SuperCareApp.Persistence.Extensions;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

// Register services
builder
    .Services.AddApiServices()
    .AddApplicationServices(configuration)
    .AddPersistenceServices(configuration)
    .AddHealthChecks(configuration)
    .AddApiVersioningConfiguration();

// Build and configure application
var app = builder.Build();

// **************************************************************************
// TODO: DEVELOPMENT ONLY - REMOVE BEFORE DEPLOYING TO PRODUCTION
// **************************************************************************
// This code ensures the database exists and applies migrations on application startup
// It is intended for development purposes only
//
// To disable this behavior before production deployment:
// 1. Remove or comment out the database initialization lines below
// 2. Or set developmentOnly to true and ensure you're not in Development environment
// **************************************************************************

// Apply migrations (will create database and apply all migrations if database doesn't exist)
// This now works properly without conflicts with EnsureCreated
await app.MigrateDatabaseAsync<ApplicationDbContext>(developmentOnly: true);

// Seed the database with initial data
app.SeedDatabase(developmentOnly: true);

// Configure middleware

app.ConfigureMiddleware();

// Configure health checks
app.UseHealthChecks();

app.UseCors(corsPolicyBuilder =>
{
    corsPolicyBuilder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
});

// Start application
app.Run();
