# 📝 Serilog Structured Logging Implementation Plan

## Overview

This document outlines the implementation plan for comprehensive structured logging using Serilog in the SuperCare application. This will provide centralized, searchable, and structured logging with multiple sinks and enrichment capabilities.

## 🎯 Objectives

- **Structured Logging**: Implement structured logging with consistent format
- **Centralized Logging**: Aggregate logs from all application components
- **Log Correlation**: Correlate logs with traces and user sessions
- **Performance Monitoring**: Track application performance through logs
- **Security Auditing**: Comprehensive audit trail for security events
- **Operational Insights**: Provide insights for troubleshooting and monitoring

## 🏗️ Architecture Overview

```
Application → Serilog → Multiple Sinks → Log Aggregation → Analysis & Alerting
                         ↓
                    Console, File, Database, Elasticsearch, Seq
```

## 📋 Implementation Phases

### Phase 1: Serilog Foundation (Week 1-2)

#### 1.1 Package Installation
```xml
<!-- SuperCareApp.API.csproj -->
<PackageReference Include="Serilog" Version="3.0.1" />
<PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
<PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
<PackageReference Include="Serilog.Extensions.Logging" Version="7.0.0" />

<!-- Sinks -->
<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
<PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.3.0" />
<PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
<PackageReference Include="Serilog.Sinks.Seq" Version="5.2.2" />

<!-- Enrichers -->
<PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
<PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
<PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
<PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
<PackageReference Include="Serilog.Enrichers.ClientInfo" Version="1.2.0" />

<!-- Formatters -->
<PackageReference Include="Serilog.Formatting.Compact" Version="1.1.0" />
<PackageReference Include="Serilog.Formatting.Elasticsearch" Version="9.0.3" />
```

#### 1.2 Basic Configuration
```csharp
// Program.cs
public static void Main(string[] args)
{
    // Configure Serilog early
    Log.Logger = new LoggerConfiguration()
        .MinimumLevel.Information()
        .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
        .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
        .MinimumLevel.Override("System", LogEventLevel.Warning)
        .Enrich.FromLogContext()
        .Enrich.WithEnvironmentName()
        .Enrich.WithMachineName()
        .Enrich.WithProcessId()
        .Enrich.WithThreadId()
        .Enrich.WithCorrelationId()
        .WriteTo.Console(new CompactJsonFormatter())
        .WriteTo.File(
            path: "logs/supercare-.log",
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 30,
            formatter: new CompactJsonFormatter())
        .CreateBootstrapLogger();

    try
    {
        Log.Information("Starting SuperCare application");
        
        var builder = WebApplication.CreateBuilder(args);
        
        // Use Serilog for logging
        builder.Host.UseSerilog((context, services, configuration) => configuration
            .ReadFrom.Configuration(context.Configuration)
            .ReadFrom.Services(services)
            .Enrich.FromLogContext()
            .WriteTo.Console(new CompactJsonFormatter())
            .WriteTo.File(
                path: "logs/supercare-.log",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 30,
                formatter: new CompactJsonFormatter())
            .WriteTo.MSSqlServer(
                connectionString: context.Configuration.GetConnectionString("DefaultConnection"),
                sinkOptions: new MSSqlServerSinkOptions
                {
                    TableName = "Logs",
                    AutoCreateSqlTable = true,
                    BatchPostingLimit = 1000,
                    Period = TimeSpan.FromSeconds(10)
                })
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri("http://localhost:9200"))
            {
                IndexFormat = "supercare-logs-{0:yyyy.MM.dd}",
                AutoRegisterTemplate = true,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                BatchPostingLimit = 50,
                Period = TimeSpan.FromSeconds(2)
            }));

        var app = builder.Build();
        
        // Add request logging middleware
        app.UseSerilogRequestLogging(options =>
        {
            options.MessageTemplate = "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";
            options.GetLevel = (httpContext, elapsed, ex) => ex != null
                ? LogEventLevel.Error
                : httpContext.Response.StatusCode > 499
                    ? LogEventLevel.Error
                    : LogEventLevel.Information;
            
            options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
            {
                diagnosticContext.Set("RequestHost", httpContext.Request.Host.Value);
                diagnosticContext.Set("RequestScheme", httpContext.Request.Scheme);
                diagnosticContext.Set("UserAgent", httpContext.Request.Headers["User-Agent"].FirstOrDefault());
                diagnosticContext.Set("ClientIP", httpContext.Connection.RemoteIpAddress?.ToString());
                
                if (httpContext.User.Identity?.IsAuthenticated == true)
                {
                    diagnosticContext.Set("UserId", httpContext.User.FindFirst("sub")?.Value);
                    diagnosticContext.Set("UserEmail", httpContext.User.FindFirst("email")?.Value);
                }
            };
        });
        
        app.Run();
    }
    catch (Exception ex)
    {
        Log.Fatal(ex, "Application terminated unexpectedly");
    }
    finally
    {
        Log.CloseAndFlush();
    }
}
```

#### 1.3 Configuration File Setup
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.MSSqlServer"],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "System": "Warning",
        "Microsoft.EntityFrameworkCore": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/supercare-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
        }
      },
      {
        "Name": "MSSqlServer",
        "Args": {
          "connectionString": "DefaultConnection",
          "sinkOptions": {
            "tableName": "Logs",
            "autoCreateSqlTable": true,
            "batchPostingLimit": 1000,
            "period": "00:00:10"
          }
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithEnvironmentName",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId",
      "WithCorrelationId"
    ],
    "Properties": {
      "Application": "SuperCareApp",
      "Environment": "Development"
    }
  }
}
```

### Phase 2: Structured Logging Patterns (Week 3-4)

#### 2.1 Custom Log Context Enricher
```csharp
public class SuperCareLogContextEnricher : ILogEventEnricher
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ICurrentUserService _currentUserService;

    public SuperCareLogContextEnricher(
        IHttpContextAccessor httpContextAccessor,
        ICurrentUserService currentUserService)
    {
        _httpContextAccessor = httpContextAccessor;
        _currentUserService = currentUserService;
    }

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            // Add correlation ID
            if (httpContext.TraceIdentifier != null)
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("CorrelationId", httpContext.TraceIdentifier));
            }

            // Add user information
            if (_currentUserService.UserId.HasValue)
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("UserId", _currentUserService.UserId.Value));
            }

            // Add request information
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("RequestPath", httpContext.Request.Path.Value));
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("RequestMethod", httpContext.Request.Method));
            
            // Add client information
            var clientIp = httpContext.Connection.RemoteIpAddress?.ToString();
            if (!string.IsNullOrEmpty(clientIp))
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("ClientIP", clientIp));
            }

            var userAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();
            if (!string.IsNullOrEmpty(userAgent))
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("UserAgent", userAgent));
            }
        }

        // Add application context
        logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("Application", "SuperCareApp"));
        logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("Version", Assembly.GetExecutingAssembly().GetName().Version?.ToString()));
    }
}
```

#### 2.2 Service-Level Logging Patterns
```csharp
public class BookingService : IBookingService
{
    private readonly ILogger<BookingService> _logger;
    private readonly IBookingRepository _repository;

    public BookingService(ILogger<BookingService> logger, IBookingRepository repository)
    {
        _logger = logger;
        _repository = repository;
    }

    public async Task<Result<BookingResponse>> CreateBookingAsync(CreateBookingRequest request)
    {
        using var _ = LogContext.PushProperty("Operation", "CreateBooking");
        using var __ = LogContext.PushProperty("BookingRequest", request, true);

        _logger.LogInformation("Starting booking creation for client {ClientId} with provider {ProviderId}",
            request.ClientId, request.ProviderId);

        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Validate request
            var validationResult = await ValidateBookingRequestAsync(request);
            if (validationResult.IsFailure)
            {
                _logger.LogWarning("Booking validation failed: {ValidationErrors}",
                    validationResult.Error.Message);
                return validationResult;
            }

            // Create booking
            var booking = await _repository.CreateAsync(MapToBooking(request));
            
            stopwatch.Stop();
            
            _logger.LogInformation("Booking {BookingId} created successfully in {ElapsedMs}ms",
                booking.Id, stopwatch.ElapsedMilliseconds);

            // Log business event
            _logger.LogInformation("Business Event: Booking Created {@BookingCreated}",
                new
                {
                    BookingId = booking.Id,
                    ClientId = request.ClientId,
                    ProviderId = request.ProviderId,
                    CategoryId = request.CategoryId,
                    Amount = request.TotalAmount,
                    BookingDate = request.BookingDate,
                    CreatedAt = DateTime.UtcNow
                });

            return Result.Success(MapToResponse(booking));
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            _logger.LogError(ex, "Error creating booking for client {ClientId}: {ErrorMessage}",
                request.ClientId, ex.Message);

            // Log error event with context
            _logger.LogError("Business Event: Booking Creation Failed {@BookingCreationFailed}",
                new
                {
                    ClientId = request.ClientId,
                    ProviderId = request.ProviderId,
                    ErrorType = ex.GetType().Name,
                    ErrorMessage = ex.Message,
                    ElapsedMs = stopwatch.ElapsedMilliseconds,
                    FailedAt = DateTime.UtcNow
                });

            return Result.Failure<BookingResponse>(Error.Internal("Failed to create booking"));
        }
    }
}
```

#### 2.3 Security Audit Logging
```csharp
public class SecurityAuditLogger
{
    private readonly ILogger<SecurityAuditLogger> _logger;

    public SecurityAuditLogger(ILogger<SecurityAuditLogger> logger)
    {
        _logger = logger;
    }

    public void LogLoginAttempt(string email, bool success, string ipAddress, string userAgent)
    {
        using var _ = LogContext.PushProperty("EventType", "Authentication");
        using var __ = LogContext.PushProperty("SecurityEvent", "LoginAttempt");

        if (success)
        {
            _logger.LogInformation("Successful login for user {Email} from {IPAddress}",
                email, ipAddress);
        }
        else
        {
            _logger.LogWarning("Failed login attempt for user {Email} from {IPAddress}",
                email, ipAddress);
        }

        _logger.LogInformation("Security Audit: Login Attempt {@LoginAttempt}",
            new
            {
                Email = email,
                Success = success,
                IPAddress = ipAddress,
                UserAgent = userAgent,
                Timestamp = DateTime.UtcNow
            });
    }

    public void LogPasswordChange(Guid userId, bool success, string ipAddress)
    {
        using var _ = LogContext.PushProperty("EventType", "Authentication");
        using var __ = LogContext.PushProperty("SecurityEvent", "PasswordChange");

        _logger.LogInformation("Password change {Status} for user {UserId} from {IPAddress}",
            success ? "successful" : "failed", userId, ipAddress);

        _logger.LogInformation("Security Audit: Password Change {@PasswordChange}",
            new
            {
                UserId = userId,
                Success = success,
                IPAddress = ipAddress,
                Timestamp = DateTime.UtcNow
            });
    }

    public void LogDataAccess(Guid userId, string resourceType, string resourceId, string action)
    {
        using var _ = LogContext.PushProperty("EventType", "DataAccess");
        using var __ = LogContext.PushProperty("SecurityEvent", "ResourceAccess");

        _logger.LogInformation("User {UserId} performed {Action} on {ResourceType} {ResourceId}",
            userId, action, resourceType, resourceId);

        _logger.LogInformation("Security Audit: Data Access {@DataAccess}",
            new
            {
                UserId = userId,
                ResourceType = resourceType,
                ResourceId = resourceId,
                Action = action,
                Timestamp = DateTime.UtcNow
            });
    }
}
```

### Phase 3: Advanced Logging Features (Week 5-6)

#### 3.1 Performance Logging
```csharp
public class PerformanceLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceLoggingMiddleware> _logger;

    public PerformanceLoggingMiddleware(RequestDelegate next, ILogger<PerformanceLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString();

        using var _ = LogContext.PushProperty("RequestId", requestId);
        using var __ = LogContext.PushProperty("RequestPath", context.Request.Path.Value);

        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            var elapsed = stopwatch.ElapsedMilliseconds;

            var logLevel = elapsed > 5000 ? LogEventLevel.Warning :
                          elapsed > 1000 ? LogEventLevel.Information :
                          LogEventLevel.Debug;

            _logger.Write(logLevel, "Request {RequestMethod} {RequestPath} completed in {ElapsedMs}ms with status {StatusCode}",
                context.Request.Method, context.Request.Path, elapsed, context.Response.StatusCode);

            // Log performance metrics
            if (elapsed > 1000) // Log slow requests
            {
                _logger.LogWarning("Performance: Slow Request {@SlowRequest}",
                    new
                    {
                        RequestId = requestId,
                        Method = context.Request.Method,
                        Path = context.Request.Path.Value,
                        ElapsedMs = elapsed,
                        StatusCode = context.Response.StatusCode,
                        UserId = context.User?.FindFirst("sub")?.Value,
                        Timestamp = DateTime.UtcNow
                    });
            }
        }
    }
}
```

#### 3.2 Database Query Logging
```csharp
public class DatabaseQueryLogger : IInterceptor
{
    private readonly ILogger<DatabaseQueryLogger> _logger;

    public DatabaseQueryLogger(ILogger<DatabaseQueryLogger> logger)
    {
        _logger = logger;
    }

    public async ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
        DbCommand command,
        CommandEventData eventData,
        InterceptionResult<DbDataReader> result,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        using var _ = LogContext.PushProperty("QueryType", "Reader");
        using var __ = LogContext.PushProperty("CommandText", command.CommandText);

        _logger.LogDebug("Executing database query: {CommandText}", command.CommandText);

        var queryResult = await base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        
        stopwatch.Stop();
        
        if (stopwatch.ElapsedMilliseconds > 1000) // Log slow queries
        {
            _logger.LogWarning("Performance: Slow Database Query {@SlowQuery}",
                new
                {
                    CommandText = command.CommandText,
                    ElapsedMs = stopwatch.ElapsedMilliseconds,
                    Parameters = command.Parameters.Cast<DbParameter>()
                        .ToDictionary(p => p.ParameterName, p => p.Value),
                    Timestamp = DateTime.UtcNow
                });
        }

        return queryResult;
    }
}
```

## 🔧 Log Analysis and Monitoring

### 1. Elasticsearch Integration
```csharp
// Configure Elasticsearch sink with custom index template
.WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri("http://localhost:9200"))
{
    IndexFormat = "supercare-logs-{0:yyyy.MM.dd}",
    AutoRegisterTemplate = true,
    AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
    TemplateName = "supercare-logs",
    CustomFormatter = new ElasticsearchJsonFormatter(),
    BatchPostingLimit = 50,
    Period = TimeSpan.FromSeconds(2),
    FailureCallback = e => Console.WriteLine($"Unable to submit event {e.MessageTemplate}"),
    EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog |
                      EmitEventFailureHandling.WriteToFailureSink |
                      EmitEventFailureHandling.RaiseCallback,
    FailureSink = new FileSink("logs/elasticsearch-failures-.log", new JsonFormatter(), null)
})
```

### 2. Seq Integration for Development
```csharp
// Add Seq sink for development environment
if (builder.Environment.IsDevelopment())
{
    configuration.WriteTo.Seq("http://localhost:5341");
}
```

### 3. Custom Log Queries and Dashboards
```json
// Elasticsearch query for error analysis
{
  "query": {
    "bool": {
      "must": [
        {"term": {"level": "Error"}},
        {"range": {"@timestamp": {"gte": "now-1h"}}}
      ]
    }
  },
  "aggs": {
    "error_types": {
      "terms": {"field": "fields.ErrorType.keyword"}
    },
    "error_timeline": {
      "date_histogram": {
        "field": "@timestamp",
        "interval": "5m"
      }
    }
  }
}
```

## 🚨 Log-Based Alerting

### 1. Error Rate Monitoring
```csharp
public class LogBasedAlertingService : IHostedService
{
    private readonly IElasticClient _elasticClient;
    private readonly INotificationService _notificationService;
    private readonly Timer _timer;

    public LogBasedAlertingService(IElasticClient elasticClient, INotificationService notificationService)
    {
        _elasticClient = elasticClient;
        _notificationService = notificationService;
        _timer = new Timer(CheckAlerts, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
    }

    private async void CheckAlerts(object state)
    {
        // Check error rate in last 5 minutes
        var errorCount = await _elasticClient.CountAsync<LogEvent>(c => c
            .Index("supercare-logs-*")
            .Query(q => q
                .Bool(b => b
                    .Must(
                        m => m.Term(t => t.Field("level").Value("Error")),
                        m => m.DateRange(dr => dr
                            .Field("@timestamp")
                            .GreaterThanOrEquals(DateTime.UtcNow.AddMinutes(-5))
                        )
                    )
                )
            )
        );

        if (errorCount.Count > 10) // Threshold: 10 errors in 5 minutes
        {
            await _notificationService.SendAlertAsync(
                "High Error Rate Alert",
                $"Detected {errorCount.Count} errors in the last 5 minutes"
            );
        }
    }
}
```

## 📊 Log Retention and Archival

### 1. Automated Log Cleanup
```csharp
public class LogRetentionService : IHostedService
{
    private readonly IConfiguration _configuration;
    private readonly Timer _timer;

    public LogRetentionService(IConfiguration configuration)
    {
        _configuration = configuration;
        _timer = new Timer(CleanupLogs, null, TimeSpan.Zero, TimeSpan.FromDays(1));
    }

    private void CleanupLogs(object state)
    {
        var retentionDays = _configuration.GetValue<int>("Logging:RetentionDays", 30);
        var logDirectory = _configuration.GetValue<string>("Logging:Directory", "logs");
        
        var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
        
        var oldLogFiles = Directory.GetFiles(logDirectory, "*.log")
            .Where(file => File.GetCreationTime(file) < cutoffDate);
            
        foreach (var file in oldLogFiles)
        {
            try
            {
                File.Delete(file);
                Log.Information("Deleted old log file: {FileName}", file);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to delete log file: {FileName}", file);
            }
        }
    }
}
```

## 🧪 Testing Logging

### 1. Log Testing Utilities
```csharp
public class LogTestHelper
{
    public static ILogger<T> CreateTestLogger<T>() where T : class
    {
        return new LoggerConfiguration()
            .WriteTo.TestOutput()
            .CreateLogger()
            .ForContext<T>();
    }

    public static void AssertLogContains(ITestOutputHelper output, LogEventLevel level, string message)
    {
        var logs = output.ToString();
        Assert.Contains($"[{level}]", logs);
        Assert.Contains(message, logs);
    }
}

[Test]
public async Task CreateBooking_ShouldLogSuccessfulCreation()
{
    // Arrange
    var logger = LogTestHelper.CreateTestLogger<BookingService>();
    var service = new BookingService(logger, _mockRepository.Object);
    
    // Act
    var result = await service.CreateBookingAsync(validRequest);
    
    // Assert
    Assert.That(result.IsSuccess, Is.True);
    LogTestHelper.AssertLogContains(_output, LogEventLevel.Information, "Booking created successfully");
}
```

## 📈 Future Enhancements

1. **Machine Learning**: Anomaly detection in log patterns
2. **Real-time Analytics**: Stream processing for real-time insights
3. **Log Correlation**: Advanced correlation with traces and metrics
4. **Automated Remediation**: Trigger automated responses based on log patterns
5. **Compliance Reporting**: Automated compliance reports from audit logs
6. **Log Sampling**: Intelligent sampling for high-volume scenarios

## 📚 Resources

- [Serilog Documentation](https://serilog.net/)
- [Structured Logging Best Practices](https://nblumhardt.com/2016/06/structured-logging-concepts-in-net-series-1/)
- [Elasticsearch Logging Guide](https://www.elastic.co/guide/en/elasticsearch/reference/current/logging.html)
- [Seq Documentation](https://docs.datalust.co/docs)
