﻿using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Address.Queries
{
    public record GetUserAddressesQuery(Guid UserId) : IQuery<Result<List<AddressDto>>>;

    internal sealed class GetUserAddressesQueryHandler
        : IQueryHandler<GetUserAddressesQuery, Result<List<AddressDto>>>
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<GetUserAddressesQueryHandler> _logger;

        public GetUserAddressesQueryHandler(
            IAddressService addressService,
            ILogger<GetUserAddressesQueryHandler> logger
        )
        {
            _addressService = addressService;
            _logger = logger;
        }

        public async Task<Result<List<AddressDto>>> Handle(
            GetUserAddressesQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _addressService.GetAddressesByUserIdAsync(request.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving addresses for user {UserId}",
                    request.UserId
                );
                return Result.Failure<List<AddressDto>>(
                    Error.Internal($"Error retrieving addresses: {ex.Message}")
                );
            }
        }
    }
}
