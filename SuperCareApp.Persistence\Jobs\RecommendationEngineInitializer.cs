﻿using System.Diagnostics;
using Microsoft.Extensions.Hosting;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Jobs;

public class RecommendationEngineInitializer : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RecommendationEngineInitializer> _logger;
    private readonly RecommendationEngineOptions _options;
    private DateTime _lastRefresh = DateTime.MinValue;

    public RecommendationEngineInitializer(
        IServiceProvider serviceProvider,
        ILogger<RecommendationEngineInitializer> logger,
        IOptions<RecommendationEngineOptions> options
    )
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting RecommendationEngineInitializer...");

            // Initial startup delay to allow other services to initialize
            if (_options.StartupDelaySeconds > 0)
            {
                _logger.LogInformation(
                    "Waiting {DelaySeconds} seconds before initialization...",
                    _options.StartupDelaySeconds
                );
                await Task.Delay(TimeSpan.FromSeconds(_options.StartupDelaySeconds), stoppingToken);
            }

            // Perform initial full initialization
            await InitializeRecommendationEngineAsync(stoppingToken);

            // Start periodic refresh
            await StartPeriodicRefreshAsync(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("RecommendationEngineInitializer was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error in RecommendationEngineInitializer");
            throw; // Re-throw to signal service failure
        }
    }

    private async Task InitializeRecommendationEngineAsync(CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Initializing recommendation engine...");

            using var scope = _serviceProvider.CreateScope();
            var recommendationService =
                scope.ServiceProvider.GetRequiredService<IRecommendationService>();

            await recommendationService.InitializeEngineAsync(cancellationToken);

            _lastRefresh = DateTime.UtcNow;
            stopwatch.Stop();

            _logger.LogInformation(
                "Recommendation engine initialized successfully in {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds
            );
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Failed to initialize recommendation engine after {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    private async Task StartPeriodicRefreshAsync(CancellationToken stoppingToken)
    {
        var refreshInterval = TimeSpan.FromMinutes(_options.RefreshIntervalMinutes);
        _logger.LogInformation(
            "Starting periodic refresh with interval of {RefreshInterval}",
            refreshInterval
        );

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(refreshInterval, stoppingToken);

                if (stoppingToken.IsCancellationRequested)
                    break;

                await RefreshProviderDataAsync(stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error during periodic refresh, will retry in {RefreshInterval}",
                    refreshInterval
                );
                // Continue the loop to retry
            }
        }
    }

    private async Task RefreshProviderDataAsync(CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting periodic provider data refresh...");

            using var scope = _serviceProvider.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            var recommendationService =
                scope.ServiceProvider.GetRequiredService<IRecommendationService>();

            // Get providers that have been updated since last refresh
            var updatedProviders = await GetUpdatedProvidersAsync(
                unitOfWork,
                _lastRefresh,
                cancellationToken
            );

            if (updatedProviders.Any())
            {
                _logger.LogInformation(
                    "Found {Count} updated providers to refresh",
                    updatedProviders.Count
                );

                // Process in batches to avoid memory issues
                var batches = updatedProviders.Chunk(_options.BatchSize);

                foreach (var batch in batches)
                {
                    await ProcessProviderBatchAsync(
                        recommendationService,
                        batch,
                        cancellationToken
                    );
                }
            }
            else
            {
                _logger.LogDebug("No providers updated since last refresh");
            }

            _lastRefresh = DateTime.UtcNow;
            stopwatch.Stop();

            _logger.LogInformation(
                "Provider data refresh completed in {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds
            );
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Failed to refresh provider data after {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds
            );
            throw;
        }
    }

    private async Task<List<CareProviderProfile>> GetUpdatedProvidersAsync(
        IUnitOfWork unitOfWork,
        DateTime since,
        CancellationToken cancellationToken
    )
    {
        var repository = unitOfWork.Repository<CareProviderProfile>();

        // This assumes your entities have UpdatedAt or ModifiedAt fields
        // Adjust the query based on your actual entity structure
        var result = await repository.GetAllAsync(cancellationToken); // You might want to add a method to filter by date

        if (!result.IsSuccess)
        {
            _logger.LogWarning("Failed to retrieve providers for refresh: {Error}", result.Error);
            return [];
        }

        // Filter by update time if your entity supports it
        // This is a placeholder - adjust based on your actual entity properties
        return result
            .Value.Where(p =>
                p.UpdatedAt == null || p.UpdatedAt > since || since == DateTime.MinValue // Include all on first run
            )
            .ToList();
    }

    private async Task ProcessProviderBatchAsync(
        IRecommendationService recommendationService,
        IEnumerable<CareProviderProfile> providers,
        CancellationToken cancellationToken
    )
    {
        var tasks = providers.Select(async provider =>
        {
            try
            {
                await recommendationService.RefreshProviderDataAsync(provider);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to refresh provider {ProviderId}", provider.Id);
            }
        });

        await Task.WhenAll(tasks);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping RecommendationEngineInitializer...");

        await base.StopAsync(cancellationToken);

        _logger.LogInformation("RecommendationEngineInitializer stopped");
    }

    /// <inheritdoc />
    public override void Dispose()
    {
        _logger.LogInformation("Disposing RecommendationEngineInitializer");
        base.Dispose();
    }
}

public class RecommendationEngineOptions
{
    public const string SectionName = "RecommendationEngine";

    /// <summary>
    /// How often to refresh provider data (in minutes)
    /// </summary>
    public int RefreshIntervalMinutes { get; set; } = 60;

    /// <summary>
    /// Delay before starting the background service (in seconds)
    /// </summary>
    public int StartupDelaySeconds { get; set; } = 30;

    /// <summary>
    /// Whether to perform full refresh on startup
    /// </summary>
    public bool PerformFullRefreshOnStartup { get; set; } = true;

    /// <summary>
    /// Maximum number of providers to process in a single batch
    /// </summary>
    public int BatchSize { get; set; } = 100;
}
