﻿namespace SuperCareApp.Application.Common.Models.Bookings;

public class InMemoryPayment
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid ProviderId { get; set; }
    public Guid BookingId { get; set; }
    public string PaymentMethodId { get; set; }
    public decimal Amount { get; set; }
    public string Status { get; set; } = "Pending";
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
