﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.TrackingSessions.Commands;

public sealed record PauseTrackingSessionCommand(Guid SessionId, Guid ProviderId)
    : ICommand<Result>;

internal sealed class PauseTrackingSessionHandler(ApplicationDbContext db, TimeProvider time)
    : ICommandHandler<PauseTrackingSessionCommand, Result>
{
    private readonly ApplicationDbContext _db = db;
    private readonly TimeProvider _time = time;

    public async Task<Result> Handle(PauseTrackingSessionCommand req, CancellationToken ct)
    {
        try
        {
            var session = await _db.TrackingSessions.SingleOrDefaultAsync(
                s => s.Id == req.SessionId && s.ProviderId == req.ProviderId,
                ct
            );

            if (session is null)
                return Result.Failure(Error.NotFound("Session not found"));

            if (session.Status != TrackingSessionStatus.Running)
                return Result.Failure(Error.BadRequest("Session not running"));

            session.Status = TrackingSessionStatus.Paused;
            session.PausedAt = _time.GetUtcNow();

            await _db.SaveChangesAsync(ct);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(Error.BadRequest(ex.Message + "" + ex.StackTrace));
        }
    }
}
