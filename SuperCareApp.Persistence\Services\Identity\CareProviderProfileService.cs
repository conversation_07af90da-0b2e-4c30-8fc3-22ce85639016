﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Services.Identity;

public class CareProviderProfileService : ICareProviderProfileService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CareProviderProfileService> _logger;

    public CareProviderProfileService(
        ApplicationDbContext context,
        ILogger<CareProviderProfileService> logger
    )
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Result<Guid>> CreateAsync(CareProviderProfile profile)
    {
        // Ensure the ApplicationUser exists
        var userExists = await _context.Users.AsNoTracking().AnyAsync(u => u.Id == profile.UserId);

        if (!userExists)
        {
            return Result<Guid>.Failure(Error.NotFound("User not found"));
        }

        await _context.CareProviderProfiles.AddAsync(profile);
        await _context.SaveChangesAsync();

        return profile.Id;
    }

    public async Task<Result<CareProviderProfile?>> GetByUserIdAsync(Guid userId)
    {
        var result = await _context
            .CareProviderProfiles.AsNoTracking()
            .Include(cp => cp.User)
            .FirstOrDefaultAsync(p =>
                p.UserId == userId
                && !p.IsDeleted
                && p.VerificationStatus != Domain.Enums.VerificationStatus.Pending
            );

        if (result == null)
        {
            return Result<CareProviderProfile?>.Failure(Error.NotFound("Profile not found"));
        }
        return result;
    }

    public async Task<Result<CareProviderProfile?>> GetByUserIdNoFilterAsync(Guid userId)
    {
        var result = await _context
            .CareProviderProfiles.AsNoTracking()
            .Include(cp => cp.User)
            .FirstOrDefaultAsync(p => p.UserId == userId && !p.IsDeleted);

        if (result == null)
        {
            return Result<CareProviderProfile?>.Failure(Error.NotFound("Profile not found"));
        }
        return result;
    }

    public async Task<Result<Guid>> UpdateAsync(Guid userId, CareProviderProfile profile)
    {
        // Find the existing profile by ID to avoid tracking conflicts
        var existingProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
            p.Id == profile.Id && !p.IsDeleted
        );

        if (existingProfile == null)
        {
            return Result<Guid>.Failure(Error.NotFound("Care provider profile not found"));
        }

        // Update properties manually
        existingProfile.Bio = profile.Bio;
        existingProfile.YearsExperience = profile.YearsExperience;
        existingProfile.HourlyRate = profile.HourlyRate;
        existingProfile.ProvidesOvernight = profile.ProvidesOvernight;
        existingProfile.ProvidesLiveIn = profile.ProvidesLiveIn;
        existingProfile.Qualifications = profile.Qualifications;
        existingProfile.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return existingProfile.Id;
    }

    public async Task<Result> DeleteAsync(Guid userId)
    {
        try
        {
            var profile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
                p.UserId == userId && !p.IsDeleted
            );

            if (profile == null)
            {
                return Result.Failure(Error.NotFound("Care provider profile not found"));
            }

            // Soft delete
            profile.IsDeleted = true;
            profile.DeletedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation(
                "Care provider profile for user {UserId} has been soft deleted",
                userId
            );
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting care provider profile for user {UserId}", userId);
            return Result.Failure(Error.Internal($"Error deleting profile: {ex.Message}"));
        }
    }
}
