﻿using SuperCareApp.Application.Shared.Utility;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    /// <summary>
    /// Paged list of a client's bookings
    /// </summary>
    public class PagedBookingList
    {
        public List<BookingResponse> Bookings { get; set; } = new List<BookingResponse>();
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// Converts this paged list to a PaginationMetadata object
        /// </summary>
        public PaginationMetadata ToMetadata()
        {
            return new PaginationMetadata(PageNumber, TotalPages, TotalCount, PageSize);
        }
    }
}
