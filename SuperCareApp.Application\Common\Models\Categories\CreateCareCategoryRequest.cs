﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Categories
{
    /// <summary>
    /// Request model for creating a care category
    /// </summary>
    public class CreateCareCategoryRequest
    {
        public string Name { get; init; } = string.Empty;
        public string? Description { get; init; }
        public bool IsActive { get; init; } = false;
        public decimal HourlyRate { get; init; } = 0;
        public decimal PlatformFee { get; set; } = 0;
        public string? Icon { get; init; }
        public string? Color { get; init; }
    }
}
