﻿using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Address.Queries
{
    public record GetAddressQuery(Guid AddressId) : IQuery<Result<AddressDto>>;

    internal sealed class GetAddressQueryHandler
        : IQueryHandler<GetAddressQuery, Result<AddressDto>>
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<GetAddressQueryHandler> _logger;

        public GetAddressQueryHandler(
            IAddressService addressService,
            ILogger<GetAddressQueryHandler> logger
        )
        {
            _addressService = addressService;
            _logger = logger;
        }

        public async Task<Result<AddressDto>> Handle(
            GetAddressQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _addressService.GetAddressByIdAsync(request.AddressId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving address {AddressId}", request.AddressId);
                return Result.Failure<AddressDto>(
                    Error.Internal($"Error retrieving address: {ex.Message}")
                );
            }
        }
    }
}
