using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace SuperCareApp.Persistence.Extensions
{
    /// <summary>
    /// Extension methods specifically designed for staging database initialization
    /// Handles the three staging requirements:
    /// 1. If DB doesn't exist: create and seed data
    /// 2. If DB exists and there are migration changes: apply them
    /// 3. If DB exists and migrations are applied: do nothing
    /// </summary>
    public static class StagingDatabaseExtensions
    {
        /// <summary>
        /// Initializes the database for staging environment with seeding support
        /// </summary>
        /// <param name="host">The host to initialize the database for</param>
        /// <param name="seedDataAsync">Optional function to seed initial data when database is created</param>
        /// <param name="environmentCheck">Whether to check if running in staging/production environment</param>
        /// <returns>The host</returns>
        public static async Task<IHost> InitializeStagingDatabaseAsync<TContext>(
            this IHost host,
            Func<TContext, IServiceProvider, Task>? seedDataAsync = null,
            bool environmentCheck = true
        )
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var environment = services.GetRequiredService<IHostEnvironment>();

                // Skip if in development and environmentCheck is true
                if (environmentCheck && environment.IsDevelopment())
                {
                    logger.LogInformation(
                        "Skipping staging database initialization in development environment"
                    );
                    return host;
                }

                var context = services.GetService<TContext>();
                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}. Skipping staging database initialization.",
                        typeof(TContext).Name
                    );
                    return host;
                }

                logger.LogInformation(
                    "Starting staging database initialization for context {DbContextName}",
                    typeof(TContext).Name
                );

                // Check if database exists
                var canConnect = await context.Database.CanConnectAsync();

                if (!canConnect)
                {
                    // Requirement 1: Database doesn't exist - create and seed
                    logger.LogInformation(
                        "Database doesn't exist. Creating database and applying all migrations."
                    );

                    // This creates the database and applies all migrations
                    await context.Database.MigrateAsync();

                    logger.LogInformation(
                        "Database created and all migrations applied successfully."
                    );

                    // Seed initial data if provided
                    if (seedDataAsync != null)
                    {
                        logger.LogInformation("Seeding initial data...");
                        await seedDataAsync(context, services);
                        logger.LogInformation("Initial data seeded successfully.");
                    }
                }
                else
                {
                    // Database exists - check for pending migrations
                    var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                    var pendingList = pendingMigrations.ToList();

                    if (pendingList.Count != 0)
                    {
                        // Requirement 2: Database exists with pending migrations - apply them
                        logger.LogInformation(
                            "Found {Count} pending migrations: {Migrations}",
                            pendingList.Count,
                            string.Join(", ", pendingList)
                        );

                        await context.Database.MigrateAsync();

                        logger.LogInformation(
                            "Successfully applied {Count} migrations for context {DbContextName}",
                            pendingList.Count,
                            typeof(TContext).Name
                        );
                    }
                    else
                    {
                        // Requirement 3: Database exists and is up to date - do nothing
                        logger.LogInformation(
                            "Database is up to date. No migrations needed for context {DbContextName}",
                            typeof(TContext).Name
                        );
                    }
                }

                logger.LogInformation(
                    "Staging database initialization completed successfully for context {DbContextName}",
                    typeof(TContext).Name
                );
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(
                    ex,
                    "An error occurred during staging database initialization for context {DbContextName}",
                    typeof(TContext).Name
                );

                // Re-throw for staging to make issues visible
                throw;
            }

            return host;
        }

        /// <summary>
        /// Synchronous version of staging database initialization
        /// </summary>
        /// <param name="host">The host to initialize the database for</param>
        /// <param name="seedDataAsync">Optional function to seed initial data when database is created</param>
        /// <param name="environmentCheck">Whether to check if running in staging/production environment</param>
        /// <returns>The host</returns>
        public static IHost InitializeStagingDatabase<TContext>(
            this IHost host,
            Func<TContext, IServiceProvider, Task>? seedDataAsync = null,
            bool environmentCheck = true
        )
            where TContext : DbContext
        {
            return host.InitializeStagingDatabaseAsync<TContext>(seedDataAsync, environmentCheck)
                .GetAwaiter()
                .GetResult();
        }

        /// <summary>
        /// Gets detailed information about the database state for staging diagnostics
        /// </summary>
        /// <param name="host">The host</param>
        /// <returns>The host</returns>
        public static async Task<IHost> StagingDatabaseInfoAsync<TContext>(this IHost host)
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var context = services.GetService<TContext>();

                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}",
                        typeof(TContext).Name
                    );
                    return host;
                }

                logger.LogInformation(
                    "=== Staging Database Information for {ContextType} ===",
                    typeof(TContext).Name
                );

                var canConnect = await context.Database.CanConnectAsync();
                logger.LogInformation("Can connect to database: {CanConnect}", canConnect);

                if (canConnect)
                {
                    var appliedMigrations = await context.Database.GetAppliedMigrationsAsync();
                    var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                    var appliedList = appliedMigrations.ToList();
                    var pendingList = pendingMigrations.ToList();

                    logger.LogInformation(
                        "Applied migrations ({Count}): {Migrations}",
                        appliedList.Count,
                        appliedList.Count != 0 ? string.Join(", ", appliedList) : "None"
                    );

                    logger.LogInformation(
                        "Pending migrations ({Count}): {Migrations}",
                        pendingList.Count,
                        pendingList.Count != 0 ? string.Join(", ", pendingList) : "None"
                    );

                    // Additional staging-specific info
                    logger.LogInformation(
                        "Database state: {State}",
                        pendingList.Count != 0 ? "Needs Migration" : "Up to Date"
                    );
                }
                else
                {
                    logger.LogInformation("Database state: Does Not Exist");
                }

                logger.LogInformation("=== End Staging Database Information ===");
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(ex, "Error getting staging database information");
            }

            return host;
        }
    }
}
