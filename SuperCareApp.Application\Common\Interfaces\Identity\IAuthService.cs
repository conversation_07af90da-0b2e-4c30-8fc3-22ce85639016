using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.Otp;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Application.Common.Interfaces.Identity;

public interface IAuthService
{
    Task<Result<AuthResponse>> AuthenticateAsync(string email, string password);
    Task<Result<OtpResponse>> GenerateOtpAsync(string email, string actionType);
    Task<Result<Guid>> RegisterAsync(
        string email,
        string password,
        string? phoneNumber,
        bool isCareProvider
    );
    Task<Result<ApplicationUser>> VerifyOtpAsync(string email, string otp);
    Task<Result<string>> VerifyOtpForgetPasswordAsync(string email, string otp);
    Task<Result> ResetPasswordAsync(string email, string password, string resetToken);
    Task<Result<CreateUserProfileResponse>> CreateUserProfileAsync(
        string email,
        string? firstName,
        string? lastName,
        string? phoneNumber
    );
    Task<Result<UpdateuserProfileResponse>> UpdateUserProfileAsync(
        string email,
        UpdateUserProfileRequest request
    );
    Task<Result> AddToRoleAsync(string userId, string role);
}
