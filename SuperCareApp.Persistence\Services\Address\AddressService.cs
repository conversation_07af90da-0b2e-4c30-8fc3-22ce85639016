﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Services.Address
{
    public class AddressService : IAddressService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AddressService> _logger;

        public AddressService(ApplicationDbContext context, ILogger<AddressService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Result<Guid>> CreateAddressAsync(
            Guid userId,
            CreateAddressRequest request
        )
        {
            try
            {
                // Verify user exists
                var userExists = await _context
                    .Users.AsNoTracking()
                    .AnyAsync(u => u.Id == userId && !u.IsDeleted);

                if (!userExists)
                {
                    return Result<Guid>.Failure(Error.NotFound("User not found"));
                }

                // Create new address
                var address = new Domain.Entities.Address
                {
                    StreetAddress = request.StreetAddress,
                    City = request.City,
                    State = request.State,
                    PostalCode = request.PostalCode,
                    Latitude = request.Latitude,
                    Longitude = request.Longitude,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                };

                await _context.Addresses.AddAsync(address);

                // If this is set as primary, unset any existing primary addresses
                if (request.IsPrimary)
                {
                    var existingPrimaryAddresses = await _context
                        .UserAddresses.Where(ua => ua.UserId == userId && ua.IsPrimary)
                        .ToListAsync();

                    foreach (var existingPrimary in existingPrimaryAddresses)
                    {
                        existingPrimary.IsPrimary = false;
                    }
                }

                // Create user-address relationship
                var userAddress = new UserAddress
                {
                    UserId = userId,
                    AddressId = address.Id,
                    IsPrimary = request.IsPrimary,
                    Label = request.Label,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                };

                await _context.UserAddresses.AddAsync(userAddress);
                await _context.SaveChangesAsync();

                return Result<Guid>.Success(address.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating address for user {UserId}", userId);
                return Result<Guid>.Failure(
                    Error.Internal($"Error creating address: {ex.Message}")
                );
            }
        }

        public async Task<Result<AddressDto>> GetAddressByIdAsync(Guid addressId)
        {
            try
            {
                var addressWithUserAddress = await _context
                    .Addresses.AsNoTracking()
                    .Include(a => a.UserAddresses)
                    .FirstOrDefaultAsync(a => a.Id == addressId && !a.IsDeleted);

                if (addressWithUserAddress == null)
                {
                    return Result<AddressDto>.Failure(Error.NotFound("Address not found"));
                }

                var userAddress = addressWithUserAddress.UserAddresses.FirstOrDefault();

                var addressDto = new AddressDto
                {
                    Id = addressWithUserAddress.Id,
                    StreetAddress = addressWithUserAddress.StreetAddress,
                    City = addressWithUserAddress.City,
                    State = addressWithUserAddress.State,
                    PostalCode = addressWithUserAddress.PostalCode,
                    Latitude = addressWithUserAddress.Latitude,
                    Longitude = addressWithUserAddress.Longitude,
                    IsPrimary = userAddress?.IsPrimary ?? false,
                    Label = userAddress?.Label,
                };

                return Result<AddressDto>.Success(addressDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving address {AddressId}", addressId);
                return Result<AddressDto>.Failure(
                    Error.Internal($"Error retrieving address: {ex.Message}")
                );
            }
        }

        public async Task<Result<List<AddressDto>>> GetAddressesByUserIdAsync(Guid userId)
        {
            try
            {
                var userAddresses = await _context
                    .UserAddresses.AsNoTracking()
                    .Include(ua => ua.Address)
                    .Where(ua => ua.UserId == userId && !ua.IsDeleted && !ua.Address.IsDeleted)
                    .ToListAsync();

                var addressDtos = userAddresses
                    .Select(ua => new AddressDto
                    {
                        Id = ua.Address.Id,
                        StreetAddress = ua.Address.StreetAddress,
                        City = ua.Address.City,
                        State = ua.Address.State,
                        PostalCode = ua.Address.PostalCode,
                        Latitude = ua.Address.Latitude,
                        Longitude = ua.Address.Longitude,
                        IsPrimary = ua.IsPrimary,
                        Label = ua.Label,
                    })
                    .ToList();

                return Result<List<AddressDto>>.Success(addressDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving addresses for user {UserId}", userId);
                return Result<List<AddressDto>>.Failure(
                    Error.Internal($"Error retrieving addresses: {ex.Message}")
                );
            }
        }

        public async Task<Result<Guid>> UpdateAddressAsync(
            Guid userId,
            UpdateAddressRequest request
        )
        {
            try
            {
                // Verify user has access to this address
                var userAddress = await _context
                    .UserAddresses.Include(ua => ua.Address)
                    .FirstOrDefaultAsync(ua =>
                        ua.UserId == userId && ua.AddressId == request.AddressId && !ua.IsDeleted
                    );

                if (userAddress == null)
                {
                    return Result<Guid>.Failure(
                        Error.NotFound("Address not found or user does not have access")
                    );
                }

                var address = userAddress.Address;

                // Update address properties if provided
                if (!string.IsNullOrWhiteSpace(request.StreetAddress))
                    address.StreetAddress = request.StreetAddress;

                if (!string.IsNullOrWhiteSpace(request.City))
                    address.City = request.City;

                if (!string.IsNullOrWhiteSpace(request.State))
                    address.State = request.State;

                if (!string.IsNullOrWhiteSpace(request.PostalCode))
                    address.PostalCode = request.PostalCode;

                if (request.Latitude.HasValue)
                    address.Latitude = request.Latitude;

                if (request.Longitude.HasValue)
                    address.Longitude = request.Longitude;

                address.UpdatedAt = DateTime.UtcNow;

                // Update user address properties if provided
                if (request.Label != null)
                    userAddress.Label = request.Label;

                // Handle primary address change
                if (request.IsPrimary.HasValue && request.IsPrimary.Value && !userAddress.IsPrimary)
                {
                    // Unset any existing primary addresses
                    var existingPrimaryAddresses = await _context
                        .UserAddresses.Where(ua =>
                            ua.UserId == userId && ua.IsPrimary && ua.Id != userAddress.Id
                        )
                        .ToListAsync();

                    foreach (var existingPrimary in existingPrimaryAddresses)
                    {
                        existingPrimary.IsPrimary = false;
                    }

                    userAddress.IsPrimary = true;
                }

                userAddress.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Result<Guid>.Success(address.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating address {AddressId} for user {UserId}",
                    request.AddressId,
                    userId
                );
                return Result<Guid>.Failure(
                    Error.Internal($"Error updating address: {ex.Message}")
                );
            }
        }

        public async Task<Result<bool>> DeleteAddressAsync(Guid userId, Guid addressId)
        {
            try
            {
                // Verify user has access to this address
                var userAddress = await _context
                    .UserAddresses.Include(ua => ua.Address)
                    .FirstOrDefaultAsync(ua =>
                        ua.UserId == userId && ua.AddressId == addressId && !ua.IsDeleted
                    );

                if (userAddress == null)
                {
                    return Result<bool>.Failure(
                        Error.NotFound("Address not found or user does not have access")
                    );
                }

                // Soft delete the user-address relationship
                userAddress.IsDeleted = true;
                userAddress.DeletedAt = DateTime.UtcNow;

                // Check if this address is used by other users
                var otherUsersUsingAddress = await _context.UserAddresses.AnyAsync(ua =>
                    ua.AddressId == addressId && ua.UserId != userId && !ua.IsDeleted
                );

                // If no other users are using this address, soft delete it too
                if (!otherUsersUsingAddress)
                {
                    userAddress.Address.IsDeleted = true;
                    userAddress.Address.DeletedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error deleting address {AddressId} for user {UserId}",
                    addressId,
                    userId
                );
                return Result<bool>.Failure(
                    Error.Internal($"Error deleting address: {ex.Message}")
                );
            }
        }

        public async Task<Result<Guid>> SetPrimaryAddressAsync(Guid userId, Guid addressId)
        {
            try
            {
                // Verify user has access to this address
                var userAddress = await _context.UserAddresses.FirstOrDefaultAsync(ua =>
                    ua.UserId == userId && ua.AddressId == addressId && !ua.IsDeleted
                );

                if (userAddress == null)
                {
                    return Result<Guid>.Failure(
                        Error.NotFound("Address not found or user does not have access")
                    );
                }

                // If already primary, nothing to do
                if (userAddress.IsPrimary)
                {
                    return Result<Guid>.Success(addressId);
                }

                // Unset any existing primary addresses
                var existingPrimaryAddresses = await _context
                    .UserAddresses.Where(ua => ua.UserId == userId && ua.IsPrimary)
                    .ToListAsync();

                foreach (var existingPrimary in existingPrimaryAddresses)
                {
                    existingPrimary.IsPrimary = false;
                    existingPrimary.UpdatedAt = DateTime.UtcNow;
                }

                // Set this address as primary
                userAddress.IsPrimary = true;
                userAddress.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Result<Guid>.Success(addressId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error setting primary address {AddressId} for user {UserId}",
                    addressId,
                    userId
                );
                return Result<Guid>.Failure(
                    Error.Internal($"Error setting primary address: {ex.Message}")
                );
            }
        }
    }
}
