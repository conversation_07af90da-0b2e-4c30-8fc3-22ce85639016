﻿using System.ComponentModel.DataAnnotations.Schema;
using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class Booking : BaseEntity
    {
        public Guid ClientId { get; set; }
        public Guid ProviderId { get; set; }
        public Guid CategoryId { get; set; }
        public int? WorkingHours { get; set; }
        public string? SpecialInstructions { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PlatformFee { get; set; }
        public decimal ProviderAmount { get; set; }

        // Navigation properties
        public ApplicationUser Client { get; set; } = null!;
        public CareProviderProfile Provider { get; set; } = null!;
        public CareCategory Category { get; set; } = null!;
        public BookingStatus? Status { get; set; }

        [NotMapped]
        public ICollection<BookingStatus> StatusHistory { get; set; } = new List<BookingStatus>();

        [NotMapped]
        public BookingStatus? LatestStatus =>
            StatusHistory.OrderByDescending(s => s.CreatedAt).FirstOrDefault();
        public ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public ICollection<Review> Reviews { get; set; } = new List<Review>();
        public ICollection<BookingWindow> BookingWindows { get; set; } = new List<BookingWindow>();
    }
}
