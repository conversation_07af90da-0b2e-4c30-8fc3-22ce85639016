using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

/// <summary>
/// Query to get provider availability template showing all configured slots regardless of bookings.
/// isAvailable is based only on leave periods and manual availability overrides.
/// </summary>
/// <param name="ProviderId">The provider ID</param>
/// <param name="CheckDate">Optional date to check for leave periods (defaults to current date)</param>
public record GetProviderAvailabilityTemplateQuery(Guid ProviderId, DateTime? CheckDate = null)
    : IQuery<Result<AvailabilityTemplateResponse>>;

/// <summary>
/// Handler for GetProviderAvailabilityTemplateQuery
/// </summary>
public class GetProviderAvailabilityTemplateQueryHandler
    : IQueryHandler<GetProviderAvailabilityTemplateQuery, Result<AvailabilityTemplateResponse>>
{
    private readonly IAvailabilityService _availabilityService;

    public GetProviderAvailabilityTemplateQueryHandler(IAvailabilityService availabilityService)
    {
        _availabilityService = availabilityService;
    }

    public async Task<Result<AvailabilityTemplateResponse>> Handle(
        GetProviderAvailabilityTemplateQuery request,
        CancellationToken cancellationToken
    )
    {
        return await _availabilityService.GetProviderAvailabilityTemplateAsync(
            request.ProviderId,
            request.CheckDate
        );
    }
}
