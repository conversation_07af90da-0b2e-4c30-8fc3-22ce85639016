using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using SuperCareApp.Application.Common.Interfaces.Admin;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Application.Shared.Utility;
using Swashbuckle.AspNetCore.Annotations;

namespace super_care_app.Controllers.Admin
{
    /// <summary>
    /// Admin dashboard controller for statistics and analytics
    /// </summary>
    [Authorize(Roles = "Admin")]
    [SwaggerTag("Admin Dashboard - Statistics and analytics for administrators")]
    public class DashboardController : BaseController
    {
        private readonly IDashboardStatisticsService _dashboardStatisticsService;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(
            IDashboardStatisticsService dashboardStatisticsService,
            ILogger<DashboardController> logger
        )
        {
            _dashboardStatisticsService = dashboardStatisticsService;
            _logger = logger;
        }

        /// <summary>
        /// Gets comprehensive dashboard statistics
        /// </summary>
        /// <param name="request">Statistics request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Complete dashboard statistics</returns>
        [HttpPost(ApiRoutes.Admin.GetDashboardStatistics)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<DashboardStatisticsResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Get dashboard statistics",
            Description = "Retrieves comprehensive dashboard statistics including user analytics, booking analytics, financial analytics, and notification analytics.",
            OperationId = "Admin_GetDashboardStatistics",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> GetDashboardStatistics(
            [FromBody] DashboardStatisticsRequest request,
            CancellationToken cancellationToken = default
        )
        {
            _logger.LogInformation(
                "Admin {UserId} requested dashboard statistics",
                User.Identity?.Name
            );

            var result = await _dashboardStatisticsService.GetDashboardStatisticsAsync(
                request,
                cancellationToken
            );

            return FromResult(result, "Dashboard statistics retrieved successfully");
        }

        /// <summary>
        /// Gets system overview statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics (optional)</param>
        /// <param name="endDate">End date for statistics (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>System overview statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetSystemOverview)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<SystemOverviewStatistics>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Get system overview",
            Description = "Retrieves high-level system overview statistics including user counts, booking counts, and revenue.",
            OperationId = "Admin_GetSystemOverview",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> GetSystemOverview(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            CancellationToken cancellationToken = default
        )
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            _logger.LogInformation(
                "Admin {UserId} requested system overview for {StartDate} to {EndDate}",
                User.Identity?.Name,
                start,
                end
            );

            var result = await _dashboardStatisticsService.GetSystemOverviewAsync(
                start,
                end,
                cancellationToken
            );

            return FromResult(result, "System overview retrieved successfully");
        }

        /// <summary>
        /// Gets user analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics (optional)</param>
        /// <param name="endDate">End date for statistics (optional)</param>
        /// <param name="includeDetails">Include detailed breakdowns (optional, default: true)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>User analytics statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetUserAnalytics)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<UserAnalyticsStatistics>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status500InternalServerError,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Get user analytics",
            Description = "Retrieves detailed user analytics including registrations, activity, and demographics.",
            OperationId = "Admin_GetUserAnalytics",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> GetUserAnalytics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] bool includeDetails = true,
            CancellationToken cancellationToken = default
        )
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            _logger.LogInformation(
                "Admin {UserId} requested user analytics for {StartDate} to {EndDate}",
                User.Identity?.Name,
                start,
                end
            );

            var result = await _dashboardStatisticsService.GetUserAnalyticsAsync(
                start,
                end,
                includeDetails,
                cancellationToken
            );

            return FromResult(result, "User analytics retrieved successfully");
        }

        /// <summary>
        /// Gets booking analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics (optional)</param>
        /// <param name="endDate">End date for statistics (optional)</param>
        /// <param name="includeDetails">Include detailed breakdowns (optional, default: true)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Booking analytics statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetBookingAnalytics)]
        [SwaggerOperation(
            Summary = "Get booking analytics",
            Description = "Retrieves detailed booking analytics including booking trends, completion rates, and provider performance."
        )]
        [SwaggerResponse(
            200,
            "Booking analytics retrieved successfully",
            typeof(ApiResponseModel<BookingAnalyticsStatistics>)
        )]
        [SwaggerResponse(401, "Unauthorized access", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(
            403,
            "Forbidden - Admin access required",
            typeof(ApiResponseModel<object>)
        )]
        [SwaggerResponse(500, "Internal server error", typeof(ApiResponseModel<object>))]
        public async Task<
            ActionResult<ApiResponseModel<BookingAnalyticsStatistics>>
        > GetBookingAnalytics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] bool includeDetails = true,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                _logger.LogInformation(
                    "Admin {UserId} requested booking analytics for {StartDate} to {EndDate}",
                    User.Identity?.Name,
                    start,
                    end
                );

                var result = await _dashboardStatisticsService.GetBookingAnalyticsAsync(
                    start,
                    end,
                    includeDetails,
                    cancellationToken
                );

                if (result.IsFailure)
                {
                    _logger.LogWarning(
                        "Failed to get booking analytics: {Error}",
                        result.Error.Message
                    );
                    return BadRequest(
                        new ApiResponseModel<object>(
                            ApiResponseStatusEnum.Error,
                            result.Error.Message,
                            null
                        )
                    );
                }

                return Ok(
                    new ApiResponseModel<BookingAnalyticsStatistics>(
                        ApiResponseStatusEnum.Success,
                        "Booking analytics retrieved successfully",
                        result.Value
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting booking analytics");
                return StatusCode(
                    500,
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.Error,
                        "An error occurred while retrieving booking analytics",
                        null
                    )
                );
            }
        }

        /// <summary>
        /// Gets financial analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics (optional)</param>
        /// <param name="endDate">End date for statistics (optional)</param>
        /// <param name="includeDetails">Include detailed breakdowns (optional, default: true)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Financial analytics statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetFinancialAnalytics)]
        [SwaggerOperation(
            Summary = "Get financial analytics",
            Description = "Retrieves detailed financial analytics including revenue trends, payment methods, and top earners."
        )]
        [SwaggerResponse(
            200,
            "Financial analytics retrieved successfully",
            typeof(ApiResponseModel<FinancialAnalyticsStatistics>)
        )]
        [SwaggerResponse(401, "Unauthorized access", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(
            403,
            "Forbidden - Admin access required",
            typeof(ApiResponseModel<object>)
        )]
        [SwaggerResponse(500, "Internal server error", typeof(ApiResponseModel<object>))]
        public async Task<
            ActionResult<ApiResponseModel<FinancialAnalyticsStatistics>>
        > GetFinancialAnalytics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] bool includeDetails = true,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                _logger.LogInformation(
                    "Admin {UserId} requested financial analytics for {StartDate} to {EndDate}",
                    User.Identity?.Name,
                    start,
                    end
                );

                var result = await _dashboardStatisticsService.GetFinancialAnalyticsAsync(
                    start,
                    end,
                    includeDetails,
                    cancellationToken
                );

                if (result.IsFailure)
                {
                    _logger.LogWarning(
                        "Failed to get financial analytics: {Error}",
                        result.Error.Message
                    );
                    return BadRequest(
                        new ApiResponseModel<object>(
                            ApiResponseStatusEnum.Error,
                            result.Error.Message,
                            null
                        )
                    );
                }

                return Ok(
                    new ApiResponseModel<FinancialAnalyticsStatistics>(
                        ApiResponseStatusEnum.Success,
                        "Financial analytics retrieved successfully",
                        result.Value
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting financial analytics");
                return StatusCode(
                    500,
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.Error,
                        "An error occurred while retrieving financial analytics",
                        null
                    )
                );
            }
        }

        /// <summary>
        /// Gets notification analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics (optional)</param>
        /// <param name="endDate">End date for statistics (optional)</param>
        /// <param name="includeRealTimeStats">Include real-time WebSocket statistics (optional, default: true)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Notification analytics statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetNotificationAnalytics)]
        [SwaggerOperation(
            Summary = "Get notification analytics",
            Description = "Retrieves detailed notification analytics including delivery rates, channels, and WebSocket statistics."
        )]
        [SwaggerResponse(
            200,
            "Notification analytics retrieved successfully",
            typeof(ApiResponseModel<NotificationAnalyticsStatistics>)
        )]
        [SwaggerResponse(401, "Unauthorized access", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(
            403,
            "Forbidden - Admin access required",
            typeof(ApiResponseModel<object>)
        )]
        [SwaggerResponse(500, "Internal server error", typeof(ApiResponseModel<object>))]
        public async Task<
            ActionResult<ApiResponseModel<NotificationAnalyticsStatistics>>
        > GetNotificationAnalytics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] bool includeRealTimeStats = true,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                _logger.LogInformation(
                    "Admin {UserId} requested notification analytics for {StartDate} to {EndDate}",
                    User.Identity?.Name,
                    start,
                    end
                );

                var result = await _dashboardStatisticsService.GetNotificationAnalyticsAsync(
                    start,
                    end,
                    includeRealTimeStats,
                    cancellationToken
                );

                if (result.IsFailure)
                {
                    _logger.LogWarning(
                        "Failed to get notification analytics: {Error}",
                        result.Error.Message
                    );
                    return BadRequest(
                        new ApiResponseModel<object>(
                            ApiResponseStatusEnum.Error,
                            result.Error.Message,
                            null
                        )
                    );
                }

                return Ok(
                    new ApiResponseModel<NotificationAnalyticsStatistics>(
                        ApiResponseStatusEnum.Success,
                        "Notification analytics retrieved successfully",
                        result.Value
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification analytics");
                return StatusCode(
                    500,
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.Error,
                        "An error occurred while retrieving notification analytics",
                        null
                    )
                );
            }
        }

        /// <summary>
        /// Gets real-time WebSocket statistics
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>WebSocket statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetWebSocketStatistics)]
        [SwaggerOperation(
            Summary = "Get WebSocket statistics",
            Description = "Retrieves real-time WebSocket connection and messaging statistics."
        )]
        [SwaggerResponse(
            200,
            "WebSocket statistics retrieved successfully",
            typeof(ApiResponseModel<WebSocketStatistics>)
        )]
        [SwaggerResponse(401, "Unauthorized access", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(
            403,
            "Forbidden - Admin access required",
            typeof(ApiResponseModel<object>)
        )]
        [SwaggerResponse(500, "Internal server error", typeof(ApiResponseModel<object>))]
        public async Task<
            ActionResult<ApiResponseModel<WebSocketStatistics>>
        > GetWebSocketStatistics(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation(
                    "Admin {UserId} requested WebSocket statistics",
                    User.Identity?.Name
                );

                var result = await _dashboardStatisticsService.GetWebSocketStatisticsAsync(
                    cancellationToken
                );

                if (result.IsFailure)
                {
                    _logger.LogWarning(
                        "Failed to get WebSocket statistics: {Error}",
                        result.Error.Message
                    );
                    return BadRequest(
                        new ApiResponseModel<object>(
                            ApiResponseStatusEnum.Error,
                            result.Error.Message,
                            null
                        )
                    );
                }

                return Ok(
                    new ApiResponseModel<WebSocketStatistics>(
                        ApiResponseStatusEnum.Success,
                        "WebSocket statistics retrieved successfully",
                        result.Value
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WebSocket statistics");
                return StatusCode(
                    500,
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.Error,
                        "An error occurred while retrieving WebSocket statistics",
                        null
                    )
                );
            }
        }

        /// <summary>
        /// Gets trending data for dashboard charts
        /// </summary>
        /// <param name="metric">Metric type (users, bookings, revenue)</param>
        /// <param name="startDate">Start date (optional)</param>
        /// <param name="endDate">End date (optional)</param>
        /// <param name="granularity">Data granularity (daily, weekly, monthly) (optional, default: daily)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Trending data points</returns>
        [HttpGet(ApiRoutes.Admin.GetTrendingData)]
        [SwaggerOperation(
            Summary = "Get trending data",
            Description = "Retrieves trending data for dashboard charts with specified granularity."
        )]
        [SwaggerResponse(
            200,
            "Trending data retrieved successfully",
            typeof(ApiResponseModel<List<TrendingDataPoint>>)
        )]
        [SwaggerResponse(400, "Invalid metric type", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(401, "Unauthorized access", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(
            403,
            "Forbidden - Admin access required",
            typeof(ApiResponseModel<object>)
        )]
        [SwaggerResponse(500, "Internal server error", typeof(ApiResponseModel<object>))]
        public async Task<ActionResult<ApiResponseModel<List<TrendingDataPoint>>>> GetTrendingData(
            [FromRoute] [Required] string metric,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string granularity = "daily",
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow.AddDays(-30);
                var end = endDate ?? DateTime.UtcNow;

                _logger.LogInformation(
                    "Admin {UserId} requested trending data for {Metric} from {StartDate} to {EndDate} with {Granularity} granularity",
                    User.Identity?.Name,
                    metric,
                    start,
                    end,
                    granularity
                );

                var result = await _dashboardStatisticsService.GetTrendingDataAsync(
                    metric,
                    start,
                    end,
                    granularity,
                    cancellationToken
                );

                if (result.IsFailure)
                {
                    _logger.LogWarning(
                        "Failed to get trending data: {Error}",
                        result.Error.Message
                    );
                    return BadRequest(
                        new ApiResponseModel<object>(
                            ApiResponseStatusEnum.Error,
                            result.Error.Message,
                            null
                        )
                    );
                }

                return Ok(
                    new ApiResponseModel<List<TrendingDataPoint>>(
                        ApiResponseStatusEnum.Success,
                        "Trending data retrieved successfully",
                        result.Value
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trending data for metric {Metric}", metric);
                return StatusCode(
                    500,
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.Error,
                        "An error occurred while retrieving trending data",
                        null
                    )
                );
            }
        }

        /// <summary>
        /// Gets comparative statistics between two periods
        /// </summary>
        /// <param name="currentStartDate">Current period start date</param>
        /// <param name="currentEndDate">Current period end date</param>
        /// <param name="previousStartDate">Previous period start date (optional)</param>
        /// <param name="previousEndDate">Previous period end date (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comparative statistics</returns>
        [HttpGet(ApiRoutes.Admin.GetComparativeStatistics)]
        [SwaggerOperation(
            Summary = "Get comparative statistics",
            Description = "Retrieves comparative statistics between two time periods showing growth rates and changes."
        )]
        [SwaggerResponse(
            200,
            "Comparative statistics retrieved successfully",
            typeof(ApiResponseModel<ComparativeStatistics>)
        )]
        [SwaggerResponse(400, "Invalid date parameters", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(401, "Unauthorized access", typeof(ApiResponseModel<object>))]
        [SwaggerResponse(
            403,
            "Forbidden - Admin access required",
            typeof(ApiResponseModel<object>)
        )]
        [SwaggerResponse(500, "Internal server error", typeof(ApiResponseModel<object>))]
        public async Task<
            ActionResult<ApiResponseModel<ComparativeStatistics>>
        > GetComparativeStatistics(
            [FromQuery] [Required] DateTime currentStartDate,
            [FromQuery] [Required] DateTime currentEndDate,
            [FromQuery] DateTime? previousStartDate = null,
            [FromQuery] DateTime? previousEndDate = null,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Calculate previous period if not provided
                var periodDays = (currentEndDate - currentStartDate).Days;
                var prevStart = previousStartDate ?? currentStartDate.AddDays(-periodDays);
                var prevEnd = previousEndDate ?? currentStartDate;

                _logger.LogInformation(
                    "Admin {UserId} requested comparative statistics: Current({CurrentStart}-{CurrentEnd}) vs Previous({PrevStart}-{PrevEnd})",
                    User.Identity?.Name,
                    currentStartDate,
                    currentEndDate,
                    prevStart,
                    prevEnd
                );

                var result = await _dashboardStatisticsService.GetComparativeStatisticsAsync(
                    currentStartDate,
                    currentEndDate,
                    prevStart,
                    prevEnd,
                    cancellationToken
                );

                if (result.IsFailure)
                {
                    _logger.LogWarning(
                        "Failed to get comparative statistics: {Error}",
                        result.Error.Message
                    );
                    return BadRequest(
                        new ApiResponseModel<object>(
                            ApiResponseStatusEnum.Error,
                            result.Error.Message,
                            null
                        )
                    );
                }

                return Ok(
                    new ApiResponseModel<ComparativeStatistics>(
                        ApiResponseStatusEnum.Success,
                        "Comparative statistics retrieved successfully",
                        result.Value
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting comparative statistics");
                return StatusCode(
                    500,
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.Error,
                        "An error occurred while retrieving comparative statistics",
                        null
                    )
                );
            }
        }
    }
}
