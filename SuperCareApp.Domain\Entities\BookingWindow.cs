using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities
{
    public class BookingWindow : BaseEntity
    {
        public Guid BookingId { get; set; }
        public DateOnly Date { get; set; }
        public TimeOnly StartTime { get; set; }
        public TimeOnly EndTime { get; set; }

        public BookingWindowStatus? Status { get; set; }
        public decimal? DailyRate { get; set; }
        public string? DaySpecialInstructions { get; set; }
        public int? DurationMinutes { get; set; }

        // Navigation properties
        public Booking Booking { get; set; } = null!;
        public Invoice? Invoice { get; set; }
        public ICollection<TrackingSession> TrackingSessions { get; set; } =
            new List<TrackingSession>();

        // Validation
        public bool IsValid => StartTime < EndTime && Date >= DateOnly.FromDateTime(DateTime.Today);

        // Helper methods
        public double GetDurationHours() => (EndTime - StartTime).TotalHours;

        public bool OverlapsWith(BookingWindow other)
        {
            if (Date != other.Date)
                return false;
            return StartTime < other.EndTime && EndTime > other.StartTime;
        }

        public bool IsWithinTimeRange(TimeOnly start, TimeOnly end)
        {
            return StartTime >= start && EndTime <= end;
        }
    }
}
