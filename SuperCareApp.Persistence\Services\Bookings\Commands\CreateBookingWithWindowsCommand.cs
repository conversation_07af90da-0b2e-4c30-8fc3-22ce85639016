using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

public record CreateBookingWithWindowsCommand(Guid UserId, CreateBookingRequest Request)
    : ICommand<Result<Guid>>;

internal sealed class CreateBookingWithWindowsCommandHandler
    : ICommandHandler<CreateBookingWithWindowsCommand, Result<Guid>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CreateBookingWithWindowsCommandHandler> _logger;

    public CreateBookingWithWindowsCommandHandler(
        ApplicationDbContext context,
        ILogger<CreateBookingWithWindowsCommandHandler> logger
    )
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Result<Guid>> Handle(
        CreateBookingWithWindowsCommand command,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var (userId, request) = command;

            // 1. Basic existence checks
            var client = await _context.Users.FindAsync([userId], cancellationToken);
            if (client == null)
            {
                return Result.Failure<Guid>(Error.NotFound("Client not found."));
            }

            // Load provider with availability data in one query
            var provider = await _context
                .CareProviderProfiles.Include(p => p.Availabilities)
                .ThenInclude(a => a.AvailabilitySlots)
                .FirstOrDefaultAsync(p => p.Id == request.ProviderId, cancellationToken);

            if (provider == null)
            {
                return Result.Failure<Guid>(Error.NotFound("Provider not found."));
            }

            var category = await _context.CareCategories.FindAsync(
                [request.CategoryId],
                cancellationToken
            );
            if (category == null)
            {
                return Result.Failure<Guid>(Error.NotFound("Care category not found."));
            }

            // 2. Validate intra-request overlaps (windows within same request)
            var sortedWindows = request.BookingWindows.GroupBy(w => w.Date.Date).ToList();

            foreach (var dayGroup in sortedWindows)
            {
                var windowsForDay = dayGroup.OrderBy(w => w.StartTime).ToList();
                for (int i = 1; i < windowsForDay.Count; i++)
                {
                    if (windowsForDay[i].StartTime < windowsForDay[i - 1].EndTime)
                    {
                        return Result.Failure<Guid>(
                            Error.Conflict(
                                $"Booking windows overlap within the same request on {dayGroup.Key:yyyy-MM-dd}."
                            )
                        );
                    }
                }
            }

            // 3. Group availability by DayOfWeek for efficient lookup
            var requestedDays = request
                .BookingWindows.Select(w => w.Date.DayOfWeek.ToString())
                .Distinct()
                .ToList();

            var availabilityMap = provider
                .Availabilities.Where(a => requestedDays.Contains(a.DayOfWeek))
                .ToDictionary(a => a.DayOfWeek, a => a);

            // 4. Validate each window against provider availability
            foreach (var window in request.BookingWindows)
            {
                var dayOfWeek = window.Date.DayOfWeek.ToString();

                // Check if provider is available on this day
                if (
                    !availabilityMap.TryGetValue(dayOfWeek, out var availability)
                    || !availability.IsAvailable
                )
                {
                    return Result.Failure<Guid>(
                        Error.Conflict(
                            $"Provider is not available on {window.Date:yyyy-MM-dd} ({dayOfWeek})."
                        )
                    );
                }

                // Check if requested window falls within provider's available time slots
                var fitsInAvailableSlot = availability.AvailabilitySlots.Any(slot =>
                    slot.StartTime <= window.StartTime && slot.EndTime >= window.EndTime
                );

                if (!fitsInAvailableSlot)
                {
                    return Result.Failure<Guid>(
                        Error.Conflict(
                            $"Requested time {window.StartTime:hh\\:mm}–{window.EndTime:hh\\:mm} "
                                + $"on {window.Date:yyyy-MM-dd} falls outside provider's available hours."
                        )
                    );
                }
            }

            // 5. Check for conflicts with existing active bookings
            var activeStatuses = new[]
            {
                BookingStatusType.Requested,
                BookingStatusType.Accepted,
                BookingStatusType.InProgress,
                BookingStatusType.Confirmed,
            };

            var requestedDates = request
                .BookingWindows.Select(w => DateOnly.FromDateTime(w.Date))
                .Distinct()
                .ToList();

            var existingBookingWindows = await _context
                .Bookings.Where(b =>
                    b.ProviderId == request.ProviderId && activeStatuses.Contains(b.Status!.Status)
                )
                .SelectMany(b => b.BookingWindows)
                .Where(bw => requestedDates.Contains(bw.Date))
                .ToListAsync(cancellationToken);

            // Check for overlaps with existing bookings
            foreach (var window in request.BookingWindows)
            {
                var windowDate = DateOnly.FromDateTime(window.Date);
                var conflictingWindows = existingBookingWindows
                    .Where(existing => existing.Date == windowDate)
                    .Where(existing =>
                        window.StartTime < existing.EndTime && window.EndTime > existing.StartTime
                    )
                    .ToList();

                if (conflictingWindows.Any())
                {
                    var conflict = conflictingWindows.First();
                    return Result.Failure<Guid>(
                        Error.Conflict(
                            $"Provider already has a booking on {window.Date:yyyy-MM-dd} "
                                + $"from {conflict.StartTime:hh\\:mm} to {conflict.EndTime:hh\\:mm} "
                                + $"which conflicts with requested time {window.StartTime:hh\\:mm}–{window.EndTime:hh\\:mm}."
                        )
                    );
                }
            }

            // 6. Calculate total amounts from windows
            var totalHours = request.BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours);
            var totalAmount =
                provider
                    .CareProviderCategories.FirstOrDefault(cpc => cpc.CategoryId == category.Id)
                    ?.HourlyRate * (decimal)totalHours;
            var serviceFee = category.PlatformFee;
            var providerAmount = totalAmount / (1 + serviceFee);

            // 7. Create Booking entity
            var booking = new Booking
            {
                ClientId = userId,
                ProviderId = request.ProviderId,
                CategoryId = request.CategoryId,
                TotalAmount =
                    totalAmount
                    ?? throw new InvalidOperationException("Total amount cannot be null"),
                PlatformFee = serviceFee,
                ProviderAmount =
                    providerAmount
                    ?? throw new InvalidOperationException("Provider amount cannot be null"),
                SpecialInstructions = request.SpecialInstructions,
                CreatedBy = userId,
                Status = new BookingStatus
                {
                    Status = BookingStatusType.Requested,
                    CreatedBy = userId,
                },
            };

            // 8. Create BookingWindow entities
            var bookingWindowEntities = request
                .BookingWindows.Select(w => new BookingWindow
                {
                    Booking = booking,
                    Date = DateOnly.FromDateTime(w.Date),
                    StartTime = w.StartTime,
                    EndTime = w.EndTime,
                    Status = BookingWindowStatus.Upcoming,
                })
                .ToList();

            booking.BookingWindows = bookingWindowEntities;

            // 9. Save to database
            await _context.Bookings.AddAsync(booking, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);

            return Result.Success(booking.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error creating booking with windows for user {UserId}",
                command.UserId
            );
            return Result.Failure<Guid>(Error.Internal("Error creating booking with windows."));
        }
    }
}
