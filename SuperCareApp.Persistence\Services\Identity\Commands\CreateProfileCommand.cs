using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Interfaces.Provider;
using SuperCareApp.Application.Common.Interfaces.Storage;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Identity.Enums;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record CreateProfileCommand(Guid UserId, UserType UserType, CreateUserRequest Request)
    : ICommand<Result<Guid>>;

internal sealed class CreateProfileCommandHandler
    : ICommandHandler<CreateProfileCommand, Result<Guid>>
{
    private readonly IUserService _userService;
    private readonly IUserProfileService _userProfileService;
    private readonly ICareProviderService _careProviderService;
    private readonly IFileStorageService _fileStorageService;
    private readonly IMediator _mediator;
    private readonly ILogger<CreateProfileCommandHandler> _logger;

    public CreateProfileCommandHandler(
        IUserService userService,
        IUserProfileService userProfileService,
        ICareProviderService careProviderService,
        IFileStorageService fileStorageService,
        IMediator mediator,
        ILogger<CreateProfileCommandHandler> logger
    )
    {
        _userService = userService;
        _userProfileService = userProfileService;
        _careProviderService = careProviderService;
        _fileStorageService = fileStorageService;
        _mediator = mediator;
        _logger = logger;
    }

    public async Task<Result<Guid>> Handle(
        CreateProfileCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Verify user exists
            var user = await _userService.GetByIdAsync(request.UserId);
            if (user.IsFailure || user.Value == null)
            {
                _logger.LogWarning("User not found for profile creation: {UserId}", request.UserId);
                return Result.Failure<Guid>(Error.NotFound("User not found"));
            }

            // Handle profile picture upload if provided
            if (request.Request.ProfilePicture != null)
            {
                await HandleProfilePictureUpload(
                    request.UserId,
                    request.Request.ProfilePicture,
                    cancellationToken
                );
            }

            switch (request.UserType)
            {
                case UserType.Client:
                    return await CreateUserProfile(request.UserId, request.Request);

                case UserType.CareProvider:
                    return await CreateCareProviderProfile(request.UserId, request.Request);

                default:
                    _logger.LogWarning("Unsupported user type: {UserType}", request.UserType);
                    return Result.Failure<Guid>(Error.BadRequest("Unsupported user type"));
            }
        }
        catch (FormatException ex)
        {
            _logger.LogError(ex, "Invalid format in profile creation request");
            return Result.Failure<Guid>(Error.BadRequest($"Invalid format: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating profile for user {UserId}", request.UserId);
            return Result.Failure<Guid>(Error.Internal($"Error creating profile: {ex.Message}"));
        }
    }

    private async Task<Result<ProfilePictureResponse>> HandleProfilePictureUpload(
        Guid userId,
        IFormFile profilePicture,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var uploadCommand = new UploadProfilePictureCommand(profilePicture, userId);
            var result = await _mediator.Send(uploadCommand, cancellationToken);

            if (result.IsFailure)
            {
                _logger.LogWarning(
                    "Failed to upload profile picture for user {UserId}: {Error}",
                    userId,
                    result.Error.Message
                );
            }
            else
            {
                _logger.LogInformation(
                    "Profile picture uploaded successfully for user {UserId}",
                    userId
                );
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading profile picture for user {UserId}", userId);
            return Result.Failure<ProfilePictureResponse>(
                Error.Internal("An error occurred while uploading the profile picture")
            );
        }
    }

    private async Task<Result<Guid>> CreateUserProfile(Guid userId, CreateUserRequest request)
    {
        var userProfile = new UserProfile
        {
            ApplicationUserId = userId,
            FirstName = request
                .Name.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)
                .FirstOrDefault(),
            LastName = request
                .Name.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)
                .Skip(1)
                .FirstOrDefault(),
            PhoneNumber = request.PhoneNumber,
            Gender = request.Gender,
            DateOfBirth = DateTime.Parse(request.DateOfBirth),
        };

        return await _userProfileService.CreateAsync(userProfile);
    }

    private async Task<Result<Guid>> CreateCareProviderProfile(
        Guid userId,
        CreateUserRequest request
    )
    {
        // First create the user profile (care providers also need a user profile)
        var userProfileResult = await CreateUserProfile(userId, request);
        if (userProfileResult.IsFailure)
        {
            _logger.LogError(
                "Failed to create user profile for care provider {UserId}: {Error}",
                userId,
                userProfileResult.Error.Message
            );
            return userProfileResult;
        }

        // Then create the care provider profile with availability template
        var createProviderRequest = new CreateCareProviderProfileRequest
        {
            YearsExperience = request.YearsExperience ?? 0,
        };

        var providerResult = await _careProviderService.CreateProfileAsync(
            userId,
            createProviderRequest
        );
        if (providerResult.IsFailure)
        {
            _logger.LogError(
                "Failed to create care provider profile for user {UserId}: {Error}",
                userId,
                providerResult.Error.Message
            );
            return providerResult;
        }

        _logger.LogInformation(
            "Successfully created care provider profile with availability template for user {UserId}",
            userId
        );
        return providerResult;
    }
}
