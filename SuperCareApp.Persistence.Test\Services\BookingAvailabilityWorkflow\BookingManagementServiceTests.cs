using Microsoft.EntityFrameworkCore;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services;
using AvailabilityEntity = SuperCareApp.Domain.Entities.Availability;

namespace SuperCareApp.Persistence.Test.Services.BookingAvailabilityWorkflow;

public class BookingManagementServiceTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BookingManagementService _service;
    private readonly Guid _providerId;
    private readonly Guid _clientId;
    private readonly Guid _categoryId;
    private readonly Guid _userId;

    public BookingManagementServiceTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _service = new BookingManagementService(_context);

        _providerId = Guid.NewGuid();
        _clientId = Guid.NewGuid();
        _categoryId = Guid.NewGuid();
        _userId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create provider profile
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.CareProviderProfiles.Add(providerProfile);

        // Create availability for Monday (9:00 AM - 5:00 PM)
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Monday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderNotFound_ReturnsEmptyList()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();
        var date = new DateOnly(2025, 7, 21); // Monday

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(nonExistentProviderId, date);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderHasNoAvailability_ReturnsEmptyList()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 22); // Tuesday (no availability set)

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderIsNotAvailable_ReturnsEmptyList()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Set provider as not available on Monday
        var availability = await _context.Availabilities.FirstAsync(a =>
            a.ProviderId == _providerId && a.DayOfWeek == "Monday"
        );
        availability.IsAvailable = false;
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderIsAvailableWithNoBookings_ReturnsFullSlot()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Single(result);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(17, 0), result[0].End);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderIsOnLeave_ReturnsEmptyList()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = date.ToDateTime(TimeOnly.MinValue),
            EndDate = date.ToDateTime(TimeOnly.MaxValue),
            Reason = "Sick leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderHasConfirmedBooking_AppliesBufferTime()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Create a confirmed booking from 12:00 PM - 1:00 PM
        var booking = new Booking
        {
            Id = Guid.NewGuid(),
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 1,
            TotalAmount = 100,
            PlatformFee = 10,
            ProviderAmount = 90,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = booking.Id,
            Status = BookingStatusType.Confirmed,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var bookingWindow = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = booking.Id,
            Date = date,
            StartTime = new TimeOnly(12, 0),
            EndTime = new TimeOnly(13, 0),
            DurationMinutes = 60,
            DailyRate = 100,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        booking.Status = bookingStatus;
        _context.Bookings.Add(booking);
        _context.BookingStatuses.Add(bookingStatus);
        _context.BookingWindows.Add(bookingWindow);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Equal(2, result.Count);

        // First slot: 9:00 AM - 11:30 AM (12:00 PM - 30 min buffer)
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(11, 30), result[0].End);

        // Second slot: 1:30 PM - 5:00 PM (1:00 PM + 30 min buffer)
        Assert.Equal(new TimeOnly(13, 30), result[1].Start);
        Assert.Equal(new TimeOnly(17, 0), result[1].End);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderHasRequestedBooking_DoesNotApplyBuffer()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Create a requested (not confirmed) booking from 12:00 PM - 1:00 PM
        var booking = new Booking
        {
            Id = Guid.NewGuid(),
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 1,
            TotalAmount = 100,
            PlatformFee = 10,
            ProviderAmount = 90,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = booking.Id,
            Status = BookingStatusType.Requested, // Not confirmed
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var bookingWindow = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = booking.Id,
            Date = date,
            StartTime = new TimeOnly(12, 0),
            EndTime = new TimeOnly(13, 0),
            DurationMinutes = 60,
            DailyRate = 100,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        booking.Status = bookingStatus;
        _context.Bookings.Add(booking);
        _context.BookingStatuses.Add(bookingStatus);
        _context.BookingWindows.Add(bookingWindow);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        // Should return full availability since requested bookings don't block slots
        Assert.Single(result);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(17, 0), result[0].End);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderHasMultipleBookings_HandlesComplexScenario()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Create multiple confirmed bookings
        var booking1 = CreateBooking(date, new TimeOnly(10, 0), new TimeOnly(11, 0));
        var booking2 = CreateBooking(date, new TimeOnly(14, 0), new TimeOnly(15, 0));

        _context.Bookings.AddRange(booking1.booking, booking2.booking);
        _context.BookingStatuses.AddRange(booking1.status, booking2.status);
        _context.BookingWindows.AddRange(booking1.window, booking2.window);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Equal(3, result.Count);

        // First slot: 9:00 AM - 9:30 AM (10:00 AM - 30 min buffer)
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(9, 30), result[0].End);

        // Second slot: 11:30 AM - 1:30 PM (between bookings with buffers)
        Assert.Equal(new TimeOnly(11, 30), result[1].Start);
        Assert.Equal(new TimeOnly(13, 30), result[1].End);

        // Third slot: 3:30 PM - 5:00 PM (3:00 PM + 30 min buffer)
        Assert.Equal(new TimeOnly(15, 30), result[2].Start);
        Assert.Equal(new TimeOnly(17, 0), result[2].End);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_WhenProviderHasPartialLeave_HandlesPartialDayLeave()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Create a leave from 1:00 PM - 3:00 PM
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = date.ToDateTime(new TimeOnly(13, 0)),
            EndDate = date.ToDateTime(new TimeOnly(15, 0)),
            Reason = "Doctor appointment",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        Assert.Equal(2, result.Count);

        // First slot: 9:00 AM - 1:00 PM
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(13, 0), result[0].End);

        // Second slot: 3:00 PM - 5:00 PM
        Assert.Equal(new TimeOnly(15, 0), result[1].Start);
        Assert.Equal(new TimeOnly(17, 0), result[1].End);
    }

    [Fact]
    public async Task GetAvailableSlotsForDateAsync_FiltersOutSlotsShorterThanMinimumDuration()
    {
        // Arrange
        var date = new DateOnly(2025, 7, 21); // Monday

        // Create a booking that leaves only a 15-minute slot (less than 30-minute minimum)
        var booking = CreateBooking(date, new TimeOnly(16, 45), new TimeOnly(17, 0));

        _context.Bookings.Add(booking.booking);
        _context.BookingStatuses.Add(booking.status);
        _context.BookingWindows.Add(booking.window);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAvailableSlotsForDateAsync(_providerId, date);

        // Assert
        // Should only return the morning slot, as the remaining 15-minute slot is too short
        Assert.Single(result);
        Assert.Equal(new TimeOnly(9, 0), result[0].Start);
        Assert.Equal(new TimeOnly(16, 15), result[0].End); // 16:45 - 30 min buffer
    }

    private (Booking booking, BookingStatus status, BookingWindow window) CreateBooking(
        DateOnly date,
        TimeOnly startTime,
        TimeOnly endTime
    )
    {
        var bookingId = Guid.NewGuid();

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 1,
            TotalAmount = 100,
            PlatformFee = 10,
            ProviderAmount = 90,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var status = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Confirmed,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var window = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Date = date,
            StartTime = startTime,
            EndTime = endTime,
            DurationMinutes = (int)(endTime - startTime).TotalMinutes,
            DailyRate = 100,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        booking.Status = status;
        return (booking, status, window);
    }
}
