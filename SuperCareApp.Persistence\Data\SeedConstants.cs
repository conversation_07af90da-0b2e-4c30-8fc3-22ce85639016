using System;

namespace SuperCareApp.Persistence.Data
{
    /// <summary>
    /// Constants for database seeding with hardcoded GUIDs to avoid changing IDs
    /// </summary>
    public static class SeedConstants
    {
        /// <summary>
        /// Admin user constants
        /// </summary>
        public static class Admin
        {
            public static readonly Guid UserId = new("a1b2c3d4-e5f6-7788-9900-aabbccddeeff");
            public static readonly Guid ProfileId = new("b2c3d4e5-f6a7-8899-0011-bbccddeeff00");
        }

        /// <summary>
        /// Care provider user and profile constants
        /// </summary>
        public static class CareProviders
        {
            // First 6 providers (existing)
            public static readonly Guid Provider1UserId =
                new("c3d4e5f6-a7b8-9900-1122-ccddeeff0011");
            public static readonly Guid Provider1ProfileId =
                new("d4e5f6a7-b8c9-0011-2233-ddeeff001122");

            public static readonly Guid Provider2UserId =
                new("e5f6a7b8-c9d0-1122-3344-eeff00112233");
            public static readonly Guid Provider2ProfileId =
                new("f6a7b8c9-d0e1-2233-4455-ff0011223344");

            public static readonly Guid Provider3UserId =
                new("a7b8c9d0-e1f2-3344-5566-001122334455");
            public static readonly Guid Provider3ProfileId =
                new("b8c9d0e1-f2a3-4455-6677-112233445566");

            public static readonly Guid Provider4UserId =
                new("c9d0e1f2-a3b4-5566-7788-************");
            public static readonly Guid Provider4ProfileId =
                new("d0e1f2a3-b4c5-6677-8899-************");

            public static readonly Guid Provider5UserId =
                new("e1f2a3b4-c5d6-7788-9900-************");
            public static readonly Guid Provider5ProfileId =
                new("f2a3b4c5-d6e7-8899-0011-************");

            public static readonly Guid Provider6UserId =
                new("a3b4c5d6-e7f8-9900-1122-************");
            public static readonly Guid Provider6ProfileId =
                new("b4c5d6e7-f8a9-0011-2233-************");

            // New 10 providers
            public static readonly Guid Provider7UserId =
                new("c5d6e7f8-a9b0-1122-3344-************");
            public static readonly Guid Provider7ProfileId =
                new("d6e7f8a9-b0c1-2233-4455-************");

            public static readonly Guid Provider8UserId =
                new("e7f8a9b0-c1d2-3344-5566-001122334455");
            public static readonly Guid Provider8ProfileId =
                new("f8a9b0c1-d2e3-4455-6677-112233445566");

            public static readonly Guid Provider9UserId =
                new("a9b0c1d2-e3f4-5566-7788-************");
            public static readonly Guid Provider9ProfileId =
                new("b0c1d2e3-f4a5-6677-8899-************");

            public static readonly Guid Provider10UserId =
                new("c1d2e3f4-a5b6-7788-9900-************");
            public static readonly Guid Provider10ProfileId =
                new("d2e3f4a5-b6c7-8899-0011-************");

            public static readonly Guid Provider11UserId =
                new("e3f4a5b6-c7d8-9900-1122-************");
            public static readonly Guid Provider11ProfileId =
                new("f4a5b6c7-d8e9-0011-2233-************");

            public static readonly Guid Provider12UserId =
                new("a5b6c7d8-e9f0-1122-3344-************");
            public static readonly Guid Provider12ProfileId =
                new("b6c7d8e9-f0a1-2233-4455-************");

            public static readonly Guid Provider13UserId =
                new("c7d8e9f0-a1b2-3344-5566-001122334455");
            public static readonly Guid Provider13ProfileId =
                new("d8e9f0a1-b2c3-4455-6677-112233445566");

            public static readonly Guid Provider14UserId =
                new("e9f0a1b2-c3d4-5566-7788-************");
            public static readonly Guid Provider14ProfileId =
                new("f0a1b2c3-d4e5-6677-8899-************");

            public static readonly Guid Provider15UserId =
                new("a1b2c3d4-e5f6-7788-9900-aabbccddeef0");
            public static readonly Guid Provider15ProfileId =
                new("b2c3d4e5-f6a7-8899-0011-bbccddeeff01");

            public static readonly Guid Provider16UserId =
                new("c3d4e5f6-a7b8-9900-1122-ccddeeff0012");
            public static readonly Guid Provider16ProfileId =
                new("d4e5f6a7-b8c9-0011-2233-ddeeff001123");
        }

        /// <summary>
        /// Client user and profile constants
        /// </summary>
        public static class Clients
        {
            public static readonly Guid Client1UserId = new("a2b3c4d5-e6f7-8899-0011-bbccddeeff11");
            public static readonly Guid Client1ProfileId =
                new("b3c4d5e6-f7a8-9900-1122-ccddeeff1122");

            public static readonly Guid Client2UserId = new("c4d5e6f7-a8b9-0011-2233-ddeeff112233");
            public static readonly Guid Client2ProfileId =
                new("d5e6f7a8-b9c0-1122-3344-eeff11223344");

            public static readonly Guid Client3UserId = new("e6f7a8b9-c0d1-2233-4455-ff1122334455");
            public static readonly Guid Client3ProfileId =
                new("f7a8b9c0-d1e2-3344-5566-001122334455");

            public static readonly Guid Client4UserId = new("a8b9c0d1-e2f3-4455-6677-112233445566");
            public static readonly Guid Client4ProfileId =
                new("b9c0d1e2-f3a4-5566-7788-************");

            public static readonly Guid Client5UserId = new("c0d1e2f3-a4b5-6677-8899-************");
            public static readonly Guid Client5ProfileId =
                new("d1e2f3a4-b5c6-7788-9900-************");
        }

        /// <summary>
        /// Care category constants
        /// </summary>
        public static class CareCategories
        {
            public static readonly Guid ChildCareId = new("a3b4c5d6-e7f8-9900-1122-bbccddeeff22");
            public static readonly Guid ElderlyCareId = new("b4c5d6e7-f8a9-0011-2233-ccddeeff2233");
            public static readonly Guid DisabilityCareId =
                new("c5d6e7f8-a9b0-1122-3344-ddeeff223344");
        }

        /// <summary>
        /// Role constants
        /// </summary>
        public static class Roles
        {
            public static readonly Guid AdminRoleId = new("a4b5c6d7-e8f9-0011-2233-bbccddeeff33");
            public static readonly Guid ClientRoleId = new("b5c6d7e8-f9a0-1122-3344-ccddeeff3344");
            public static readonly Guid CareProviderRoleId =
                new("c6d7e8f9-a0b1-2233-4455-ddeeff334455");
        }

        /// <summary>
        /// Approval constants
        /// </summary>
        public static class Approvals
        {
            public static readonly Guid Provider1ApprovalId =
                new("d7e8f9a0-b1c2-3344-5566-eeff33445566");
            public static readonly Guid Provider2ApprovalId =
                new("e8f9a0b1-c2d3-4455-6677-ff3344556677");
            public static readonly Guid Provider3ApprovalId =
                new("f9a0b1c2-d3e4-5566-7788-003344556677");
            public static readonly Guid Provider4ApprovalId =
                new("a0b1c2d3-e4f5-6677-8899-113344556677");
            public static readonly Guid Provider5ApprovalId =
                new("b1c2d3e4-f5a6-7788-9900-************");
            public static readonly Guid Provider6ApprovalId =
                new("c2d3e4f5-a6b7-8899-0011-************");
            public static readonly Guid Provider7ApprovalId =
                new("d3e4f5a6-b7c8-9900-1122-************");
            public static readonly Guid Provider8ApprovalId =
                new("e4f5a6b7-c8d9-0011-2233-************");
            public static readonly Guid Provider9ApprovalId =
                new("f5a6b7c8-d9e0-1122-3344-************");
            public static readonly Guid Provider10ApprovalId =
                new("a6b7c8d9-e0f1-2233-4455-************");
            public static readonly Guid Provider11ApprovalId =
                new("b7c8d9e0-f1a2-3344-5566-************");
            public static readonly Guid Provider12ApprovalId =
                new("c8d9e0f1-a2b3-4455-6677-************");
            public static readonly Guid Provider13ApprovalId =
                new("d9e0f1a2-b3c4-5566-7788-aa3344556677");
            public static readonly Guid Provider14ApprovalId =
                new("e0f1a2b3-c4d5-6677-8899-bb3344556677");
            public static readonly Guid Provider15ApprovalId =
                new("f1a2b3c4-d5e6-7788-9900-cc3344556677");
            public static readonly Guid Provider16ApprovalId =
                new("a2b3c4d5-e6f7-8899-0011-dd3344556677");
        }

        /// <summary>
        /// Booking constants
        /// </summary>
        public static class Bookings
        {
            public static readonly Guid Booking1Id = new("b3c4d5e6-f7a8-9900-1122-ee3344556677");
            public static readonly Guid Booking2Id = new("c4d5e6f7-a8b9-0011-2233-ff3344556677");
            public static readonly Guid Booking3Id = new("d5e6f7a8-b9c0-1122-3344-003455667788");
            public static readonly Guid Booking4Id = new("e6f7a8b9-c0d1-2233-4455-113455667788");
            public static readonly Guid Booking5Id = new("f7a8b9c0-d1e2-3344-5566-************");
            public static readonly Guid Booking6Id = new("a8b9c0d1-e2f3-4455-6677-************");
            public static readonly Guid Booking7Id = new("b9c0d1e2-f3a4-5566-7788-************");
            public static readonly Guid Booking8Id = new("c0d1e2f3-a4b5-6677-8899-************");
            public static readonly Guid Booking9Id = new("d1e2f3a4-b5c6-7788-9900-************");
            public static readonly Guid Booking10Id = new("e2f3a4b5-c6d7-8899-0011-************");
            public static readonly Guid Booking11Id = new("f3a4b5c6-d7e8-9900-1122-************");
            public static readonly Guid Booking12Id = new("a4b5c6d7-e8f9-0011-2233-************");
            public static readonly Guid Booking13Id = new("b5c6d7e8-f9a0-1122-3344-aa3455667788");
            public static readonly Guid Booking14Id = new("c6d7e8f9-a0b1-2233-4455-bb3455667788");
            public static readonly Guid Booking15Id = new("d7e8f9a0-b1c2-3344-5566-cc3455667788");
        }

        /// <summary>
        /// Address constants
        /// </summary>
        public static class Addresses
        {
            public static readonly Guid Address1Id = new("e8f9a0b1-c2d3-4455-6677-dd3455667788");
            public static readonly Guid Address2Id = new("f9a0b1c2-d3e4-5566-7788-ee3455667788");
            public static readonly Guid Address3Id = new("a0b1c2d3-e4f5-6677-8899-ff3455667788");
            public static readonly Guid Address4Id = new("b1c2d3e4-f5a6-7788-9900-003566778899");
            public static readonly Guid Address5Id = new("c2d3e4f5-a6b7-8899-0011-113566778899");
        }

        /// <summary>
        /// User address constants
        /// </summary>
        public static class UserAddresses
        {
            public static readonly Guid UserAddress1Id =
                new("d3e4f5a6-b7c8-9900-1122-************");
            public static readonly Guid UserAddress2Id =
                new("e4f5a6b7-c8d9-0011-2233-************");
            public static readonly Guid UserAddress3Id =
                new("f5a6b7c8-d9e0-1122-3344-************");
            public static readonly Guid UserAddress4Id =
                new("a6b7c8d9-e0f1-2233-4455-************");
            public static readonly Guid UserAddress5Id =
                new("b7c8d9e0-f1a2-3344-5566-************");
        }

        /// <summary>
        /// Booking status constants (starting from a base and incrementing)
        /// </summary>
        public static class BookingStatuses
        {
            // Base GUID for booking statuses - we'll generate sequential IDs from this base
            public static readonly Guid BaseId = new("c8d9e0f1-a2b3-4455-6677-************");

            // Helper method to generate sequential booking status GUIDs
            public static Guid GetBookingStatusId(int index)
            {
                var baseBytes = BaseId.ToByteArray();
                // Increment the last 4 bytes to create unique sequential GUIDs
                var increment = BitConverter.GetBytes(index);
                for (int i = 0; i < 4 && i < baseBytes.Length - 12; i++)
                {
                    baseBytes[baseBytes.Length - 4 + i] = increment[i];
                }
                return new Guid(baseBytes);
            }
        }
    }
}
