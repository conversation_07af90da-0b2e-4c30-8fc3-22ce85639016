﻿#nullable disable
namespace SuperCareApp.Domain.Common.Results
{
    /// <summary>
    /// Represents an error that occurred during an operation
    /// </summary>
    public class Error : IEquatable<Error>
    {
        public string Code { get; }
        public string Message { get; }
        public IDictionary<string, string[]> ValidationErrors { get; }

        public Error(string code, string message)
        {
            Code = code;
            Message = message;
            ValidationErrors = new Dictionary<string, string[]>();
        }

        public Error(string code, string message, IDictionary<string, string[]> validationErrors)
        {
            Code = code;
            Message = message;
            ValidationErrors = validationErrors ?? new Dictionary<string, string[]>();
        }

        public static Error None => new Error(string.Empty, string.Empty);

        public static Error NotFound(string message = "Resource not found") =>
            new Error("NotFound", message);

        public static Error Validation(string message = "Validation error") =>
            new Error("Validation", message);

        public static Error Validation(
            string message,
            IDictionary<string, string[]> validationErrors
        ) => new Error("Validation", message, validationErrors);

        public static Error Unauthorized(string message = "Unauthorized") =>
            new Error("Unauthorized", message);

        public static Error Forbidden(string message = "Forbidden") =>
            new Error("Forbidden", message);

        public static Error Conflict(string message = "Conflict") => new Error("Conflict", message);

        public static Error BadRequest(string message = "Bad request") =>
            new Error("BadRequest", message);

        public static Error Internal(string message = "Internal server error") =>
            new Error("Internal", message);

        public static Error ExternalService(string message = "External service error") =>
            new Error("ExternalService", message);

        public static Error Custom(string code, string message) => new Error(code, message);

        public bool Equals(Error other)
        {
            if (other is null)
                return false;

            // Compare Code and Message
            if (Code != other.Code || Message != other.Message)
                return false;

            // Compare ValidationErrors
            if (ValidationErrors.Count != other.ValidationErrors.Count)
                return false;

            foreach (var kvp in ValidationErrors)
            {
                if (!other.ValidationErrors.TryGetValue(kvp.Key, out var otherErrors))
                    return false;

                if (kvp.Value.Length != otherErrors.Length)
                    return false;

                for (int i = 0; i < kvp.Value.Length; i++)
                {
                    if (kvp.Value[i] != otherErrors[i])
                        return false;
                }
            }

            return true;
        }

        public override bool Equals(object obj)
        {
            return obj is Error error && Equals(error);
        }

        public override int GetHashCode()
        {
            var hash = HashCode.Combine(Code, Message);

            // Include ValidationErrors in the hash code
            foreach (var kvp in ValidationErrors)
            {
                hash = HashCode.Combine(hash, kvp.Key.GetHashCode());
                foreach (var error in kvp.Value)
                {
                    hash = HashCode.Combine(hash, error.GetHashCode());
                }
            }

            return hash;
        }

        public static bool operator ==(Error left, Error right)
        {
            if (left is null)
                return right is null;

            return left.Equals(right);
        }

        public static bool operator !=(Error left, Error right)
        {
            return !(left == right);
        }
    }
}
