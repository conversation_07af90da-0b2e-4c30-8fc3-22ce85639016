﻿using System.Linq.Expressions;

namespace SuperCareApp.Persistence.Repositories
{
    public class GenericRepository<TEntity> : IRepository<TEntity>
        where TEntity : BaseEntity
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly DbSet<TEntity> _dbSet;

        public GenericRepository(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
            _dbSet = dbContext.Set<TEntity>();
        }

        public virtual async Task<Result<IEnumerable<TEntity>>> GetAllAsync(
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entities = await _dbSet.Where(e => !e.IsDeleted).ToListAsync(cancellationToken);
                return Result.Success<IEnumerable<TEntity>>(entities);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<TEntity>>(Error.Internal(ex.Message));
            }
        }

        public virtual async Task<Result<IEnumerable<TEntity>>> GetByFilterAsync(
            Expression<Func<TEntity, bool>> filter,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entities = await _dbSet
                    .Where(e => !e.IsDeleted)
                    .Where(filter)
                    .ToListAsync(cancellationToken);
                return Result.Success<IEnumerable<TEntity>>(entities);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<TEntity>>(Error.Internal(ex.Message));
            }
        }

        public virtual async Task<Result<TEntity>> GetByIdAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entity = await _dbSet
                    .Where(e => e.Id == id && !e.IsDeleted)
                    .FirstOrDefaultAsync(cancellationToken);

                if (entity == null)
                {
                    return Result.Failure<TEntity>(
                        Error.NotFound(
                            $"Entity of type {typeof(TEntity).Name} with id {id} was not found."
                        )
                    );
                }

                return Result.Success(entity);
            }
            catch (Exception ex)
            {
                return Result.Failure<TEntity>(Error.Internal(ex.Message));
            }
        }

        public virtual async Task<Result<TEntity>> AddAsync(
            TEntity entity,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                await _dbSet.AddAsync(entity, cancellationToken);
                return Result.Success(entity);
            }
            catch (Exception ex)
            {
                return Result.Failure<TEntity>(Error.Internal(ex.Message));
            }
        }

        public virtual Task<Result<TEntity>> UpdateAsync(
            TEntity entity,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                _dbContext.Entry(entity).State = EntityState.Modified;
                return Task.FromResult(Result.Success(entity));
            }
            catch (Exception ex)
            {
                return Task.FromResult(Result.Failure<TEntity>(Error.Internal(ex.Message)));
            }
        }

        public virtual async Task<Result> DeleteAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entityResult = await GetByIdAsync(id, cancellationToken);

                if (entityResult.IsFailure)
                {
                    return Result.Failure(entityResult.Error);
                }

                _dbSet.Remove(entityResult.Value);
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
        }

        public virtual async Task<Result> SoftDeleteAsync(
            Guid id,
            Guid deletedBy,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var entityResult = await GetByIdAsync(id, cancellationToken);

                if (entityResult.IsFailure)
                {
                    return Result.Failure(entityResult.Error);
                }

                var entity = entityResult.Value;
                entity.IsDeleted = true;
                entity.DeletedAt = DateTime.UtcNow;
                entity.DeletedBy = deletedBy;

                _dbContext.Entry(entity).State = EntityState.Modified;
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
        }

        public virtual async Task<Result<bool>> ExistsAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var exists = await _dbSet.AnyAsync(
                    e => e.Id == id && !e.IsDeleted,
                    cancellationToken
                );
                return Result.Success(exists);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool>(Error.Internal(ex.Message));
            }
        }

        public virtual IQueryable<TEntity> AsQueryable()
        {
            return _dbSet.Where(e => !e.IsDeleted).AsQueryable();
        }
    }
}
