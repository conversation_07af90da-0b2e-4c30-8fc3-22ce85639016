﻿using System.Text.Json;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Helper class for JSON serialization
    /// </summary>
    public static class JsonH<PERSON>per
    {
        /// <summary>
        /// Shared JSON serializer options
        /// </summary>
        private static readonly JsonSerializerOptions _options =
            new() { WriteIndented = true, PropertyNamingPolicy = JsonNamingPolicy.CamelCase };

        /// <summary>
        /// Serializes an object to JSON
        /// </summary>
        /// <typeparam name="T">Type of object to serialize</typeparam>
        /// <param name="obj">Object to serialize</param>
        /// <returns>JSON string</returns>
        public static string SerializeToJson<T>(T obj)
        {
            return JsonSerializer.Serialize(obj, _options);
        }
    }
}
