﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Persistence.Configurations
{
    public class ApplicationRoleConfiguration : IEntityTypeConfiguration<ApplicationRole>
    {
        public void Configure(EntityTypeBuilder<ApplicationRole> builder)
        {
            // Audit properties
            builder.Property(r => r.CreatedAt).IsRequired();

            builder.Property(r => r.CreatedBy).IsRequired();

            builder.Property(r => r.IsDeleted).IsRequired().HasDefaultValue(false);
        }
    }
}
