# Design Document

## Overview

The DigitalOcean Spaces migration will replace the current `LocalFileStorageService` with a new `DigitalOceanSpacesStorageService` that implements the same `IFileStorageService` interface. This approach ensures zero code changes in existing services while providing cloud-based file storage with CDN integration, automatic backups, and horizontal scalability.

The design leverages the AWS S3 SDK since DigitalOcean Spaces is S3-compatible, providing a robust and well-tested foundation for file operations. The migration includes a comprehensive data migration strategy, monitoring capabilities, and proper error handling for production reliability.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
│              DocumentsController (No Changes)               │
├─────────────────────────────────────────────────────────────┤
│              Application Layer (Services)                   │
│              DocumentService (No Changes)                   │
├─────────────────────────────────────────────────────────────┤
│              Storage Layer (New Implementation)             │
│  IFileStorageService -> DigitalOceanSpacesStorageService    │
├─────────────────────────────────────────────────────────────┤
│                   External Services                         │
│  DigitalOcean Spaces (S3-Compatible) + CDN                 │
└─────────────────────────────────────────────────────────────┘
```

### Integration Points

1. **Existing Interface**: Maintains `IFileStorageService` contract for seamless integration
2. **AWS S3 SDK**: Uses proven S3 SDK for DigitalOcean Spaces compatibility
3. **Configuration System**: Integrates with existing configuration management
4. **Logging Infrastructure**: Uses existing structured logging patterns
5. **Error Handling**: Maintains existing `Result<T>` pattern for consistency

## Components and Interfaces

### Configuration

#### DigitalOcean Spaces Settings

```csharp
public class DigitalOceanSpacesSettings
{
    public string AccessKey { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public string BucketName { get; set; } = string.Empty;
    public string Region { get; set; } = "nyc3"; // Default DigitalOcean region
    public string ServiceUrl { get; set; } = string.Empty; // e.g., https://nyc3.digitaloceanspaces.com
    public string CdnUrl { get; set; } = string.Empty; // CDN endpoint URL
    public bool UseHttps { get; set; } = true;
    public int UploadTimeoutMinutes { get; set; } = 10;
    public int MaxRetryAttempts { get; set; } = 3;
    public bool EnableLogging { get; set; } = true;
    public string DefaultAcl { get; set; } = "public-read"; // For public file access
}
```

#### Configuration Setup

```json
// appsettings.json
{
  "DigitalOceanSpaces": {
    "AccessKey": "", // Set via environment variable
    "SecretKey": "", // Set via environment variable
    "BucketName": "supercare-files",
    "Region": "nyc3",
    "ServiceUrl": "https://nyc3.digitaloceanspaces.com",
    "CdnUrl": "https://supercare-files.nyc3.cdn.digitaloceanspaces.com",
    "UseHttps": true,
    "UploadTimeoutMinutes": 10,
    "MaxRetryAttempts": 3,
    "EnableLogging": true,
    "DefaultAcl": "public-read"
  }
}
```

### DigitalOcean Spaces Storage Service

```csharp
public class DigitalOceanSpacesStorageService : IFileStorageService
{
    private readonly AmazonS3Client _s3Client;
    private readonly DigitalOceanSpacesSettings _settings;
    private readonly ILogger<DigitalOceanSpacesStorageService> _logger;
    private readonly IMetricsCollector _metricsCollector;

    public DigitalOceanSpacesStorageService(
        IOptions<DigitalOceanSpacesSettings> settings,
        ILogger<DigitalOceanSpacesStorageService> logger,
        IMetricsCollector metricsCollector)
    {
        _settings = settings.Value;
        _logger = logger;
        _metricsCollector = metricsCollector;
        
        var config = new AmazonS3Config
        {
            ServiceURL = _settings.ServiceUrl,
            ForcePathStyle = true, // Required for DigitalOcean Spaces
            UseHttp = !_settings.UseHttps,
            Timeout = TimeSpan.FromMinutes(_settings.UploadTimeoutMinutes),
            MaxErrorRetry = _settings.MaxRetryAttempts
        };

        _s3Client = new AmazonS3Client(_settings.AccessKey, _settings.SecretKey, config);
    }

    public async Task<Result<FileMetadata>> UploadFileAsync(
        IFormFile file,
        string containerName,
        string? fileName = null)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (file == null || file.Length == 0)
            {
                return Result.Failure<FileMetadata>(Error.BadRequest("File is empty or null"));
            }

            // Generate unique file name if not provided
            var storedFileName = fileName ?? GenerateUniqueFileName(file.FileName);
            var key = BuildObjectKey(containerName, storedFileName);

            // Prepare upload request
            var request = new PutObjectRequest
            {
                BucketName = _settings.BucketName,
                Key = key,
                InputStream = file.OpenReadStream(),
                ContentType = file.ContentType,
                CannedACL = S3CannedACL.FindValue(_settings.DefaultAcl),
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                Metadata =
                {
                    ["original-filename"] = file.FileName,
                    ["upload-timestamp"] = DateTime.UtcNow.ToString("O"),
                    ["file-size"] = file.Length.ToString()
                }
            };

            // Upload with retry logic
            var response = await ExecuteWithRetryAsync(async () =>
            {
                return await _s3Client.PutObjectAsync(request);
            });

            if (response.HttpStatusCode != HttpStatusCode.OK)
            {
                return Result.Failure<FileMetadata>(
                    Error.Internal($"Failed to upload file. Status: {response.HttpStatusCode}"));
            }

            // Generate URLs
            var fileUrl = GenerateFileUrl(key);
            var cdnUrl = GenerateCdnUrl(key);

            var metadata = new FileMetadata
            {
                OriginalFileName = file.FileName,
                StoredFileName = storedFileName,
                FilePath = key, // Use S3 key as file path
                RelativePath = $"/{key}",
                FileUrl = cdnUrl, // Use CDN URL for better performance
                FileSize = file.Length,
                ContentType = file.ContentType,
                UploadedAt = DateTime.UtcNow
            };

            // Record metrics
            stopwatch.Stop();
            _metricsCollector.RecordFileUpload(file.Length, stopwatch.ElapsedMilliseconds, containerName);

            _logger.LogInformation("File uploaded successfully to DigitalOcean Spaces: {Key}", key);
            return Result.Success(metadata);
        }
        catch (AmazonS3Exception ex)
        {
            _logger.LogError(ex, "S3 error uploading file {FileName}: {ErrorCode} - {ErrorMessage}", 
                file.FileName, ex.ErrorCode, ex.Message);
            return Result.Failure<FileMetadata>(
                Error.Internal($"Storage service error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName}", file.FileName);
            return Result.Failure<FileMetadata>(
                Error.Internal("An error occurred while uploading the file"));
        }
    }

    public async Task<Result<bool>> DeleteFileAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                return Result.Failure<bool>(Error.BadRequest("File path is empty or null"));
            }

            // Extract key from file path
            var key = ExtractKeyFromPath(filePath);

            // Check if object exists
            var existsResult = await ObjectExistsAsync(key);
            if (!existsResult.IsSuccess)
            {
                return Result.Failure<bool>(existsResult.Error);
            }

            if (!existsResult.Value)
            {
                return Result.Failure<bool>(Error.NotFound("File not found"));
            }

            // Delete object with retry logic
            var deleteRequest = new DeleteObjectRequest
            {
                BucketName = _settings.BucketName,
                Key = key
            };

            await ExecuteWithRetryAsync(async () =>
            {
                return await _s3Client.DeleteObjectAsync(deleteRequest);
            });

            _metricsCollector.RecordFileDelete(key);
            _logger.LogInformation("File deleted successfully from DigitalOcean Spaces: {Key}", key);
            return Result.Success(true);
        }
        catch (AmazonS3Exception ex)
        {
            _logger.LogError(ex, "S3 error deleting file {FilePath}: {ErrorCode} - {ErrorMessage}", 
                filePath, ex.ErrorCode, ex.Message);
            return Result.Failure<bool>(
                Error.Internal($"Storage service error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FilePath}", filePath);
            return Result.Failure<bool>(
                Error.Internal("An error occurred while deleting the file"));
        }
    }

    public string GetFileUrl(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return string.Empty;
        }

        // If it's already a full URL, return it
        if (path.StartsWith("http://") || path.StartsWith("https://"))
        {
            return path;
        }

        // Extract key and generate CDN URL
        var key = ExtractKeyFromPath(path);
        return GenerateCdnUrl(key);
    }

    public Result<bool> ValidateFile(IFormFile file, int maxSizeInMb, string[] allowedExtensions)
    {
        if (file == null || file.Length == 0)
        {
            return Result.Failure<bool>(Error.BadRequest("File is empty or null"));
        }

        // Check file size
        var maxSizeInBytes = maxSizeInMb * 1024 * 1024;
        if (file.Length > maxSizeInBytes)
        {
            return Result.Failure<bool>(
                Error.BadRequest($"File size exceeds the maximum allowed size of {maxSizeInMb}MB"));
        }

        // Check file extension
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
        {
            return Result.Failure<bool>(
                Error.BadRequest(
                    $"File type {fileExtension} is not allowed. Allowed types: {string.Join(", ", allowedExtensions)}"));
        }

        return Result.Success(true);
    }

    // Additional methods for DigitalOcean Spaces specific functionality
    public async Task<Result<bool>> ObjectExistsAsync(string key)
    {
        try
        {
            var request = new GetObjectMetadataRequest
            {
                BucketName = _settings.BucketName,
                Key = key
            };

            await _s3Client.GetObjectMetadataAsync(request);
            return Result.Success(true);
        }
        catch (AmazonS3Exception ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return Result.Success(false);
        }
        catch (AmazonS3Exception ex)
        {
            _logger.LogError(ex, "S3 error checking object existence {Key}: {ErrorCode}", key, ex.ErrorCode);
            return Result.Failure<bool>(Error.Internal($"Error checking file existence: {ex.Message}"));
        }
    }

    public async Task<Result<Stream>> GetFileStreamAsync(string key)
    {
        try
        {
            var request = new GetObjectRequest
            {
                BucketName = _settings.BucketName,
                Key = key
            };

            var response = await _s3Client.GetObjectAsync(request);
            return Result.Success(response.ResponseStream);
        }
        catch (AmazonS3Exception ex)
        {
            _logger.LogError(ex, "S3 error getting file stream {Key}: {ErrorCode}", key, ex.ErrorCode);
            return Result.Failure<Stream>(Error.Internal($"Error retrieving file: {ex.Message}"));
        }
    }

    private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation)
    {
        var retryCount = 0;
        var delay = TimeSpan.FromSeconds(1);

        while (retryCount < _settings.MaxRetryAttempts)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (retryCount < _settings.MaxRetryAttempts - 1 && IsRetryableException(ex))
            {
                retryCount++;
                _logger.LogWarning(ex, "Retrying operation (attempt {RetryCount}/{MaxRetries}) after {Delay}ms", 
                    retryCount, _settings.MaxRetryAttempts, delay.TotalMilliseconds);
                
                await Task.Delay(delay);
                delay = TimeSpan.FromMilliseconds(delay.TotalMilliseconds * 2); // Exponential backoff
            }
        }

        // Final attempt without catch
        return await operation();
    }

    private static bool IsRetryableException(Exception ex)
    {
        return ex is AmazonS3Exception s3Ex && 
               (s3Ex.StatusCode == HttpStatusCode.InternalServerError ||
                s3Ex.StatusCode == HttpStatusCode.BadGateway ||
                s3Ex.StatusCode == HttpStatusCode.ServiceUnavailable ||
                s3Ex.StatusCode == HttpStatusCode.GatewayTimeout);
    }

    private string GenerateUniqueFileName(string originalFileName)
    {
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
        var fileExtension = Path.GetExtension(originalFileName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");
        var randomPart = Guid.NewGuid().ToString("N")[..8];

        return $"{SanitizeFileName(fileNameWithoutExtension)}_{timestamp}_{randomPart}{fileExtension}";
    }

    private static string SanitizeFileName(string fileName)
    {
        // Remove invalid characters for S3 keys
        var invalidChars = new char[] { '\\', '/', ':', '*', '?', '"', '<', '>', '|' };
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        
        // Replace spaces with underscores
        sanitized = sanitized.Replace(" ", "_");
        
        // Remove any other potentially problematic characters
        sanitized = Regex.Replace(sanitized, @"[^\w\-\.]", "");
        
        return sanitized;
    }

    private static string BuildObjectKey(string containerName, string fileName)
    {
        // Build S3 object key with proper path structure
        var sanitizedContainer = containerName.Replace("\\", "/").Trim('/');
        return $"{sanitizedContainer}/{fileName}";
    }

    private string ExtractKeyFromPath(string path)
    {
        // Handle different path formats and extract S3 key
        if (path.StartsWith("http://") || path.StartsWith("https://"))
        {
            var uri = new Uri(path);
            return uri.AbsolutePath.TrimStart('/');
        }

        return path.TrimStart('/');
    }

    private string GenerateFileUrl(string key)
    {
        return $"{_settings.ServiceUrl}/{_settings.BucketName}/{key}";
    }

    private string GenerateCdnUrl(string key)
    {
        if (string.IsNullOrEmpty(_settings.CdnUrl))
        {
            return GenerateFileUrl(key);
        }

        return $"{_settings.CdnUrl.TrimEnd('/')}/{key}";
    }

    public void Dispose()
    {
        _s3Client?.Dispose();
    }
}
```

### Metrics Collection Service

```csharp
public interface IMetricsCollector
{
    void RecordFileUpload(long fileSize, long durationMs, string containerName);
    void RecordFileDelete(string key);
    void RecordFileDownload(string key, long durationMs);
    void RecordError(string operation, string errorType);
}

public class MetricsCollector : IMetricsCollector
{
    private readonly ILogger<MetricsCollector> _logger;

    public MetricsCollector(ILogger<MetricsCollector> logger)
    {
        _logger = logger;
    }

    public void RecordFileUpload(long fileSize, long durationMs, string containerName)
    {
        _logger.LogInformation("File upload metrics: Size={FileSize}bytes, Duration={Duration}ms, Container={Container}",
            fileSize, durationMs, containerName);
        
        // Here you could integrate with metrics services like Prometheus, DataDog, etc.
    }

    public void RecordFileDelete(string key)
    {
        _logger.LogInformation("File deleted: Key={Key}", key);
    }

    public void RecordFileDownload(string key, long durationMs)
    {
        _logger.LogInformation("File download metrics: Key={Key}, Duration={Duration}ms", key, durationMs);
    }

    public void RecordError(string operation, string errorType)
    {
        _logger.LogError("File storage error: Operation={Operation}, ErrorType={ErrorType}", operation, errorType);
    }
}
```

### Migration Service

```csharp
public interface IFileMigrationService
{
    Task<Result<MigrationReport>> MigrateAllFilesAsync(CancellationToken cancellationToken = default);
    Task<Result<MigrationReport>> MigrateFilesByContainerAsync(string containerName, CancellationToken cancellationToken = default);
    Task<Result<bool>> ValidateMigrationAsync(CancellationToken cancellationToken = default);
}

public class FileMigrationService : IFileMigrationService
{
    private readonly ApplicationDbContext _context;
    private readonly IFileStorageService _newStorageService;
    private readonly ILogger<FileMigrationService> _logger;
    private readonly string _localStoragePath;

    public FileMigrationService(
        ApplicationDbContext context,
        IFileStorageService newStorageService,
        ILogger<FileMigrationService> logger,
        IWebHostEnvironment environment)
    {
        _context = context;
        _newStorageService = newStorageService;
        _logger = logger;
        _localStoragePath = Path.Combine(environment.WebRootPath, "uploads");
    }

    public async Task<Result<MigrationReport>> MigrateAllFilesAsync(CancellationToken cancellationToken = default)
    {
        var report = new MigrationReport();
        
        try
        {
            // Get all documents with file paths
            var documents = await _context.Documents
                .Where(d => !d.IsDeleted && !string.IsNullOrEmpty(d.DocumentUrl))
                .ToListAsync(cancellationToken);

            _logger.LogInformation("Starting migration of {DocumentCount} documents", documents.Count);

            foreach (var document in documents)
            {
                try
                {
                    var migrationResult = await MigrateDocumentAsync(document, cancellationToken);
                    if (migrationResult.IsSuccess)
                    {
                        report.SuccessfulMigrations++;
                    }
                    else
                    {
                        report.FailedMigrations++;
                        report.Errors.Add($"Document {document.Id}: {migrationResult.Error.Message}");
                    }
                }
                catch (Exception ex)
                {
                    report.FailedMigrations++;
                    report.Errors.Add($"Document {document.Id}: {ex.Message}");
                    _logger.LogError(ex, "Error migrating document {DocumentId}", document.Id);
                }
            }

            report.TotalFiles = documents.Count;
            report.CompletedAt = DateTime.UtcNow;

            _logger.LogInformation("Migration completed: {Successful} successful, {Failed} failed", 
                report.SuccessfulMigrations, report.FailedMigrations);

            return Result.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during file migration");
            return Result.Failure<MigrationReport>(Error.Internal("Migration failed"));
        }
    }

    public async Task<Result<MigrationReport>> MigrateFilesByContainerAsync(string containerName, CancellationToken cancellationToken = default)
    {
        var report = new MigrationReport();
        
        try
        {
            // Get documents for specific container (document type)
            var documents = await _context.Documents
                .Where(d => !d.IsDeleted && 
                           !string.IsNullOrEmpty(d.DocumentUrl) && 
                           d.DocumentType == containerName)
                .ToListAsync(cancellationToken);

            _logger.LogInformation("Starting migration of {DocumentCount} documents for container {Container}", 
                documents.Count, containerName);

            foreach (var document in documents)
            {
                var migrationResult = await MigrateDocumentAsync(document, cancellationToken);
                if (migrationResult.IsSuccess)
                {
                    report.SuccessfulMigrations++;
                }
                else
                {
                    report.FailedMigrations++;
                    report.Errors.Add($"Document {document.Id}: {migrationResult.Error.Message}");
                }
            }

            report.TotalFiles = documents.Count;
            report.CompletedAt = DateTime.UtcNow;

            return Result.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during container migration for {Container}", containerName);
            return Result.Failure<MigrationReport>(Error.Internal($"Container migration failed: {ex.Message}"));
        }
    }

    public async Task<Result<bool>> ValidateMigrationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var documents = await _context.Documents
                .Where(d => !d.IsDeleted && !string.IsNullOrEmpty(d.DocumentUrl))
                .ToListAsync(cancellationToken);

            var validationErrors = new List<string>();

            foreach (var document in documents)
            {
                // Check if file exists in new storage
                var key = ExtractKeyFromDocumentUrl(document.DocumentUrl);
                if (_newStorageService is DigitalOceanSpacesStorageService spacesService)
                {
                    var existsResult = await spacesService.ObjectExistsAsync(key);
                    if (!existsResult.IsSuccess || !existsResult.Value)
                    {
                        validationErrors.Add($"Document {document.Id}: File not found in DigitalOcean Spaces");
                    }
                }
            }

            if (validationErrors.Any())
            {
                _logger.LogWarning("Migration validation found {ErrorCount} issues", validationErrors.Count);
                foreach (var error in validationErrors.Take(10)) // Log first 10 errors
                {
                    _logger.LogWarning("Validation error: {Error}", error);
                }
                return Result.Failure<bool>(Error.Validation($"Found {validationErrors.Count} validation errors"));
            }

            _logger.LogInformation("Migration validation successful for {DocumentCount} documents", documents.Count);
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during migration validation");
            return Result.Failure<bool>(Error.Internal("Validation failed"));
        }
    }

    private async Task<Result<bool>> MigrateDocumentAsync(Document document, CancellationToken cancellationToken)
    {
        try
        {
            // Build local file path
            var localFilePath = Path.Combine(_localStoragePath, document.DocumentUrl.TrimStart('/'));
            
            if (!File.Exists(localFilePath))
            {
                return Result.Failure<bool>(Error.NotFound($"Local file not found: {localFilePath}"));
            }

            // Create form file from local file
            using var fileStream = new FileStream(localFilePath, FileMode.Open, FileAccess.Read);
            var fileName = Path.GetFileName(localFilePath);
            var contentType = GetContentType(fileName);
            
            var formFile = new FormFile(fileStream, 0, fileStream.Length, "file", fileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = contentType
            };

            // Determine container name
            var containerName = $"documents/{document.DocumentType}/{document.UserId}";

            // Upload to new storage
            var uploadResult = await _newStorageService.UploadFileAsync(formFile, containerName, fileName);
            if (!uploadResult.IsSuccess)
            {
                return Result.Failure<bool>(uploadResult.Error);
            }

            // Update document URL in database
            document.DocumentUrl = uploadResult.Value.RelativePath;
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully migrated document {DocumentId} from {LocalPath} to {NewPath}", 
                document.Id, localFilePath, uploadResult.Value.RelativePath);

            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error migrating document {DocumentId}", document.Id);
            return Result.Failure<bool>(Error.Internal($"Migration failed: {ex.Message}"));
        }
    }

    private static string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".pdf" => "application/pdf",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".tiff" => "image/tiff",
            _ => "application/octet-stream"
        };
    }

    private static string ExtractKeyFromDocumentUrl(string documentUrl)
    {
        return documentUrl.TrimStart('/');
    }
}

public class MigrationReport
{
    public int TotalFiles { get; set; }
    public int SuccessfulMigrations { get; set; }
    public int FailedMigrations { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; }
    
    public TimeSpan Duration => CompletedAt?.Subtract(StartedAt) ?? TimeSpan.Zero;
    public decimal SuccessRate => TotalFiles > 0 ? (decimal)SuccessfulMigrations / TotalFiles * 100 : 0;
}
```

### Dependency Injection Configuration

```csharp
public static class DigitalOceanSpacesExtensions
{
    public static IServiceCollection AddDigitalOceanSpaces(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure settings
        services.Configure<DigitalOceanSpacesSettings>(
            configuration.GetSection("DigitalOceanSpaces"));

        // Register services
        services.AddScoped<IFileStorageService, DigitalOceanSpacesStorageService>();
        services.AddScoped<IMetricsCollector, MetricsCollector>();
        services.AddScoped<IFileMigrationService, FileMigrationService>();

        // Add AWS SDK services
        services.AddAWSService<IAmazonS3>();

        return services;
    }
}
```

### Health Checks

```csharp
public class DigitalOceanSpacesHealthCheck : IHealthCheck
{
    private readonly DigitalOceanSpacesStorageService _storageService;
    private readonly ILogger<DigitalOceanSpacesHealthCheck> _logger;

    public DigitalOceanSpacesHealthCheck(
        IFileStorageService storageService,
        ILogger<DigitalOceanSpacesHealthCheck> logger)
    {
        _storageService = (DigitalOceanSpacesStorageService)storageService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Test connectivity by checking if a test object exists
            var testKey = "health-check/test.txt";
            var existsResult = await _storageService.ObjectExistsAsync(testKey);
            
            if (existsResult.IsSuccess)
            {
                return HealthCheckResult.Healthy("DigitalOcean Spaces is accessible");
            }
            else
            {
                return HealthCheckResult.Degraded($"DigitalOcean Spaces check failed: {existsResult.Error.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DigitalOcean Spaces health check failed");
            return HealthCheckResult.Unhealthy("DigitalOcean Spaces is not accessible", ex);
        }
    }
}
```

## Error Handling

### Centralized Error Types

```csharp
public static class StorageErrors
{
    public static Error FileNotFound(string filePath) => 
        Error.NotFound($"File not found: {filePath}");
    
    public static Error UploadFailed(string reason) => 
        Error.Internal($"File upload failed: {reason}");
    
    public static Error DeleteFailed(string reason) => 
        Error.Internal($"File deletion failed: {reason}");
    
    public static Error InvalidConfiguration(string setting) => 
        Error.Configuration($"Invalid DigitalOcean Spaces configuration: {setting}");
    
    public static Error ServiceUnavailable() => 
        Error.External("DigitalOcean Spaces service is currently unavailable");
    
    public static Error QuotaExceeded() => 
        Error.BusinessRule("Storage quota exceeded");
    
    public static Error InvalidFileType(string fileType) => 
        Error.Validation($"File type {fileType} is not supported");
}
```

## Testing Strategy

### Unit Tests

1. **DigitalOceanSpacesStorageService Tests**
   - File upload scenarios with various file types and sizes
   - File deletion with existing and non-existing files
   - URL generation for different path formats
   - Error handling for network failures and S3 exceptions
   - Retry logic validation

2. **Migration Service Tests**
   - Document migration with valid and invalid local files
   - Database update verification
   - Error handling for missing files and database failures
   - Validation logic for migrated files

3. **Configuration Tests**
   - Settings validation with valid and invalid configurations
   - Environment variable loading
   - Default value handling

### Integration Tests

1. **End-to-End File Operations**
   - Complete upload, retrieve, and delete workflows
   - Large file handling and timeout scenarios
   - Concurrent operation handling

2. **Migration Integration**
   - Full migration process with real database
   - Validation of migrated files
   - Rollback scenarios

3. **Health Check Integration**
   - Service availability monitoring
   - Error condition handling

### Performance Tests

1. **Upload Performance**
   - Large file upload times
   - Concurrent upload handling
   - Memory usage during uploads

2. **CDN Performance**
   - File access speed through CDN
   - Cache behavior validation

## Security Considerations

### Access Control

1. **Credential Management**: Secure storage of access keys using environment variables
2. **IAM Policies**: Proper DigitalOcean Spaces permissions for read/write operations
3. **File Access**: Public read access for user-uploaded documents with proper ACLs
4. **Encryption**: Server-side encryption for all stored files

### Data Protection

1. **Backup Strategy**: Automatic backups through DigitalOcean Spaces
2. **Versioning**: Enable versioning for accidental deletion protection
3. **Access Logging**: Monitor file access patterns for security
4. **CORS Configuration**: Proper CORS settings for web application access

This design provides a comprehensive migration strategy that maintains compatibility while adding cloud storage benefits, proper error handling, monitoring capabilities, and a robust migration process.