using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.UnitOfWork;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Comprehensive unit tests for CareCategoryService covering all operations including edge cases and error scenarios
/// </summary>
public class CareCategoryServiceComprehensiveTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CareCategoryService _service;
    private readonly ICareCategoryRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CareCategoryServiceComprehensiveTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _repository = new CareCategoryRepository(_context);
        _unitOfWork = new UnitOfWork.UnitOfWork(_context);
        _service = new CareCategoryService(
            _repository,
            _context,
            _unitOfWork,
            NullLogger<CareCategoryService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region GetAllCategoriesAsync Tests

    [Fact]
    public async Task GetAllCategoriesAsync_WithActiveCategories_ShouldReturnOnlyActive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAllCategoriesAsync(includeInactive: false);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetAllCategoriesAsync_WithIncludeInactive_ShouldReturnAll()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAllCategoriesAsync(includeInactive: true);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());
    }

    [Fact]
    public async Task GetAllCategoriesAsync_WithDeletedCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAllCategoriesAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetAllCategoriesAsync_WithNoCategories_ShouldReturnEmptyList()
    {
        // Act
        var result = await _service.GetAllCategoriesAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Empty(result.Value);
    }

    #endregion

    #region GetPaginatedCategoriesAsync Tests

    [Fact]
    public async Task GetPaginatedCategoriesAsync_WithValidParameters_ShouldReturnPaginatedResults()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categories = Enumerable.Range(1, 5).Select(i => new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i:D2}",
            IsActive = true,
            PlatformFee = i * 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        }).ToArray();

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPaginatedCategoriesAsync(pageNumber: 1, pageSize: 3);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(3, result.Value.Categories.Count());
        Assert.Equal(5, result.Value.TotalCount);
        Assert.Equal(2, result.Value.TotalPages);
        Assert.Equal(1, result.Value.PageNumber);
        Assert.Equal(3, result.Value.PageSize);
    }

    [Fact]
    public async Task GetPaginatedCategoriesAsync_WithInvalidPageNumber_ShouldDefaultToPageOne()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPaginatedCategoriesAsync(pageNumber: -1, pageSize: 10);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(1, result.Value.PageNumber);
    }

    [Fact]
    public async Task GetPaginatedCategoriesAsync_WithInvalidPageSize_ShouldDefaultToTen()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPaginatedCategoriesAsync(pageNumber: 1, pageSize: -1);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(10, result.Value.PageSize);
    }

    [Fact]
    public async Task GetPaginatedCategoriesAsync_WithLargePageSize_ShouldCapAtHundred()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPaginatedCategoriesAsync(pageNumber: 1, pageSize: 150);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(100, result.Value.PageSize);
    }

    #endregion

    #region ValidateUniqueNameAsync Tests

    [Fact]
    public async Task ValidateUniqueNameAsync_WithUniqueName_ShouldReturnTrue()
    {
        // Arrange
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid()
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidateUniqueNameAsync("New Unique Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value); // Name is unique
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_WithDuplicateName_ShouldReturnFalse()
    {
        // Arrange
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid()
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidateUniqueNameAsync("Existing Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value); // Name is not unique
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_WithCaseInsensitiveDuplicate_ShouldReturnFalse()
    {
        // Arrange
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid()
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidateUniqueNameAsync("EXISTING CATEGORY");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value); // Name is not unique (case insensitive)
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_WithExcludeId_ShouldIgnoreExcludedCategory()
    {
        // Arrange
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid()
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidateUniqueNameAsync("Existing Category", existingCategory.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value); // Name is unique when excluding the existing category
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_WithEmptyName_ShouldReturnFailure()
    {
        // Act
        var result = await _service.ValidateUniqueNameAsync("");

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("Category name cannot be empty", result.Error.Message);
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_WithWhitespaceName_ShouldReturnFailure()
    {
        // Act
        var result = await _service.ValidateUniqueNameAsync("   ");

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("Category name cannot be empty", result.Error.Message);
    }

    [Fact]
    public async Task ValidateUniqueNameAsync_WithDeletedCategory_ShouldIgnoreDeleted()
    {
        // Arrange
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = Guid.NewGuid(),
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid()
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ValidateUniqueNameAsync("Deleted Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value); // Name is unique because deleted category is ignored
    }

    #endregion

    #region GetCategoryByIdAsync Tests

    [Fact]
    public async Task GetCategoryByIdAsync_WithValidId_ShouldReturnCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-heart",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoryByIdAsync(category.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(category.Id, result.Value.Id);
        Assert.Equal(category.Name, result.Value.Name);
        Assert.Equal(category.Description, result.Value.Description);
        Assert.Equal(category.IsActive, result.Value.IsActive);
        Assert.Equal(category.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(category.Icon, result.Value.Icon);
        Assert.Equal(category.Color, result.Value.Color);
    }

    [Fact]
    public async Task GetCategoryByIdAsync_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var result = await _service.GetCategoryByIdAsync(nonExistentId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task GetCategoryByIdAsync_WithDeletedCategory_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoryByIdAsync(deletedCategory.Id);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion
    
    #region CreateCategoryAsync Tests

    [Fact]
    public async Task CreateCategoryAsync_WithValidRequest_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateCareCategoryRequest
        {
            Name = "New Category",
            Description = "New Description",
            IsActive = true,
            PlatformFee = 25.00m,
            Icon = "fas fa-new",
            Color = "#123456"
        };

        // Act
        var result = await _service.CreateCategoryAsync(request, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(request.Name, result.Value.Name);
        Assert.Equal(request.Description, result.Value.Description);
        Assert.Equal(request.IsActive, result.Value.IsActive);
        Assert.Equal(request.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(request.Icon, result.Value.Icon);
        Assert.Equal(request.Color, result.Value.Color);

        // Verify in database
        var categoryInDb = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == result.Value.Id);
        Assert.NotNull(categoryInDb);
        Assert.Equal(userId, categoryInDb.CreatedBy);
        Assert.False(categoryInDb.IsDeleted);
    }

    [Fact]
    public async Task CreateCategoryAsync_WithDuplicateName_ShouldReturnConflict()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        var request = new CreateCareCategoryRequest
        {
            Name = "Existing Category",
            Description = "Duplicate name",
            IsActive = true,
            PlatformFee = 15.00m
        };

        // Act
        var result = await _service.CreateCategoryAsync(request, userId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("already exists", result.Error.Message);
    }

    [Fact]
    public async Task CreateCategoryAsync_WithCaseInsensitiveDuplicateName_ShouldReturnConflict()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        var request = new CreateCareCategoryRequest
        {
            Name = "EXISTING CATEGORY",
            Description = "Case insensitive duplicate",
            IsActive = true,
            PlatformFee = 15.00m
        };

        // Act
        var result = await _service.CreateCategoryAsync(request, userId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("already exists", result.Error.Message);
    }

    [Fact]
    public async Task CreateCategoryAsync_WithNullOptionalFields_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateCareCategoryRequest
        {
            Name = "Minimal Category",
            Description = null,
            IsActive = true,
            PlatformFee = 0m,
            Icon = null,
            Color = null
        };

        // Act
        var result = await _service.CreateCategoryAsync(request, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(request.Name, result.Value.Name);
        Assert.Null(result.Value.Description);
        Assert.Null(result.Value.Icon);
        Assert.Null(result.Value.Color);
    }

    #endregion

    #region UpdateCategoryAsync Tests

    [Fact]
    public async Task UpdateCategoryAsync_WithValidRequest_ShouldUpdateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-old",
            Color = "#000000",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Description = "Updated Description",
            IsActive = false,
            PlatformFee = 20.00m,
            Icon = "fas fa-new",
            Color = "#FFFFFF"
        };

        // Act
        var result = await _service.UpdateCategoryAsync(category.Id, updateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(updateRequest.Name, result.Value.Name);
        Assert.Equal(updateRequest.Description, result.Value.Description);
        Assert.Equal(updateRequest.IsActive, result.Value.IsActive);
        Assert.Equal(updateRequest.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(updateRequest.Icon, result.Value.Icon);
        Assert.Equal(updateRequest.Color, result.Value.Color);

        // Verify in database
        var updatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == category.Id);
        Assert.NotNull(updatedCategory);
        Assert.Equal(userId, updatedCategory.UpdatedBy);
        Assert.NotNull(updatedCategory.UpdatedAt);
    }

    [Fact]
    public async Task UpdateCategoryAsync_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category"
        };

        // Act
        var result = await _service.UpdateCategoryAsync(nonExistentId, updateRequest, Guid.NewGuid());

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task UpdateCategoryAsync_WithDuplicateName_ShouldReturnConflict()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Category 1" // Trying to rename category2 to category1's name
        };

        // Act
        var result = await _service.UpdateCategoryAsync(category2.Id, updateRequest, userId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("already exists", result.Error.Message);
    }

    [Fact]
    public async Task UpdateCategoryAsync_WithSameName_ShouldUpdateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Same Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Same Category", // Same name
            Description = "Updated Description"
        };

        // Act
        var result = await _service.UpdateCategoryAsync(category.Id, updateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("Updated Description", result.Value.Description);
    }

    [Fact]
    public async Task UpdateCategoryAsync_WithNullValues_ShouldKeepOriginalValues()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-original",
            Color = "#123456",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = null, // Should keep original
            Description = null, // Should keep original
            IsActive = null, // Should keep original
            PlatformFee = null, // Should keep original
            Icon = null, // Should keep original
            Color = null // Should keep original
        };

        // Act
        var result = await _service.UpdateCategoryAsync(category.Id, updateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("Original Category", result.Value.Name);
        Assert.Equal("Original Description", result.Value.Description);
        Assert.True(result.Value.IsActive);
        Assert.Equal(10.00m, result.Value.PlatformFee);
        Assert.Equal("fas fa-original", result.Value.Icon);
        Assert.Equal("#123456", result.Value.Color);
    }

    #endregion

    #region DeleteCategoryAsync Tests

    [Fact]
    public async Task DeleteCategoryAsync_WithValidId_ShouldSoftDeleteSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category to Delete",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.DeleteCategoryAsync(category.Id, userId);

        // Assert
        Assert.True(result.IsSuccess);

        // Verify soft delete in database
        var deletedCategory = await _context.CareCategories
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(c => c.Id == category.Id);
        Assert.NotNull(deletedCategory);
        Assert.True(deletedCategory.IsDeleted);
        Assert.NotNull(deletedCategory.DeletedAt);
        Assert.Equal(userId, deletedCategory.DeletedBy);

        // Verify category is not returned in normal queries
        var getResult = await _service.GetCategoryByIdAsync(category.Id);
        Assert.True(getResult.IsFailure);
    }

    [Fact]
    public async Task DeleteCategoryAsync_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var result = await _service.DeleteCategoryAsync(nonExistentId, Guid.NewGuid());

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task DeleteCategoryAsync_WithAlreadyDeletedCategory_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Already Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.DeleteCategoryAsync(deletedCategory.Id, userId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion

    #region GetCategoriesByProviderIdAsync Tests

    [Fact]
    public async Task GetCategoriesByProviderIdAsync_WithValidProviderId_ShouldReturnProviderCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-provider1",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = "fas fa-provider2",
            Color = "#33FF57",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategory1 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category1.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            ProviderSpecificDescription = "Provider specific description 1"
        };

        var providerCategory2 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category2.Id,
            HourlyRate = 60.00m,
            ExperienceYears = 3,
            ProviderSpecificDescription = "Provider specific description 2"
        };

        _context.CareCategories.AddRange(category1, category2);
        _context.CareProviderCategories.AddRange(providerCategory1, providerCategory2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoriesByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());

        var providerCategories = result.Value.ToList();
        var providerCat1 = providerCategories.First(c => c.Name == "Provider Category 1");
        Assert.Equal(50.00m, providerCat1.HourlyRate);
        Assert.Equal(5, providerCat1.ExperienceYears);
        Assert.Equal("Provider specific description 1", providerCat1.Description);
        Assert.Equal("fas fa-provider1", providerCat1.Icon);
        Assert.Equal("#FF5733", providerCat1.Color);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdAsync_WithNonExistentProviderId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();

        // Act
        var result = await _service.GetCategoriesByProviderIdAsync(nonExistentProviderId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("No care categories found", result.Error.Message);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdAsync_WithZeroHourlyRate_ShouldExcludeCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Zero Rate Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 0m, // Zero hourly rate should be excluded
            ExperienceYears = 5
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoriesByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("No care categories found", result.Error.Message);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdAsync_WithDeletedProviderCategory_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(deletedProviderCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoriesByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("No care categories found", result.Error.Message);
    }

    #endregion
    
   #region BulkUpdateCategoriesAsync Tests

    [Fact]
    public async Task BulkUpdateCategoriesAsync_WithValidRequest_ShouldUpdateAllCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 1",
            Description = "Original Description 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 2",
            Description = "Original Description 2",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2);
        await _context.SaveChangesAsync();

        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = category1.Id,
                    Name = "Updated Category 1",
                    Description = "Updated Description 1",
                    IsActive = false,
                    PlatformFee = 20.00m
                },
                new CareCategoryUpdateItem
                {
                    Id = category2.Id,
                    Name = "Updated Category 2",
                    Description = "Updated Description 2",
                    IsActive = true,
                    PlatformFee = 25.00m
                }
            }
        };

        // Act
        var result = await _service.BulkUpdateCategoriesAsync(bulkUpdateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());

        var updatedCategories = result.Value.ToList();
        var updatedCat1 = updatedCategories.First(c => c.Id == category1.Id);
        Assert.Equal("Updated Category 1", updatedCat1.Name);
        Assert.Equal("Updated Description 1", updatedCat1.Description);
        Assert.False(updatedCat1.IsActive);
        Assert.Equal(20.00m, updatedCat1.PlatformFee);

        var updatedCat2 = updatedCategories.First(c => c.Id == category2.Id);
        Assert.Equal("Updated Category 2", updatedCat2.Name);
        Assert.Equal("Updated Description 2", updatedCat2.Description);
        Assert.True(updatedCat2.IsActive);
        Assert.Equal(25.00m, updatedCat2.PlatformFee);
    }

    [Fact]
    public async Task BulkUpdateCategoriesAsync_WithPartialUpdates_ShouldUpdateOnlyProvidedFields()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = category.Id,
                    Name = "Updated Name Only",
                    Description = null, // Should not update
                    IsActive = null, // Should not update
                    PlatformFee = null // Should not update
                }
            }
        };

        // Act
        var result = await _service.BulkUpdateCategoriesAsync(bulkUpdateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);

        var updatedCategory = result.Value.First();
        Assert.Equal("Updated Name Only", updatedCategory.Name);
        Assert.Equal("Original Description", updatedCategory.Description); // Unchanged
        Assert.True(updatedCategory.IsActive); // Unchanged
        Assert.Equal(10.00m, updatedCategory.PlatformFee); // Unchanged
    }

    [Fact]
    public async Task BulkUpdateCategoriesAsync_WithNonExistentCategory_ShouldReturnPartialSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        var nonExistentId = Guid.NewGuid();
        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = existingCategory.Id,
                    Name = "Updated Existing Category"
                },
                new CareCategoryUpdateItem
                {
                    Id = nonExistentId,
                    Name = "Non-existent Category"
                }
            }
        };

        // Act
        var result = await _service.BulkUpdateCategoriesAsync(bulkUpdateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess); // Partial success
        Assert.Single(result.Value); // Only one category updated
        Assert.Equal("Updated Existing Category", result.Value.First().Name);
    }

    [Fact]
    public async Task BulkUpdateCategoriesAsync_WithDuplicateNames_ShouldReturnPartialSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category3 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 3",
            IsActive = true,
            PlatformFee = 20.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2, category3);
        await _context.SaveChangesAsync();

        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = category1.Id,
                    Name = "Updated Category 1" // Valid update
                },
                new CareCategoryUpdateItem
                {
                    Id = category2.Id,
                    Name = "Category 3" // Duplicate name - should fail
                },
                new CareCategoryUpdateItem
                {
                    Id = category3.Id,
                    Name = "Updated Category 3" // Valid update
                }
            }
        };

        // Act
        var result = await _service.BulkUpdateCategoriesAsync(bulkUpdateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess); // Partial success
        Assert.Equal(2, result.Value.Count()); // Two categories updated successfully

        var updatedCategories = result.Value.ToList();
        Assert.Contains(updatedCategories, c => c.Name == "Updated Category 1");
        Assert.Contains(updatedCategories, c => c.Name == "Updated Category 3");
        Assert.DoesNotContain(updatedCategories, c => c.Id == category2.Id);
    }

    [Fact]
    public async Task BulkUpdateCategoriesAsync_WithAllFailures_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var nonExistentId1 = Guid.NewGuid();
        var nonExistentId2 = Guid.NewGuid();

        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = nonExistentId1,
                    Name = "Non-existent Category 1"
                },
                new CareCategoryUpdateItem
                {
                    Id = nonExistentId2,
                    Name = "Non-existent Category 2"
                }
            }
        };

        // Act
        var result = await _service.BulkUpdateCategoriesAsync(bulkUpdateRequest, userId);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("Bulk update failed", result.Error.Message);
    }

    [Fact]
    public async Task BulkUpdateCategoriesAsync_WithEmptyRequest_ShouldReturnEmptySuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>()
        };

        // Act
        var result = await _service.BulkUpdateCategoriesAsync(bulkUpdateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Empty(result.Value);
    }

    #endregion

}    
