﻿using System.Diagnostics;
using SuperCareApp.Application.Common.Interfaces.Mediator;

namespace SuperCareApp.Persistence.Behaviours;

/// <summary>
/// Pipeline behavior for monitoring the performance of request handlers.
/// </summary>
/// <typeparam name="TRequest">The type of request.</typeparam>
/// <typeparam name="TResponse">The type of response.</typeparam>
public class PerformanceMonitoringBehavior<TRequest, TResponse>
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<PerformanceMonitoringBehavior<TRequest, TResponse>> _logger;
    private readonly Stopwatch _timer;
    private readonly string _thresholdInMs;

    /// <summary>
    /// Initializes a new instance of the <see cref="PerformanceMonitoringBehavior{TRequest, TResponse}"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    /// <param name="configuration">The configuration.</param>
    public PerformanceMonitoringBehavior(
        ILogger<PerformanceMonitoringBehavior<TRequest, TResponse>> logger,
        IConfiguration configuration
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _timer = new Stopwatch();
        _thresholdInMs = configuration["PerformanceMonitoring:ThresholdInMs"] ?? "500";
    }

    /// <inheritdoc/>
    public async Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken
    )
    {
        _timer.Start();

        var response = await next();

        _timer.Stop();

        var elapsedMilliseconds = _timer.ElapsedMilliseconds;
        var threshold = int.Parse(_thresholdInMs);

        if (elapsedMilliseconds > threshold)
        {
            var requestName = typeof(TRequest).Name;
            _logger.LogWarning(
                "Long running request: {RequestName} ({ElapsedMilliseconds} milliseconds) - threshold: {Threshold} ms",
                requestName,
                elapsedMilliseconds,
                threshold
            );
        }

        return response;
    }
}

/// <summary>
/// Pipeline behavior for monitoring the performance of request handlers without response.
/// </summary>
/// <typeparam name="TRequest">The type of request.</typeparam>
public class PerformanceMonitoringBehavior<TRequest> : IPipelineBehavior<TRequest>
    where TRequest : IRequest
{
    private readonly ILogger<PerformanceMonitoringBehavior<TRequest>> _logger;
    private readonly Stopwatch _timer;
    private readonly string _thresholdInMs;

    /// <summary>
    /// Initializes a new instance of the <see cref="PerformanceMonitoringBehavior{TRequest}"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    /// <param name="configuration">The configuration.</param>
    public PerformanceMonitoringBehavior(
        ILogger<PerformanceMonitoringBehavior<TRequest>> logger,
        IConfiguration configuration
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _timer = new Stopwatch();
        _thresholdInMs = configuration["PerformanceMonitoring:ThresholdInMs"] ?? "500";
    }

    /// <inheritdoc/>
    public async Task Handle(
        TRequest request,
        RequestHandlerDelegate next,
        CancellationToken cancellationToken
    )
    {
        _timer.Start();

        await next();

        _timer.Stop();

        var elapsedMilliseconds = _timer.ElapsedMilliseconds;
        var threshold = int.Parse(_thresholdInMs);

        if (elapsedMilliseconds > threshold)
        {
            var requestName = typeof(TRequest).Name;
            _logger.LogWarning(
                "Long running request: {RequestName} ({ElapsedMilliseconds} milliseconds) - threshold: {Threshold} ms",
                requestName,
                elapsedMilliseconds,
                threshold
            );
        }
    }
}
