using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Entities.ValueObjects;
using SuperCareApp.Persistence.Services.Identity.Commands;

namespace SuperCareApp.Persistence.Services.Admin.Commands
{
    /// <summary>
    /// Command to update admin profile
    /// /// </summary>
    public record UpdateAdminProfileCommand(UpdateAdminProfileRequest Request, Guid AdminId)
        : ICommand<Result<UpdateAdminProfileResponse>>;

    /// <summary>
    /// Handler for the UpdateAdminProfileCommand
    /// </summary>
    internal sealed class UpdateAdminProfileCommandHandler
        : ICommandHandler<UpdateAdminProfileCommand, Result<UpdateAdminProfileResponse>>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IMediator _mediator;
        private readonly ILogger<UpdateAdminProfileCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateAdminProfileCommandHandler(
            ApplicationDbContext dbContext,
            IMediator mediator,
            ILogger<UpdateAdminProfileCommandHandler> logger
        )
        {
            _dbContext = dbContext;
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result<UpdateAdminProfileResponse>> Handle(
            UpdateAdminProfileCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                _logger.LogInformation(
                    "Starting admin profile update for user {AdminId}",
                    request.AdminId
                );

                // Get the admin user with profile
                var adminUser = await _dbContext
                    .Users.Include(u => u.UserProfile)
                    .FirstOrDefaultAsync(
                        u => u.Id == request.AdminId && !u.IsDeleted,
                        cancellationToken
                    );

                if (adminUser == null)
                {
                    return Result.Failure<UpdateAdminProfileResponse>(
                        Error.NotFound("Admin user not found")
                    );
                }

                // Check if email is being changed and if it's already taken
                if (request.Request.Email != adminUser.Email)
                {
                    var emailExists = await _dbContext.Users.AnyAsync(
                        u =>
                            u.Email == request.Request.Email
                            && u.Id != request.AdminId
                            && !u.IsDeleted,
                        cancellationToken
                    );

                    if (emailExists)
                    {
                        return Result.Failure<UpdateAdminProfileResponse>(
                            Error.Conflict("Email address is already in use by another user")
                        );
                    }

                    // Update email
                    adminUser.Email = request.Request.Email;
                    adminUser.UserName = request.Request.Email; // Keep username in sync with email
                    adminUser.NormalizedEmail = request.Request.Email.ToUpperInvariant();
                    adminUser.NormalizedUserName = request.Request.Email.ToUpperInvariant();
                }

                // Normalize phone number
                string? normalizedPhoneNumber = null;
                if (!string.IsNullOrWhiteSpace(request.Request.PhoneNumber))
                {
                    try
                    {
                        normalizedPhoneNumber = PhoneNumber.Create(
                            request.Request.PhoneNumber,
                            "US"
                        );
                    }
                    catch (FormatException ex)
                    {
                        _logger.LogError(
                            ex,
                            "Invalid phone number format for admin {AdminId}",
                            request.AdminId
                        );
                        return Result.Failure<UpdateAdminProfileResponse>(
                            Error.Validation("Invalid phone number format.")
                        );
                    }
                }

                // Update or create user profile
                if (adminUser.UserProfile == null)
                {
                    adminUser.UserProfile = new Domain.Entities.UserProfile
                    {
                        ApplicationUserId = request.AdminId,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = request.AdminId,
                    };
                    _dbContext.UserProfiles.Add(adminUser.UserProfile);
                }

                // Update profile fields
                adminUser.UserProfile.FirstName = request.Request.FirstName;
                adminUser.UserProfile.LastName = request.Request.LastName;
                adminUser.UserProfile.PhoneNumber = normalizedPhoneNumber;
                adminUser.UserProfile.Gender = request.Request.Gender;
                adminUser.UserProfile.UpdatedAt = DateTime.UtcNow;
                adminUser.UserProfile.UpdatedBy = request.AdminId;

                // Update admin user fields
                adminUser.UpdatedAt = DateTime.UtcNow;
                adminUser.UpdatedBy = request.AdminId;

                string? profilePictureUrl = null;

                // Handle profile picture upload if provided
                if (request.Request.ProfilePicture != null)
                {
                    try
                    {
                        var uploadCommand = new UploadProfilePictureCommand(
                            request.Request.ProfilePicture,
                            request.AdminId
                        );
                        var uploadResult = await _mediator.Send(uploadCommand, cancellationToken);

                        if (uploadResult.IsSuccess)
                        {
                            profilePictureUrl = uploadResult.Value.ImageUrl;
                            _logger.LogInformation(
                                "Profile picture uploaded successfully for admin {AdminId}",
                                request.AdminId
                            );
                        }
                        else
                        {
                            _logger.LogWarning(
                                "Failed to upload profile picture for admin {AdminId}: {Error}",
                                request.AdminId,
                                uploadResult.Error.Message
                            );
                            // Continue with profile update even if picture upload fails
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error uploading profile picture for admin {AdminId}",
                            request.AdminId
                        );
                        // Continue with profile update even if picture upload fails
                    }
                }
                else if (adminUser.UserProfile.ImagePath != null)
                {
                    // Use existing profile picture URL if no new picture is uploaded
                    profilePictureUrl = adminUser.UserProfile.ImagePath;
                }

                // Save changes
                await _dbContext.SaveChangesAsync(cancellationToken);

                _logger.LogInformation(
                    "Admin profile updated successfully for user {AdminId}",
                    request.AdminId
                );

                // Create response
                var response = new UpdateAdminProfileResponse
                {
                    UserId = adminUser.Id,
                    Name = $"{adminUser.UserProfile.FirstName} {adminUser.UserProfile.LastName}",
                    Email = adminUser.Email!,
                    PhoneNumber = adminUser.UserProfile.PhoneNumber ?? string.Empty,
                    Gender = adminUser.UserProfile.Gender ?? string.Empty,
                    ProfilePictureUrl = profilePictureUrl,
                    UpdatedAt = adminUser.UserProfile.UpdatedAt ?? DateTime.UtcNow,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating admin profile for user {AdminId}",
                    request.AdminId
                );
                return Result.Failure<UpdateAdminProfileResponse>(
                    Error.Internal("An error occurred while updating the admin profile")
                );
            }
        }
    }
}
