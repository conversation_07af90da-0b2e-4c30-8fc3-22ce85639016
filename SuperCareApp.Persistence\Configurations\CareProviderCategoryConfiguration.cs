﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class CareProviderCategoryConfiguration : IEntityTypeConfiguration<CareProviderCategory>
    {
        public void Configure(EntityTypeBuilder<CareProviderCategory> builder)
        {
            builder.HasKey(pc => pc.Id);

            builder
                .Property(p => p.ProviderSpecificDescription)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnType("varchar(500)")
                .IsRequired(false);

            builder.Property(pc => pc.HourlyRate).HasColumnType("decimal(18,6)");

            builder.Property(p => p.MaxHoursPerWeek).HasColumnType("int").HasDefaultValue(0);

            builder.Property(p => p.MinHoursPerWeek).HasColumnType("int").HasDefaultValue(0);

            builder.Property(p => p.ExperienceYears).HasColumnType("int").HasDefaultValue(0);

            // Relationships
            builder
                .HasOne(pc => pc.CareProviderProfile)
                .WithMany(p => p.CareProviderCategories)
                .HasForeignKey(pc => pc.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasOne(pc => pc.CareCategory)
                .WithMany(c => c.CareProviderCategories)
                .HasForeignKey(pc => pc.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint to prevent duplicate categories for a provider
            builder.HasIndex(pc => new { pc.ProviderId, pc.CategoryId }).IsUnique();
        }
    }
}
