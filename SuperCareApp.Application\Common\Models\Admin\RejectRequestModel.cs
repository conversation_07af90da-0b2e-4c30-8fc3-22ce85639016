﻿namespace SuperCareApp.Application.Common.Models.Admin;

/// <summary>
/// Model for rejecting an approval request
/// </summary>
public class RejectRequestModel
{
    /// <summary>
    /// Reason for rejecting the request
    /// </summary>
    public string RejectionReason { get; set; } = string.Empty;

    /// <summary>
    /// Optional notes about the rejection
    /// </summary>
    public string? Notes { get; set; }
}
