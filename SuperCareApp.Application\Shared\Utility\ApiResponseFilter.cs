using System.Net;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Shared.Utility
{
    /// <summary>
    /// Filter to automatically wrap all controller responses in a standardized API response format
    /// </summary>
    public class ApiResponseFilter : IActionFilter, IResultFilter
    {
        private readonly IWebHostEnvironment _env;

        public ApiResponseFilter(IWebHostEnvironment env)
        {
            _env = env;
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            // Check if the model state is valid
            if (!context.ModelState.IsValid)
            {
                // Collect all validation errors
                var validationErrors = context
                    .ModelState.Where(e => e.Value!.Errors.Count > 0)
                    .ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value!.Errors.Select(e => e.ErrorMessage).ToArray()
                    );

                // Create a detailed error message
                var errorMessage = "Validation failed";

                // Create API error response
                var response = new ApiResponseModel<object>(
                    ApiResponseStatusEnum.BadRequest,
                    errorMessage,
                    validationErrors
                );

                context.Result = new BadRequestObjectResult(response);
            }
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
            // Handle exceptions
            if (context.Exception != null && !context.ExceptionHandled)
            {
                var response = new ApiResponseModel<object>(
                    ApiResponseStatusEnum.InternalServerError,
                    _env.IsDevelopment()
                        ? context.Exception.Message
                        : "An unexpected error occurred. Please try again later.",
                    null
                );

                context.Result = new ObjectResult(response)
                {
                    StatusCode = (int)HttpStatusCode.InternalServerError,
                };

                context.ExceptionHandled = true;
            }
        }

        public void OnResultExecuting(ResultExecutingContext context)
        {
            // Skip if the result is already an ApiResponseModel (check more thoroughly)
            if (context.Result is ObjectResult objectResult)
            {
                // Check if the value is already an ApiResponseModel of any generic type
                if (
                    objectResult.Value != null
                    && objectResult.Value.GetType().IsGenericType
                    && objectResult.Value.GetType().GetGenericTypeDefinition()
                        == typeof(ApiResponseModel<>)
                )
                {
                    return;
                }
            }

            // Skip if the result is a file result or redirect
            if (
                context.Result is FileResult
                || context.Result is RedirectResult
                || context.Result is RedirectToActionResult
                || context.Result is RedirectToRouteResult
            )
            {
                return;
            }

            // Wrap the result in an ApiResponseModel
            if (context.Result is ObjectResult objResult)
            {
                // Check if the value is a Result or IResult, which should be handled by ResultActionFilter
                if (objResult.Value is Result || objResult.Value is IResult)
                {
                    return;
                }

                // Check if the value is already a PaginatedResponseModel or ApiResponseModel
                if (
                    objResult.Value != null
                    && (
                        objResult.Value.GetType().IsGenericType
                        && (
                            objResult.Value.GetType().GetGenericTypeDefinition()
                                == typeof(PaginatedResponseModel<>)
                            || objResult.Value.GetType().GetGenericTypeDefinition()
                                == typeof(ApiResponseModel<>)
                        )
                    )
                )
                {
                    return;
                }

                var statusCode = objResult.StatusCode ?? 200;
                var status = GetApiResponseStatus(statusCode);
                var message = GetDefaultMessage(status);

                var response = new ApiResponseModel<object>(status, message, objResult.Value);

                context.Result = new ObjectResult(response) { StatusCode = statusCode };
            }
            else if (context.Result is StatusCodeResult statusCodeResult)
            {
                var status = GetApiResponseStatus(statusCodeResult.StatusCode);
                var message = GetDefaultMessage(status);

                var response = new ApiResponseModel<object>(status, message, null);

                context.Result = new ObjectResult(response)
                {
                    StatusCode = statusCodeResult.StatusCode,
                };
            }
        }

        public void OnResultExecuted(ResultExecutedContext context)
        {
            // No action needed
        }

        private ApiResponseStatusEnum GetApiResponseStatus(int statusCode)
        {
            return statusCode switch
            {
                >= 200 and < 300 => ApiResponseStatusEnum.Success,
                400 => ApiResponseStatusEnum.BadRequest,
                401 => ApiResponseStatusEnum.Unauthorized,
                403 => ApiResponseStatusEnum.Forbidden,
                404 => ApiResponseStatusEnum.NotFound,
                >= 500 => ApiResponseStatusEnum.InternalServerError,
                _ => ApiResponseStatusEnum.Error,
            };
        }

        private string GetDefaultMessage(ApiResponseStatusEnum status)
        {
            return status switch
            {
                ApiResponseStatusEnum.Success => "The operation completed successfully.",
                ApiResponseStatusEnum.Warning => "The operation completed with warnings.",
                ApiResponseStatusEnum.Info => "Information response.",
                ApiResponseStatusEnum.Error => "The operation completed with errors.",
                ApiResponseStatusEnum.NotFound => "The requested resource was not found.",
                ApiResponseStatusEnum.BadRequest => "The request is invalid.",
                ApiResponseStatusEnum.Unauthorized => "Unauthorized access.",
                ApiResponseStatusEnum.Forbidden => "Access to the resource is forbidden.",
                ApiResponseStatusEnum.InternalServerError => "An internal server error occurred.",
                _ => string.Empty,
            };
        }
    }
}
