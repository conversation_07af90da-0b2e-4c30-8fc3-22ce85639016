﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class UpdateBookingRequest
    {
        public string? SpecialInstructions { get; set; }
        public List<UpdateBookingWindowRequest>? BookingWindows { get; set; } = [];
    }

    public class UpdateBookingWindowRequest
    {
        public DateTime BookingDate { get; set; }
        public TimeOnly StartTime { get; set; }
        public TimeOnly EndTime { get; set; }
    }

    public class UpdateBookingWindowRequestValidator : AbstractValidator<UpdateBookingWindowRequest>
    {
        public UpdateBookingWindowRequestValidator()
        {
            RuleFor(x => x.BookingDate)
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Booking date must be today or in the future.");

            RuleFor(x => x.EndTime)
                .GreaterThan(x => x.StartTime)
                .WithMessage("End time must be after start time.");
        }
    }

    public class UpdateBookingRequestValidator : AbstractValidator<UpdateBookingRequest>
    {
        public UpdateBookingRequestValidator()
        {
            RuleFor(x => x.SpecialInstructions)
                .MaximumLength(500)
                .WithMessage("Special instructions cannot exceed 500 characters.");

            RuleForEach(x => x.BookingWindows)
                .SetValidator(new UpdateBookingWindowRequestValidator());

            RuleFor(x => x.BookingWindows)
                .NotNull()
                .WithMessage("Booking windows are required.")
                .NotEmpty()
                .WithMessage("At least one booking window must be provided.");
        }
    }
}
