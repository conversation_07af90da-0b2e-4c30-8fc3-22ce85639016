using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Bookings;

public interface IRecommendationService
{
    Task InitializeEngineAsync(CancellationToken cancellationToken = default);

    Task<List<CareProviderProfile>> GetRecommendedProvidersAsync(
        double clientLat,
        double clientLon,
        string? requiredService,
        TimeOnly? requestedStart,
        TimeOnly? requestedEnd,
        double searchRadiusKm = 5,
        int topN = 10,
        CancellationToken cancellationToken = default
    );

    Task RefreshProviderDataAsync(CareProviderProfile provider);

    Task RemoveProviderAsync(Guid providerId);

    List<CareProviderProfile> GetRecommendations(
        double clientLat,
        double clientLon,
        string requiredService,
        TimeOnly requestedStart,
        TimeOnly requestedEnd
    );
}
