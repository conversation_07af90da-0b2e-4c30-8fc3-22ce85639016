{"developmentMode": {"defaultEnvironment": "Development", "defaultPorts": {"http": 5000, "https": 5001}, "availableEnvironments": ["Development", "Staging", "Testing", "Local"], "watchPatterns": ["**/*.cs", "**/*.json", "**/*.c<PERSON><PERSON>j", "**/*.razor", "**/*.cshtml"], "excludePatterns": ["**/bin/**", "**/obj/**", "**/node_modules/**", "**/.git/**", "**/artifacts/**"], "autoMigrate": true, "hotReload": true, "metricsEnabled": true, "debounceMs": 1000, "maxPortScanRange": 50, "healthCheckInterval": 30, "logLevel": "Information"}, "portRanges": {"development": {"start": 5000, "end": 5099}, "testing": {"start": 6000, "end": 6099}, "staging": {"start": 7000, "end": 7099}}, "environmentSettings": {"Development": {"enableSwagger": true, "enableDetailedErrors": true, "enableSensitiveDataLogging": true, "corsOrigins": ["http://localhost:3000", "http://localhost:4200"]}, "Staging": {"enableSwagger": false, "enableDetailedErrors": false, "enableSensitiveDataLogging": false, "corsOrigins": ["https://staging.supercare.com"]}, "Testing": {"enableSwagger": true, "enableDetailedErrors": true, "enableSensitiveDataLogging": false, "corsOrigins": ["*"]}}}