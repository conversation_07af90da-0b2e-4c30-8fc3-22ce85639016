﻿using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Application.Common.Interfaces.Bookings;

public interface IPaymentStore
{
    Task<InMemoryPayment> CreateAsync(InMemoryPayment payment);
    Task<List<InMemoryPayment>> GetByProviderAsync(Guid providerId);
    Task<InMemoryPayment?> GetByIdAsync(Guid paymentId);
    Task<List<InMemoryPayment>> GetAllAsync();
    Task<IEnumerable<PaymentMethodResponse>> GetPaymentMethodsForUserAsync(Guid userId);
    Task<PaymentMethodResponse> AddPaymentMethodAsync(Guid userId, PaymentMethodRequest request);
}
