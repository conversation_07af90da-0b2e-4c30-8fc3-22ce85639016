using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Categories.Commands;

public record UpdateCareProviderCategoryCommand(
    Guid ProviderId,
    Guid CategoryId,
    UpdateCareCategoryForProviderRequest Request
) : ICommand<Result>;

public sealed class UpdateCareProviderCategoryCommandHandler
    : ICommandHandler<UpdateCareProviderCategoryCommand, Result>
{
    private readonly ICurrentUserService _currentUserService;
    private readonly ApplicationDbContext _context;

    public UpdateCareProviderCategoryCommandHandler(
        ICurrentUserService currentUserService,
        ApplicationDbContext context
    )
    {
        _currentUserService = currentUserService;
        _context = context;
    }

    public async Task<Result> Handle(
        UpdateCareProviderCategoryCommand request,
        CancellationToken cancellationToken
    )
    {
        // 1. Validate the request
        if (request.Request.HourlyRate is < 0)
            return Result.Failure(Error.BadRequest("Hourly rate cannot be negative."));

        // Use explicit transaction for better control
        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            // 2. Check if the provider-category relationship exists
            var providerCategory = await _context.CareProviderCategories.FirstOrDefaultAsync(
                x =>
                    x.ProviderId == request.ProviderId
                    && x.CategoryId == request.CategoryId
                    && !x.IsDeleted,
                cancellationToken
            );

            var currentUserId = _currentUserService.UserId!.Value;
            var currentTime = DateTime.UtcNow;

            if (providerCategory == null)
            {
                // Create a new provider category if it doesn't exist
                providerCategory = new CareProviderCategory
                {
                    ProviderId = request.ProviderId,
                    CategoryId = request.CategoryId,
                    CreatedAt = currentTime,
                    CreatedBy = currentUserId,
                    ProviderSpecificDescription = request.Request.ProviderSpecificDescription,
                    HourlyRate = request.Request.HourlyRate ?? 0,
                    ExperienceYears = request.Request.ExperienceLevel,
                };
                _context.CareProviderCategories.Add(providerCategory);
            }
            else
            {
                // Update existing fields
                if (request.Request.ProviderSpecificDescription is not null)
                    providerCategory.ProviderSpecificDescription = request
                        .Request
                        .ProviderSpecificDescription;

                if (request.Request.HourlyRate.HasValue)
                    providerCategory.HourlyRate = request.Request.HourlyRate.Value;

                if (request.Request.ExperienceLevel.HasValue)
                    providerCategory.ExperienceYears = request.Request.ExperienceLevel;

                // Update audit fields
                providerCategory.UpdatedAt = currentTime;
                providerCategory.UpdatedBy = currentUserId;
            }

            // Single save operation
            await _context.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            return Result.Success();
        }
        catch (DbUpdateException ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            return Result.Failure(
                Error.Internal($"Failed to update provider category: {ex.Message}")
            );
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            return Result.Failure(Error.Internal($"An unexpected error occurred: {ex.Message}"));
        }
    }
}
