﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities
{
    public class Payment : BaseEntity
    {
        public Guid InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string? TransactionId { get; set; }
        public PaymentStatus Status { get; set; }
        public DateTime PaymentDateTime { get; set; }
        public string? InvoiceNumber { get; set; }

        // Navigation properties
        public Invoice Invoice { get; set; } = null!;
    }
}
