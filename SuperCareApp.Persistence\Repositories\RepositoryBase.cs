﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using SuperCareApp.Application.Common.Interfaces.Persistence;

namespace SuperCareApp.Persistence.Repositories
{
    /// <summary>
    /// Base repository implementation for generic CRUD operations
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TContext">The database context type</typeparam>
    public class RepositoryBase<TEntity, TContext> : IRepositoryBase<TEntity>
        where TEntity : class
        where TContext : DbContext
    {
        /// <summary>
        /// The database context
        /// </summary>
        protected readonly TContext Context;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">The database context</param>
        public RepositoryBase(TContext context)
        {
            Context = context;
            Context.Set<TEntity>();
        }

        /// <summary>
        /// Gets the dataset expression
        /// </summary>
        /// <returns>The dataset expression</returns>
        public virtual Expression<Func<TContext, DbSet<TEntity>>> DataSet() => null!;

        /// <summary>
        /// Gets the key expression
        /// </summary>
        /// <returns>The key expression</returns>
        public virtual Expression<Func<TEntity, object>> Key() => null!;

        /// <summary>
        /// Gets all entities
        /// </summary>
        /// <returns>All entities</returns>
        public IEnumerable<TEntity> ListAll()
        {
            var entity = DataSet().Compile()(Context);

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                return entity
                    .OfType<BaseEntity>()
                    .Where(e => !e.IsDeleted)
                    .Cast<TEntity>()
                    .ToList();
            }

            return entity.ToList();
        }

        /// <summary>
        /// Gets all entities asynchronously
        /// </summary>
        /// <returns>All entities</returns>
        public async Task<IEnumerable<TEntity>> ListAllAsync()
        {
            var entity = DataSet().Compile()(Context);

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                var result = await entity.ToListAsync();
                return result
                    .OfType<BaseEntity>()
                    .Where(e => !e.IsDeleted)
                    .Cast<TEntity>()
                    .ToList();
            }

            return await entity.ToListAsync();
        }

        /// <summary>
        /// Gets entities based on specification
        /// </summary>
        /// <param name="specification">The specification to apply</param>
        /// <returns>Filtered entities</returns>
        public IReadOnlyList<TEntity> List(ISpecification<TEntity> specification)
        {
            var query = Context.Set<TEntity>().AsQueryable();

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                query = query
                    .OfType<BaseEntity>()
                    .Where(e => !e.IsDeleted)
                    .Cast<TEntity>()
                    .AsQueryable();
            }

            query = ApplySpecificationList(query, specification);
            return query.ToList();
        }

        /// <summary>
        /// Gets entities based on specification asynchronously
        /// </summary>
        /// <param name="specification">The specification to apply</param>
        /// <returns>Filtered entities</returns>
        public async Task<IReadOnlyList<TEntity>> ListAsync(ISpecification<TEntity> specification)
        {
            var query = Context.Set<TEntity>().AsQueryable();

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                query = query
                    .OfType<BaseEntity>()
                    .Where(e => !e.IsDeleted)
                    .Cast<TEntity>()
                    .AsQueryable();
            }

            query = ApplySpecificationList(query, specification);
            return await query.ToListAsync();
        }

        /// <summary>
        /// Gets an entity by its ID
        /// </summary>
        /// <param name="id">The entity ID</param>
        /// <returns>The entity or null if not found</returns>
        public TEntity? GetById(object id)
        {
            var entity = Context.Set<TEntity>().Find(id);

            // Check if entity is deleted if it implements BaseEntity
            if (entity != null && entity is BaseEntity baseEntity && baseEntity.IsDeleted)
            {
                return null; // Return null for deleted entities
            }

            return entity;
        }

        /// <summary>
        /// Gets an entity by its ID asynchronously
        /// </summary>
        /// <param name="id">The entity ID</param>
        /// <returns>The entity or null if not found</returns>
        public async Task<TEntity?> GetByIdAsync(object id)
        {
            var entity = await Context.Set<TEntity>().FindAsync(id);

            // Check if entity is deleted if it implements BaseEntity
            if (entity != null && entity is BaseEntity baseEntity && baseEntity.IsDeleted)
            {
                return null; // Return null for deleted entities
            }

            return entity;
        }

        /// <summary>
        /// Finds entities based on a predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>Filtered entities</returns>
        public IEnumerable<TEntity> Find(Expression<Func<TEntity, bool>> predicate)
        {
            var query = Context.Set<TEntity>().AsQueryable();

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                // We need to combine the predicates
                var parameter = Expression.Parameter(typeof(TEntity), "e");
                var notDeletedProperty = Expression.Property(
                    Expression.Convert(parameter, typeof(BaseEntity)),
                    "IsDeleted"
                );
                var notDeletedExpression = Expression.Not(notDeletedProperty);
                var combinedExpression = Expression.AndAlso(
                    Expression.Invoke(predicate, parameter),
                    notDeletedExpression
                );

                var lambda = Expression.Lambda<Func<TEntity, bool>>(combinedExpression, parameter);
                return query.Where(lambda).ToList();
            }

            return query.Where(predicate).ToList();
        }

        /// <summary>
        /// Finds entities based on a predicate asynchronously
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>Filtered entities</returns>
        public async Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate)
        {
            var query = Context.Set<TEntity>().AsQueryable();

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                // We need to combine the predicates
                var parameter = Expression.Parameter(typeof(TEntity), "e");
                var notDeletedProperty = Expression.Property(
                    Expression.Convert(parameter, typeof(BaseEntity)),
                    "IsDeleted"
                );
                var notDeletedExpression = Expression.Not(notDeletedProperty);
                var combinedExpression = Expression.AndAlso(
                    Expression.Invoke(predicate, parameter),
                    notDeletedExpression
                );

                var lambda = Expression.Lambda<Func<TEntity, bool>>(combinedExpression, parameter);
                return await query.Where(lambda).ToListAsync();
            }

            return await query.Where(predicate).ToListAsync();
        }

        /// <summary>
        /// Adds a new entity
        /// </summary>
        /// <param name="entity">The entity to add</param>
        /// <returns>The added entity</returns>
        public TEntity Add(TEntity entity)
        {
            var entityEntry = Context.Set<TEntity>().Add(entity);
            return entityEntry.Entity;
        }

        /// <summary>
        /// Adds a new entity asynchronously
        /// </summary>
        /// <param name="entity">The entity to add</param>
        /// <returns>The added entity</returns>
        public async Task<TEntity> AddAsync(TEntity entity)
        {
            var entityEntry = await Context.Set<TEntity>().AddAsync(entity);
            return entityEntry.Entity;
        }

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <returns>The updated entity</returns>
        public TEntity Update(TEntity entity)
        {
            Context.Entry(entity).State = EntityState.Modified;
            return entity;
        }

        /// <summary>
        /// Updates an existing entity asynchronously
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <returns>The updated entity</returns>
        public async Task<TEntity> UpdateAsync(TEntity entity)
        {
            Context.Entry(entity).State = EntityState.Modified;
            await Task.Yield();
            return entity;
        }

        /// <summary>
        /// Checks if an entity exists based on a predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>True if an entity exists, false otherwise</returns>
        public bool CheckExists(Expression<Func<TEntity, bool>> predicate)
        {
            var query = Context.Set<TEntity>().AsNoTracking();

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                // We need to combine the predicates
                var parameter = Expression.Parameter(typeof(TEntity), "e");
                var notDeletedProperty = Expression.Property(
                    Expression.Convert(parameter, typeof(BaseEntity)),
                    "IsDeleted"
                );
                var notDeletedExpression = Expression.Not(notDeletedProperty);
                var combinedExpression = Expression.AndAlso(
                    Expression.Invoke(predicate, parameter),
                    notDeletedExpression
                );

                var lambda = Expression.Lambda<Func<TEntity, bool>>(combinedExpression, parameter);
                return query.Any(lambda);
            }

            return query.Any(predicate);
        }

        /// <summary>
        /// Checks if an entity exists based on a predicate asynchronously
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>True if an entity exists, false otherwise</returns>
        public async Task<bool> CheckExistsAsync(Expression<Func<TEntity, bool>> predicate)
        {
            var query = Context.Set<TEntity>().AsNoTracking();

            // Filter out deleted entities if they implement BaseEntity
            if (typeof(BaseEntity).IsAssignableFrom(typeof(TEntity)))
            {
                // We need to combine the predicates
                var parameter = Expression.Parameter(typeof(TEntity), "e");
                var notDeletedProperty = Expression.Property(
                    Expression.Convert(parameter, typeof(BaseEntity)),
                    "IsDeleted"
                );
                var notDeletedExpression = Expression.Not(notDeletedProperty);
                var combinedExpression = Expression.AndAlso(
                    Expression.Invoke(predicate, parameter),
                    notDeletedExpression
                );

                var lambda = Expression.Lambda<Func<TEntity, bool>>(combinedExpression, parameter);
                return await query.AnyAsync(lambda);
            }

            return await query.AnyAsync(predicate);
        }

        /// <summary>
        /// Deletes an entity
        /// </summary>
        /// <param name="entity">The entity to delete</param>
        public void Delete(TEntity entity)
        {
            Context.Set<TEntity>().Remove(entity);
        }

        /// <summary>
        /// Deletes an entity asynchronously
        /// </summary>
        /// <param name="entity">The entity to delete</param>
        public async Task DeleteAsync(TEntity entity)
        {
            await Task.Run(() => Context.Set<TEntity>().Remove(entity));
        }

        /// <summary>
        /// Applies a specification to a query
        /// </summary>
        /// <param name="query">The query</param>
        /// <param name="specification">The specification</param>
        /// <returns>The filtered query</returns>
        private IQueryable<TEntity> ApplySpecificationList(
            IQueryable<TEntity> query,
            ISpecification<TEntity> specification
        )
        {
            if (specification == null)
                return query;
            if (specification.Criteria != null)
            {
                query = query.Where(specification.Criteria);
            }

            query = specification.Includes.Aggregate(
                query,
                (current, include) => current.Include(include)
            );

            query = specification.OrderingExpressions.Aggregate(
                query,
                (current, orderingExpression) =>
                    orderingExpression.Direction == OrderingDirection.Ascending
                        ? current.OrderBy(orderingExpression.OrderingKeySelector!)
                        : current.OrderByDescending(orderingExpression.OrderingKeySelector!)
            );

            if (specification.Take.HasValue)
            {
                query = query.Take(specification.Take.Value);
            }

            if (specification.Skip.HasValue)
            {
                query = query.Skip(specification.Skip.Value);
            }

            return query;
        }
    }
}
