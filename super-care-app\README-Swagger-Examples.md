# Swagger Examples and Best Practices

This document provides examples and best practices for documenting your API using Swagger/OpenAPI in the SuperCare application.

## Table of Contents

1. [Controller Documentation Examples](#controller-documentation-examples)
2. [Model Documentation Examples](#model-documentation-examples)
3. [Response Examples](#response-examples)
4. [Authentication Examples](#authentication-examples)
5. [Common Patterns](#common-patterns)
6. [Best Practices](#best-practices)

## Controller Documentation Examples

### Basic Controller Documentation

```csharp
/// <summary>
/// Manages user authentication and authorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[SwaggerTag("Authentication", "Endpoints for user authentication and authorization")]
public class AuthController : BaseController
{
    // Controller implementation
}
```

### Action Method Documentation

```csharp
/// <summary>
/// Authenticates a user and returns authentication tokens
/// </summary>
/// <param name="request">Login credentials</param>
/// <returns>Authentication tokens and expiration details</returns>
/// <response code="200">Authentication successful</response>
/// <response code="400">Invalid credentials or validation errors</response>
/// <response code="404">User not found</response>
[HttpPost("login")]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<AuthResponse>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Authenticates a user",
    Description = "Authenticates a user with email and password and returns JWT tokens",
    OperationId = "Auth_Login",
    Tags = new[] { "Authentication" }
)]
public async Task<IActionResult> Login(AuthRequest request)
{
    // Method implementation
}
```

### Grouping Endpoints with Tags

```csharp
[ApiController]
[Route("api/[controller]")]
public class UsersController : BaseController
{
    [HttpGet]
    [SwaggerOperation(Tags = new[] { "User Management" })]
    public async Task<IActionResult> GetUsers()
    {
        // Implementation
    }
    
    [HttpPost]
    [SwaggerOperation(Tags = new[] { "User Management" })]
    public async Task<IActionResult> CreateUser(CreateUserRequest request)
    {
        // Implementation
    }
    
    [HttpGet("profile")]
    [SwaggerOperation(Tags = new[] { "User Profile" })]
    public async Task<IActionResult> GetProfile()
    {
        // Implementation
    }
    
    [HttpPut("profile")]
    [SwaggerOperation(Tags = new[] { "User Profile" })]
    public async Task<IActionResult> UpdateProfile(UpdateProfileRequest request)
    {
        // Implementation
    }
}
```

### Documenting File Upload Endpoints

```csharp
/// <summary>
/// Uploads a profile picture
/// </summary>
/// <param name="file">The image file to upload (JPEG, PNG, or GIF)</param>
/// <returns>URL of the uploaded image</returns>
[HttpPost("profile-picture")]
[Consumes("multipart/form-data")]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<string>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Uploads a profile picture",
    Description = "Uploads a user profile picture. Supported formats: JPEG, PNG, GIF. Maximum size: 5MB.",
    OperationId = "Users_UploadProfilePicture",
    Tags = new[] { "User Profile" }
)]
public async Task<IActionResult> UploadProfilePicture(IFormFile file)
{
    // Implementation
}
```

## Model Documentation Examples

### Request Model Documentation

```csharp
/// <summary>
/// User registration request
/// </summary>
public class RegisterRequest
{
    /// <summary>
    /// Email address (must be unique)
    /// </summary>
    /// <example><EMAIL></example>
    [Required]
    [EmailAddress]
    public string Email { get; set; }
    
    /// <summary>
    /// Password (minimum 8 characters, must include uppercase, lowercase, number, and special character)
    /// </summary>
    /// <example>P@ssw0rd123</example>
    [Required]
    [MinLength(8)]
    public string Password { get; set; }
    
    /// <summary>
    /// Confirm password (must match Password)
    /// </summary>
    /// <example>P@ssw0rd123</example>
    [Required]
    [Compare("Password")]
    public string ConfirmPassword { get; set; }
    
    /// <summary>
    /// User's first name
    /// </summary>
    /// <example>John</example>
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; }
    
    /// <summary>
    /// User's last name
    /// </summary>
    /// <example>Doe</example>
    [Required]
    [MaxLength(100)]
    public string LastName { get; set; }
    
    /// <summary>
    /// User's phone number (optional)
    /// </summary>
    /// <example>+**********</example>
    [Phone]
    [MaxLength(20)]
    public string PhoneNumber { get; set; }
    
    /// <summary>
    /// Whether the user is registering as a care provider
    /// </summary>
    /// <example>true</example>
    public bool IsCareProvider { get; set; }
}
```

### Response Model Documentation

```csharp
/// <summary>
/// Authentication response containing tokens and expiration details
/// </summary>
public class AuthResponse
{
    /// <summary>
    /// JWT access token for API authentication
    /// </summary>
    /// <example>eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</example>
    public string AccessToken { get; set; }
    
    /// <summary>
    /// Refresh token for obtaining a new access token
    /// </summary>
    /// <example>eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...</example>
    public string RefreshToken { get; set; }
    
    /// <summary>
    /// Token expiration time in seconds
    /// </summary>
    /// <example>3600</example>
    public int ExpiresIn { get; set; }
    
    /// <summary>
    /// Token type (always "Bearer")
    /// </summary>
    /// <example>Bearer</example>
    public string TokenType { get; set; }
}
```

### Enum Documentation

```csharp
/// <summary>
/// User role types in the system
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum UserRoleType
{
    /// <summary>
    /// Standard user role
    /// </summary>
    [Description("Standard user")]
    Client = 0,
    
    /// <summary>
    /// Care provider role with additional permissions
    /// </summary>
    [Description("Care provider")]
    CareProvider = 1,
    
    /// <summary>
    /// Administrator role with full system access
    /// </summary>
    [Description("Administrator")]
    Admin = 2
}
```

## Response Examples

### Success Response Example

```csharp
public static string UserProfileSuccessExample()
{
    var userDto = new UserDto
    {
        Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
        Email = "<EMAIL>",
        EmailVerified = true,
        IsActive = true,
        LastLogin = DateTime.Parse("2023-05-15T10:30:00Z"),
        AuthProvider = "local",
        CreatedAt = DateTime.Parse("2023-01-01T00:00:00Z"),
        UpdatedAt = DateTime.Parse("2023-05-10T15:45:00Z"),
        Roles = new List<UserRoleType> { UserRoleType.Client },
        Profile = new UserProfileDto
        {
            Id = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d479"),
            UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "+**********",
            ProfilePicture = "https://example.com/profiles/johndoe.jpg",
            Address = "123 Main St",
            City = "New York",
            State = "NY",
            PostalCode = "10001",
            Country = "USA",
            Preferences = new Dictionary<string, object>
            {
                { "theme", "dark" },
                { "notifications", true }
            },
            CreatedAt = DateTime.Parse("2023-01-01T00:00:00Z"),
            UpdatedAt = DateTime.Parse("2023-05-10T15:45:00Z")
        }
    };

    var response = new ApiResponseModel<UserDto>(
        ApiResponseStatusEnum.Success,
        "User profile retrieved successfully",
        userDto
    );

    return SerializeToJson(response);
}
```

### Validation Error Example

```csharp
public static string ValidationErrorExample()
{
    var validationErrors = new Dictionary<string, string[]>
    {
        { "email", new[] { "Email is required", "Email format is invalid" } },
        { "password", new[] { "Password must be at least 8 characters", "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character" } }
    };

    var response = new ApiResponseModel<object>(
        ApiResponseStatusEnum.BadRequest,
        "Validation failed",
        validationErrors
    );

    return SerializeToJson(response);
}
```

### Not Found Error Example

```csharp
public static string NotFoundErrorExample()
{
    var response = new ApiResponseModel<object>(
        ApiResponseStatusEnum.NotFound,
        "The requested resource was not found",
        null
    );

    return SerializeToJson(response);
}
```

## Authentication Examples

### JWT Authentication Documentation

```csharp
// In SwaggerDocumentGenerator.cs
public void Configure(SwaggerGenOptions options)
{
    // Add JWT Authentication
    options.AddSecurityDefinition(
        "Bearer",
        new OpenApiSecurityScheme
        {
            Description =
                "JWT Authorization header using the Bearer scheme.\r\n\r\n" +
                "Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\n" +
                "Example: \"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer",
            BearerFormat = "JWT"
        }
    );

    options.AddSecurityRequirement(
        new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer",
                    },
                },
                Array.Empty<string>()
            },
        }
    );
}
```

### Documenting Secured Endpoints

```csharp
/// <summary>
/// Gets the current user's profile
/// </summary>
/// <returns>User profile information</returns>
[HttpGet("profile")]
[Authorize]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<UserProfileDto>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Gets the current user's profile",
    Description = "Retrieves the profile information for the currently authenticated user",
    OperationId = "Users_GetProfile",
    Tags = new[] { "User Profile" }
)]
[SwaggerResponse(StatusCodes.Status200OK, "Profile retrieved successfully", typeof(ApiResponseModel<UserProfileDto>))]
[SwaggerResponse(StatusCodes.Status401Unauthorized, "User is not authenticated", typeof(ApiResponseModel<object>))]
public async Task<IActionResult> GetProfile()
{
    // Implementation
}
```

## Common Patterns

### Pagination Documentation

```csharp
/// <summary>
/// Gets a list of care providers
/// </summary>
/// <param name="page">Page number (1-based)</param>
/// <param name="pageSize">Number of items per page</param>
/// <param name="sortBy">Field to sort by (e.g., "rating", "hourlyRate")</param>
/// <param name="sortDirection">Sort direction ("asc" or "desc")</param>
/// <returns>Paginated list of care providers</returns>
[HttpGet]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedResponseModel<List<CareProviderDto>>))]
[SwaggerOperation(
    Summary = "Gets a list of care providers",
    Description = "Retrieves a paginated list of care providers with optional sorting",
    OperationId = "CareProviders_GetList",
    Tags = new[] { "Care Providers" }
)]
public async Task<IActionResult> GetCareProviders(
    [FromQuery, SwaggerParameter("Page number (1-based)", Required = false)] int page = 1,
    [FromQuery, SwaggerParameter("Number of items per page", Required = false)] int pageSize = 10,
    [FromQuery, SwaggerParameter("Field to sort by", Required = false)] string sortBy = "rating",
    [FromQuery, SwaggerParameter("Sort direction", Required = false)] string sortDirection = "desc")
{
    // Implementation
}
```

### Filtering Documentation

```csharp
/// <summary>
/// Searches for care providers
/// </summary>
/// <param name="query">Search query</param>
/// <param name="category">Service category ID</param>
/// <param name="minRating">Minimum rating (1-5)</param>
/// <param name="maxHourlyRate">Maximum hourly rate</param>
/// <param name="availability">Availability date (ISO 8601 format)</param>
/// <param name="page">Page number (1-based)</param>
/// <param name="pageSize">Number of items per page</param>
/// <returns>Paginated list of matching care providers</returns>
[HttpGet("search")]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PaginatedResponseModel<List<CareProviderDto>>))]
[SwaggerOperation(
    Summary = "Searches for care providers",
    Description = "Searches for care providers based on various criteria",
    OperationId = "CareProviders_Search",
    Tags = new[] { "Care Providers" }
)]
public async Task<IActionResult> SearchCareProviders(
    [FromQuery, SwaggerParameter("Search query", Required = false)] string query = null,
    [FromQuery, SwaggerParameter("Service category ID", Required = false)] Guid? category = null,
    [FromQuery, SwaggerParameter("Minimum rating (1-5)", Required = false)] int? minRating = null,
    [FromQuery, SwaggerParameter("Maximum hourly rate", Required = false)] decimal? maxHourlyRate = null,
    [FromQuery, SwaggerParameter("Availability date (ISO 8601)", Required = false)] DateTime? availability = null,
    [FromQuery, SwaggerParameter("Page number (1-based)", Required = false)] int page = 1,
    [FromQuery, SwaggerParameter("Number of items per page", Required = false)] int pageSize = 10)
{
    // Implementation
}
```

### CRUD Operations Documentation

```csharp
/// <summary>
/// Creates a new booking
/// </summary>
/// <param name="request">Booking details</param>
/// <returns>Created booking information</returns>
[HttpPost]
[Authorize]
[ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponseModel<BookingDto>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Creates a new booking",
    Description = "Creates a new booking with the specified care provider",
    OperationId = "Bookings_Create",
    Tags = new[] { "Bookings" }
)]
public async Task<IActionResult> CreateBooking(CreateBookingRequest request)
{
    // Implementation
}

/// <summary>
/// Gets a booking by ID
/// </summary>
/// <param name="id">Booking ID</param>
/// <returns>Booking information</returns>
[HttpGet("{id}")]
[Authorize]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<BookingDto>))]
[ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Gets a booking by ID",
    Description = "Retrieves detailed information about a specific booking",
    OperationId = "Bookings_GetById",
    Tags = new[] { "Bookings" }
)]
public async Task<IActionResult> GetBooking(Guid id)
{
    // Implementation
}

/// <summary>
/// Updates a booking
/// </summary>
/// <param name="id">Booking ID</param>
/// <param name="request">Updated booking details</param>
/// <returns>Updated booking information</returns>
[HttpPut("{id}")]
[Authorize]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<BookingDto>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Updates a booking",
    Description = "Updates an existing booking with new details",
    OperationId = "Bookings_Update",
    Tags = new[] { "Bookings" }
)]
public async Task<IActionResult> UpdateBooking(Guid id, UpdateBookingRequest request)
{
    // Implementation
}

/// <summary>
/// Cancels a booking
/// </summary>
/// <param name="id">Booking ID</param>
/// <param name="request">Cancellation details</param>
/// <returns>Cancelled booking information</returns>
[HttpPost("{id}/cancel")]
[Authorize]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<BookingDto>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[SwaggerOperation(
    Summary = "Cancels a booking",
    Description = "Cancels an existing booking with optional cancellation reason",
    OperationId = "Bookings_Cancel",
    Tags = new[] { "Bookings" }
)]
public async Task<IActionResult> CancelBooking(Guid id, CancelBookingRequest request)
{
    // Implementation
}
```

## Best Practices

### 1. Consistent Naming

Use consistent naming conventions for:
- Operation IDs: `{Controller}_{Action}`
- Tags: Use meaningful categories
- Parameters: Use descriptive names
- Response types: Use consistent types

### 2. Detailed Descriptions

- Provide clear, concise descriptions for endpoints
- Explain what the endpoint does, not how it does it
- Include parameter constraints and validation rules
- Document all possible response status codes

### 3. Realistic Examples

- Provide realistic examples for request and response models
- Use example attributes to show expected values
- Include examples for error responses
- Keep examples up-to-date with the actual implementation

### 4. Proper Organization

- Group related endpoints with tags
- Use consistent tag names across controllers
- Order endpoints logically (e.g., CRUD operations)
- Separate public and admin endpoints

### 5. Complete Documentation

- Document all endpoints, parameters, and responses
- Include authentication requirements
- Document pagination, filtering, and sorting options
- Provide examples for complex scenarios

### 6. Validation Rules

- Document validation rules in property descriptions
- Include format requirements (e.g., email, phone)
- Document length constraints
- Explain business rules and constraints

### 7. Security Information

- Document authentication requirements
- Explain authorization rules
- Include rate limiting information
- Document sensitive data handling

### 8. Versioning Information

- Include version information in the documentation
- Document deprecated endpoints
- Provide migration guidance for deprecated features
- Use appropriate status codes for versioning errors

### 9. Error Handling

- Document all possible error responses
- Provide examples of error responses
- Explain error codes and messages
- Include troubleshooting information

### 10. Keep Documentation Updated

- Update documentation when the API changes
- Remove documentation for removed endpoints
- Mark deprecated endpoints appropriately
- Review and update examples regularly

## Conclusion

Following these examples and best practices will help you create comprehensive, user-friendly API documentation using Swagger/OpenAPI in the SuperCare application. Well-documented APIs are easier to use, reduce integration issues, and provide a better developer experience.
