using System.Text.RegularExpressions;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Shared.Utility
{
    public static class ValidationExtensions
    {
        private static readonly string EmailPattern =
            @"^([a-zA-Z0-9]+[!#$%&'*+\-/=?^_`{|}~]?([a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)?)?)@([a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)?)\.([a-zA-Z]{2,})$";
        private static readonly string PhonePattern = @"^\+?[0-9]{8,15}$";

        public static Result ValidateEmail(this string email)
        {
            if (string.IsNullOrEmpty(email))
                return Result.Failure(Error.BadRequest("Email is required"));

            if (!Regex.IsMatch(email, EmailPattern))
                return Result.Failure(Error.BadRequest("Invalid email format"));

            return Result.Success();
        }

        public static Result ValidatePhoneNumber(this string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return Result.Failure(Error.BadRequest("Phone number is required"));

            if (!Regex.IsMatch(phoneNumber, PhonePattern))
                return Result.Failure(Error.BadRequest("Invalid phone number format"));

            return Result.Success();
        }

        public static Result ValidatePassword(this string password)
        {
            if (string.IsNullOrEmpty(password))
                return Result.Failure(Error.BadRequest("Password is required"));

            if (password.Length < 6)
                return Result.Failure(Error.BadRequest("Password must be at least 6 characters"));

            return Result.Success();
        }

        public static Result ValidateName(this string name, string fieldName = "Name")
        {
            if (string.IsNullOrEmpty(name))
                return Result.Failure(Error.BadRequest($"{fieldName} is required"));

            if (name.Length < 2)
                return Result.Failure(
                    Error.BadRequest($"{fieldName} must be at least 2 characters")
                );

            return Result.Success();
        }
    }
}
