﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

public class UserNotificationConfiguration : IEntityTypeConfiguration<UserNotification>
{
    public void Configure(EntityTypeBuilder<UserNotification> builder)
    {
        builder.HasKey(un => un.Id);

        builder.Property(un => un.ApplicationUserId).IsRequired();

        builder.Property(un => un.IsRead).IsRequired().HasDefaultValue(false);

        // Configure the relationship with ApplicationUser
        builder
            .HasOne(un => un.ApplicationUser)
            .WithMany(u => u.UserNotifications)
            .HasForeignKey(un => un.ApplicationUserId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(un => un.Notification)
            .WithMany(n => n.UserNotifications)
            .HasForeignKey(un => un.NotificationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(un => new { un.ApplicationUserId, un.IsRead });
        builder.HasIndex(un => new { un.ApplicationUserId, un.CreatedAt });
    }
}
