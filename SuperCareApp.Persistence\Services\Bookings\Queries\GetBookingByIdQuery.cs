﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries
{
    public record GetBookingByIdQuery(Guid bookingId) : IRequest<Result<BookingResponse>>;

    public class GetBookingByIdQueryHandler
        : IRequestHandler<GetBookingByIdQuery, Result<BookingResponse>>
    {
        private readonly IBookingService _bookingService;

        public GetBookingByIdQueryHandler(IBookingService bookingService)
        {
            _bookingService = bookingService;
        }

        public Task<Result<BookingResponse>> Handle(
            GetBookingByIdQuery request,
            CancellationToken cancellationToken
        )
        {
            return _bookingService.GetBookingByIdAsync(request.bookingId);
        }
    }
}
