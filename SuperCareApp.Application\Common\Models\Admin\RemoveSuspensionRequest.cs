﻿using System.ComponentModel.DataAnnotations;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Request model for removing suspension from a care provider
    /// </summary>
    public class RemoveSuspensionRequest
    {
        /// <summary>
        /// Optional reason for removing the suspension
        /// </summary>
        public string? RemovalReason { get; set; }

        /// <summary>
        /// Optional additional notes
        /// </summary>
        public string? Notes { get; set; }
    }
}
