namespace SuperCareApp.Domain.Entities.ValueObjects;

public record Currency
{
    internal static Currency NONE = new("");
    public static Currency EUR = new("EUR");
    public static Currency USD = new("USD");
    public static Currency GBP = new("GBP");
    public static Currency CHF = new("CHF");
    public static Currency CAD = new("CAD");
    public static Currency AUD = new("AUD");
    public static Currency JPY = new("JPY");
    public static Currency CNY = new("CNY");
    public static Currency INR = new("INR");
    public static Currency KRW = new("KRW");
    public static Currency SGD = new("SGD");
    public static Currency THB = new("THB");
    public static Currency HKD = new("HKD");
    public static Currency MYR = new("MYR");

    public static implicit operator Currency(string code) => new(code);

    private Currency(string code) => Code = code;

    public string Code { get; init; }

    public static Currency FromCode(string code) =>
        All.FirstOrDefault(x => x.Code == code)
        ?? throw new ApplicationException("Invalid currency code");

    public static readonly IReadOnlyCollection<Currency> All = new[]
    {
        EUR,
        USD,
        GBP,
        CHF,
        CAD,
        AUD,
        JPY,
        CNY,
        INR,
        KRW,
        SGD,
        THB,
        HKD,
        MYR,
    };

    public override string ToString() => Code;
}
