namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class AvailabilityResponse
    {
        public Guid Id { get; set; }
        public string Day { get; set; } = string.Empty;
        public bool Available { get; set; }
        public List<TimeSlotResponse> Slots { get; set; } = new List<TimeSlotResponse>();
    }

    /// <summary>
    /// Enhanced availability response with monthly calendar view
    /// </summary>
    public class EnhancedAvailabilityResponse
    {
        public Dictionary<string, List<TimeSlotResponse>> AvailableSlots { get; set; } = new();
        public List<MonthlyAvailabilityResponse> AvailableDaysByMonth { get; set; } = new();
    }

    /// <summary>
    /// Monthly availability response
    /// </summary>
    public class MonthlyAvailabilityResponse
    {
        public string Month { get; set; } = string.Empty;
        public int Year { get; set; }
        public List<AvailableDayResponse> Days { get; set; } = new();
    }

    /// <summary>
    /// Available day response
    /// </summary>
    public class AvailableDayResponse
    {
        public string Date { get; set; } = string.Empty;
        public string DayOfWeek { get; set; } = string.Empty;
    }

    public class AvailabilityTemplateResponse
    {
        public IEnumerable<AvailabilityResponse> Availabilities { get; set; }
        public int BufferDuration { get; set; }
        public bool ProvidesRecurringBooking { get; set; }
        public int WorkingHoursPerDay { get; set; }
    }
}
