
# Real-Time Communication with Raw WebSockets

## 1. Overview

This document outlines the architecture and implementation strategy for integrating a real-time communication system into the Super Care App backend using raw WebSockets. This will enable features like instant notifications, a real-time chat system, and alerts for long-running background jobs.

### 1.1. Why Raw WebSockets?

While SignalR is a powerful framework for real-time web functionality in ASP.NET Core, it comes with a specific client/server protocol that can introduce complexity and potential vendor lock-in. For our goal of a flexible, multi-cloud architecture, using **raw WebSockets** provides several advantages:

- **Protocol Agnostic:** We have full control over the message format, allowing us to define a simple, lightweight, and efficient protocol (e.g., JSON-based).
- **Universal Client Support:** Any client (web, mobile, desktop) with a standard WebSocket library can connect without needing a specific SignalR client.
- **Lower Overhead:** We avoid the abstractions of SignalR, resulting in potentially better performance and a smaller footprint.
- **Explicit Control:** It forces a deeper understanding of the underlying mechanics, giving us fine-grained control over connection lifecycle, authentication, and message handling.

### 1.2. Intended Use Cases

- **User Notifications:** Instantly alert users about booking updates, payment confirmations, etc.
- **Real-Time Chat:** Facilitate private and group messaging between users (e.g., patients and care providers).
- **System Alerts:** Push global alerts to all connected users or specific user roles (e.g., "System maintenance starting in 10 minutes").
- **Job Completion Alerts:** Notify a user when a long-running task (e.g., a report generation) is complete.

---

## 2. Architectural Design

Our WebSocket implementation will be built on three core components:

1.  **WebSocket Middleware:** A custom ASP.NET Core middleware to handle the initial WebSocket handshake, accept connections, and manage the connection lifecycle.
2.  **Connection Manager:** A singleton service responsible for keeping track of all active WebSocket connections. It will map connections to authenticated users.
3.  **Message Handler/Dispatcher:** A service responsible for processing incoming messages, deserializing them, and routing them to the appropriate business logic.

![WebSocket Architecture Diagram](https://i.imgur.com/9gZ1bJm.png)

### 2.1. Connection Management

We will create an `IWebSocketConnectionManager` interface and a corresponding implementation that uses a `ConcurrentDictionary<string, WebSocket>` to store active connections.

- The **key** of the dictionary will be the authenticated `UserId`. This allows us to easily find and send a message to a specific user.
- The **value** will be the `WebSocket` object itself.

This service will be registered as a **singleton** to maintain state across the entire application.

### 2.2. Authentication

Security is paramount. Unauthenticated clients must not be allowed to establish a persistent connection.

- The client will initiate the WebSocket connection to an endpoint like `wss://api.supercareapp.com/ws`.
- The JWT authentication token will be passed as a **query parameter** in the connection URL: `wss://api.supercareapp.com/ws?token=YOUR_JWT_TOKEN`.
- Our middleware will intercept the connection request, extract the token, and validate it using the existing `JwtBearer` logic.
- If the token is valid, the connection is accepted, and the `UserId` from the token is used to register the connection in the `ConnectionManager`.
- If the token is invalid or missing, the connection is rejected with an appropriate status code.

### 2.3. Message Protocol

To ensure consistency, all messages (client-to-server and server-to-client) will follow a standardized JSON format.

```json
{
  "type": "message.type.string",
  "payload": {
    // JSON object with message-specific data
  }
}
```

**Example Types:**

- `chat.private_message`: For sending a private chat message.
- `notification.new`: For a new user notification.
- `system.alert`: For a global system alert.
- `echo.request`: A simple type for testing connectivity.

This structure is simple, extensible, and easy to parse on both the client and server.

---

## 3. Step-by-Step Implementation Guide

This guide provides a clear path for a developer to implement the WebSocket system.

### Step 1: Create the Connection Manager

This service will manage all active connections.

**`SuperCareApp.Application/Common/Interfaces/IWebSocketConnectionManager.cs`**
```csharp
using System.Net.WebSockets;
using System.Threading.Tasks;

public interface IWebSocketConnectionManager
{
    Task AddSocket(string userId, WebSocket socket);
    Task RemoveSocket(string userId);
    WebSocket GetSocketByUserId(string userId);
    IEnumerable<string> GetAllUserIds();
}
```

**`SuperCareApp.Infrastructure/Services/WebSocketConnectionManager.cs`** (A new file)
```csharp
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Threading;
using System.Threading.Tasks;

public class WebSocketConnectionManager : IWebSocketConnectionManager
{
    private readonly ConcurrentDictionary<string, WebSocket> _sockets = new ConcurrentDictionary<string, WebSocket>();

    public WebSocket GetSocketByUserId(string userId)
    {
        return _sockets.TryGetValue(userId, out var socket) ? socket : null;
    }

    public IEnumerable<string> GetAllUserIds()
    {
        return _sockets.Keys;
    }

    public async Task AddSocket(string userId, WebSocket socket)
    {
        _sockets.AddOrUpdate(userId, socket, (key, oldSocket) =>
        {
            // If a user opens a new tab, close the old connection and replace it.
            if (oldSocket != null && oldSocket.State == WebSocketState.Open)
            {
                var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                oldSocket.CloseAsync(WebSocketCloseStatus.PolicyViolation, "New connection established", cts.Token);
            }
            return socket;
        });
    }

    public async Task RemoveSocket(string userId)
    {
        if (_sockets.TryRemove(userId, out var socket))
        {
            if (socket.State == WebSocketState.Open)
            {
                 var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                 await socket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Socket connection closed", cts.Token);
            }
        }
    }
}
```

### Step 2: Create the Message Handler

This service will process incoming messages.

**`SuperCareApp.Application/Common/Interfaces/IWebSocketMessageHandler.cs`**
```csharp
using System.Net.WebSockets;
using System.Threading.Tasks;

public interface IWebSocketMessageHandler
{
    Task HandleMessage(WebSocketReceiveResult result, byte[] buffer, WebSocket socket, string userId);
}
```

**`SuperCareApp.Application/Services/WebSocketMessageHandler.cs`** (A new file)
```csharp
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

// Base message format
public record WebSocketMessage(string Type, JsonElement Payload);

public class WebSocketMessageHandler : IWebSocketMessageHandler
{
    private readonly IWebSocketConnectionManager _connectionManager;
    // Inject other services like INotificationService, IChatService etc. as needed

    public WebSocketMessageHandler(IWebSocketConnectionManager connectionManager)
    {
        _connectionManager = connectionManager;
    }

    public async Task HandleMessage(WebSocketReceiveResult result, byte[] buffer, WebSocket socket, string userId)
    {
        var messageJson = Encoding.UTF8.GetString(buffer, 0, result.Count);
        var message = JsonSerializer.Deserialize<WebSocketMessage>(messageJson);

        switch (message.Type)
        {
            case "echo.request":
                await HandleEcho(socket, message.Payload);
                break;

            case "chat.private_message":
                // You would deserialize the payload into a specific DTO
                // var chatPayload = message.Payload.Deserialize<PrivateMessagePayload>();
                // await _chatService.ProcessPrivateMessage(userId, chatPayload);
                break;

            // Add more cases for other message types
            default:
                // Handle unknown message type
                break;
        }
    }

    private async Task HandleEcho(WebSocket socket, JsonElement payload)
    {
        var responsePayload = new { message = $"Echo: {payload.ToString()}" };
        var response = new { type = "echo.response", payload = responsePayload };
        var responseJson = JsonSerializer.Serialize(response);
        var responseBytes = Encoding.UTF8.GetBytes(responseJson);

        await socket.SendAsync(new ArraySegment<byte>(responseBytes), WebSocketMessageType.Text, true, CancellationToken.None);
    }
}
```

### Step 3: Create the WebSocket Middleware

This middleware will manage the connection lifecycle.

**`super-care-app/Middleware/WebSocketMiddleware.cs`**
```csharp
using Microsoft.AspNetCore.Http;
using System.Net.WebSockets;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;

public class WebSocketMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IWebSocketConnectionManager _connectionManager;
    private readonly IWebSocketMessageHandler _messageHandler;

    public WebSocketMiddleware(RequestDelegate next, IWebSocketConnectionManager connectionManager, IWebSocketMessageHandler messageHandler)
    {
        _next = next;
        _connectionManager = connectionManager;
        _messageHandler = messageHandler;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.WebSockets.IsWebSocketRequest)
        {
            // 1. Authentication
            var token = context.Request.Query["token"];
            if (string.IsNullOrEmpty(token))
            {
                context.Response.StatusCode = 401; // Unauthorized
                return;
            }

            // This part requires access to your token validation logic.
            // You might need to inject a service to validate the token and get the principal.
            // For simplicity, we assume a function `ValidateTokenAndGetPrincipal` exists.
            // In a real scenario, you would use `ISecurityTokenValidator`.
            var principal = ValidateTokenAndGetPrincipal(token); // PSEUDO-CODE
            if (principal == null)
            {
                context.Response.StatusCode = 401;
                return;
            }

            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                context.Response.StatusCode = 401;
                return;
            }

            // 2. Connection Acceptance
            var webSocket = await context.WebSockets.AcceptWebSocketAsync();
            await _connectionManager.AddSocket(userId, webSocket);

            // 3. Message Loop
            await ReceiveLoop(webSocket, userId);

            // 4. Cleanup
            await _connectionManager.RemoveSocket(userId);
        }
        else
        {
            await _next(context);
        }
    }

    private async Task ReceiveLoop(WebSocket socket, string userId)
    {
        var buffer = new byte[1024 * 4];
        while (socket.State == WebSocketState.Open)
        {
            var result = await socket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            if (result.MessageType == WebSocketMessageType.Close)
            {
                break;
            }
            await _messageHandler.HandleMessage(result, buffer, socket, userId);
        }
    }

    // NOTE: This is a placeholder. You must replace this with your actual token validation logic.
    private ClaimsPrincipal ValidateTokenAndGetPrincipal(string token)
    {
        // In your real app, inject `ISecurityTokenValidator` and your `TokenValidationParameters`
        // and use `validator.ValidateToken(...)`.
        // For now, this is a simplified example.
        // This is the most critical security step.
        // ...
        // return principal;
        return new ClaimsPrincipal(new ClaimsIdentity(new[] { new Claim(ClaimTypes.NameIdentifier, "temp-user-id-from-token") }));
    }
}
```

### Step 4: Register Services and Middleware

In `Program.cs` (or your service registration extension method), add the services and configure the middleware.

**`super-care-app/Program.cs`**
```csharp
// ... other using statements

// 1. Register Services (as Singletons and Scoped)
builder.Services.AddSingleton<IWebSocketConnectionManager, WebSocketConnectionManager>();
builder.Services.AddScoped<IWebSocketMessageHandler, WebSocketMessageHandler>();

// ... other service registrations

var app = builder.Build();

// ... other middleware

// 2. Configure WebSocket options
var webSocketOptions = new WebSocketOptions
{
    KeepAliveInterval = TimeSpan.FromMinutes(2)
};
app.UseWebSockets(webSocketOptions);

// 3. Add the custom WebSocket middleware
app.UseMiddleware<WebSocketMiddleware>();

// ... map controllers, etc.

app.Run();
```

### Step 5: Abstracting WebSocket Logic with a Dispatcher Service

To keep our business logic clean and decoupled from the WebSocket infrastructure, we will introduce a `NotificationDispatcher` service. Your application services (like `BookingService`, `PaymentService`, etc.) will use this dispatcher to send notifications without needing to know anything about WebSockets.

This approach follows the **Single Responsibility Principle** and makes the system easier to maintain and test.

#### 5.1. Create the Dispatcher Interface and Implementation

**`SuperCareApp.Application/Common/Interfaces/INotificationDispatcher.cs`**
```csharp
using System.Threading.Tasks;

// A generic payload structure for notifications
public record NotificationPayload(string Title, string Message, object Data);

public interface INotificationDispatcher
{
    Task DispatchToUserAsync(string userId, string messageType, object payload);
    Task DispatchToAllAsync(string messageType, object payload);
    Task DispatchToRoleAsync(string role, string messageType, object payload);
}
```

**`SuperCareApp.Infrastructure/Services/NotificationDispatcher.cs`** (A new file)
```csharp
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Domain.Identity; // Assuming you have access to user roles

public class NotificationDispatcher : INotificationDispatcher
{
    private readonly IWebSocketConnectionManager _connectionManager;
    private readonly IUserService _userService; // Example service to get users by role

    public NotificationDispatcher(IWebSocketConnectionManager connectionManager, IUserService userService)
    {
        _connectionManager = connectionManager;
        _userService = userService;
    }

    public async Task DispatchToUserAsync(string userId, string messageType, object payload)
    {
        var socket = _connectionManager.GetSocketByUserId(userId);
        if (socket != null && socket.State == WebSocketState.Open)
        {
            await SendMessageAsync(socket, messageType, payload);
        }
        else
        {
            // User is not connected. Here you can implement fallback logic:
            // 1. Save the notification to the database to be pulled later.
            // 2. Send a push notification via a different service (e.g., Firebase).
            // 3. Send an email.
        }
    }

    public async Task DispatchToAllAsync(string messageType, object payload)
    {
        var allUserIds = _connectionManager.GetAllUserIds();
        foreach (var userId in allUserIds)
        {
            await DispatchToUserAsync(userId, messageType, payload);
        }
    }

    public async Task DispatchToRoleAsync(string role, string messageType, object payload)
    {
        // This requires a way to get all users in a specific role.
        // The implementation details depend on your identity setup.
        var userIdsInRole = await _userService.GetUserIdsByRoleAsync(role); // PSEUDO-CODE
        foreach (var userId in userIdsInRole)
        {
            // This will only send to currently connected users in that role.
            await DispatchToUserAsync(userId, messageType, payload);
        }
    }

    private async Task SendMessageAsync(WebSocket socket, string messageType, object payload)
    {
        var wsMessage = new { type = messageType, payload = payload };
        var messageJson = JsonSerializer.Serialize(wsMessage);
        var messageBytes = Encoding.UTF8.GetBytes(messageJson);

        await socket.SendAsync(new ArraySegment<byte>(messageBytes), WebSocketMessageType.Text, true, CancellationToken.None);
    }
}
```

#### 5.2. Register the Dispatcher Service

In `Program.cs` or your dependency injection setup:
```csharp
// ...
builder.Services.AddScoped<INotificationDispatcher, NotificationDispatcher>();
// ...
```

#### 5.3. Using the Dispatcher in Your Services

Now, your application services can use the dispatcher without any direct reference to `WebSocket`.

**Example: `SuperCareApp.Application/Services/BookingService.cs`**
```csharp
public class BookingService
{
    private readonly INotificationDispatcher _notificationDispatcher;
    private readonly IBookingRepository _bookingRepository;

    public BookingService(INotificationDispatcher notificationDispatcher, IBookingRepository bookingRepository)
    {
        _notificationDispatcher = notificationDispatcher;
        _bookingRepository = bookingRepository;
    }

    public async Task ConfirmBookingAsync(int bookingId)
    {
        var booking = await _bookingRepository.GetByIdAsync(bookingId);
        // ... logic to confirm the booking

        // Send a real-time notification to the patient
        var notificationPayload = new
        {
            BookingId = booking.Id,
            Message = $"Your booking for {booking.Service.Name} has been confirmed."
        };
        await _notificationDispatcher.DispatchToUserAsync(booking.PatientId, "booking.confirmed", notificationPayload);

        // Also notify the care provider
        await _notificationDispatcher.DispatchToUserAsync(booking.CareProviderId, "booking.new", notificationPayload);
    }
}
```

This design provides a clean separation of concerns and makes your real-time notification system highly maintainable and extensible.

### Step 6: Handling Different Notification Types

A robust system needs to handle various notification scenarios. Here’s how to structure and implement them using the `INotificationDispatcher`.

#### 6.1. User-Specific Notifications

These are the most common type of notifications, targeted at a single user.

**Use Case:** A user receives an alert that their password was changed.

**Implementation:**
```csharp
// In your AccountService or similar
public async Task ChangePasswordAsync(string userId, string newPassword)
{
    // ... logic to change the password

    var notificationPayload = new
    {
        Title = "Security Alert",
        Message = "Your password was successfully changed. If you did not initiate this, please contact support immediately.",
        Timestamp = DateTime.UtcNow
    };

    await _notificationDispatcher.DispatchToUserAsync(userId, "user.security_alert", notificationPayload);
}
```

#### 6.2. Role-Based Notifications

These notifications are sent to all users who belong to a specific role (e.g., `Admin`, `CareProvider`).

**Use Case:** An administrator publishes a new policy that all care providers must acknowledge.

**Implementation:**
```csharp
// In an AdminService or similar
public async Task PublishNewPolicyAsync(string policyContent)
{
    // ... logic to save the new policy

    var notificationPayload = new
    {
        Title = "New Policy Published",
        Message = "A new clinical policy has been published. Please review it in the documents section.",
        Url = "/policies/new-policy-id"
    };

    // The dispatcher will resolve all users in the 'CareProvider' role
    // and send the message to each one who is currently connected.
    await _notificationDispatcher.DispatchToRoleAsync("CareProvider", "system.policy_update", notificationPayload);
}
```

#### 6.3. System-Wide (Broadcast) Notifications

These are sent to every single connected user, regardless of their role or status.

**Use Case:** Announcing a scheduled system maintenance window.

**Implementation:**
This is best handled via a dedicated administrative endpoint.

**`AdminController.cs`**
```csharp
[ApiController]
[Route("api/admin/system")]
[Authorize(Roles = "Admin")]
public class AdminSystemController : ControllerBase
{
    private readonly INotificationDispatcher _notificationDispatcher;

    public AdminSystemController(INotificationDispatcher notificationDispatcher)
    {
        _notificationDispatcher = notificationDispatcher;
    }

    [HttpPost("broadcast")]
    public async Task<IActionResult> BroadcastMessage([FromBody] BroadcastMessageRequest request)
    {
        var payload = new { Title = "System Announcement", Message = request.Message };
        await _notificationDispatcher.DispatchToAllAsync("system.broadcast_alert", payload);
        return Ok("Broadcast message sent to all connected users.");
    }
}
```

#### 6.4. Long-Running Job Completion

Notify a user when a task they initiated is complete.

**Use Case:** A user requests a data export, which takes several minutes to generate.

**Implementation:**
This is often handled by a background service (e.g., using Hangfire or Quartz.NET).

```csharp
// In a background job service
public class ReportGenerationJob
{
    private readonly INotificationDispatcher _notificationDispatcher;

    public ReportGenerationJob(INotificationDispatcher notificationDispatcher)
    {
        _notificationDispatcher = notificationDispatcher;
    }

    public async Task Execute(string userId, int reportId)
    {
        // ... long-running logic to generate the report ...

        var notificationPayload = new
        {
            Title = "Report Ready",
            Message = "Your requested report is now available for download.",
            DownloadUrl = $ "/reports/{reportId}"
        };

        // Notify the user who initiated the job
        await _notificationDispatcher.DispatchToUserAsync(userId, "job.completion_success", notificationPayload);
    }
}
```

By categorizing notifications and using the dispatcher, you create a clear and scalable system for real-time communication.

### Step 7: Hangfire Job for Stale Connection Cleanup

In a real-world scenario, a client might disconnect abruptly (e.g., by closing their browser or losing internet) without properly closing the WebSocket connection. This can lead to "zombie" connections in our `WebSocketConnectionManager` that are no longer valid.

To solve this, we will implement a recurring Hangfire background job that periodically checks for and cleans up these stale connections.

#### 7.1. The Stale Connection Cleanup Service

First, we define a service that contains the cleanup logic. This keeps the implementation separate from the Hangfire registration.

**`SuperCareApp.Application/Common/Interfaces/IWebSocketCleanupService.cs`**
```csharp
using System.Threading.Tasks;

public interface IWebSocketCleanupService
{
    Task CleanUpStaleConnectionsAsync();
}
```

**`SuperCareApp.Infrastructure/Services/WebSocketCleanupService.cs`** (A new file)
```csharp
using System.Net.WebSockets;
using System.Threading.Tasks;
using SuperCareApp.Application.Common.Interfaces;
using Microsoft.Extensions.Logging;

public class WebSocketCleanupService : IWebSocketCleanupService
{
    private readonly IWebSocketConnectionManager _connectionManager;
    private readonly ILogger<WebSocketCleanupService> _logger;

    public WebSocketCleanupService(IWebSocketConnectionManager connectionManager, ILogger<WebSocketCleanupService> logger)
    {
        _connectionManager = connectionManager;
        _logger = logger;
    }

    public async Task CleanUpStaleConnectionsAsync()
    {
        _logger.LogInformation("Starting stale WebSocket connection cleanup job.");
        var userIds = _connectionManager.GetAllUserIds().ToList();
        int cleanupCount = 0;

        foreach (var userId in userIds)
        {
            var socket = _connectionManager.GetSocketByUserId(userId);
            if (socket == null || socket.State != WebSocketState.Open)
            {
                _logger.LogWarning($"Found stale connection for user {userId}. State: {socket?.State}. Removing.");
                await _connectionManager.RemoveSocket(userId);
                cleanupCount++;
            }
        }

        _logger.LogInformation($"Finished cleanup job. Removed {cleanupCount} stale connections.");
    }
}
```

#### 7.2. Registering the Service and the Hangfire Job

Next, we register the service and schedule the recurring job in `Program.cs`.

**`super-care-app/Program.cs`**
```csharp
// ... other using statements

// 1. Register the cleanup service
builder.Services.AddTransient<IWebSocketCleanupService, WebSocketCleanupService>();

// ... other service registrations (including Hangfire setup)

var app = builder.Build();

// ... other middleware

// 2. Schedule the recurring Hangfire job
var recurringJobManager = app.Services.GetRequiredService<IRecurringJobManager>();
recurringJobManager.AddOrUpdate<IWebSocketCleanupService>(
    "websocket-cleanup-job",
    service => service.CleanUpStaleConnectionsAsync(),
    Cron.MinuteInterval(5) // Run the job every 5 minutes
);

// ...
```

This ensures that our `WebSocketConnectionManager` remains healthy and only contains active, open connections, which is crucial for the stability and performance of the real-time system.

### Step 8: Implementing a Heartbeat Mechanism

Network intermediaries like proxies and load balancers may close WebSocket connections that they consider idle. To prevent this, and to more proactively detect disconnected clients, we will implement a heartbeat (or ping-pong) mechanism.

1.  The **server** will periodically send a "ping" message to the client.
2.  The **client** must respond with a "pong" message within a certain time frame.
3.  If the server does not receive a pong, it assumes the connection is dead and closes it.

This is more efficient than the Hangfire cleanup job for detecting disconnections in real-time.

#### 8.1. Update the Message Protocol

First, we add new message types for our heartbeat.

**`Message Protocol`**
```json
{
  "type": "system.ping",
  "payload": { "timestamp": ********** }
}
```

```json
{
  "type": "system.pong",
  "payload": { "timestamp": ********** }
}
```

#### 8.2. Server-Side Heartbeat Logic

We can integrate the ping mechanism directly into our `WebSocketMiddleware`'s `ReceiveLoop`.

**`super-care-app/Middleware/WebSocketMiddleware.cs`** (Updated `ReceiveLoop`)
```csharp
private async Task ReceiveLoop(WebSocket socket, string userId)
{
    var buffer = new byte[1024 * 4];
    var lastPongReceived = DateTime.UtcNow;
    var pingInterval = TimeSpan.FromSeconds(30);
    var timeout = TimeSpan.FromSeconds(10);

    while (socket.State == WebSocketState.Open)
    {
        // Send a ping
        await SendPing(socket);

        var receiveTask = socket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
        var delayTask = Task.Delay(pingInterval + timeout);

        var completedTask = await Task.WhenAny(receiveTask, delayTask);

        if (completedTask == delayTask || DateTime.UtcNow - lastPongReceived > pingInterval + timeout)
        {
            // No message or pong received in time, assume connection is stale.
            break;
        }

        var result = await receiveTask;
        if (result.MessageType == WebSocketMessageType.Close)
        {
            break;
        }

        // Check for pong response
        var messageJson = Encoding.UTF8.GetString(buffer, 0, result.Count);
        if (IsPongMessage(messageJson))
        {
            lastPongReceived = DateTime.UtcNow;
            continue; // Skip message handler for pongs
        }

        await _messageHandler.HandleMessage(result, buffer, socket, userId);
    }
}

private async Task SendPing(WebSocket socket)
{
    var pingPayload = new { timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds() };
    var pingMessage = new { type = "system.ping", payload = pingPayload };
    var messageJson = JsonSerializer.Serialize(pingMessage);
    var messageBytes = Encoding.UTF8.GetBytes(messageJson);
    await socket.SendAsync(new ArraySegment<byte>(messageBytes), WebSocketMessageType.Text, true, CancellationToken.None);
}

private bool IsPongMessage(string message)
{
    try
    {
        var msg = JsonSerializer.Deserialize<WebSocketMessage>(message);
        return msg?.Type == "system.pong";
    }
    catch
    {
        return false;
    }
}
```

#### 8.3. Client-Side Heartbeat Logic

The client must listen for `system.ping` and respond with `system.pong`.

**`Client-Side JavaScript`** (Updated)
```javascript
// ... inside the 'message' event listener
socket.addEventListener('message', (event) => {
    const message = JSON.parse(event.data);

    if (message.type === 'system.ping') {
        // Respond to the server's ping
        const pongMessage = {
            type: 'system.pong',
            payload: message.payload // Echo the timestamp
        };
        socket.send(JSON.stringify(pongMessage));
        return; // Don't process this as a normal message
    }

    // ... handle other message types
});
```

With this heartbeat mechanism, the system can now reliably detect and clean up broken connections, ensuring a more stable and performant real-time experience.

---

## 4. Scalability: The Redis Backplane

When running multiple instances of the application (e.g., in a containerized or cloud environment), the `WebSocketConnectionManager` on one instance will not know about connections on another. This is a major issue.

**Solution:** Use a **Redis Pub/Sub backplane**.

1.  When **Server A** wants to send a message to a user, it first checks its local connection manager.
2.  If the user is not connected locally, **Server A** publishes the message to a Redis channel (e.g., `websockets:messages`). The Redis message should contain the target `UserId` and the message payload.
3.  **All server instances** (A, B, C...) are subscribed to this Redis channel.
4.  When a server receives a message from the Redis channel, it checks its **local** connection manager to see if the target user is connected to *it*.
5.  If so, it sends the message through the WebSocket. If not, it ignores the message.

This ensures that a message originating from any server can reach a user connected to any other server. The implementation would involve creating a Redis subscriber service that is registered as a hosted service (`IHostedService`) to listen for messages in the background.

---

## 5. Client-Side Implementation (Example)

Here is a basic JavaScript example of how a client would connect.

```javascript
// Retrieve the auth token from local storage, cookies, etc.
const authToken = 'your_jwt_token_here';

// Establish the connection
const socket = new WebSocket(`wss://api.supercareapp.com/ws?token=${authToken}`);

// Connection opened
socket.addEventListener('open', (event) => {
    console.log('WebSocket connection established.');
    // Send a test message
    const testMessage = {
        type: 'echo.request',
        payload: { data: 'Hello from client!' }
    };
    socket.send(JSON.stringify(testMessage));
});

// Listen for messages
socket.addEventListener('message', (event) => {
    console.log('Message from server: ', event.data);
    const message = JSON.parse(event.data);

    // Handle different message types
    if (message.type === 'notification.new') {
        // Display the notification to the user
        alert(`New Notification: ${message.payload.content}`);
    } else if (message.type === 'echo.response') {
        console.log('Echo response received:', message.payload);
    }
});

// Connection closed
socket.addEventListener('close', (event) => {
    console.log('WebSocket connection closed.', event);
    // Implement reconnection logic if needed
});

// Connection error
socket.addEventListener('error', (event) => {
    console.error('WebSocket error:', event);
});
```
