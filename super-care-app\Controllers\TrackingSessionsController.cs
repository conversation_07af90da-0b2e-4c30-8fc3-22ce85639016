using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Persistence.Services.TrackingSessions.Commands;
using SuperCareApp.Persistence.Services.TrackingSessions.Queries;

namespace super_care_app.Controllers;

[Authorize(Roles = "CareProvider")]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
public class TrackingSessionsController : BaseController
{
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;

    public TrackingSessionsController(IMediator mediator, ICurrentUserService currentUserService)
    {
        _mediator = mediator;
        _currentUserService = currentUserService;
    }

    [HttpPost("start")]
    public async Task<IActionResult> Start([FromQuery] Guid bookingWindowId, CancellationToken ct)
    {
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || currentUserId.Value == Guid.Empty)
        {
            return UnauthorizedResponse<object>("User not logged in");
        }
        var cmd = new StartTrackingSessionCommand(bookingWindowId, currentUserId.Value);
        var result = await _mediator.Send(cmd, ct);
        if (!result.IsSuccess)
        {
            return ErrorResponseFromError<object>(result.Error);
        }
        return SuccessResponse(result.Value, "Time tracking session started.");
    }

    [HttpPost("{sessionId:guid}/pause")]
    public async Task<IActionResult> Pause(Guid sessionId, CancellationToken ct)
    {
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || currentUserId.Value == Guid.Empty)
        {
            return UnauthorizedResponse<object>("User not logged in");
        }
        var result = await _mediator.Send(
            new PauseTrackingSessionCommand(sessionId, currentUserId.Value),
            ct
        );
        if (!result.IsSuccess)
        {
            return ErrorResponseFromError<object>(result.Error);
        }
        var response = new GenericObjectResponse("Session has been paused.", Guid.NewGuid());
        return SuccessResponse(response, "Session has been paused.");
    }

    [HttpPost("{sessionId:guid}/resume")]
    public async Task<IActionResult> Resume(Guid sessionId, CancellationToken ct)
    {
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || currentUserId.Value == Guid.Empty)
        {
            return UnauthorizedResponse<object>("User not logged in");
        }
        var result = await _mediator.Send(
            new ResumeTrackingSessionCommand(sessionId, currentUserId.Value),
            ct
        );
        if (!result.IsSuccess)
        {
            return ErrorResponseFromError<object>(result.Error);
        }
        var response = new GenericObjectResponse("Session has been resumed.", Guid.NewGuid());
        return SuccessResponse(response, "Session has been resumed.");
    }

    [HttpPost("{sessionId:guid}/stop")]
    public async Task<IActionResult> Stop(Guid sessionId, CancellationToken ct)
    {
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || currentUserId.Value == Guid.Empty)
        {
            return UnauthorizedResponse<object>("User not logged in");
        }
        var result = await _mediator.Send(
            new StopTrackingSessionCommand(sessionId, currentUserId.Value),
            ct
        );
        if (!result.IsSuccess)
        {
            return ErrorResponseFromError<object>(result.Error);
        }
        return SuccessResponse(result.Value, "Session has been ended");
    }

    [HttpGet("active")]
    public async Task<IActionResult> GetActive([FromQuery] Guid providerId, CancellationToken ct)
    {
        var query = new GetActiveTrackingSessionQuery(providerId);
        var response = await _mediator.Send(query, ct);
        if (!response.IsSuccess)
        {
            return ErrorResponseFromError<object>(response.Error);
        }
        return SuccessResponse(response.Value, "Active session found");
    }
}
