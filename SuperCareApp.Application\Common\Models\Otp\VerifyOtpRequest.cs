using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Otp
{
    /// <summary>
    /// Request model for verifying an OTP
    /// </summary>
    public class VerifyOtpRequest
    {
        /// <summary>
        /// Email address associated with the OTP (required if phone number is not provided)
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Phone number associated with the OTP (required if email is not provided)
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// The OTP code to verify for email (required if email is provided)
        /// </summary>
        public string? EmailOtpCode { get; set; }

        /// <summary>
        /// The OTP code to verify for phone (required if phone number is provided)
        /// </summary>
        public string? PhoneOtpCode { get; set; }

        /// <summary>
        /// Type of verification (e.g., "Registration", "PasswordReset")
        /// </summary>
        public string VerificationType { get; set; } = "Verification";
    }

    public class VerifyOtpRequestValidator : AbstractValidator<VerifyOtpRequest>
    {
        public VerifyOtpRequestValidator()
        {
            RuleFor(x => x.VerificationType)
                .NotEmpty()
                .WithMessage("Verification type is required.")
                .Must(type => type == "Registration" || type == "PasswordReset")
                .WithMessage("Invalid verification type. Allowed: Registration or PasswordReset.");

            RuleFor(x => x)
                .Must(x =>
                    !string.IsNullOrWhiteSpace(x.Email) || !string.IsNullOrWhiteSpace(x.PhoneNumber)
                )
                .WithMessage("Either email or phone number must be provided.");

            When(
                x => !string.IsNullOrWhiteSpace(x.Email),
                () =>
                {
                    RuleFor(x => x.EmailOtpCode)
                        .NotEmpty()
                        .WithMessage("Email OTP code is required when email is provided.");
                }
            );

            When(
                x => !string.IsNullOrWhiteSpace(x.PhoneNumber),
                () =>
                {
                    RuleFor(x => x.PhoneOtpCode)
                        .NotEmpty()
                        .WithMessage("Phone OTP code is required when phone number is provided.");
                }
            );
        }
    }
}
