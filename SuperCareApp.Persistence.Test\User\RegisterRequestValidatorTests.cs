using PhoneNumbers;
using SuperCareApp.Application.Common.Models.Identity;
using Xunit;

namespace SuperCareApp.Persistence.Test;

public class RegisterRequestValidatorTests
{
    private readonly RegisterRequestValidator _validator = new();

    [Theory]
    [InlineData("+***********")] // US
    [InlineData("+************")] // UK
    [InlineData("+************")] // India
    [InlineData("+************")] // Japan
    [InlineData("+***********")] // France
    [InlineData("+8613812345678")] // China
    [InlineData("+5511999999999")] // Brazil
    [InlineData("+***********")] // South Africa
    [InlineData("+***********")] // Australia
    [InlineData("+************")] // Italy
    public void Should_Pass_Validation_For_Valid_PhoneNumbers(string phoneNumber)
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass123!",
            ConfirmPassword = "Pass123!",
            PhoneNumber = phoneNumber,
            IsCareProvider = false,
            FirstName = "John",
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.True(result.IsValid, $"Phone number '{phoneNumber}' should be valid.");
    }

    [Theory]
    [InlineData("***********")] // Missing +
    [InlineData("+1234567890123456")] // Too long
    [InlineData("+123")] // Too short
    [InlineData("+12a34567890")] // Invalid characters
    [InlineData("+***********")] // Invalid country code (0 prefix)
    [InlineData("+12..34567890")] // Consecutive dots in number
    [InlineData("+12 34567890")] // Space in number
    public void Should_Fail_Validation_For_Invalid_PhoneNumbers(string phoneNumber)
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass123!",
            ConfirmPassword = "Pass123!",
            PhoneNumber = phoneNumber,
            IsCareProvider = false,
            FirstName = "John",
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.False(result.IsValid, $"Phone number '{phoneNumber}' should be invalid.");
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(RegisterRequest.PhoneNumber));
    }

    [Fact]
    public void Should_Debug_UK_Phone_Number()
    {
        var phoneNumber = "+************";

        // Manual libphonenumber test
        var phoneUtil = PhoneNumberUtil.GetInstance();
        try
        {
            var parsed = phoneUtil.Parse(phoneNumber, null);
            var isValid = phoneUtil.IsValidNumber(parsed);
            var region = phoneUtil.GetRegionCodeForNumber(parsed);
            var type = phoneUtil.GetNumberType(parsed);
            var exampleNumber = phoneUtil.GetExampleNumber("GB");

            System.Diagnostics.Debug.WriteLine($"Input Phone: {phoneNumber}");
            System.Diagnostics.Debug.WriteLine($"Parsed: {parsed}");
            System.Diagnostics.Debug.WriteLine($"IsValid: {isValid}");
            System.Diagnostics.Debug.WriteLine($"Region: {region}");
            System.Diagnostics.Debug.WriteLine($"Type: {type}");
            System.Diagnostics.Debug.WriteLine($"Example GB Number: {exampleNumber}");
            System.Diagnostics.Debug.WriteLine(
                $"Example Valid: {phoneUtil.IsValidNumber(exampleNumber)}"
            );

            // If it's not valid, let's see why
            if (!isValid)
            {
                var nationalSignificantNumber = phoneUtil.GetNationalSignificantNumber(parsed);
                var lengthOfNationalNumber = nationalSignificantNumber.Length;
                System.Diagnostics.Debug.WriteLine(
                    $"National Significant Number: {nationalSignificantNumber}"
                );
                System.Diagnostics.Debug.WriteLine($"Length: {lengthOfNationalNumber}");
            }
        }
        catch (NumberParseException ex)
        {
            System.Diagnostics.Debug.WriteLine($"Parse error for {phoneNumber}: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"Error Type: {ex.ErrorType}");
            throw;
        }

        // Test with a known valid UK number
        var knownValidUK = "+447911123456"; // This should be valid
        try
        {
            var parsed2 = phoneUtil.Parse(knownValidUK, null);
            var isValid2 = phoneUtil.IsValidNumber(parsed2);
            System.Diagnostics.Debug.WriteLine(
                $"Known valid UK {knownValidUK} - Valid: {isValid2}"
            );
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error with known valid: {ex.Message}");
        }
    }

    [Fact]
    public void Should_Test_Various_UK_Numbers()
    {
        var phoneUtil = PhoneNumberUtil.GetInstance();
        var ukNumbers = new[]
        {
            "+447700900123", // Your original number
            "+447911123456", // Typical mobile
            "+442079460018", // London landline
            "+441632960018", // Fictional geographic
            "+447700900123", // Your number again
        };

        foreach (var number in ukNumbers)
        {
            try
            {
                var parsed = phoneUtil.Parse(number, null);
                var isValid = phoneUtil.IsValidNumber(parsed);
                var region = phoneUtil.GetRegionCodeForNumber(parsed);

                System.Diagnostics.Debug.WriteLine(
                    $"{number} - Valid: {isValid}, Region: {region}"
                );
            }
            catch (NumberParseException ex)
            {
                System.Diagnostics.Debug.WriteLine($"{number} - Parse Error: {ex.Message}");
            }
        }
    }

    [Fact]
    public void Should_Allow_Null_Or_Empty_PhoneNumber()
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass123!",
            ConfirmPassword = "Pass123!",
            PhoneNumber = null,
            IsCareProvider = false,
            FirstName = "John",
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.True(result.IsValid, "PhoneNumber is optional.");
    }

    [Fact]
    public void Should_Fail_When_Passwords_Do_Not_Match()
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass123!",
            ConfirmPassword = "Pass1234!",
            PhoneNumber = "+***********",
            IsCareProvider = false,
            FirstName = "John",
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.False(result.IsValid);
        Assert.Contains(
            result.Errors,
            e => e.PropertyName == nameof(RegisterRequest.ConfirmPassword)
        );
    }

    [Fact]
    public void Should_Fail_When_Password_Is_Too_Short()
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass1",
            ConfirmPassword = "Pass1",
            PhoneNumber = "+***********",
            IsCareProvider = false,
            FirstName = "John",
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(RegisterRequest.Password));
    }

    [Fact]
    public void Should_Fail_When_Email_Is_Invalid()
    {
        var model = new RegisterRequest
        {
            Email = "invalid-email",
            Password = "Pass123!",
            ConfirmPassword = "Pass123!",
            PhoneNumber = "+***********",
            IsCareProvider = false,
            FirstName = "John",
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(RegisterRequest.Email));
    }

    [Theory]
    [InlineData("John123")]
    [InlineData("John@Doe")]
    [InlineData("John_Doe")]
    public void Should_Fail_When_FirstName_Contains_Invalid_Characters(string firstName)
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass123!",
            ConfirmPassword = "Pass123!",
            PhoneNumber = "+***********",
            IsCareProvider = false,
            FirstName = firstName,
            LastName = "Doe",
        };

        var result = _validator.Validate(model);

        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(RegisterRequest.FirstName));
    }

    [Theory]
    [InlineData("Doe123")]
    [InlineData("Doe@Smith")]
    [InlineData("Doe_Smith")]
    public void Should_Fail_When_LastName_Contains_Invalid_Characters(string lastName)
    {
        var model = new RegisterRequest
        {
            Email = "<EMAIL>",
            Password = "Pass123!",
            ConfirmPassword = "Pass123!",
            PhoneNumber = "+***********",
            IsCareProvider = false,
            FirstName = "John",
            LastName = lastName,
        };

        var result = _validator.Validate(model);

        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == nameof(RegisterRequest.LastName));
    }
}
