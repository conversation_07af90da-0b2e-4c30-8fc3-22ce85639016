﻿using System.ComponentModel;

namespace SuperCareApp.Domain.Enums
{
    public enum BookingStatusType
    {
        [Description("Requested")]
        Requested,

        [Description("Accepted")]
        Accepted,

        [Description("Rejected")]
        Rejected,

        [Description("Cancelled")]
        Cancelled,

        [Description("Completed")]
        Completed,

        [Description("In Progress")]
        InProgress,

        [Description("Expired")]
        Expired,

        [Description("Confirmed")]
        Confirmed,

        [Description("Modified")]
        Modified,

        [Description("No Show")]
        NoShow,
    }

    public static class BookingStatusEnumExtensions
    {
        public static string GetDescription(this BookingStatusType status)
        {
            return status switch
            {
                BookingStatusType.Requested => "Requested",
                BookingStatusType.Accepted => "Accepted",
                BookingStatusType.Rejected => "Rejected",
                BookingStatusType.Cancelled => "Cancelled",
                BookingStatusType.Completed => "Completed",
                BookingStatusType.InProgress => "In Progress",
                BookingStatusType.Expired => "Expired",
                BookingStatusType.Confirmed => "Confirmed",
                BookingStatusType.Modified => "Modified",
                BookingStatusType.NoShow => "No Show",
                _ => "Unknown",
            };
        }

        public static readonly IReadOnlyCollection<string> RequiresNotes = new[]
        {
            nameof(BookingStatusType.Cancelled),
            nameof(BookingStatusType.Rejected),
            nameof(BookingStatusType.NoShow),
        };
    }
}
