using System.Text.Json.Serialization;

namespace SuperCareApp.Shared.Utility
{
    /// <summary>
    /// Metadata for paginated responses
    /// </summary>
    public class PaginationMetadata
    {
        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        [JsonPropertyName("currentPage")]
        public int CurrentPage { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        [JsonPropertyName("totalPages")]
        public int TotalPages { get; set; }

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        [JsonPropertyName("totalCount")]
        public int TotalCount { get; set; }

        /// <summary>
        /// Number of items per page
        /// </summary>
        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        [JsonPropertyName("hasPreviousPage")]
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        [JsonPropertyName("hasNextPage")]
        public bool HasNextPage => CurrentPage < TotalPages;

        public PaginationMetadata() { }

        public PaginationMetadata(int currentPage, int totalPages, int totalCount, int pageSize)
        {
            CurrentPage = currentPage;
            TotalPages = totalPages;
            TotalCount = totalCount;
            PageSize = pageSize;
        }
    }
}
