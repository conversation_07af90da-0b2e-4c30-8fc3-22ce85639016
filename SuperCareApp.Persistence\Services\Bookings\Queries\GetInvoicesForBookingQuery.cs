﻿using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

public record GetInvoicesForBookingQuery(Guid BookingId)
    : IQuery<Result<IEnumerable<InvoiceResponse>>>;

public class GetInvoicesForBookingQueryHandler
    : IQueryHandler<GetInvoicesForBookingQuery, Result<IEnumerable<InvoiceResponse>>>
{
    private readonly ApplicationDbContext _context;

    public GetInvoicesForBookingQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<IEnumerable<InvoiceResponse>>> Handle(
        GetInvoicesForBookingQuery request,
        CancellationToken cancellationToken
    )
    {
        var bookingWindows = await _context
            .BookingWindows.Where(bw => bw.BookingId == request.BookingId)
            .Select(bw => bw.Id)
            .ToListAsync(cancellationToken);

        if (!bookingWindows.Any())
            return Result.Failure<IEnumerable<InvoiceResponse>>(
                Error.NotFound("No booking windows found for this booking.")
            );

        var invoices = await _context
            .Invoices.Include(i => i.BookingWindow)
            .ThenInclude(bw => bw.Booking)
            .Where(i => bookingWindows.Contains(i.BookingWindowId))
            .Select(i => new InvoiceResponse(
                i.Id,
                i.InvoiceNumber,
                i.BookingWindowId,
                i.BookingWindow.Booking.ClientId,
                $"{i.BookingWindow.Booking.Client.UserProfile.FirstName} {i.BookingWindow.Booking.Client.UserProfile.LastName}".Trim(),
                i.BookingWindow.Booking.ProviderId,
                $"{i.BookingWindow.Booking.Provider.User.UserProfile.FirstName} {i.BookingWindow.Booking.Provider.User.UserProfile.LastName}".Trim(),
                i.TotalAmount,
                i.Currency,
                i.BookingWindow.Booking.Category.Name,
                i.InvoiceDate.ToString("yyyy-MM-dd"),
                i.Status.ToString(),
                i.FileName,
                i.FileUrl
            ))
            .ToListAsync(cancellationToken);

        return Result.Success<IEnumerable<InvoiceResponse>>(invoices);
    }
}
