using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.Services.Categories.Queries;
using SuperCareApp.Persistence.UnitOfWork;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Comprehensive unit tests for all care category query handlers
/// </summary>
public class CareCategoryQueryHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly ICareCategoryService _service;

    public CareCategoryQueryHandlerTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        var repository = new CareCategoryRepository(_context);
        var unitOfWork = new UnitOfWork.UnitOfWork(_context);
        _service = new CareCategoryService(
            repository,
            _context,
            unitOfWork,
            NullLogger<CareCategoryService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region GetAllCategoriesQueryHandler Tests

    [Fact]
    public async Task GetAllCategoriesQueryHandler_WithActiveCategories_ShouldReturnOnlyActive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        await _context.SaveChangesAsync();

        var handler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var query = new GetAllCategoriesQuery(IncludeInactive: false);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetAllCategoriesQueryHandler_WithIncludeInactive_ShouldReturnAll()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        await _context.SaveChangesAsync();

        var handler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var query = new GetAllCategoriesQuery(IncludeInactive: true);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());
    }

    [Fact]
    public async Task GetAllCategoriesQueryHandler_WithDeletedCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, deletedCategory);
        await _context.SaveChangesAsync();

        var handler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var query = new GetAllCategoriesQuery(IncludeInactive: false);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetAllCategoriesQueryHandler_WithProviderId_ShouldReturnProviderSpecificData()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "General Description",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            ProviderSpecificDescription = "Provider specific description"
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        await _context.SaveChangesAsync();

        var handler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var query = new GetAllCategoriesQuery(IncludeInactive: false, ProviderId: providerId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);

        var categoryResponse = result.Value.First();
        Assert.Equal("Provider specific description", categoryResponse.Description);
        Assert.Equal(50.00m, categoryResponse.HourlyRate);
        Assert.Equal(5, categoryResponse.ExperienceYears);
    }

    [Fact]
    public async Task GetAllCategoriesQueryHandler_WithProviderIdButNoProviderCategories_ShouldReturnGeneralData()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "General Description",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var handler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var query = new GetAllCategoriesQuery(IncludeInactive: false, ProviderId: providerId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);

        var categoryResponse = result.Value.First();
        Assert.Equal(0m, categoryResponse.HourlyRate);
        Assert.Equal(0, categoryResponse.ExperienceYears);
    }

    [Fact]
    public async Task GetAllCategoriesQueryHandler_WithDeletedProviderCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "General Description",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            ProviderSpecificDescription = "Deleted provider description",
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(deletedProviderCategory);
        await _context.SaveChangesAsync();

        var handler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var query = new GetAllCategoriesQuery(IncludeInactive: false, ProviderId: providerId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);

        var categoryResponse = result.Value.First();
        Assert.Equal(0m, categoryResponse.HourlyRate); // Should be 0 since provider category is deleted
        Assert.Equal(0, categoryResponse.ExperienceYears); // Should be 0 since provider category is deleted
    }

    #endregion

    #region GetCategoryByIdQueryHandler Tests

    [Fact]
    public async Task GetCategoryByIdQueryHandler_WithValidId_ShouldReturnCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-heart",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var handler = new GetCategoryByIdQueryHandler(_service, NullLogger<GetCategoryByIdQueryHandler>.Instance);
        var query = new GetCategoryByIdQuery(category.Id);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(category.Id, result.Value.Id);
        Assert.Equal(category.Name, result.Value.Name);
        Assert.Equal(category.Description, result.Value.Description);
        Assert.Equal(category.Icon, result.Value.Icon);
        Assert.Equal(category.Color, result.Value.Color);
    }

    [Fact]
    public async Task GetCategoryByIdQueryHandler_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var handler = new GetCategoryByIdQueryHandler(_service, NullLogger<GetCategoryByIdQueryHandler>.Instance);
        var query = new GetCategoryByIdQuery(nonExistentId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task GetCategoryByIdQueryHandler_WithDeletedCategory_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        var handler = new GetCategoryByIdQueryHandler(_service, NullLogger<GetCategoryByIdQueryHandler>.Instance);
        var query = new GetCategoryByIdQuery(deletedCategory.Id);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion

    #region GetPaginatedCategoriesQueryHandler Tests

    [Fact]
    public async Task GetPaginatedCategoriesQueryHandler_WithValidParameters_ShouldReturnPaginatedResults()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categories = Enumerable.Range(1, 5).Select(i => new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i:D2}",
            IsActive = true,
            PlatformFee = i * 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        }).ToArray();

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        var handler = new GetPaginatedCategoriesQueryHandler(_service, NullLogger<GetPaginatedCategoriesQueryHandler>.Instance);
        var query = new GetPaginatedCategoriesQuery(PageNumber: 1, PageSize: 3, IncludeInactive: false);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(3, result.Value.Categories.Count());
        Assert.Equal(5, result.Value.TotalCount);
        Assert.Equal(2, result.Value.TotalPages);
        Assert.Equal(1, result.Value.PageNumber);
        Assert.Equal(3, result.Value.PageSize);
    }

    [Fact]
    public async Task GetPaginatedCategoriesQueryHandler_WithSecondPage_ShouldReturnCorrectPage()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categories = Enumerable.Range(1, 5).Select(i => new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i:D2}",
            IsActive = true,
            PlatformFee = i * 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        }).ToArray();

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        var handler = new GetPaginatedCategoriesQueryHandler(_service, NullLogger<GetPaginatedCategoriesQueryHandler>.Instance);
        var query = new GetPaginatedCategoriesQuery(PageNumber: 2, PageSize: 3, IncludeInactive: false);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Categories.Count()); // Remaining 2 items on second page
        Assert.Equal(5, result.Value.TotalCount);
        Assert.Equal(2, result.Value.TotalPages);
        Assert.Equal(2, result.Value.PageNumber);
        Assert.Equal(3, result.Value.PageSize);
    }

    [Fact]
    public async Task GetPaginatedCategoriesQueryHandler_WithDeletedCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, deletedCategory);
        await _context.SaveChangesAsync();

        var handler = new GetPaginatedCategoriesQueryHandler(_service, NullLogger<GetPaginatedCategoriesQueryHandler>.Instance);
        var query = new GetPaginatedCategoriesQuery(PageNumber: 1, PageSize: 10, IncludeInactive: false);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value.Categories);
        Assert.Equal(1, result.Value.TotalCount);
        Assert.Equal("Active Category", result.Value.Categories.First().Name);
    }

    #endregion

    #region GetCategoriesByProviderIdQueryHandler Tests

    [Fact]
    public async Task GetCategoriesByProviderIdQueryHandler_WithValidProviderId_ShouldReturnProviderCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-provider",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            ProviderSpecificDescription = "Provider specific description"
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        await _context.SaveChangesAsync();

        var handler = new GetCategoriesByProviderIdQueryHandler(_service, NullLogger<GetCategoriesByProviderIdQueryHandler>.Instance);
        var query = new GetCategoriesByProviderIdQuery(providerId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);

        var providerCategoryResponse = result.Value.First();
        Assert.Equal(category.Id, providerCategoryResponse.Id);
        Assert.Equal(category.Name, providerCategoryResponse.Name);
        Assert.Equal("Provider specific description", providerCategoryResponse.Description);
        Assert.Equal(50.00m, providerCategoryResponse.HourlyRate);
        Assert.Equal(5, providerCategoryResponse.ExperienceYears);
        Assert.Equal(category.Icon, providerCategoryResponse.Icon);
        Assert.Equal(category.Color, providerCategoryResponse.Color);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdQueryHandler_WithNonExistentProviderId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();
        var handler = new GetCategoriesByProviderIdQueryHandler(_service, NullLogger<GetCategoriesByProviderIdQueryHandler>.Instance);
        var query = new GetCategoriesByProviderIdQuery(nonExistentProviderId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("No care categories found", result.Error.Message);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdQueryHandler_WithZeroHourlyRate_ShouldExcludeCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Zero Rate Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 0m, // Zero hourly rate should be excluded
            ExperienceYears = 5
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        await _context.SaveChangesAsync();

        var handler = new GetCategoriesByProviderIdQueryHandler(_service, NullLogger<GetCategoriesByProviderIdQueryHandler>.Instance);
        var query = new GetCategoriesByProviderIdQuery(providerId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("No care categories found", result.Error.Message);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdQueryHandler_WithDeletedProviderCategory_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(deletedProviderCategory);
        await _context.SaveChangesAsync();

        var handler = new GetCategoriesByProviderIdQueryHandler(_service, NullLogger<GetCategoriesByProviderIdQueryHandler>.Instance);
        var query = new GetCategoriesByProviderIdQuery(providerId);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("No care categories found", result.Error.Message);
    }

    #endregion
}