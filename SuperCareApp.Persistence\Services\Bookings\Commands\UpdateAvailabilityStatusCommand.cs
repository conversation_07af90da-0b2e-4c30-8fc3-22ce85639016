using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;

namespace SuperCareApp.Persistence.Services.Bookings.Commands
{
    /// <summary>
    /// Command to update availability status (manual override)
    /// </summary>
    /// <param name="UserId">The ID of the user making the update</param>
    /// <param name="AvailabilityId">The ID of the availability to update</param>
    /// <param name="IsAvailable">The new availability status</param>
    /// <param name="Reason">Optional reason for the change</param>
    public record UpdateAvailabilityStatusCommand(
        Guid UserId,
        Guid AvailabilityId,
        bool IsAvailable,
        string? Reason = null
    ) : ICommand<Result>;

    /// <summary>
    /// Handler for UpdateAvailabilityStatusCommand
    /// </summary>
    internal class UpdateAvailabilityStatusCommandHandler
        : ICommandHandler<UpdateAvailabilityStatusCommand, Result>
    {
        private readonly IAvailabilityService _availabilityService;
        private readonly ILogger<UpdateAvailabilityStatusCommandHandler> _logger;

        public UpdateAvailabilityStatusCommandHandler(
            IAvailabilityService availabilityService,
            ILogger<UpdateAvailabilityStatusCommandHandler> logger
        )
        {
            _availabilityService = availabilityService;
            _logger = logger;
        }

        public async Task<Result> Handle(
            UpdateAvailabilityStatusCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _availabilityService.UpdateAvailabilityStatusAsync(
                    request.UserId,
                    request.AvailabilityId,
                    request.IsAvailable
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error handling UpdateAvailabilityStatusCommand for availability {AvailabilityId}",
                    request.AvailabilityId
                );
                return Result.Failure(Error.Internal("Error updating availability status"));
            }
        }
    }
}
