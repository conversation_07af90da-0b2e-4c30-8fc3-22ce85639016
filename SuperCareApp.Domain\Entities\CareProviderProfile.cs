﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class CareProviderProfile : BaseEntity
    {
        public Guid UserId { get; set; }
        public string? Bio { get; set; }
        public int YearsExperience { get; set; }
        public decimal? HourlyRate { get; set; }
        public string? CareDescription { get; set; }
        public bool? ProvidesOvernight { get; set; }
        public bool? ProvidesLiveIn { get; set; }
        public int? WorkingHours { get; set; }
        public string? Qualifications { get; set; } // Stored as JSON string
        public VerificationStatus VerificationStatus { get; set; }
        public decimal? Rating { get; set; }
        public int? RatingCount { get; set; }
        public int BufferDuration { get; set; }

        // Navigation properties
        public ApplicationUser User { get; set; } = null!;
        public ICollection<CareProviderCategory> CareProviderCategories { get; set; } =
            new List<CareProviderCategory>();
        public ICollection<Availability> Availabilities { get; set; } = new List<Availability>();
        public ICollection<Leave> Leaves { get; set; } = new List<Leave>();
    }
}
