using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Admin;
using Xunit;

namespace SuperCareApp.Persistence.Test.Admin;

public class ApprovalServiceTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly ApprovalService _service;

    public ApprovalServiceTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _service = new ApprovalService(_context, NullLogger<ApprovalService>.Instance);
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task CreateApprovalAsync_ShouldCreateApprovalSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var approvalType = ApprovalType.CareProviderVerification;
        var approvalData = "Test approval data";
        var relatedEntityId = Guid.NewGuid();

        // Create a user first to satisfy foreign key constraint
        var user = new ApplicationUser
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsDeleted = false,
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.CreateApprovalAsync(
            userId,
            approvalType,
            approvalData,
            relatedEntityId
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(userId, result.Value.UserId);
        Assert.Equal(approvalType, result.Value.ApprovalType);
        Assert.Equal(approvalData, result.Value.ApprovalData);
        Assert.Equal(relatedEntityId, result.Value.RelatedEntityId);
        Assert.Null(result.Value.IsApproved); // Should be pending (null)
        Assert.False(result.Value.IsDeleted);

        // Verify in database
        var approvalInDb = await _context.Approvals.FirstOrDefaultAsync(a =>
            a.Id == result.Value.Id
        );
        Assert.NotNull(approvalInDb);
        Assert.Equal(userId, approvalInDb.UserId);
    }

    [Fact]
    public async Task CreateApprovalAsync_NonExistentUser_ShouldReturnNotFoundError()
    {
        // Arrange
        var nonExistentUserId = Guid.NewGuid();
        var approvalType = ApprovalType.DocumentVerification;

        // Act
        var result = await _service.CreateApprovalAsync(nonExistentUserId, approvalType);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not found", result.Error.Message);
    }

    [Fact]
    public async Task CreateApprovalAsync_DuplicatePendingApproval_ShouldReturnValidationError()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var approvalType = ApprovalType.ProfileUpdate;

        // Create a user first
        var user = new ApplicationUser
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsDeleted = false,
        };
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Create first approval
        var firstResult = await _service.CreateApprovalAsync(userId, approvalType);
        Assert.True(firstResult.IsSuccess);

        // Act - Try to create second approval of same type
        var secondResult = await _service.CreateApprovalAsync(userId, approvalType);

        // Assert
        Assert.False(secondResult.IsSuccess);
        Assert.Contains("pending approval", secondResult.Error.Message);
    }

    [Fact]
    public async Task GetPendingApprovalsAsync_ShouldReturnOnlyPendingApprovals()
    {
        // Arrange
        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();
        var adminId = Guid.NewGuid();

        // Create users first
        var user1 = new ApplicationUser
        {
            Id = userId1,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsDeleted = false,
        };
        var user2 = new ApplicationUser
        {
            Id = userId2,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsDeleted = false,
        };
        _context.Users.AddRange(user1, user2);
        await _context.SaveChangesAsync();

        // Create test approvals
        var pendingApproval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId1,
            ApprovalType = ApprovalType.DocumentVerification,
            IsApproved = null, // Pending
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId1,
            IsDeleted = false,
        };

        var approvedApproval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId2,
            ApprovalType = ApprovalType.CareProviderVerification,
            IsApproved = true, // Approved
            ProcessedBy = adminId,
            ProcessedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            CreatedBy = userId2,
            IsDeleted = false,
        };

        _context.Approvals.AddRange(pendingApproval, approvedApproval);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetPendingApprovalsAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal(pendingApproval.Id, result.Value.First().Id);
    }

    [Fact]
    public async Task ApproveAsync_ShouldApproveSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var adminId = Guid.NewGuid();
        var notes = "Approved by admin";

        // Create the admin user with proper role/permissions
        var adminUser = new ApplicationUser
        {
            Id = adminId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
            // ... other required properties
        };

        var role = new ApplicationRole
        {
            Id = Guid.NewGuid(),
            Name = "Admin",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.Empty,
        };

        var userRole = new ApplicationUserRole { UserId = adminId, RoleId = role.Id };

        // Create the user who owns the approval
        var user = new ApplicationUser
        {
            Id = userId,
            // Set other required properties
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
            // ... other required properties
        };

        var approval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            ApprovalType = ApprovalType.DocumentVerification,
            IsApproved = null, // Pending
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            IsDeleted = false,
        };

        // Add all entities to context
        _context.Roles.Add(role);
        _context.UserRoles.Add(userRole);
        _context.Users.Add(adminUser);
        _context.Users.Add(user);
        _context.Approvals.Add(approval);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ApproveAsync(approval.Id, adminId, notes);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.True(result.Value.IsApproved);
        Assert.Equal(adminId, result.Value.ProcessedBy);
        Assert.NotNull(result.Value.ProcessedAt);
        Assert.Equal(notes, result.Value.Notes);
        Assert.Equal(adminId, result.Value.UpdatedBy);
        Assert.NotNull(result.Value.UpdatedAt);
    }

    [Fact]
    public async Task ApproveAsync_AlreadyProcessed_ShouldReturnValidationError()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var adminId = Guid.NewGuid();
        var existingAdminId = Guid.NewGuid();

        var user = new ApplicationUser
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var role = new ApplicationRole
        {
            Id = Guid.NewGuid(),
            Name = "Admin",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.Empty,
        };

        var userRole = new ApplicationUserRole { UserId = adminId, RoleId = role.Id };

        var approval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            ApprovalType = ApprovalType.AccountActivation,
            IsApproved = true, // Already approved
            ProcessedBy = existingAdminId,
            ProcessedAt = DateTime.UtcNow.AddHours(-1),
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            CreatedBy = userId,
            IsDeleted = false,
        };
        _context.Roles.Add(role);
        _context.UserRoles.Add(userRole);
        _context.Users.Add(user);
        _context.Approvals.Add(approval);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.ApproveAsync(approval.Id, adminId, "New notes");

        // Assert
        Assert.False(result.IsSuccess);
    }

    [Fact]
    public async Task RejectAsync_ShouldRejectSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var adminId = Guid.NewGuid();
        var rejectionReason = "Insufficient documentation";
        var notes = "Please provide additional documents";

        // Create the admin user with proper role/permissions
        var adminUser = new ApplicationUser
        {
            Id = adminId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var user = new ApplicationUser
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var approval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            ApprovalType = ApprovalType.DocumentVerification,
            IsApproved = null, // Pending
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            IsDeleted = false,
        };

        _context.Users.Add(adminUser); // Add the admin user to the context before adding the approval
        _context.Users.Add(user); // Add the user to the context before adding the approval to avoid foreign key constraint error.
        _context.Approvals.Add(approval);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.RejectAsync(approval.Id, adminId, rejectionReason, notes);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.False(result.Value.IsApproved);
        Assert.Equal(adminId, result.Value.ProcessedBy);
        Assert.NotNull(result.Value.ProcessedAt);
        Assert.Equal(rejectionReason, result.Value.RejectionReason);
        Assert.Equal(notes, result.Value.Notes);
        Assert.Equal(adminId, result.Value.UpdatedBy);
        Assert.NotNull(result.Value.UpdatedAt);
    }

    [Fact]
    public async Task RejectAsync_EmptyRejectionReason_ShouldReturnValidationError()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var adminId = Guid.NewGuid();

        var adminUser = new ApplicationUser
        {
            Id = adminId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var user = new ApplicationUser
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var approval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            ApprovalType = ApprovalType.Other,
            IsApproved = null,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            IsDeleted = false,
        };

        _context.Users.Add(adminUser); // Add the admin user to the context before adding the approval
        _context.Users.Add(user); // Add the user to the context before adding the approval to avoid foreign key constraint error.
        _context.Approvals.Add(approval);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.RejectAsync(approval.Id, adminId, "", "Notes");

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Rejection reason is required", result.Error.Message);
    }

    [Fact]
    public async Task GetApprovalsByUserIdAsync_ShouldReturnUserApprovals()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var otherUserId = Guid.NewGuid();
        var adminId = Guid.NewGuid();

        var adminUser = new ApplicationUser
        {
            Id = adminId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var user = new ApplicationUser
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var otherUser = new ApplicationUser
        {
            Id = otherUserId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var userApproval1 = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            ApprovalType = ApprovalType.CareProviderVerification,
            IsApproved = null,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            IsDeleted = false,
        };

        var userApproval2 = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            ApprovalType = ApprovalType.DocumentVerification,
            IsApproved = true,
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            CreatedBy = userId,
            IsDeleted = false,
        };

        var otherUserApproval = new Approval
        {
            Id = Guid.NewGuid(),
            UserId = otherUserId,
            ApprovalType = ApprovalType.ProfileUpdate,
            IsApproved = null,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = otherUserId,
            IsDeleted = false,
        };

        _context.Users.Add(adminUser); // Add the admin user to the context before adding the approvals
        _context.Users.Add(user);
        _context.Users.Add(otherUser); // Add the user to the context before adding the approvals to avoid foreign key constraint error.
        _context.Approvals.AddRange(userApproval1, userApproval2, otherUserApproval);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetApprovalsByUserIdAsync(userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());
        Assert.All(result.Value, approval => Assert.Equal(userId, approval.UserId));
    }
}
