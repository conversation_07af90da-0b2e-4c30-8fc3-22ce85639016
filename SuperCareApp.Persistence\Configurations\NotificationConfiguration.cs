﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class NotificationConfiguration : IEntityTypeConfiguration<Notification>
    {
        public void Configure(EntityTypeBuilder<Notification> builder)
        {
            builder.HasKey(n => n.Id);

            builder.Property(n => n.Title).IsRequired().HasMaxLength(200);

            builder.Property(n => n.Content).IsRequired().HasMaxLength(1000);

            builder.Property(n => n.NotificationType).IsRequired().HasConversion<string>();

            builder.Property(n => n.ActionUrl).HasMaxLength(512);
        }
    }
}
