﻿version: "3.8"
services:
  # API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supercare-api
    ports:
      - "5089:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=db;Database=supercare;Username=postgres;Password=postgres;Port=5432
      - JwtSettings__Secret=SuperSecureKeyWithAtLeast32Characters123
      - JwtSettings__Issuer=SuperCareApp
      - JwtSettings__Audience=SuperCareAppUsers
      - JwtSettings__ExpiryMinutes=60
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - supercare-network
    volumes:
      - aspnet-keys:/root/.aspnet/DataProtection-Keys

  # PostgreSQL Database
  db:
    image: postgres:16
    container_name: supercare-db
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=supercare
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - supercare-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
 
networks:
  supercare-network:
    driver: bridge

volumes:
  postgres-data:
  aspnet-keys:
