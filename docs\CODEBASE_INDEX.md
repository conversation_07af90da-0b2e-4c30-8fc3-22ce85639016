# SuperCareApp Codebase Index and Architecture

This document provides a comprehensive overview of the `SuperCareApp` solution's architecture and a detailed index of the codebase. The project follows the principles of Clean Architecture, separating concerns into distinct layers to promote maintainability, scalability, and testability.

## High-Level Architecture

The solution is divided into the following primary layers:

- **Domain:** The core of the application, containing business entities and logic.
- **Application:** Orchestrates the domain logic and handles application-specific use cases.
- **Persistence (Infrastructure):** Responsible for data access and other external services.
- **Presentation:** The user-facing layer, implemented as an ASP.NET Core API.
- **Tests:** Contains tests for the various layers of the application.

### Architecture Diagram

The following diagram illustrates the dependencies between these layers:

```mermaid
graph TD
    subgraph Presentation
        A[super-care-app]
    end
    subgraph Application
        B[SuperCareApp.Application]
    end
    subgraph Domain
        C[SuperCareApp.Domain]
    end
    subgraph Infrastructure
        D[SuperCareApp.Persistence]
    end
    subgraph Tests
        E[SuperCareApp.Tests]
    end

    A --> B
    B --> C
    D --> C
    B -- uses --> D
    E --> A
    E --> B
    E --> C
    E --> D
```

---

## Detailed Layer Breakdown

This section provides a more detailed look at the structure and responsibilities of each layer.

### 1. `super-care-app` (Presentation Layer)

This is the main ASP.NET Core Web API project, serving as the entry point for the application. It's responsible for handling HTTP requests, routing, and hosting the API.

- **`Controllers/`**: Manages incoming HTTP requests and orchestrates responses. Each controller corresponds to a specific resource or feature area.
  - `AccountController.cs`: Handles user account management, such as profile updates.
  - `AdminController.cs`: Provides endpoints for administrative tasks.
  - `AuditController.cs`: Exposes endpoints for retrieving audit trail data.
  - `AuthController.cs`: Manages user authentication, including login, logout, and token generation.
  - `AvailabilityTemplateController.cs`: Manages availability templates for care providers.
  - `BookingsController.cs`: Handles all booking-related operations.
  - `CalendarController.cs`: Provides endpoints for calendar and availability management.
  - `CareCategoriesController.cs`: Manages the categories of care services offered.
  - `CertificationsController.cs`: Handles operations related to care provider certifications.
  - `DocumentsController.cs`: Manages document uploads, downloads, and management.
  - `ExampleController.cs`: Contains sample endpoints for developers to reference.
  - `HealthController.cs`: Provides health check endpoints to monitor application status.
  - `TrackingSessionsController.cs`: Manages location tracking for care providers during bookings.
  - `Admin/DashboardController.cs`: Manages data and statistics for the admin dashboard.
- **`Extensions/`**: Contains extension methods that add functionality to existing classes, simplifying service registration and configuration.
- **`Filters/`**: Includes custom action filters for cross-cutting concerns like request validation and response formatting.
- **`Middleware/`**: Custom middleware components for processing requests and responses in the pipeline, such as global exception handling.
- **`Models/`**: Contains Data Transfer Objects (DTOs) and view models used for API requests and responses.
- **`Properties/`**: Includes project-specific settings, such as `launchSettings.json` for defining debug profiles.
- **`Swagger/`**: Holds configurations for Swagger/OpenAPI, enabling interactive API documentation.
- **`Program.cs`**: The application's main entry point, where services are configured and the application is built and run.

### 2. `SuperCareApp.Application` (Application Layer)

This project contains the core application logic, including business rules, services, and interfaces. It acts as a bridge between the API and the domain layer and follows CQRS principles.

- **`Common/`**: Houses shared components for the application layer.
  - **`Interfaces/`**: Defines the contracts (interfaces) for services and repositories, promoting loose coupling. These are organized by feature (e.g., `Bookings`, `Identity`, `Documents`).
    - `IRecommendationEngine.cs`: Defines the contract for the recommendation engine.
    - `IBookingService.cs`: Defines operations for booking management.
    - `ICalendarService.cs`: Defines operations for calendar and availability management.
    - `IDocumentService.cs`: Defines operations for document management.
    - `IAuthService.cs`: Defines operations for user identity and authentication.
    - `ITrackingSessionService.cs`: Defines operations for location tracking during bookings.
  - **`Models/`**: Contains DTOs and models used internally within the application layer, organized by feature.
    - Request/response models for API operations
    - Specialized DTOs for internal service communication
    - Validation models for input validation
  - **`Settings/`**: Holds configuration settings classes (e.g., `CacheSettings`, `OtpSettings`, `TwilioSettings`).
  - **`Specifications/`**: Contains base classes for the specification pattern, enabling complex queries.
- **`Shared/`**: Provides shared utilities and helper classes.
  - **`Dispatcher/`**: Defines interfaces for dispatching events or messages, like `IOtpDispatcher`.
  - **`Utility/`**: Contains helper classes and extensions for validation, pagination (`PagedList.cs`), and API responses.
- **`DependencyInjection.cs`**: This file contains the service registration for the application layer, making it easy to manage dependencies using the built-in DI container in ASP.NET Core.

#### Use Cases

The Application layer handles the following key use cases:

- User registration, authentication, and profile management
- Booking creation, modification, and cancellation
- Provider availability management and scheduling
- Document upload, verification, and management
- Location tracking during service delivery
- Notifications and messaging between users
- Administrative operations and reporting
- Payment processing and invoice generation

### 3. `SuperCareApp.Domain` (Domain Layer)

This project is the heart of the application, representing the core business domain. It contains all the entities, enums, and domain-specific logic.

- **`Entities/`**: This directory contains the core business objects of the application, such as `Booking`, `CareProviderProfile`, `UserProfile`, and `Invoice`. These classes represent the fundamental concepts of the domain.
- **`Enums/`**: Contains various enumerations used throughout the domain, such as `BookingStatus`, `UserRole`, and `PaymentStatus`. These provide strong typing for states and categories.
- **`Identity/`**: Defines the entities for authentication and authorization, built upon ASP.NET Core Identity. This includes `ApplicationUser`, `ApplicationRole`, and their related claims and tokens.
- **`Common/`**: Holds base classes, exceptions, and other common components that are shared across the domain.
- **`Validation/`**: Contains domain-specific validation logic.
- **`Constants/`**: Defines constants used within the domain layer.

#### Domain Rules and Invariants

The Domain layer enforces critical business rules such as:

- A booking cannot be scheduled outside a provider's availability
- A provider must have verified credentials to offer certain care categories
- Users must verify their identity before accessing sensitive features
- Payments must be processed before a booking is confirmed
- Location tracking is only active during active bookings
- Reviews can only be submitted for completed bookings

### 4. `SuperCareApp.Persistence` (Infrastructure Layer)

This project is responsible for all data access and persistence concerns. It uses Entity Framework Core to interact with the database and implements the CQRS pattern for data retrieval and modification.

- **`Repositories/`**: Contains the concrete implementations of the repository interfaces defined in the application layer. It uses Entity Framework Core for data access and includes a generic repository pattern to reduce boilerplate code. The `UnitOfWork.cs` class is used to manage transactions and ensure data consistency.
- **`Services/`**: Implements the various service interfaces defined in the application layer. This includes services for authentication, sending emails, and interacting with third-party APIs. The use of the Command and Query Responsibility Segregation (CQRS) pattern is a key feature of this layer, with commands and queries clearly separated into their own folders.
- **`Context/`**: Contains the `DbContext` class, which represents the session with the database and allows for querying and saving data.
- **`Migrations/`**: Holds the database migration files, which are used to manage the database schema over time.
- **`Configurations/`**: Contains the Entity Framework Core configurations for the domain entities, defining how they map to the database tables.
- **`DependencyInjection.cs`**: This file is responsible for registering the services and repositories from the persistence layer with the dependency injection container.
- **`Behaviours/`**: Contains MediatR pipeline behaviors for cross-cutting concerns like caching, logging, and validation.
- **`Interceptors/`**: EF Core interceptors for database operations, such as the `AuditInterceptor` for automatically logging data changes.
- **`Jobs/`**: Contains background jobs and scheduled tasks, such as the `RecommendationEngineInitializer`.

#### Implementation Details

The Persistence layer implements several advanced patterns and features:

- CQRS pattern with separate command and query handlers
- Repository pattern for data access abstraction
- Unit of Work pattern for transaction management
- Specification pattern for complex queries
- Audit logging through EF Core interceptors
- Database migrations for schema evolution
- Spatial data handling with R-Tree indexes for location-based recommendations
- Caching strategies for performance optimization
- Background job processing for asynchronous operations

### 5. `SuperCareApp.Tests` (Testing Layer)

This project is dedicated to testing the application's functionality. It includes unit tests, integration tests, and any other tests needed to ensure code quality.

- **`Admin/`**: Contains tests specifically for administrative functionalities.
- **`Services/`**: Contains tests for service implementations.
