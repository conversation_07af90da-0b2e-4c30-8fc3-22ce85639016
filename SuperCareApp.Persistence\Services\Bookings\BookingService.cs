using System.Globalization;
using System.Linq.Expressions;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Shared.Utility;
using BookingStatus = SuperCareApp.Domain.Entities.BookingStatus;

namespace SuperCareApp.Persistence.Services.Bookings
{
    public class BookingService : IBookingService
    {
        private readonly ApplicationDbContext _context;
        private readonly IBookingManagementService _scheduleService;
        private readonly ILogger<BookingService> _logger;

        public BookingService(
            ApplicationDbContext context,
            IBookingManagementService scheduleService,
            ILogger<BookingService> logger
        )
        {
            _context = context;
            _scheduleService = scheduleService;
            _logger = logger;
        }

        public async Task<Result<Guid>> CreateBookingAsync(
            Guid userId,
            Guid providerId,
            Guid categoryId,
            DateTime startDate,
            DateTime endDate,
            TimeOnly? startTime,
            TimeOnly? endTime,
            string? specialInstructions,
            int workingHours = 8
        )
        {
            try
            {
                //Check whether start and end dates are in past
                if (startDate.Date < DateTime.Today || endDate.Date < DateTime.Today)
                    return Result.Failure<Guid>(Error.BadRequest("Dates cannot be in the past"));
                // Validate entities exist

                //If client acts as provider should not be able to create booking
                if (userId == providerId)
                    return Result.Failure<Guid>(Error.Forbidden("cannot book their own services"));

                var validationResult = await ValidateBookingEntitiesAsync(
                    userId,
                    providerId,
                    categoryId
                );
                if (!validationResult.IsSuccess)
                    return Result.Failure<Guid>(validationResult.Error);

                var (client, providerProfile, category) = validationResult.Value;

                // Validate date range
                if (endDate.Date < startDate.Date)
                    return Result.Failure<Guid>(
                        Error.Validation("End date must be after start date")
                    );

                var bookingWindows = new List<BookingWindowRequest>();
                if (startDate.Date == endDate.Date)
                {
                    if (startTime == null || endTime == null)
                        return Result.Failure<Guid>(
                            Error.Validation(
                                "Start time and end time are required for single-day booking."
                            )
                        );
                    var timeValidation = ValidateTimeRange((TimeOnly)startTime, (TimeOnly)endTime);
                    if (!timeValidation.IsSuccess)
                        return Result.Failure<Guid>(timeValidation.Error);

                    bookingWindows.Add(
                        new BookingWindowRequest
                        {
                            Date = startDate,
                            StartTime = startTime.Value,
                            EndTime = endTime.Value,
                        }
                    );
                }
                else
                {
                    if (startTime == null || endTime == null)
                        return Result.Failure<Guid>(
                            Error.Validation(
                                "Start time and end time are required for multi-day booking."
                            )
                        );
                    var timeValidation = ValidateTimeRange((TimeOnly)startTime, (TimeOnly)endTime);
                    if (!timeValidation.IsSuccess)
                        return Result.Failure<Guid>(timeValidation.Error);

                    for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
                    {
                        bookingWindows.Add(
                            new BookingWindowRequest
                            {
                                Date = date,
                                StartTime = startTime.Value,
                                EndTime = endTime.Value,
                            }
                        );
                    }
                }

                // 1. Validate each window against provider availability
                foreach (var window in bookingWindows)
                {
                    var availabilityResult = await ValidateAvailabilityAsync(
                        providerId,
                        window.Date,
                        window.StartTime,
                        window.EndTime
                    );
                    if (!availabilityResult.IsSuccess)
                    {
                        return Result.Failure<Guid>(
                            Error.Conflict(
                                $"Provider is not available on {window.Date.ToShortDateString()} from {window.StartTime} to {window.EndTime}."
                            )
                        );
                    }
                }

                // 2. Calculate total amounts from windows
                var totalHours = bookingWindows.Sum(w =>
                    (decimal)(w.EndTime - w.StartTime).TotalHours
                );
                var (providerAmount, serviceFee, totalAmount) = CalculateBookingAmounts(
                    providerProfile,
                    category,
                    (int)totalHours,
                    1
                );

                // 3. Create Booking entity
                var booking = new Booking
                {
                    Id = Guid.NewGuid(),
                    ClientId = client.Id,
                    ProviderId = providerId,
                    CategoryId = categoryId,
                    TotalAmount = totalAmount,
                    PlatformFee = serviceFee,
                    ProviderAmount = providerAmount,
                    SpecialInstructions = specialInstructions,
                    CreatedBy = userId,
                    WorkingHours = (int)totalHours,
                    CreatedAt = DateTime.UtcNow,
                };

                // 4. Create BookingWindow entities
                var bookingWindowEntities = bookingWindows
                    .Select(w => new BookingWindow
                    {
                        BookingId = booking.Id,
                        Date = DateOnly.FromDateTime(w.Date),
                        StartTime = w.StartTime,
                        EndTime = w.EndTime,
                        CreatedBy = userId,
                        Status = BookingWindowStatus.Upcoming,
                    })
                    .ToList();
                booking.BookingWindows = bookingWindowEntities;

                var bookingStatus = CreateBookingStatusEntity(
                    booking.Id,
                    userId,
                    "Booking created"
                );

                await SaveBookingAsync(booking, bookingStatus);

                _logger.LogInformation(
                    "Booking {BookingId} created successfully for client {ClientId} and provider {ProviderId}, WorkingHours: {WorkingHours}",
                    booking.Id,
                    client.Id,
                    providerId,
                    booking.WorkingHours
                );

                return Result.Success(booking.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating booking for user {UserId}", userId);
                return Result.Failure<Guid>(
                    Error.Internal($"Error creating booking {ex.Message} {ex.StackTrace}")
                );
            }
        }

        private (
            decimal providerAmount,
            decimal serviceFee,
            decimal totalAmount
        ) CalculateBookingAmounts(
            CareProviderProfile providerProfile,
            CareCategory category,
            int workingHours,
            int days
        )
        {
            // Assuming HourlyRate is used for pricing
            var hourlyRate =
                providerProfile
                    .CareProviderCategories?.FirstOrDefault(c => c.CategoryId == category.Id)
                    ?.HourlyRate ?? 0m;

            // Log warning if hourly rate is 0 to help with debugging
            if (hourlyRate == 0m)
            {
                _logger.LogWarning(
                    "Hourly rate is 0 for provider {ProviderId} and category {CategoryId}. "
                        + "CareProviderCategories count: {CategoriesCount}",
                    providerProfile.Id,
                    category.Id,
                    providerProfile.CareProviderCategories?.Count ?? 0
                );
            }

            var providerAmount = hourlyRate * workingHours * days;
            var serviceFee = category.PlatformFee;
            var platformFee = providerAmount * serviceFee;
            var totalAmount = providerAmount + platformFee;
            return (providerAmount, serviceFee, totalAmount);
        }

        public async Task<Result<Guid>> CreateBookingWithWindowsAsync(
            Guid userId,
            Guid providerId,
            Guid categoryId,
            DateTime startDate,
            DateTime endDate,
            bool isRecurring,
            string? specialInstructions,
            List<BookingWindowRequest> bookingWindows
        )
        {
            try
            {
                var validationResult = await ValidateBookingEntitiesAsync(
                    userId,
                    providerId,
                    categoryId
                );
                if (!validationResult.IsSuccess)
                    return Result.Failure<Guid>(validationResult.Error);

                var (client, providerProfile, category) = validationResult.Value;

                // 1. Validate each window against provider availability
                foreach (var window in bookingWindows)
                {
                    var availabilityResult = await ValidateAvailabilityAsync(
                        providerId,
                        window.Date,
                        window.StartTime,
                        window.EndTime
                    );
                    if (!availabilityResult.IsSuccess)
                    {
                        return Result.Failure<Guid>(
                            Error.Conflict(
                                $"Provider is not available on {window.Date.ToShortDateString()} from {window.StartTime} to {window.EndTime}."
                            )
                        );
                    }
                }

                // 2. Calculate total amounts from windows
                var totalAmount = bookingWindows.Sum(w =>
                {
                    var hourlyRate =
                        providerProfile
                            .CareProviderCategories?.FirstOrDefault(c => c.CategoryId == categoryId)
                            ?.HourlyRate ?? 0m;
                    return hourlyRate * (decimal)(w.EndTime - w.StartTime).TotalHours;
                });
                var serviceFee = category.PlatformFee; // Or calculate based on total
                var providerAmount = totalAmount / (1 + serviceFee);

                // 3. Create Booking entity (without StartTime/EndTime)
                var booking = new Booking
                {
                    ClientId = client.Id,
                    ProviderId = providerId,
                    CategoryId = categoryId,
                    TotalAmount =
                        totalAmount != null!
                            ? totalAmount
                            : throw new InvalidOperationException("Total amount cannot be null"),
                    PlatformFee = serviceFee,
                    ProviderAmount =
                        providerAmount != null!
                            ? providerAmount
                            : throw new InvalidOperationException("Provider amount cannot be null"),
                    SpecialInstructions = specialInstructions,
                    CreatedBy = userId,
                };

                // 4. Create BookingWindow entities
                var bookingWindowEntities = bookingWindows
                    .Select(w => new BookingWindow
                    {
                        BookingId = booking.Id,
                        Date = DateOnly.FromDateTime(w.Date),
                        StartTime = w.StartTime,
                        EndTime = w.EndTime,
                        CreatedBy = userId,
                    })
                    .ToList();
                booking.BookingWindows = bookingWindowEntities;
                // 5. Save to database
                await _context.Bookings.AddAsync(booking);
                // BookingWindows will be saved automatically due to the relationship
                await _context.SaveChangesAsync();
                return Result.Success(booking.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error creating booking with windows for user {UserId} {Message} {StackTrace}",
                    userId,
                    ex.Message,
                    ex.StackTrace
                );
                return Result.Failure<Guid>(Error.Internal("Error creating booking with windows."));
            }
        }

        public async Task<Result> UpdateBookingAsync(
            Guid userId,
            Guid bookingId,
            Guid categoryId,
            DateTime bookingDate,
            TimeOnly startTime,
            TimeOnly endTime,
            string? specialInstruction
        )
        {
            try
            {
                //Validate for past dates
                if (bookingDate.Date < DateTime.Today)
                    return Result.Failure(Error.BadRequest("Dates cannot be in the past"));
                // Validate user is not a provider
                if (await _context.CareProviderProfiles.AnyAsync(p => p.UserId == userId))
                    return Result.Failure(Error.Forbidden("not authorized to update booking"));

                //Non existent booking should return failure
                var bookingExists = await _context.Bookings.AnyAsync(b => b.Id == bookingId);
                if (!bookingExists)
                    return Result.Failure(Error.NotFound("Booking not found"));

                //Non existent category should return failure
                var category = await _context.CareCategories.FirstOrDefaultAsync(c =>
                    c.Id == categoryId
                );
                if (category == null)
                    return Result.Failure(Error.NotFound("Category not found"));

                //Completed or cancelled booking should return failure
                var bookingStatus = await _context
                    .BookingStatuses.Where(b => b.BookingId == bookingId)
                    .OrderByDescending(b => b.CreatedAt)
                    .FirstOrDefaultAsync();
                if (
                    bookingStatus != null
                    && (
                        bookingStatus.Status == BookingStatusType.Completed
                        || bookingStatus.Status == BookingStatusType.Cancelled
                    )
                )
                    return Result.Failure(Error.BadRequest("Booking is completed or cancelled"));

                var booking = await _context
                    .Bookings.Include(b => b.BookingWindows)
                    .FirstOrDefaultAsync(b => b.Id == bookingId);

                if (booking == null)
                    return Result.Failure(Error.NotFound("Booking not found"));
                var providerId = booking.ProviderId;
                // Validate time range
                var timeValidation = ValidateTimeRange(startTime, endTime);
                if (!timeValidation.IsSuccess)
                    return Result.Failure(timeValidation.Error);

                // Check availability
                var availabilityResult = await ValidateAvailabilityAsync(
                    providerId,
                    bookingDate,
                    startTime,
                    endTime
                );
                if (!availabilityResult.IsSuccess)
                    return Result.Failure(availabilityResult.Error);

                // Update existing BookingWindow (assumes one window per booking)
                var window = booking.BookingWindows.FirstOrDefault();
                if (window != null)
                {
                    window.Date = DateOnly.FromDateTime(bookingDate);
                    window.StartTime = startTime;
                    window.EndTime = endTime;
                    window.UpdatedAt = DateTime.UtcNow;
                    window.UpdatedBy = userId;
                }
                else
                {
                    // If none exists, create one
                    booking.BookingWindows = new List<BookingWindow>
                    {
                        new()
                        {
                            BookingId = booking.Id,
                            Date = DateOnly.FromDateTime(bookingDate),
                            StartTime = startTime,
                            EndTime = endTime,
                            CreatedBy = userId,
                        },
                    };
                }

                // Update booking properties
                booking.ProviderId = providerId;
                booking.CategoryId = categoryId;
                booking.SpecialInstructions = specialInstruction;
                booking.UpdatedAt = DateTime.UtcNow;
                booking.UpdatedBy = userId;

                // Recalculate working hours
                booking.WorkingHours = (int)Math.Ceiling((endTime - startTime).TotalHours);

                await _context.SaveChangesAsync();

                _logger.LogInformation("Booking {BookingId} updated successfully", bookingId);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating booking {BookingId} cause {Message} {StackTrace}",
                    bookingId,
                    ex.Message,
                    ex.StackTrace
                );
                return Result.Failure(
                    Error.Internal("Error updating booking {Message}" + ex.Message)
                );
            }
        }

        public async Task<Result<BookingResponse>> GetBookingByIdAsync(Guid bookingId)
        {
            try
            {
                var booking = await GetBookingResponseAsync(bookingId);
                if (booking == null)
                    return Result.Failure<BookingResponse>(Error.NotFound("Booking not found"));

                return Result.Success(booking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving booking {BookingId}", bookingId);
                return Result.Failure<BookingResponse>(Error.Internal("Error retrieving booking"));
            }
        }

        public async Task<Result> DeleteBookingAsync(Guid userId, Guid bookingId)
        {
            try
            {
                var booking = await _context
                    .Bookings.Include(b => b.BookingWindows)
                    .FirstOrDefaultAsync(b => b.Id == bookingId); // Remove && !b.IsDeleted

                if (booking == null)
                    return Result.Failure(Error.NotFound("Booking not found"));

                if (!IsUserAuthorizedForBooking(userId, booking.ClientId))
                    return Result.Failure(
                        Error.Unauthorized("User is not authorized to delete this booking")
                    );

                // If already deleted, just return success (idempotent)
                if (booking.IsDeleted)
                {
                    _logger.LogInformation(
                        "Booking {BookingId} already deleted, returning success for idempotency",
                        bookingId
                    );
                    return Result.Success();
                }

                MarkBookingAsDeleted(booking, userId);

                // Mark all booking windows as deleted
                foreach (var window in booking.BookingWindows.Where(w => !w.IsDeleted))
                {
                    window.IsDeleted = true;
                    window.DeletedAt = DateTime.UtcNow;
                    window.DeletedBy = userId;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Booking {BookingId} deleted by user {UserId}",
                    bookingId,
                    userId
                );
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting booking {BookingId}", bookingId);
                return Result.Failure(Error.Internal("Error deleting booking"));
            }
        }

        public async Task<Result> CancelBookingAsync(
            Guid userId,
            Guid bookingId,
            string? reason = null,
            bool notifyOtherParty = true,
            bool forceCancel = false
        )
        {
            try
            {
                var booking = await GetBookingWithStatusAsync(bookingId);
                if (booking == null)
                    return Result.Failure(Error.NotFound("Booking not found"));

                if (!IsUserAuthorizedForBooking(userId, booking.ClientId, booking.ProviderId))
                    return Result.Failure(
                        Error.Unauthorized("User is not authorized to cancel this booking")
                    );

                var cancellationValidation = ValidateCancellation(booking, forceCancel);
                if (!cancellationValidation.IsSuccess)
                    return Result.Failure(cancellationValidation.Error);

                var cancellationStatus = CreateCancellationStatus(booking.Id, userId, reason);
                await SaveCancellationAsync(booking, cancellationStatus);

                _logger.LogInformation(
                    "Booking {BookingId} cancelled by user {UserId}. Reason: {Reason}",
                    bookingId,
                    userId,
                    reason ?? "No reason provided"
                );

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling booking {BookingId}", bookingId);
                return Result.Failure(Error.Internal("Error cancelling booking"));
            }
        }

        public async Task<Result<PagedBookingList>> GetAllBookingsAsync(
            BookingsRequest request,
            BookingListParams parameters,
            CancellationToken cancellationToken
        )
        {
            try
            {
                if (parameters == null)
                    return Result.Failure<PagedBookingList>(
                        Error.Validation("BookingListParams cannot be null")
                    );

                // Build base query without includes for count
                var baseQuery = _context
                    .Bookings.AsNoTracking()
                    .TagWith("GetAllBookings_Count")
                    .Where(b => !b.IsDeleted);

                // Apply filters but not includes for counting
                baseQuery = ApplyFilters(baseQuery, request);

                // Get total count efficiently
                var totalCount = await baseQuery.CountAsync(cancellationToken);
                var pagination = CalculatePagination(parameters, totalCount);

                // Build full query with includes for actual data - FIXED: Now properly applies filters
                var bookingsQuery = BuildBookingQuery(request);
                // Apply sorting and pagination to the filtered query
                var sortedQuery = ApplySortingToBookingResponse(bookingsQuery, parameters);
                var skip = (parameters.PageNumber - 1) * pagination.PageSize;
                var pagedBookings = await sortedQuery
                    .Skip(skip)
                    .Take(pagination.PageSize)
                    .ToListAsync(cancellationToken);

                var result = new PagedBookingList
                {
                    Bookings = pagedBookings,
                    PageNumber = parameters.PageNumber,
                    PageSize = pagination.PageSize,
                    TotalCount = totalCount,
                    TotalPages = pagination.TotalPages,
                };

                return Result.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving bookings for user {ex}", ex.Message);
                return Result.Failure<PagedBookingList>(
                    Error.Internal($"Error retrieving bookings {ex.Message}")
                );
            }
        }

        #region Private Helper Methods

        private async Task<
            Result<(ApplicationUser client, CareProviderProfile provider, CareCategory category)>
        > ValidateBookingEntitiesAsync(Guid userId, Guid providerId, Guid categoryId)
        {
            var client = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (client == null)
                return Result.Failure<(ApplicationUser, CareProviderProfile, CareCategory)>(
                    Error.NotFound("Client not found")
                );

            var providerProfile = await _context
                .CareProviderProfiles.Include(p => p.CareProviderCategories)
                .FirstOrDefaultAsync(p => p.Id == providerId);
            if (providerProfile == null)
                return Result.Failure<(ApplicationUser, CareProviderProfile, CareCategory)>(
                    Error.NotFound("Provider profile not found")
                );

            var category = await _context.CareCategories.FirstOrDefaultAsync(c =>
                c.Id == categoryId
            );
            if (category == null)
                return Result.Failure<(ApplicationUser, CareProviderProfile, CareCategory)>(
                    Error.NotFound("Category not found")
                );

            return Result.Success((client, providerProfile, category));
        }

        private static Result<double> ValidateTimeRange(TimeOnly startTime, TimeOnly endTime)
        {
            var duration = endTime.ToTimeSpan().TotalHours - startTime.ToTimeSpan().TotalHours;
            if (duration <= 0)
                return Result.Failure<double>(Error.Conflict("End time must be after start time"));

            return Result.Success(duration);
        }

        private async Task<Result> ValidateAvailabilityAsync(
            Guid providerId,
            DateTime bookingDate,
            TimeOnly startTime,
            TimeOnly endTime
        )
        {
            var dateOnly = DateOnly.FromDateTime(bookingDate);
            var requestedSlot = new Interval<TimeOnly>(startTime, endTime);
            requestedSlot.Validate();

            var availableSlots = await _scheduleService.GetAvailableSlotsForDateAsync(
                providerId,
                dateOnly
            );

            // Handle null collection gracefully
            if (availableSlots == null)
                return Result.Failure(Error.Internal("Failed to retrieve available slots"));

            if (!availableSlots.Any())
                return Result.Failure(Error.Conflict("No available slots for the requested date"));

            if (!availableSlots.Any(slot => IntervalUtils.IsContainedWithin(requestedSlot, slot)))
                return Result.Failure(Error.Conflict("Requested slot is not available"));

            return Result.Success();
        }

        private static BookingStatus CreateBookingStatusEntity(
            Guid bookingId,
            Guid userId,
            string notes
        )
        {
            return new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = bookingId,
                Status = BookingStatusType.Requested,
                CreatedBy = userId,
                Notes = notes,
                CreatedAt = DateTime.UtcNow,
            };
        }

        private async Task SaveBookingAsync(Booking booking, BookingStatus bookingStatus)
        {
            await _context.Bookings.AddAsync(booking);
            await _context.BookingStatuses.AddAsync(bookingStatus);
            await _context.SaveChangesAsync();
        }

        private async Task<BookingResponse?> GetBookingWithRelatedDataAsync(Guid bookingId)
        {
            // Use a projection to only select the fields needed for BookingResponse
            var booking = await _context
                .Bookings.AsNoTracking()
                .Where(b => b.Id == bookingId && !b.IsDeleted)
                .Select(b => new BookingResponse
                {
                    BookingId = b.Id,
                    Duration = b
                        .BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours)
                        .ToString(CultureInfo.InvariantCulture),
                    Description = b.SpecialInstructions,
                    Status = b.Status != null ? b.Status.Status.GetDescription() : string.Empty,
                    ServiceType = b.Category != null ? b.Category.Name : string.Empty,
                    TotalAmount = b.TotalAmount,
                    Clients = new BookingResponse.Client
                    {
                        Name =
                            (b.Client.UserProfile.FirstName ?? "")
                            + " "
                            + (b.Client.UserProfile.LastName ?? ""),
                        PhoneNumber = b.Client.PhoneNumber ?? string.Empty,
                        ProfilePictureUrl = UrlHelper.BuildImageUrl(b.Client.UserProfile.ImagePath),
                        Email = b.Client.Email ?? string.Empty,
                        Location =
                            b.Client.UserAddresses.Where(ua => !ua.IsDeleted && ua.IsPrimary)
                                .Select(ua => new BookingResponse.Location
                                {
                                    StreetAddress = ua.Address.StreetAddress ?? string.Empty,
                                    City = ua.Address.City ?? string.Empty,
                                    State = ua.Address.State ?? string.Empty,
                                    Country = b.Client.UserProfile.Country ?? string.Empty,
                                    PostalCode = ua.Address.PostalCode ?? string.Empty,
                                    Longitude = ua.Address.Longitude,
                                    Latitude = ua.Address.Latitude,
                                })
                                .FirstOrDefault()
                            ?? new BookingResponse.Location
                            {
                                StreetAddress = string.Empty,
                                City = string.Empty,
                                State = string.Empty,
                                Country = string.Empty,
                                PostalCode = string.Empty,
                                Longitude = null,
                                Latitude = null,
                            },
                    },
                    CareProviders = new BookingResponse.CareProvider
                    {
                        ProviderId = b.Provider.UserId,
                        Name =
                            (b.Provider.User.UserProfile.FirstName ?? "")
                            + " "
                            + (b.Provider.User.UserProfile.LastName ?? ""),
                        PhoneNumber = b.Provider.User.PhoneNumber ?? string.Empty,
                        ProfilePictureUrl = UrlHelper.BuildImageUrl(
                            b.Provider.User.UserProfile.ImagePath
                        ),
                        Email = b.Provider.User.Email ?? string.Empty,
                        YearsOfExperience = (int?)b.Provider.YearsExperience ?? 0,
                        Rating = b.Provider.Rating ?? 0,
                    },
                    BookingWindows = b
                        .BookingWindows.Select(w => new BookingWindowResponse
                        {
                            BookingWindowId = w.Id,
                            Date = w.Date.ToDateTime(w.StartTime),
                            StartTime = w.StartTime.ToTimeSpan(),
                            EndTime = w.EndTime.ToTimeSpan(),
                            Status = w.Status != null ? w.Status.ToString() : string.Empty,
                            InvoiceFileName = w.Invoice.FileName,
                            InvoiceFileUrl = w.Invoice.FileUrl,
                            TrackingSessionId = w
                                .TrackingSessions.Where(ts =>
                                    !ts.IsDeleted && ts.BookingWindowId == w.Id
                                )
                                .Select(ts => (Guid?)ts.Id)
                                .FirstOrDefault(),
                            TrackingSessionStatus = w
                                .TrackingSessions.Where(ts =>
                                    !ts.IsDeleted && ts.BookingWindowId == w.Id
                                )
                                .Select(ts => ts.Status.ToString())
                                .FirstOrDefault(),
                        })
                        .ToList(),
                })
                .FirstOrDefaultAsync();

            return booking;
        }

        private async Task<BookingResponse?> GetBookingResponseAsync(Guid bookingId)
        {
            try
            {
                var response = await GetBookingWithRelatedDataAsync(bookingId);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting booking response");
                throw new InvalidOperationException("Error getting booking response", ex);
            }
        }

        private static BookingResponse.Client CreateClientResponse(Booking booking)
        {
            var primaryAddress = booking.Client?.UserAddresses?.FirstOrDefault(ua =>
                ua is { IsPrimary: true, IsDeleted: false }
            );

            return new BookingResponse.Client
            {
                Name = GetFullName(
                    booking.Client?.UserProfile?.FirstName,
                    booking.Client?.UserProfile?.LastName
                ),
                PhoneNumber = booking.Client?.PhoneNumber ?? string.Empty,
                Email = booking.Client?.Email ?? string.Empty,
                Location =
                    primaryAddress != null
                        ? CreateLocationResponse(
                            primaryAddress,
                            booking.Client?.UserProfile?.Country
                        )
                        : CreateEmptyLocation(),
            };
        }

        private static BookingResponse.CareProvider CreateCareProviderResponse(Booking booking)
        {
            return new BookingResponse.CareProvider
            {
                ProviderId = booking.Provider?.UserId ?? Guid.Empty,
                Name = GetFullName(
                    booking.Provider?.User?.UserProfile?.FirstName,
                    booking.Provider?.User?.UserProfile?.LastName
                ),
                PhoneNumber = booking.Provider?.User?.PhoneNumber ?? string.Empty,
                Email = booking.Provider?.User?.Email ?? string.Empty,
                YearsOfExperience = booking.Provider?.YearsExperience ?? 0,
                Rating = booking.Provider?.Rating ?? 0,
            };
        }

        private static string GetFullName(string? firstName, string? lastName) =>
            $"{firstName} {lastName}".Trim();

        private static BookingResponse.Location CreateLocationResponse(
            UserAddress address,
            string? country
        )
        {
            return new BookingResponse.Location
            {
                StreetAddress = address.Address?.StreetAddress ?? string.Empty,
                City = address.Address?.City ?? string.Empty,
                State = address.Address?.State ?? string.Empty,
                Country = country ?? string.Empty,
                PostalCode = address.Address?.PostalCode ?? string.Empty,
                Longitude = address.Address?.Longitude,
                Latitude = address.Address?.Latitude,
            };
        }

        private static BookingResponse.Location CreateEmptyLocation()
        {
            return new BookingResponse.Location
            {
                StreetAddress = string.Empty,
                City = string.Empty,
                State = string.Empty,
                Country = string.Empty,
                PostalCode = string.Empty,
                Longitude = null,
                Latitude = null,
            };
        }

        private static bool IsUserAuthorizedForBooking(
            Guid userId,
            Guid clientId,
            Guid? providerId = null
        ) => userId == clientId || (providerId.HasValue && userId == providerId.Value);

        private static void MarkBookingAsDeleted(Booking booking, Guid userId)
        {
            booking.IsDeleted = true;
            booking.DeletedAt = DateTime.UtcNow; // Add this line
            booking.DeletedBy = userId; // Add this line
            booking.UpdatedAt = DateTime.UtcNow;
            booking.UpdatedBy = userId;
        }

        private async Task<Booking?> GetBookingWithStatusAsync(Guid bookingId)
        {
            return await _context
                .Bookings.Include(b => b.Status)
                .Include(b => b.Client)
                .Include(b => b.Provider)
                .Include(b => b.BookingWindows)
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted);
        }

        private static Result ValidateCancellation(Booking booking, bool forceCancel)
        {
            if (booking.Status?.Status == BookingStatusType.Cancelled)
                return Result.Failure(Error.Conflict("Booking is already cancelled"));

            if (booking.Status?.Status == BookingStatusType.Completed)
                return Result.Failure(Error.Conflict("Cannot cancel a completed booking"));

            if (!forceCancel)
            {
                var firstWindow = booking
                    .BookingWindows.OrderBy(w => w.Date)
                    .ThenBy(w => w.StartTime)
                    .FirstOrDefault();
                if (firstWindow != null)
                {
                    var bookingDateTime = firstWindow.Date.ToDateTime(firstWindow.StartTime);
                    var cancellationDeadline = bookingDateTime.AddHours(-24);

                    if (DateTime.UtcNow > cancellationDeadline)
                        return Result.Failure(
                            Error.Conflict(
                                "Cannot cancel booking within 24 hours of start time. Use force cancel if necessary."
                            )
                        );
                }
            }

            return Result.Success();
        }

        private static BookingStatus CreateCancellationStatus(
            Guid bookingId,
            Guid userId,
            string? reason
        )
        {
            return new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = bookingId,
                Status = BookingStatusType.Cancelled,
                CreatedBy = userId,
                Notes = string.IsNullOrWhiteSpace(reason)
                    ? "Booking cancelled"
                    : $"Booking cancelled: {reason}",
                CreatedAt = DateTime.UtcNow,
            };
        }

        private async Task SaveCancellationAsync(Booking booking, BookingStatus cancellationStatus)
        {
            _context.BookingStatuses.Add(cancellationStatus);
            booking.Status = cancellationStatus;
            await _context.SaveChangesAsync();
        }

        private static Expression<
            Func<Booking, BookingResponse>
        > CreateBookingResponseProjection() =>
            booking => new BookingResponse
            {
                BookingId = booking.Id,
                TotalAmount = booking.TotalAmount,
                Status =
                    booking.Status != null ? booking.Status.Status.GetDescription() : string.Empty,
                ServiceType = booking.Category != null ? booking.Category.Name : string.Empty,
                Clients = CreateClientResponse(booking),
                CareProviders = CreateCareProviderResponse(booking),

                // Populate BookingWindows
                BookingWindows = booking
                    .BookingWindows.Select(w => new BookingWindowResponse
                    {
                        BookingWindowId = w.Id,
                        Date = w.Date.ToDateTime(w.StartTime),
                        StartTime = w.StartTime.ToTimeSpan(),
                        EndTime = w.EndTime.ToTimeSpan(),
                        Status = w.Status != null ? w.Status.ToString() : string.Empty,
                        InvoiceFileName = w.Invoice.FileName,
                        InvoiceFileUrl = w.Invoice.FileUrl,
                    })
                    .ToList(),
                Duration = booking
                    .BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours)
                    .ToString(CultureInfo.InvariantCulture),
            };

        private IQueryable<BookingResponse> BuildBookingQuery(BookingsRequest filter)
        {
            var query = _context.Bookings.AsNoTracking().Where(b => !b.IsDeleted);

            // Apply all filters first - FIXED: Now actually applies the filters
            query = ApplyFilters(query, filter);

            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                query = ApplySearchFilter(query, filter.SearchTerm.ToLower());
            }

            query.OrderByDescending(b => b.CreatedAt);

            // Project to BookingResponse instead of loading full entities
            return query.Select(b => new BookingResponse
            {
                BookingId = b.Id,
                Duration = b
                    .BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours)
                    .ToString(CultureInfo.InvariantCulture),
                Status = b.Status != null ? b.Status.Status.GetDescription() : string.Empty,
                ServiceType = b.Category != null ? b.Category.Name : string.Empty,
                TotalAmount = b.TotalAmount,
                Description = b.SpecialInstructions,
                Clients = new BookingResponse.Client
                {
                    Name =
                        (b.Client.UserProfile.FirstName ?? "")
                        + " "
                        + (b.Client.UserProfile.LastName ?? ""),
                    PhoneNumber = b.Client.PhoneNumber ?? string.Empty,
                    Email = b.Client.Email ?? string.Empty,
                    Location =
                        b.Client.UserAddresses.Where(ua => !ua.IsDeleted && ua.IsPrimary)
                            .Select(ua => new BookingResponse.Location
                            {
                                StreetAddress = ua.Address.StreetAddress ?? string.Empty,
                                City = ua.Address.City ?? string.Empty,
                                State = ua.Address.State ?? string.Empty,
                                Country = b.Client.UserProfile.Country ?? string.Empty,
                                PostalCode = ua.Address.PostalCode ?? string.Empty,
                                Longitude = ua.Address.Longitude,
                                Latitude = ua.Address.Latitude,
                            })
                            .FirstOrDefault() ?? new BookingResponse.Location(),
                },
                CareProviders = new BookingResponse.CareProvider
                {
                    ProviderId = b.Provider.Id,
                    Name =
                        (b.Provider.User.UserProfile.FirstName ?? "")
                        + " "
                        + (b.Provider.User.UserProfile.LastName ?? ""),
                    PhoneNumber = b.Provider.User.PhoneNumber ?? string.Empty,
                    Email = b.Provider.User.Email ?? string.Empty,
                    YearsOfExperience = b.Provider.YearsExperience,
                    Rating = b.Provider.Rating ?? 0,
                },
                BookingWindows = b
                    .BookingWindows.Select(w => new BookingWindowResponse
                    {
                        BookingWindowId = w.Id,
                        Date = w.Date.ToDateTime(w.StartTime),
                        StartTime = w.StartTime.ToTimeSpan(),
                        EndTime = w.EndTime.ToTimeSpan(),
                        Status = w.Status != null ? w.Status.ToString() : string.Empty,
                    })
                    .ToList(),
            });
        }

        private static IQueryable<Booking> ApplySearchFilter(
            IQueryable<Booking> query,
            string searchTerm
        )
        {
            return query.Where(b =>
                (
                    b.Client.UserProfile.FirstName != null
                    && b.Client.UserProfile.FirstName.ToLower().Contains(searchTerm)
                )
                || (
                    b.Client.UserProfile.LastName != null
                    && b.Client.UserProfile.LastName.ToLower().Contains(searchTerm)
                )
                || (
                    b.Provider.User.UserProfile.FirstName != null
                    && b.Provider.User.UserProfile.FirstName.ToLower().Contains(searchTerm)
                )
                || (
                    b.Provider.User.UserProfile.LastName != null
                    && b.Provider.User.UserProfile.LastName.ToLower().Contains(searchTerm)
                )
                || (b.Category.Name != null && b.Category.Name.ToLower().Contains(searchTerm))
                || (
                    b.SpecialInstructions != null
                    && b.SpecialInstructions.ToLower().Contains(searchTerm)
                )
                || b.Client.UserAddresses.Any(ua =>
                    !ua.IsDeleted
                    && ua.Address != null
                    && (
                        (
                            ua.Address.StreetAddress != null
                            && ua.Address.StreetAddress.ToLower().Contains(searchTerm)
                        )
                        || (
                            ua.Address.City != null
                            && ua.Address.City.ToLower().Contains(searchTerm)
                        )
                        || (
                            ua.Address.State != null
                            && ua.Address.State.ToLower().Contains(searchTerm)
                        )
                        || (
                            ua.Address.PostalCode != null
                            && ua.Address.PostalCode.ToLower().Contains(searchTerm)
                        )
                    )
                )
            );
        }

        private IQueryable<Booking> ApplyFilters(IQueryable<Booking> query, BookingsRequest filter)
        {
            if (filter.UserId.HasValue)
            {
                var provider = _context
                    .CareProviderProfiles.Include(cpc => cpc.CareProviderCategories)
                    .ThenInclude(cpc => cpc.CareCategory)
                    .AsNoTracking()
                    .AsSplitQuery()
                    .FirstOrDefault(p => p.UserId == filter.UserId);

                query =
                    provider != null
                        ? query.Where(b => b.ProviderId == provider.Id)
                        : query.Where(b => b.ClientId == filter.UserId.Value);
            }

            if (!string.IsNullOrWhiteSpace(filter.CareServices))
            {
                var careServices = filter
                    .CareServices.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(s => s.Trim())
                    .ToHashSet(StringComparer.OrdinalIgnoreCase);

                query = query.Where(b => careServices.Contains(b.Category.Name));
            }

            if (
                !string.IsNullOrWhiteSpace(filter.BookingStatus)
                && Enum.TryParse<BookingStatusType>(filter.BookingStatus, true, out var status)
            )
            {
                query = query.Where(b => b.Status.Status == status);
            }

            if (filter.StartDate.HasValue || filter.EndDate.HasValue)
            {
                var start = filter.StartDate.HasValue
                    ? DateOnly.FromDateTime(filter.StartDate.Value)
                    : DateOnly.MinValue;
                var end = filter.EndDate.HasValue
                    ? DateOnly.FromDateTime(filter.EndDate.Value)
                    : DateOnly.MaxValue;

                query = query.Where(b =>
                    b.BookingWindows.Any(w => w.Date >= start && w.Date <= end)
                );
            }

            if (filter.StartTime.HasValue)
            {
                query = query.Where(b =>
                    b.BookingWindows.Any(w => w.StartTime >= filter.StartTime.Value)
                );
            }

            if (filter.EndTime.HasValue)
            {
                query = query.Where(b =>
                    b.BookingWindows.Any(w => w.EndTime <= filter.EndTime.Value)
                );
            }

            return query;
        }

        // Helper method to normalize sort field names
        private static string NormalizeSortField(string sortField)
        {
            return sortField.ToLower() switch
            {
                "date" => "bookingdate",
                "amount" => "totalamount",
                "client" => "clientname",
                "provider" => "providername",
                "category" => "categoryname",
                _ => sortField.ToLower(),
            };
        }

        private static IQueryable<Booking> ApplySorting(
            IQueryable<Booking> query,
            BookingListParams parameters
        )
        {
            if (string.IsNullOrWhiteSpace(parameters.SortBy))
                return parameters.SortDescending
                    ? query.OrderByDescending(b => b.CreatedAt)
                    : query.OrderBy(b => b.CreatedAt);

            // Normalize the sort field to handle different variations
            var sortField = NormalizeSortField(parameters.SortBy);

            return sortField switch
            {
                "bookingdate" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.BookingWindows.Min(w => w.Date))
                    : query.OrderBy(b => b.BookingWindows.Min(w => w.Date)),
                "totalamount" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.TotalAmount)
                    : query.OrderBy(b => b.TotalAmount),
                "status" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.Status.Status)
                    : query.OrderBy(b => b.Status.Status),
                "clientname" => parameters.SortDescending
                    ? query
                        .OrderByDescending(b => b.Client.UserProfile.FirstName)
                        .ThenByDescending(b => b.Client.UserProfile.LastName)
                    : query
                        .OrderBy(b => b.Client.UserProfile.FirstName)
                        .ThenBy(b => b.Client.UserProfile.LastName),
                "providername" => parameters.SortDescending
                    ? query
                        .OrderByDescending(b => b.Provider.User.UserProfile.FirstName)
                        .ThenByDescending(b => b.Provider.User.UserProfile.LastName)
                    : query
                        .OrderBy(b => b.Provider.User.UserProfile.FirstName)
                        .ThenBy(b => b.Provider.User.UserProfile.LastName),
                "categoryname" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.Category.Name)
                    : query.OrderBy(b => b.Category.Name),
                _ => parameters.SortDescending
                    ? query.OrderByDescending(b => b.CreatedAt)
                    : query.OrderBy(b => b.CreatedAt),
            };
        }

        private static (int PageSize, int TotalPages) CalculatePagination(
            BookingListParams parameters,
            int totalCount
        )
        {
            var pageSize =
                parameters.PageSize <= 0
                    ? 10 // <-- default
                    : Math.Min(parameters.PageSize, 50); // cap at 50
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            return (pageSize, totalPages);
        }

        private static IQueryable<BookingResponse> ApplySortingToBookingResponse(
            IQueryable<BookingResponse> query,
            BookingListParams parameters
        )
        {
            if (string.IsNullOrWhiteSpace(parameters.SortBy))
                return parameters.SortDescending
                    ? query.OrderByDescending(b => b.BookingId)
                    : query.OrderBy(b => b.BookingId);

            // Normalize the sort field to handle different variations
            var sortField = NormalizeSortField(parameters.SortBy);

            return sortField switch
            {
                "bookingdate" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.BookingWindows.Min(w => w.Date))
                    : query.OrderBy(b => b.BookingWindows.Min(w => w.Date)),
                "totalamount" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.TotalAmount)
                    : query.OrderBy(b => b.TotalAmount),
                "status" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.Status)
                    : query.OrderBy(b => b.Status),
                "clientname" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.Clients.Name)
                    : query.OrderBy(b => b.Clients.Name),
                "providername" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.CareProviders.Name)
                    : query.OrderBy(b => b.CareProviders.Name),
                "categoryname" => parameters.SortDescending
                    ? query.OrderByDescending(b => b.ServiceType)
                    : query.OrderBy(b => b.ServiceType),
                _ => parameters.SortDescending
                    ? query.OrderByDescending(b => b.BookingId)
                    : query.OrderBy(b => b.BookingId),
            };
        }

        private async Task<List<BookingResponse>> GetPagedBookingsAsync(
            BookingListParams parameters,
            IQueryable<Booking> query,
            (int PageSize, int TotalPages) pagination
        )
        {
            try
            {
                var skip = (parameters.PageNumber - 1) * pagination.PageSize;

                var projection = CreateBookingResponseProjection();
                return await query
                    .Select(projection)
                    .Skip(skip)
                    .Take(pagination.PageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving paged bookings");
                throw new InvalidOperationException("Error retrieving paged bookings", ex);
            }
        }

        public async Task<Result<UpdateBookingResponse>> UpdateBookingWithWindowsAsync(
            Guid userId,
            Guid bookingId,
            UpdateBookingWithWindowsRequest request,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // 1. Load existing booking with all related data
                var booking = await _context
                    .Bookings.Include(b => b.BookingWindows)
                    .Include(b => b.Status)
                    .Include(b => b.Provider) // includes CareProviderProfile
                    .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, cancellationToken);

                var providerId = booking.ProviderId;

                if (booking == null)
                {
                    return Result.Failure<UpdateBookingResponse>(
                        Error.NotFound("Booking not found")
                    );
                }

                // 2. Validate user authorization
                if (!IsUserAuthorizedForBooking(userId, booking.ClientId, booking.ProviderId))
                {
                    return Result.Failure<UpdateBookingResponse>(
                        Error.Unauthorized("User is not authorized to update this booking")
                    );
                }

                // 3. Validate booking status (can only update certain statuses)
                var statusValidation = ValidateBookingStatusForUpdate(booking.Status?.Status);
                if (!statusValidation.IsSuccess)
                {
                    return Result.Failure<UpdateBookingResponse>(statusValidation.Error);
                }

                // 4. Validate entities exist
                var entitiesValidation = await ValidateUpdateEntitiesAsync(
                    providerId,
                    request,
                    cancellationToken
                );
                if (!entitiesValidation.IsSuccess)
                {
                    return Result.Failure<UpdateBookingResponse>(entitiesValidation.Error);
                }

                var (provider, category) = entitiesValidation.Value;

                // 5. Validate intra-request window overlaps
                var windowValidation = ValidateBookingWindowsForUpdate(request.BookingWindows);
                if (!windowValidation.IsSuccess)
                {
                    return Result.Failure<UpdateBookingResponse>(windowValidation.Error);
                }

                // 6. Validate provider availability for new windows
                var availabilityValidation = await ValidateProviderAvailabilityForUpdateAsync(
                    provider,
                    request.BookingWindows,
                    cancellationToken
                );
                if (!availabilityValidation.IsSuccess)
                {
                    return Result.Failure<UpdateBookingResponse>(availabilityValidation.Error);
                }

                // 7. Check conflicts with other bookings (excluding current booking)
                var conflictValidation = await ValidateBookingConflictsForUpdateAsync(
                    providerId,
                    request.BookingWindows,
                    bookingId,
                    cancellationToken
                );
                if (!conflictValidation.IsSuccess)
                {
                    return Result.Failure<UpdateBookingResponse>(conflictValidation.Error);
                }

                // 8. Calculate new amounts
                var amounts = CalculateBookingAmountsForUpdate(
                    provider,
                    category,
                    request.BookingWindows
                );

                // 9. Update booking in transaction
                await using var transaction = await _context.Database.BeginTransactionAsync(
                    cancellationToken
                );
                try
                {
                    await UpdateBookingEntityForWindowsAsync(
                        booking,
                        request,
                        amounts,
                        userId,
                        cancellationToken
                    );
                    await CreateBookingStatusHistoryForUpdateAsync(
                        booking.Id,
                        userId,
                        "Booking updated",
                        cancellationToken
                    );

                    await _context.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    // 10. Return response
                    var response = CreateUpdateBookingResponseFromEntity(booking, amounts);
                    return Result.Success(response);
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating booking {BookingId} for user {UserId}",
                    bookingId,
                    userId
                );
                return Result.Failure<UpdateBookingResponse>(
                    Error.Internal("Error updating booking")
                );
            }
        }

        private static Result ValidateBookingStatusForUpdate(BookingStatusType? currentStatus)
        {
            var updatableStatuses = new[]
            {
                BookingStatusType.Requested,
                BookingStatusType.Accepted,
                BookingStatusType.Confirmed,
            };

            if (currentStatus == null || !updatableStatuses.Contains(currentStatus.Value))
            {
                return Result.Failure(
                    Error.Conflict(
                        $"Cannot update booking with status: {currentStatus?.GetDescription() ?? "Unknown"}"
                    )
                );
            }

            return Result.Success();
        }

        private async Task<
            Result<(CareProviderProfile provider, CareCategory category)>
        > ValidateUpdateEntitiesAsync(
            Guid providerId,
            UpdateBookingWithWindowsRequest request,
            CancellationToken cancellationToken
        )
        {
            var provider = await _context
                .CareProviderProfiles.Include(p => p.Availabilities)
                .ThenInclude(a => a.AvailabilitySlots)
                .Include(p => p.CareProviderCategories)
                .AsSplitQuery()
                .FirstOrDefaultAsync(p => p.Id == providerId, cancellationToken);

            if (provider == null)
            {
                return Result.Failure<(CareProviderProfile, CareCategory)>(
                    Error.NotFound("Provider not found")
                );
            }

            var category = await _context
                .CareProviderCategories.Include(c => c.CareCategory)
                .Where(c => c.ProviderId == providerId && !c.IsDeleted)
                .Select(c => c.CareCategory)
                .FirstOrDefaultAsync(cancellationToken);

            if (category == null)
            {
                return Result.Failure<(CareProviderProfile, CareCategory)>(
                    Error.NotFound("Category not found")
                );
            }

            return Result.Success((provider, category));
        }

        private static Result ValidateBookingWindowsForUpdate(List<BookingWindowRequest> windows)
        {
            if (windows == null || !windows.Any())
            {
                return Result.Failure(Error.Validation("At least one booking window is required"));
            }

            // Check for overlaps within the same request
            var windowsByDate = windows.GroupBy(w => w.Date.Date);

            foreach (var dayGroup in windowsByDate)
            {
                var sortedWindows = dayGroup.OrderBy(w => w.StartTime).ToList();
                for (int i = 1; i < sortedWindows.Count; i++)
                {
                    if (sortedWindows[i].StartTime < sortedWindows[i - 1].EndTime)
                    {
                        return Result.Failure(
                            Error.Conflict(
                                $"Booking windows overlap within the same request on {dayGroup.Key:yyyy-MM-dd}"
                            )
                        );
                    }
                }
            }

            return Result.Success();
        }

        private async Task<Result> ValidateProviderAvailabilityForUpdateAsync(
            CareProviderProfile provider,
            List<BookingWindowRequest> windows,
            CancellationToken cancellationToken
        )
        {
            var requestedDays = windows
                .Select(w => w.Date.DayOfWeek.ToString())
                .Distinct()
                .ToList();
            var availabilityMap = provider
                .Availabilities.Where(a => requestedDays.Contains(a.DayOfWeek))
                .ToDictionary(a => a.DayOfWeek, a => a);

            foreach (var window in windows)
            {
                var dayOfWeek = window.Date.DayOfWeek.ToString();

                if (
                    !availabilityMap.TryGetValue(dayOfWeek, out var availability)
                    || !availability.IsAvailable
                )
                {
                    return Result.Failure(
                        Error.Conflict(
                            $"Provider is not available on {window.Date:yyyy-MM-dd} ({dayOfWeek})"
                        )
                    );
                }

                var fitsInSlot = availability.AvailabilitySlots.Any(slot =>
                    slot.StartTime <= window.StartTime && slot.EndTime >= window.EndTime
                );

                if (!fitsInSlot)
                {
                    return Result.Failure(
                        Error.Conflict(
                            $"Time {window.StartTime:hh\\:mm}–{window.EndTime:hh\\:mm} on {window.Date:yyyy-MM-dd} "
                                + "falls outside provider's available hours"
                        )
                    );
                }
            }

            await Task.Yield();

            return Result.Success();
        }

        private async Task<Result> ValidateBookingConflictsForUpdateAsync(
            Guid providerId,
            List<BookingWindowRequest> windows,
            Guid excludeBookingId,
            CancellationToken cancellationToken
        )
        {
            var activeStatuses = new[]
            {
                BookingStatusType.Requested,
                BookingStatusType.Accepted,
                BookingStatusType.InProgress,
                BookingStatusType.Confirmed,
            };

            var requestedDates = windows
                .Select(w => DateOnly.FromDateTime(w.Date))
                .Distinct()
                .ToList();

            var existingWindows = await _context
                .Bookings.Where(b =>
                    b.ProviderId == providerId
                    && b.Id != excludeBookingId
                    && activeStatuses.Contains(b.Status!.Status)
                )
                .SelectMany(b => b.BookingWindows)
                .Where(bw => requestedDates.Contains(bw.Date))
                .ToListAsync(cancellationToken);

            foreach (var window in windows)
            {
                var windowDate = DateOnly.FromDateTime(window.Date);
                var conflicts = existingWindows
                    .Where(existing => existing.Date == windowDate)
                    .Where(existing =>
                        window.StartTime < existing.EndTime && window.EndTime > existing.StartTime
                    )
                    .ToList();

                if (conflicts.Any())
                {
                    var conflict = conflicts.First();
                    return Result.Failure(
                        Error.Conflict(
                            $"Provider has conflicting booking on {window.Date:yyyy-MM-dd} "
                                + $"from {conflict.StartTime:hh\\:mm} to {conflict.EndTime:hh\\:mm}"
                        )
                    );
                }
            }

            return Result.Success();
        }

        private static (
            decimal TotalAmount,
            decimal PlatformFee,
            decimal ProviderAmount
        ) CalculateBookingAmountsForUpdate(
            CareProviderProfile provider,
            CareCategory category,
            List<BookingWindowRequest> windows
        )
        {
            var totalHours = windows.Sum(w => (w.EndTime - w.StartTime).TotalHours);
            var hourlyRate =
                provider
                    .CareProviderCategories?.FirstOrDefault(cpc => cpc.CategoryId == category.Id)
                    ?.HourlyRate ?? 0m;

            var totalAmount = hourlyRate * (decimal)totalHours;
            var platformFee = category.PlatformFee;
            var providerAmount = totalAmount / (1 + platformFee);

            return (totalAmount, platformFee, providerAmount);
        }

        private async Task UpdateBookingEntityForWindowsAsync(
            Booking booking,
            UpdateBookingWithWindowsRequest request,
            (decimal TotalAmount, decimal PlatformFee, decimal ProviderAmount) amounts,
            Guid userId,
            CancellationToken cancellationToken
        )
        {
            // Remove existing booking windows
            _context.BookingWindows.RemoveRange(booking.BookingWindows);
            await Task.Yield();
            // Create new booking windows
            var newWindows = request
                .BookingWindows.Select(w => new BookingWindow
                {
                    BookingId = booking.Id,
                    Date = DateOnly.FromDateTime(w.Date),
                    StartTime = w.StartTime,
                    EndTime = w.EndTime,
                    CreatedBy = userId,
                    CreatedAt = DateTime.UtcNow,
                })
                .ToList();

            booking.BookingWindows = newWindows;
            booking.SpecialInstructions = request.SpecialInstructions;
            booking.TotalAmount = amounts.TotalAmount;
            booking.PlatformFee = amounts.PlatformFee;
            booking.ProviderAmount = amounts.ProviderAmount;
            booking.UpdatedAt = DateTime.UtcNow;
            booking.UpdatedBy = userId;

            // Recalculate working hours
            var totalHours = request.BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours);
            booking.WorkingHours = (int)Math.Ceiling(totalHours);
        }

        private async Task CreateBookingStatusHistoryForUpdateAsync(
            Guid bookingId,
            Guid userId,
            string notes,
            CancellationToken cancellationToken
        )
        {
            var statusHistory = new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = bookingId,
                Status = BookingStatusType.Modified,
                CreatedBy = userId,
                Notes = notes,
                CreatedAt = DateTime.UtcNow,
            };

            await _context.BookingStatuses.AddAsync(statusHistory, cancellationToken);
        }

        private static UpdateBookingResponse CreateUpdateBookingResponseFromEntity(
            Booking booking,
            (decimal TotalAmount, decimal PlatformFee, decimal ProviderAmount) amounts
        )
        {
            return new UpdateBookingResponse
            {
                BookingId = booking.Id,
                Status = BookingStatusType.Modified.GetDescription(),
                TotalAmount = amounts.TotalAmount,
                PlatformFee = amounts.PlatformFee,
                ProviderAmount = amounts.ProviderAmount,
                BookingWindows = booking
                    .BookingWindows.Select(w => new BookingWindowResponse
                    {
                        BookingWindowId = w.Id,
                        Date = w.Date.ToDateTime(w.StartTime),
                        StartTime = w.StartTime.ToTimeSpan(),
                        EndTime = w.EndTime.ToTimeSpan(),
                        InvoiceFileName = w.Invoice.FileName,
                        InvoiceFileUrl = w.Invoice.FileUrl,
                    })
                    .ToList(),
                UpdatedAt = booking.UpdatedAt ?? DateTime.UtcNow,
                SpecialInstructions = booking.SpecialInstructions,
                TotalHours = booking.WorkingHours ?? 0,
            };
        }

        #endregion
    }
}
