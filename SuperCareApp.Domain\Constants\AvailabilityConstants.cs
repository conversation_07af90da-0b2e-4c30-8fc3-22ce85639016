namespace SuperCareApp.Domain.Constants
{
    /// <summary>
    /// Constants for availability management
    /// </summary>
    public static class AvailabilityConstants
    {
        /// <summary>
        /// Standard days of the week in consistent format
        /// </summary>
        public static class DaysOfWeek
        {
            public const string Monday = "Monday";
            public const string Tuesday = "Tuesday";
            public const string Wednesday = "Wednesday";
            public const string Thursday = "Thursday";
            public const string Friday = "Friday";
            public const string Saturday = "Saturday";
            public const string Sunday = "Sunday";
        }

        /// <summary>
        /// All days of the week in order (Monday first)
        /// </summary>
        public static readonly string[] AllDays = new[]
        {
            DaysOfWeek.Monday,
            DaysOfWeek.Tuesday,
            DaysOfWeek.Wednesday,
            DaysOfWeek.Thursday,
            DaysOfWeek.Friday,
            DaysOfWeek.Saturday,
            DaysOfWeek.Sunday,
        };

        /// <summary>
        /// Weekdays only (Monday to Friday)
        /// </summary>
        public static readonly string[] Weekdays = new[]
        {
            DaysOfWeek.Monday,
            DaysOfWeek.Tuesday,
            DaysOfWeek.Wednesday,
            DaysOfWeek.Thursday,
            DaysOfWeek.Friday,
        };

        /// <summary>
        /// Weekend days only (Saturday and Sunday)
        /// </summary>
        public static readonly string[] WeekendDays = new[]
        {
            DaysOfWeek.Saturday,
            DaysOfWeek.Sunday,
        };

        /// <summary>
        /// Default availability template settings
        /// </summary>
        public static class DefaultTemplate
        {
            /// <summary>
            /// Default availability status for new providers (all days unavailable)
            /// </summary>
            public const bool IsAvailable = false;

            /// <summary>
            /// Default buffer duration in minutes
            /// </summary>
            public const int BufferDurationMinutes = 15;
        }

        /// <summary>
        /// Validates if a day string is a valid day of the week
        /// </summary>
        /// <param name="day">Day string to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidDay(string day)
        {
            return AllDays.Contains(day, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets the normalized day name (proper case)
        /// </summary>
        /// <param name="day">Day string to normalize</param>
        /// <returns>Normalized day name or null if invalid</returns>
        public static string? GetNormalizedDay(string day)
        {
            return AllDays.FirstOrDefault(d =>
                string.Equals(d, day, StringComparison.OrdinalIgnoreCase)
            );
        }
    }
}
