using System.Text.RegularExpressions;
using FluentValidation;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Validation
{
    /// <summary>
    /// Validator for UserProfile entity
    /// </summary>
    public class UserProfileValidator : AbstractValidator<UserProfile>
    {
        public UserProfileValidator()
        {
            RuleFor(x => x.ApplicationUserId).NotEmpty().WithMessage("User ID is required");
            RuleFor(x => x.FirstName)
                .NotEmpty()
                .MaximumLength(100)
                .WithMessage("First name is required and cannot exceed 100 characters");
            RuleFor(x => x.LastName)
                .NotEmpty()
                .MaximumLength(100)
                .WithMessage("Last name is required and cannot exceed 100 characters");
            RuleFor(x => x.PhoneNumber)
                .MaximumLength(20)
                .Matches(@"^\+?[0-9\s\-\(\)]+$")
                .When(x => !string.IsNullOrEmpty(x.PhoneNumber))
                .WithMessage("Phone number format is invalid");
        }
    }

    /// <summary>
    /// Validator for CareProviderProfile entity
    /// </summary>
    public class CareProviderProfileValidator : AbstractValidator<CareProviderProfile>
    {
        public CareProviderProfileValidator()
        {
            RuleFor(x => x.UserId).NotEmpty().WithMessage("User ID is required");
            RuleFor(x => x.Bio).MaximumLength(1000);
            RuleFor(x => x.YearsExperience)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Years of experience cannot be negative");
            RuleFor(x => x.HourlyRate)
                .GreaterThan(0)
                .WithMessage("Hourly rate must be greater than zero");
            RuleFor(x => x.Rating)
                .InclusiveBetween(0, 5)
                .WithMessage("Rating must be between 0 and 5");
            RuleFor(x => x.RatingCount)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Rating count cannot be negative");
        }
    }

    /// <summary>
    /// Validator for Availability entity
    /// </summary>
    public class AvailabilityValidator : AbstractValidator<Availability>
    {
        public AvailabilityValidator()
        {
            RuleFor(x => x.ProviderId).NotEmpty().WithMessage("Provider ID is required.");

            // DayOfWeek is a string (e.g., "Monday") now
            RuleFor(x => x.DayOfWeek)
                .NotEmpty()
                .Matches(
                    @"^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)$",
                    RegexOptions.IgnoreCase
                )
                .WithMessage("Day of week must be a valid day name if not one-time.");
        }
    }

    public class AvailabilitySlotValidator : AbstractValidator<AvailabilitySlot>
    {
        public AvailabilitySlotValidator()
        {
            RuleFor(x => x.AvailabilityId).NotEmpty().WithMessage("Availability ID is required.");
            RuleFor(x => x.StartTime).NotEmpty().WithMessage("Start time is required.");
            RuleFor(x => x.EndTime).NotEmpty().WithMessage("End time is required.");
            RuleFor(x => x.EndTime)
                .GreaterThan(x => x.StartTime)
                .WithMessage("End time must be after start time.");
        }
    }

    /// <summary>
    /// Validator for Document entity
    /// </summary>
    public class DocumentValidator : AbstractValidator<Document>
    {
        public DocumentValidator()
        {
            RuleFor(x => x.UserId).NotEmpty().WithMessage("User ID is required");
            RuleFor(x => x.DocumentType)
                .NotEmpty()
                .MaximumLength(50)
                .WithMessage("Document type is required");
            RuleFor(x => x.DocumentUrl)
                .NotEmpty()
                .MaximumLength(1000)
                .WithMessage("Document URL is required");
            RuleFor(x => x.VerificationStatus)
                .IsInEnum()
                .WithMessage("Invalid verification status");
            RuleFor(x => x.UploadedAt).NotEmpty().WithMessage("Upload date is required");

            // If document is verified, VerifiedAt and VerifiedBy must be set
            RuleFor(x => x.VerifiedAt)
                .NotNull()
                .When(x => x.VerificationStatus == VerificationStatus.Verified)
                .WithMessage("Verification date is required for verified documents");
            RuleFor(x => x.VerifiedBy)
                .NotNull()
                .When(x => x.VerificationStatus == VerificationStatus.Verified)
                .WithMessage("Verifier ID is required for verified documents");

            // If document is rejected, RejectionReason must be set
            RuleFor(x => x.RejectionReason)
                .NotEmpty()
                .When(x => x.VerificationStatus == VerificationStatus.Rejected)
                .WithMessage("Rejection reason is required for rejected documents");
        }
    }

    /// <summary>
    /// Validator for Subscription entity
    /// </summary>
    public class SubscriptionValidator : AbstractValidator<Subscription>
    {
        public SubscriptionValidator()
        {
            RuleFor(x => x.ProviderId).NotEmpty().WithMessage("Provider ID is required");
            RuleFor(x => x.SubscriptionType).IsInEnum().WithMessage("Invalid subscription type");
            RuleFor(x => x.StartDate).NotEmpty().WithMessage("Start date is required");
            RuleFor(x => x.EndDate).NotEmpty().WithMessage("End date is required");
            RuleFor(x => x.EndDate)
                .GreaterThan(x => x.StartDate)
                .WithMessage("End date must be after start date");
            RuleFor(x => x.Amount).GreaterThan(0).WithMessage("Amount must be greater than zero");
            RuleFor(x => x.Status).IsInEnum().WithMessage("Invalid payment status");
            RuleFor(x => x.PaymentMethod)
                .NotEmpty()
                .MaximumLength(50)
                .WithMessage("Payment method is required");
        }
    }

    /// <summary>
    /// Validator for Review entity
    /// </summary>
    public class ReviewValidator : AbstractValidator<Review>
    {
        public ReviewValidator()
        {
            RuleFor(x => x.BookingId).NotEmpty().WithMessage("Booking ID is required");
            RuleFor(x => x.ReviewerId).NotEmpty().WithMessage("Reviewer ID is required");
            RuleFor(x => x.Rating)
                .InclusiveBetween(1, 5)
                .WithMessage("Rating must be between 1 and 5");
            RuleFor(x => x.Comment).MaximumLength(1000);
        }
    }
}
