﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class BookingStatusConfiguration : IEntityTypeConfiguration<BookingStatus>
    {
        public void Configure(EntityTypeBuilder<BookingStatus> builder)
        {
            builder.HasKey(bs => bs.Id);

            builder.Property(bs => bs.Status).IsRequired().HasConversion<string>();

            builder.Property(bs => bs.Notes).HasMaxLength(500);

            // Relationships
            builder
                .HasOne(bs => bs.Booking)
                .WithOne(b => b.Status)
                .HasForeignKey<BookingStatus>(bs => bs.BookingId)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasOne(bs => bs.CreatedByUser)
                .WithMany()
                .HasForeignKey(bs => bs.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
