# 💬 Real-time Chat System Implementation Plan

## Overview

This document outlines the implementation plan for a comprehensive real-time chat system between clients and care professionals in the SuperCare application. The system will use native WebSockets for real-time communication, message persistence, and advanced chat features.

## 🎯 Objectives

- **Real-time Communication**: Instant messaging between clients and care providers
- **Message Persistence**: Store and retrieve chat history
- **File Sharing**: Support for image and document sharing in chat
- **Typing Indicators**: Real-time typing status updates
- **Message Status**: Delivery and read receipts
- **Push Notifications**: Notify users of new messages when offline
- **Moderation**: Content filtering and chat moderation capabilities

## 🏗️ Architecture Overview

```
Client App → WebSocket Connection → Chat Hub → Message Service → Database
                                      ↓
                              Notification Service → Push Notifications
                                      ↓
                              File Storage Service → Document Storage
```

## 📋 Implementation Phases

### Phase 1: Core Chat Infrastructure (Week 1-2)

#### 1.1 Domain Models

```csharp
public class ChatRoom : BaseEntity
{
    public Guid BookingId { get; set; }
    public Guid ClientId { get; set; }
    public Guid ProviderId { get; set; }
    public string RoomName { get; set; } = string.Empty;
    public ChatRoomStatus Status { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public bool IsArchived { get; set; }

    // Navigation properties
    public Booking Booking { get; set; } = null!;
    public ApplicationUser Client { get; set; } = null!;
    public ApplicationUser Provider { get; set; } = null!;
    public List<ChatMessage> Messages { get; set; } = new();
    public List<ChatParticipant> Participants { get; set; } = new();
}

public class ChatMessage : BaseEntity
{
    public Guid ChatRoomId { get; set; }
    public Guid SenderId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public MessageStatus Status { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public Guid? ReplyToMessageId { get; set; }
    public bool IsEdited { get; set; }
    public DateTime? EditedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Navigation properties
    public ChatRoom ChatRoom { get; set; } = null!;
    public ApplicationUser Sender { get; set; } = null!;
    public ChatMessage? ReplyToMessage { get; set; }
    public List<ChatMessageAttachment> Attachments { get; set; } = new();
}

public class ChatMessageAttachment : BaseEntity
{
    public Guid MessageId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string? ThumbnailUrl { get; set; }

    // Navigation properties
    public ChatMessage Message { get; set; } = null!;
}

public class ChatParticipant : BaseEntity
{
    public Guid ChatRoomId { get; set; }
    public Guid UserId { get; set; }
    public ParticipantRole Role { get; set; }
    public DateTime JoinedAt { get; set; }
    public DateTime? LastSeenAt { get; set; }
    public bool IsActive { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();

    // Navigation properties
    public ChatRoom ChatRoom { get; set; } = null!;
    public ApplicationUser User { get; set; } = null!;
}

public enum MessageType
{
    Text,
    Image,
    Document,
    System,
    Emoji,
    Location
}

public enum MessageStatus
{
    Sent,
    Delivered,
    Read,
    Failed
}

public enum ChatRoomStatus
{
    Active,
    Inactive,
    Archived,
    Blocked
}

public enum ParticipantRole
{
    Client,
    Provider,
    Moderator
}
```

#### 1.2 WebSocket Hub Implementation

```csharp
[Authorize]
public class ChatHub : Hub
{
    private readonly IChatService _chatService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<ChatHub> _logger;
    private readonly IConnectionManager _connectionManager;

    public ChatHub(
        IChatService chatService,
        ICurrentUserService currentUserService,
        ILogger<ChatHub> logger,
        IConnectionManager connectionManager)
    {
        _chatService = chatService;
        _currentUserService = currentUserService;
        _logger = logger;
        _connectionManager = connectionManager;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = _currentUserService.UserId;
        if (userId.HasValue)
        {
            await _connectionManager.AddConnectionAsync(userId.Value, Context.ConnectionId);

            // Join user to their active chat rooms
            var activeRooms = await _chatService.GetActiveRoomsForUserAsync(userId.Value);
            foreach (var room in activeRooms)
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"room_{room.Id}");
            }

            _logger.LogInformation("User {UserId} connected to chat with connection {ConnectionId}",
                userId.Value, Context.ConnectionId);
        }

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception exception)
    {
        var userId = _currentUserService.UserId;
        if (userId.HasValue)
        {
            await _connectionManager.RemoveConnectionAsync(userId.Value, Context.ConnectionId);

            _logger.LogInformation("User {UserId} disconnected from chat with connection {ConnectionId}",
                userId.Value, Context.ConnectionId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    public async Task JoinRoom(Guid roomId)
    {
        var userId = _currentUserService.UserId;
        if (!userId.HasValue) return;

        var canJoin = await _chatService.CanUserJoinRoomAsync(userId.Value, roomId);
        if (canJoin)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"room_{roomId}");
            await _chatService.UpdateLastSeenAsync(roomId, userId.Value);

            _logger.LogInformation("User {UserId} joined chat room {RoomId}", userId.Value, roomId);
        }
    }

    public async Task LeaveRoom(Guid roomId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"room_{roomId}");

        var userId = _currentUserService.UserId;
        if (userId.HasValue)
        {
            await _chatService.UpdateLastSeenAsync(roomId, userId.Value);
            _logger.LogInformation("User {UserId} left chat room {RoomId}", userId.Value, roomId);
        }
    }

    public async Task SendMessage(SendMessageRequest request)
    {
        var userId = _currentUserService.UserId;
        if (!userId.HasValue) return;

        try
        {
            var result = await _chatService.SendMessageAsync(new SendMessageCommand
            {
                ChatRoomId = request.RoomId,
                SenderId = userId.Value,
                Content = request.Content,
                MessageType = request.MessageType,
                ReplyToMessageId = request.ReplyToMessageId,
                Attachments = request.Attachments
            });

            if (result.IsSuccess)
            {
                // Broadcast message to room participants
                await Clients.Group($"room_{request.RoomId}")
                    .SendAsync("MessageReceived", result.Value);

                // Send push notifications to offline users
                await _chatService.SendPushNotificationsAsync(request.RoomId, result.Value, userId.Value);
            }
            else
            {
                await Clients.Caller.SendAsync("MessageError", result.Error.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message for user {UserId} in room {RoomId}",
                userId.Value, request.RoomId);
            await Clients.Caller.SendAsync("MessageError", "Failed to send message");
        }
    }

    public async Task MarkMessageAsRead(Guid messageId)
    {
        var userId = _currentUserService.UserId;
        if (!userId.HasValue) return;

        await _chatService.MarkMessageAsReadAsync(messageId, userId.Value);

        // Notify sender about read receipt
        var message = await _chatService.GetMessageAsync(messageId);
        if (message != null)
        {
            await Clients.Group($"room_{message.ChatRoomId}")
                .SendAsync("MessageRead", new { MessageId = messageId, ReadBy = userId.Value, ReadAt = DateTime.UtcNow });
        }
    }

    public async Task StartTyping(Guid roomId)
    {
        var userId = _currentUserService.UserId;
        if (!userId.HasValue) return;

        await Clients.OthersInGroup($"room_{roomId}")
            .SendAsync("UserTyping", new { UserId = userId.Value, RoomId = roomId });
    }

    public async Task StopTyping(Guid roomId)
    {
        var userId = _currentUserService.UserId;
        if (!userId.HasValue) return;

        await Clients.OthersInGroup($"room_{roomId}")
            .SendAsync("UserStoppedTyping", new { UserId = userId.Value, RoomId = roomId });
    }
}
```

#### 1.3 Service Interfaces

```csharp
public interface IChatService
{
    Task<Result<ChatRoomResponse>> CreateChatRoomAsync(CreateChatRoomRequest request);
    Task<Result<ChatMessageResponse>> SendMessageAsync(SendMessageCommand command);
    Task<Result<List<ChatRoomResponse>>> GetUserChatRoomsAsync(Guid userId);
    Task<Result<List<ChatMessageResponse>>> GetRoomMessagesAsync(Guid roomId, int page = 1, int pageSize = 50);
    Task<Result> MarkMessageAsReadAsync(Guid messageId, Guid userId);
    Task<Result> UpdateLastSeenAsync(Guid roomId, Guid userId);
    Task<bool> CanUserJoinRoomAsync(Guid userId, Guid roomId);
    Task<ChatMessageResponse?> GetMessageAsync(Guid messageId);
    Task<List<ChatRoomResponse>> GetActiveRoomsForUserAsync(Guid userId);
    Task SendPushNotificationsAsync(Guid roomId, ChatMessageResponse message, Guid senderId);
}

public interface IConnectionManager
{
    Task AddConnectionAsync(Guid userId, string connectionId);
    Task RemoveConnectionAsync(Guid userId, string connectionId);
    Task<List<string>> GetUserConnectionsAsync(Guid userId);
    Task<bool> IsUserOnlineAsync(Guid userId);
}

public interface IChatModerationService
{
    Task<Result<bool>> IsContentAppropriateAsync(string content);
    Task<Result> ReportMessageAsync(Guid messageId, Guid reportedBy, string reason);
    Task<Result> ModerateMessageAsync(Guid messageId, ModerationAction action, string reason);
}
```

### Phase 2: Advanced Chat Features (Week 3-4)

#### 2.1 File Sharing Implementation

```csharp
public class ChatFileService : IChatFileService
{
    private readonly IDocumentStorageService _documentStorage;
    private readonly IImageProcessingService _imageProcessing;
    private readonly ILogger<ChatFileService> _logger;

    public async Task<Result<ChatFileUploadResponse>> UploadChatFileAsync(ChatFileUploadRequest request)
    {
        try
        {
            // Validate file
            var validation = await ValidateChatFileAsync(request);
            if (validation.IsFailure) return Result.Failure<ChatFileUploadResponse>(validation.Error);

            // Upload file
            var uploadResult = await _documentStorage.UploadDocumentAsync(new DocumentUploadRequest
            {
                UserId = request.UserId,
                FileName = request.FileName,
                ContentType = request.ContentType,
                FileStream = request.FileStream,
                DocumentType = DocumentType.ChatAttachment,
                Category = DocumentCategory.Communication,
                IsPublic = false
            });

            if (uploadResult.IsFailure)
                return Result.Failure<ChatFileUploadResponse>(uploadResult.Error);

            // Generate thumbnail for images
            string? thumbnailUrl = null;
            if (IsImageFile(request.ContentType))
            {
                var thumbnailResult = await _imageProcessing.GenerateThumbnailsAsync(
                    request.FileStream, new List<int> { 150 });

                if (thumbnailResult.IsSuccess && thumbnailResult.Value.Any())
                {
                    thumbnailUrl = thumbnailResult.Value.First().Url;
                }
            }

            return Result.Success(new ChatFileUploadResponse
            {
                FileId = uploadResult.Value.DocumentId,
                FileName = request.FileName,
                FileUrl = uploadResult.Value.CdnUrl,
                ThumbnailUrl = thumbnailUrl,
                ContentType = request.ContentType,
                FileSize = request.FileStream.Length
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading chat file for user {UserId}", request.UserId);
            return Result.Failure<ChatFileUploadResponse>(Error.Internal("Failed to upload file"));
        }
    }

    private async Task<Result> ValidateChatFileAsync(ChatFileUploadRequest request)
    {
        // Check file size (max 10MB for chat files)
        if (request.FileStream.Length > 10 * 1024 * 1024)
            return Result.Failure(Error.BadRequest("File size exceeds 10MB limit"));

        // Check file type
        var allowedTypes = new[]
        {
            "image/jpeg", "image/png", "image/gif", "image/webp",
            "application/pdf", "text/plain",
            "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        };

        if (!allowedTypes.Contains(request.ContentType))
            return Result.Failure(Error.BadRequest("File type not supported"));

        return Result.Success();
    }

    private bool IsImageFile(string contentType)
    {
        return contentType.StartsWith("image/");
    }
}
```

#### 2.2 Push Notification Integration

```csharp
public class ChatNotificationService : IChatNotificationService
{
    private readonly IPushNotificationService _pushNotificationService;
    private readonly IConnectionManager _connectionManager;
    private readonly IUserRepository _userRepository;
    private readonly ILogger<ChatNotificationService> _logger;

    public async Task SendChatNotificationAsync(ChatNotificationRequest request)
    {
        try
        {
            // Check if user is online
            var isOnline = await _connectionManager.IsUserOnlineAsync(request.RecipientId);
            if (isOnline)
            {
                _logger.LogDebug("User {UserId} is online, skipping push notification", request.RecipientId);
                return;
            }

            // Get user's notification preferences
            var user = await _userRepository.GetByIdAsync(request.RecipientId);
            if (user?.NotificationSettings?.ChatNotifications != true)
            {
                _logger.LogDebug("User {UserId} has disabled chat notifications", request.RecipientId);
                return;
            }

            // Send push notification
            await _pushNotificationService.SendNotificationAsync(new PushNotificationRequest
            {
                UserId = request.RecipientId,
                Title = $"New message from {request.SenderName}",
                Body = TruncateMessage(request.MessageContent, 100),
                Data = new Dictionary<string, object>
                {
                    ["type"] = "chat_message",
                    ["roomId"] = request.RoomId.ToString(),
                    ["messageId"] = request.MessageId.ToString(),
                    ["senderId"] = request.SenderId.ToString()
                },
                Badge = await GetUnreadMessageCountAsync(request.RecipientId)
            });

            _logger.LogInformation("Sent chat notification to user {UserId} for message {MessageId}",
                request.RecipientId, request.MessageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat notification to user {UserId}", request.RecipientId);
        }
    }

    private string TruncateMessage(string message, int maxLength)
    {
        if (string.IsNullOrEmpty(message)) return "New message";
        return message.Length <= maxLength ? message : message.Substring(0, maxLength) + "...";
    }

    private async Task<int> GetUnreadMessageCountAsync(Guid userId)
    {
        // Implementation to get unread message count
        // This would query the database for unread messages for the user
        return 0; // Placeholder
    }
}
```

### Phase 3: Chat Moderation & Security (Week 5-6)

#### 2.3 Content Moderation

```csharp
public class ChatModerationService : IChatModerationService
{
    private readonly IContentFilterService _contentFilter;
    private readonly IChatRepository _chatRepository;
    private readonly ILogger<ChatModerationService> _logger;

    public async Task<Result<bool>> IsContentAppropriateAsync(string content)
    {
        try
        {
            // Check for profanity and inappropriate content
            var profanityCheck = await _contentFilter.ContainsProfanityAsync(content);
            if (profanityCheck)
            {
                _logger.LogWarning("Message contains inappropriate content: {Content}", content);
                return Result.Success(false);
            }

            // Check for spam patterns
            var spamCheck = await _contentFilter.IsSpamAsync(content);
            if (spamCheck)
            {
                _logger.LogWarning("Message identified as spam: {Content}", content);
                return Result.Success(false);
            }

            // Check for personal information (phone numbers, emails, etc.)
            var piiCheck = await _contentFilter.ContainsPersonalInfoAsync(content);
            if (piiCheck)
            {
                _logger.LogWarning("Message contains personal information: {Content}", content);
                return Result.Success(false);
            }

            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking content appropriateness");
            return Result.Failure<bool>(Error.Internal("Content moderation failed"));
        }
    }

    public async Task<Result> ReportMessageAsync(Guid messageId, Guid reportedBy, string reason)
    {
        try
        {
            var report = new MessageReport
            {
                MessageId = messageId,
                ReportedBy = reportedBy,
                Reason = reason,
                Status = ReportStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            await _chatRepository.AddMessageReportAsync(report);

            _logger.LogInformation("Message {MessageId} reported by user {UserId} for reason: {Reason}",
                messageId, reportedBy, reason);

            // Trigger moderation review
            await TriggerModerationReviewAsync(messageId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting message {MessageId}", messageId);
            return Result.Failure(Error.Internal("Failed to report message"));
        }
    }

    private async Task TriggerModerationReviewAsync(Guid messageId)
    {
        // Implementation for triggering moderation review
        // This could involve notifying moderators or adding to a review queue
    }
}
```

## 🔧 Database Schema

```sql
-- Chat Rooms Table
CREATE TABLE ChatRooms (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    BookingId UNIQUEIDENTIFIER NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    ProviderId UNIQUEIDENTIFIER NOT NULL,
    RoomName NVARCHAR(255) NOT NULL,
    Status INT NOT NULL DEFAULT 0,
    LastMessageAt DATETIME2 NULL,
    IsArchived BIT NOT NULL DEFAULT 0,
    CreatedAt DATETIME2 NOT NULL,
    UpdatedAt DATETIME2 NOT NULL,

    FOREIGN KEY (BookingId) REFERENCES Bookings(Id),
    FOREIGN KEY (ClientId) REFERENCES Users(Id),
    FOREIGN KEY (ProviderId) REFERENCES Users(Id),
    INDEX IX_ChatRooms_BookingId (BookingId),
    INDEX IX_ChatRooms_ClientId (ClientId),
    INDEX IX_ChatRooms_ProviderId (ProviderId)
);

-- Chat Messages Table
CREATE TABLE ChatMessages (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ChatRoomId UNIQUEIDENTIFIER NOT NULL,
    SenderId UNIQUEIDENTIFIER NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    MessageType INT NOT NULL DEFAULT 0,
    Status INT NOT NULL DEFAULT 0,
    DeliveredAt DATETIME2 NULL,
    ReadAt DATETIME2 NULL,
    ReplyToMessageId UNIQUEIDENTIFIER NULL,
    IsEdited BIT NOT NULL DEFAULT 0,
    EditedAt DATETIME2 NULL,
    Metadata NVARCHAR(MAX) NULL, -- JSON
    CreatedAt DATETIME2 NOT NULL,
    UpdatedAt DATETIME2 NOT NULL,

    FOREIGN KEY (ChatRoomId) REFERENCES ChatRooms(Id) ON DELETE CASCADE,
    FOREIGN KEY (SenderId) REFERENCES Users(Id),
    FOREIGN KEY (ReplyToMessageId) REFERENCES ChatMessages(Id),
    INDEX IX_ChatMessages_ChatRoomId (ChatRoomId),
    INDEX IX_ChatMessages_SenderId (SenderId),
    INDEX IX_ChatMessages_CreatedAt (CreatedAt)
);

-- Chat Participants Table
CREATE TABLE ChatParticipants (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    ChatRoomId UNIQUEIDENTIFIER NOT NULL,
    UserId UNIQUEIDENTIFIER NOT NULL,
    Role INT NOT NULL DEFAULT 0,
    JoinedAt DATETIME2 NOT NULL,
    LastSeenAt DATETIME2 NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    Settings NVARCHAR(MAX) NULL, -- JSON
    CreatedAt DATETIME2 NOT NULL,
    UpdatedAt DATETIME2 NOT NULL,

    FOREIGN KEY (ChatRoomId) REFERENCES ChatRooms(Id) ON DELETE CASCADE,
    FOREIGN KEY (UserId) REFERENCES Users(Id),
    UNIQUE(ChatRoomId, UserId),
    INDEX IX_ChatParticipants_UserId (UserId)
);

-- Message Attachments Table
CREATE TABLE ChatMessageAttachments (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    MessageId UNIQUEIDENTIFIER NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    FileUrl NVARCHAR(500) NOT NULL,
    ContentType NVARCHAR(100) NOT NULL,
    FileSize BIGINT NOT NULL,
    ThumbnailUrl NVARCHAR(500) NULL,
    CreatedAt DATETIME2 NOT NULL,

    FOREIGN KEY (MessageId) REFERENCES ChatMessages(Id) ON DELETE CASCADE,
    INDEX IX_ChatMessageAttachments_MessageId (MessageId)
);
```

## 📱 Frontend Integration

### WebSocket Client Implementation

```typescript
// TypeScript WebSocket client
export class ChatClient {
  private connection: HubConnection;
  private isConnected = false;

  constructor(private authToken: string) {
    this.connection = new HubConnectionBuilder()
      .withUrl("/chathub", {
        accessTokenFactory: () => this.authToken,
      })
      .withAutomaticReconnect()
      .build();

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.connection.on("MessageReceived", (message: ChatMessage) => {
      this.onMessageReceived(message);
    });

    this.connection.on(
      "MessageRead",
      (data: { messageId: string; readBy: string; readAt: string }) => {
        this.onMessageRead(data);
      }
    );

    this.connection.on(
      "UserTyping",
      (data: { userId: string; roomId: string }) => {
        this.onUserTyping(data);
      }
    );

    this.connection.on(
      "UserStoppedTyping",
      (data: { userId: string; roomId: string }) => {
        this.onUserStoppedTyping(data);
      }
    );
  }

  async connect(): Promise<void> {
    try {
      await this.connection.start();
      this.isConnected = true;
      console.log("Connected to chat hub");
    } catch (error) {
      console.error("Error connecting to chat hub:", error);
    }
  }

  async joinRoom(roomId: string): Promise<void> {
    if (this.isConnected) {
      await this.connection.invoke("JoinRoom", roomId);
    }
  }

  async sendMessage(
    roomId: string,
    content: string,
    messageType: MessageType = MessageType.Text
  ): Promise<void> {
    if (this.isConnected) {
      await this.connection.invoke("SendMessage", {
        roomId,
        content,
        messageType,
      });
    }
  }

  async markAsRead(messageId: string): Promise<void> {
    if (this.isConnected) {
      await this.connection.invoke("MarkMessageAsRead", messageId);
    }
  }

  async startTyping(roomId: string): Promise<void> {
    if (this.isConnected) {
      await this.connection.invoke("StartTyping", roomId);
    }
  }

  async stopTyping(roomId: string): Promise<void> {
    if (this.isConnected) {
      await this.connection.invoke("StopTyping", roomId);
    }
  }

  // Event handlers (to be implemented by the consuming application)
  onMessageReceived(message: ChatMessage): void {}
  onMessageRead(data: {
    messageId: string;
    readBy: string;
    readAt: string;
  }): void {}
  onUserTyping(data: { userId: string; roomId: string }): void {}
  onUserStoppedTyping(data: { userId: string; roomId: string }): void {}
}
```

## 🧪 Testing Strategy

### 1. Unit Tests

```csharp
[Test]
public async Task SendMessage_ValidMessage_ShouldReturnSuccess()
{
    // Arrange
    var command = new SendMessageCommand
    {
        ChatRoomId = Guid.NewGuid(),
        SenderId = Guid.NewGuid(),
        Content = "Test message",
        MessageType = MessageType.Text
    };

    _mockRepository.Setup(r => r.GetChatRoomAsync(command.ChatRoomId))
        .ReturnsAsync(new ChatRoom { Id = command.ChatRoomId });

    // Act
    var result = await _chatService.SendMessageAsync(command);

    // Assert
    Assert.That(result.IsSuccess, Is.True);
    Assert.That(result.Value.Content, Is.EqualTo("Test message"));
}
```

### 2. Integration Tests

```csharp
[Test]
public async Task ChatHub_SendMessage_ShouldBroadcastToRoom()
{
    // Arrange
    var hubContext = GetHubContext();
    var chatHub = new ChatHub(_mockChatService.Object, _mockCurrentUser.Object, _mockLogger.Object, _mockConnectionManager.Object);

    // Act
    await chatHub.SendMessage(new SendMessageRequest
    {
        RoomId = _testRoomId,
        Content = "Test message"
    });

    // Assert
    _mockChatService.Verify(s => s.SendMessageAsync(It.IsAny<SendMessageCommand>()), Times.Once);
}
```

## 📈 Future Enhancements

1. **Voice Messages**: Support for voice message recording and playback
2. **Video Calls**: Integration with video calling capabilities
3. **Message Reactions**: Emoji reactions to messages
4. **Message Threading**: Threaded conversations for better organization
5. **Chat Bots**: AI-powered chat bots for common queries
6. **Message Translation**: Real-time message translation
7. **Advanced Search**: Full-text search across chat history
8. **Message Scheduling**: Schedule messages to be sent later

## 📚 Resources

- [ASP.NET Core SignalR Documentation](https://docs.microsoft.com/en-us/aspnet/core/signalr/)
- [WebSocket Security Best Practices](https://owasp.org/www-community/attacks/WebSocket_security)
- [Real-time Communication Patterns](https://docs.microsoft.com/en-us/azure/architecture/patterns/competing-consumers)
- [Chat Application Architecture Guide](https://docs.microsoft.com/en-us/azure/architecture/example-scenario/apps/commerce-chatbot)
