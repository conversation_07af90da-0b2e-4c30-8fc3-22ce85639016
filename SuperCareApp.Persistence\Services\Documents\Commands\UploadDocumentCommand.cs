﻿using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Documents.Events;

namespace SuperCareApp.Persistence.Services.Documents.Commands;

/// <summary>
/// Command to upload a document
/// </summary>
/// <param name="File">The file to upload</param>
/// <param name="DocumentType">Type of document</param>
/// <param name="UserId">ID of the user uploading the document</param>
/// <param name="Country">Country associated with the certification</param>
/// <param name="CertificationType">Type of certification</param>
/// <param name="OtherCertificationType">Custom certification type (if "Other" is selected)</param>
/// <param name="CertificationNumber">Certification number or identifier</param>
/// <param name="ExpiryDate">Expiry date of the certification</param>
public record UploadDocumentCommand(
    IFormFile File,
    string DocumentType,
    Guid UserId,
    string Issuer,
    string Country,
    string CertificationType,
    string? OtherCertificationType,
    string? CertificationNumber,
    DateTime? ExpiryDate
) : ICommand<Result<DocumentResponse>>;

public class UploadDocumentCommandHandler
    : ICommandHandler<UploadDocumentCommand, Result<DocumentResponse>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IFileStorageService _fileStorageService;
    private readonly IMediator _mediator;
    private readonly ILogger<UploadDocumentCommandHandler> _logger;

    // Whitelist allowed document types
    private static readonly HashSet<string> AllowedDocumentTypes =
        new(StringComparer.OrdinalIgnoreCase) { "id", "passport", "certificate", "license" };

    private const int MaxFileSizeMb = 10;
    private static readonly string[] AllowedExtensions = { ".pdf", ".jpg", ".jpeg", ".png" };

    public UploadDocumentCommandHandler(
        ApplicationDbContext dbContext,
        IFileStorageService fileStorageService,
        IMediator mediator,
        ILogger<UploadDocumentCommandHandler> logger
    )
    {
        _dbContext = dbContext;
        _fileStorageService = fileStorageService;
        _mediator = mediator;
        _logger = logger;
    }

    public async Task<Result<DocumentResponse>> Handle(
        UploadDocumentCommand request,
        CancellationToken ct
    )
    {
        try
        {
            var validationResult = ValidateRequest(request);
            if (validationResult.IsFailure)
                return Result.Failure<DocumentResponse>(validationResult.Error);

            // Validate file
            var fileValidationResult = _fileStorageService.ValidateFile(
                request.File,
                MaxFileSizeMb,
                AllowedExtensions
            );
            if (fileValidationResult.IsFailure)
                return Result.Failure<DocumentResponse>(fileValidationResult.Error);

            // Parse country and certification type
            var parsedCountry = CountryExtensions.FromString(request.Country);
            CertificationType? parsedCertificationType = null;

            if (
                !string.IsNullOrEmpty(request.CertificationType)
                && Enum.TryParse<CertificationType>(request.CertificationType, out var certType)
            )
            {
                parsedCertificationType = certType;

                var validCertifications = CertificationTypeExtensions.GetCertificationsForCountry(
                    parsedCountry
                );
                if (
                    !validCertifications.Contains(certType)
                    && certType != CertificationType.OtherCertification
                )
                {
                    _logger.LogError(
                        "Invalid certification type '{CertificationType}' for country '{Country}'",
                        request.CertificationType,
                        parsedCountry.GetDisplayName()
                    );
                    return Result.Failure<DocumentResponse>(
                        Error.BadRequest(
                            $"The certification type '{request.CertificationType}' is not valid for {parsedCountry.GetDisplayName()}"
                        )
                    );
                }
            }

            if (
                parsedCertificationType == CertificationType.OtherCertification
                && string.IsNullOrWhiteSpace(request.OtherCertificationType)
            )
            {
                return Result.Failure<DocumentResponse>(
                    Error.BadRequest(
                        "Please specify the certification type when 'Other' is selected"
                    )
                );
            }

            // Lock user profile to prevent race conditions
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(
                p => p.ApplicationUserId == request.UserId && !p.IsDeleted,
                ct
            );

            if (userProfile == null)
            {
                _logger.LogWarning("User profile not found for user ID {UserId}", request.UserId);
                return Result.Failure<DocumentResponse>(
                    Error.NotFound($"User profile not found for user ID {request.UserId}")
                );
            }

            // Sanitize document type to avoid path traversal
            var sanitizedDocType = SanitizeDocumentType(request.DocumentType);
            var containerName = $"documents/{sanitizedDocType}/{request.UserId}";

            // Upload file
            var uploadResult = await _fileStorageService.UploadFileAsync(
                request.File,
                containerName
            );
            if (uploadResult.IsFailure)
            {
                _logger.LogError(
                    "Failed to upload document for user {UserId}: {Error}",
                    request.UserId,
                    uploadResult.Error
                );
                return Result.Failure<DocumentResponse>(uploadResult.Error);
            }

            // Create document entity
            var document = new Document
            {
                UserId = userProfile.Id,
                DocumentType = sanitizedDocType,
                DocumentUrl = uploadResult.Value.RelativePath,
                Issuer = request.Issuer.Trim(),
                VerificationStatus = VerificationStatus.Pending,
                UploadedAt = DateTime.UtcNow,
                Country = parsedCountry,
                CertificationType = parsedCertificationType,
                OtherCertificationType = request.OtherCertificationType?.Trim(),
                CertificationNumber = request.CertificationNumber?.Trim(),
                ExpiryDate = request.ExpiryDate.HasValue
                    ? DateTime.SpecifyKind(request.ExpiryDate.Value, DateTimeKind.Utc)
                    : null,
            };

            await _dbContext.Documents.AddAsync(document, ct);
            await _dbContext.SaveChangesAsync(ct);

            // Publish domain event
            var domainEvent = new DocumentUploadedDomainEvent(
                document.Id,
                userProfile.ApplicationUserId,
                uploadResult.Value.OriginalFileName,
                document.UploadedAt
            );

            await _mediator.Publish(domainEvent, ct);

            // Map to response DTO
            var response = new DocumentResponse
            {
                DocumentId = document.Id,
                UserId = userProfile.ApplicationUserId,
                FileName = uploadResult.Value.OriginalFileName,
                DocumentUrl = _fileStorageService.GetFileUrl(document.DocumentUrl), // Convert to absolute URL
                DocumentType = document.DocumentType,
                VerificationStatus = document.VerificationStatus.GetDescription(),
                UploadedAt = document.UploadedAt,
                Country = document.Country.GetDisplayName(),
                CertificationType = document.CertificationType?.GetDisplayName() ?? string.Empty,
                OtherCertificationType = document.OtherCertificationType,
                CertificationNumber = document.CertificationNumber,
                ExpiryDate = document.ExpiryDate,
                IsExpired =
                    document.ExpiryDate.HasValue && document.ExpiryDate.Value < DateTime.UtcNow,
            };
            _logger.LogInformation(
                "Document uploaded successfully for user {UserId}: {FileName}",
                request.UserId,
                uploadResult.Value.OriginalFileName
            );

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for user {UserId}", request.UserId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while uploading the document")
            );
        }
    }

    private Result<bool> ValidateRequest(UploadDocumentCommand request)
    {
        if (string.IsNullOrWhiteSpace(request.DocumentType))
            return Result<bool>.Failure(Error.BadRequest("Document type is required."));

        if (string.IsNullOrWhiteSpace(request.Issuer))
            return Result.Failure<bool>(Error.BadRequest("Issuer is required."));

        if (request.ExpiryDate.HasValue && request.ExpiryDate.Value.Kind != DateTimeKind.Utc)
        {
            return Result<bool>.Failure(Error.BadRequest("Expiry date must be in UTC."));
        }

        return Result.Success(true);
    }

    private static string SanitizeDocumentType(string documentType)
    {
        if (string.IsNullOrWhiteSpace(documentType))
            return "unknown";

        // Remove any non-alphanumeric characters and limit length
        var cleaned = Regex.Replace(
            documentType,
            @"[^a-zA-Z0-9]",
            "",
            RegexOptions.None,
            TimeSpan.FromMilliseconds(100)
        );
        if (string.IsNullOrEmpty(cleaned))
            return "unknown";

        if (cleaned.Length > 50)
            cleaned = cleaned.Substring(0, 50);

        return AllowedDocumentTypes.Contains(cleaned) ? cleaned : "unknown";
    }
}
