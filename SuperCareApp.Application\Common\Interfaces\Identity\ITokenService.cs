using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Application.Common.Interfaces.Identity;

/// <summary>
/// Service for managing authentication tokens
/// </summary>
public interface ITokenService
{
    Task<Result> SaveTokenAsync(ApplicationUserToken userToken);
    Task<Result> SaveTokensAsync(IEnumerable<ApplicationUserToken> userTokens);
    Task<Result<Guid>> ValidateToken(string token);
    Task<Result<string>> GenerateAccessTokenAsync(
        ApplicationUser user,
        Guid? loginSessionId = null
    );
    Task<Result<string>> GenerateRefreshTokenAsync(
        ApplicationUser user,
        string accessToke,
        Guid? loginSessionId = null
    );
    Task<Result<bool>> RevokeTokenAsync(string token);
    Task<Result<bool>> RevokeAllTokensAsync(Guid userId);
    Task<Result<bool>> RemoveTokenAsync(string token);
    Task<Result<AuthResponse>> ExchangeTokenAsync(string refreshToken);
}
