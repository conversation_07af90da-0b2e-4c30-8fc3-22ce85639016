﻿namespace SuperCareApp.Application.Common.Models.Admin;

// Existing models (RejectRequestModel and AcceptRequestModel) unchanged

/// <summary>
/// Model for resetting an admins password
/// </summary>
public class AdminResetPasswordRequest
{
    /// <summary>
    /// Current password of the admin
    /// </summary>
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// New password for the admin
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation of the new password
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}
