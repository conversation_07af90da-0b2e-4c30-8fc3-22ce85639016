﻿using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

public record GetInvoiceByIdQuery(Guid InvoiceId) : IQuery<Result<InvoiceResponse>>;

public class GetInvoiceByIdQueryHandler
    : IQueryHandler<GetInvoiceByIdQuery, Result<InvoiceResponse>>
{
    private readonly ApplicationDbContext _context;

    public GetInvoiceByIdQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<InvoiceResponse>> Handle(
        GetInvoiceByIdQuery request,
        CancellationToken cancellationToken
    )
    {
        var invoice = await _context
            .Invoices.Include(i => i.BookingWindow)
            .ThenInclude(bw => bw.Booking)
            .Where(i => i.Id == request.InvoiceId)
            .Select(i => new InvoiceResponse(
                i.Id,
                i.InvoiceNumber,
                i.BookingWindowId,
                i.BookingWindow.Booking.ClientId,
                $"{i.BookingWindow.Booking.Client.UserProfile.FirstName} {i.BookingWindow.Booking.Client.UserProfile.LastName}".Trim(),
                i.BookingWindow.Booking.ProviderId,
                $"{i.BookingWindow.Booking.Provider.User.UserProfile.FirstName} {i.BookingWindow.Booking.Provider.User.UserProfile.LastName}".Trim(),
                i.TotalAmount,
                i.Currency,
                i.BookingWindow.Booking.Category.Name,
                i.InvoiceDate.ToString("yyyy-MM-dd"),
                i.Status.ToString(),
                i.FileName,
                i.FileUrl
            ))
            .FirstOrDefaultAsync(cancellationToken);

        if (invoice == null)
            return Result.Failure<InvoiceResponse>(Error.NotFound("Invoice not found."));

        return Result.Success(invoice);
    }
}
