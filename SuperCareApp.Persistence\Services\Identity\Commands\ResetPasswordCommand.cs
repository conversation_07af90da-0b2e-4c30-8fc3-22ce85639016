﻿using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record ResetPasswordCommand(string Email, string Password, string ResetToken)
    : ICommand<Result<ResetPasswordResponse>>;

internal sealed class ResetPasswordCommandHandler
    : ICommandHandler<ResetPasswordCommand, Result<ResetPasswordResponse>>
{
    private readonly IAuthService _authService;

    public ResetPasswordCommandHandler(IAuthService authService)
    {
        _authService = authService;
    }

    public async Task<Result<ResetPasswordResponse>> Handle(
        ResetPasswordCommand request,
        CancellationToken cancellationToken
    )
    {
        var resetPasswordResult = await _authService.ResetPasswordAsync(
            request.Email,
            request.Password,
            request.ResetToken
        );
        if (resetPasswordResult.IsFailure)
        {
            return Result.Failure<ResetPasswordResponse>(resetPasswordResult.Error);
        }

        var response = new ResetPasswordResponse("Password reset successfully.", Guid.NewGuid());
        return Result.Success(response);
    }
}
