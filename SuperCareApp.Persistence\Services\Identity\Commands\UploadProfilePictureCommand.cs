﻿using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

/// <summary>
/// Command to upload a profile picture
/// </summary>
/// <param name="File">The profile picture file to upload</param>
/// <param name="UserId">ID of the user</param>
public record UploadProfilePictureCommand(IFormFile File, Guid UserId)
    : ICommand<Result<ProfilePictureResponse>>;

/// <summary>
/// Handler for the UploadProfilePictureCommand
/// </summary>
internal sealed class UploadProfilePictureCommandHandler
    : ICommandHandler<UploadProfilePictureCommand, Result<ProfilePictureResponse>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<UploadProfilePictureCommandHandler> _logger;
    private readonly string[] _allowedExtensions = [".jpg", ".jpeg", ".png"];
    private readonly int _maxFileSizeMb = 5;

    public UploadProfilePictureCommandHandler(
        ApplicationDbContext dbContext,
        IFileStorageService fileStorageService,
        ILogger<UploadProfilePictureCommandHandler> logger
    )
    {
        _dbContext = dbContext;
        _fileStorageService = fileStorageService;
        _logger = logger;
    }

    public async Task<Result<ProfilePictureResponse>> Handle(
        UploadProfilePictureCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Validate the file
            var validationResult = _fileStorageService.ValidateFile(
                request.File,
                _maxFileSizeMb,
                _allowedExtensions
            );

            if (validationResult.IsFailure)
            {
                return Result.Failure<ProfilePictureResponse>(validationResult.Error);
            }

            // Find the user profile
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(
                p => p.ApplicationUserId == request.UserId && !p.IsDeleted,
                cancellationToken
            );

            if (userProfile == null)
            {
                return Result.Failure<ProfilePictureResponse>(
                    Error.NotFound("User profile not found")
                );
            }

            // Delete the old profile picture if it exists
            if (!string.IsNullOrEmpty(userProfile.ImagePath))
            {
                // If the path is relative, we need to convert it to an absolute path
                string absolutePath = userProfile.ImagePath;
                if (!Path.IsPathRooted(userProfile.ImagePath))
                {
                    // Convert relative path to absolute path
                    absolutePath = Path.Combine(
                        _dbContext.GetWebRootPath(),
                        userProfile.ImagePath.TrimStart('/')
                    );
                }

                if (File.Exists(absolutePath))
                {
                    await _fileStorageService.DeleteFileAsync(absolutePath);
                }
            }

            // Upload the new profile picture
            var uploadResult = await _fileStorageService.UploadFileAsync(
                request.File,
                $"ProfilePictures/{request.UserId}"
            );

            if (uploadResult.IsFailure)
            {
                return Result.Failure<ProfilePictureResponse>(uploadResult.Error);
            }

            // Update the user profile with the relative path
            userProfile.ImageName = uploadResult.Value.StoredFileName;
            userProfile.ImagePath = uploadResult.Value.RelativePath; // Store relative path instead of absolute path
            userProfile.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);

            // Create the response
            var response = new ProfilePictureResponse
            {
                ImageUrl = uploadResult.Value.FileUrl,
                FileName = uploadResult.Value.StoredFileName,
                UploadedAt = uploadResult.Value.UploadedAt,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error uploading profile picture for user {UserId}",
                request.UserId
            );
            return Result.Failure<ProfilePictureResponse>(
                Error.Internal("An error occurred while uploading the profile picture")
            );
        }
    }
}
