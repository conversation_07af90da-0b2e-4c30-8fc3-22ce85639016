using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    /// <summary>
    /// Request model for updating availability status (manual override)
    /// </summary>
    public class UpdateAvailabilityStatusRequest
    {
        /// <summary>
        /// The new availability status
        /// </summary>
        public bool IsAvailable { get; set; }

        /// <summary>
        /// Optional reason for the status change
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Validator for UpdateAvailabilityStatusRequest
    /// </summary>
    public class UpdateAvailabilityStatusRequestValidator
        : AbstractValidator<UpdateAvailabilityStatusRequest>
    {
        public UpdateAvailabilityStatusRequestValidator()
        {
            RuleFor(x => x.Reason)
                .MaximumLength(500)
                .WithMessage("Reason cannot exceed 500 characters")
                .When(x => !string.IsNullOrWhiteSpace(x.Reason));
        }
    }
}
