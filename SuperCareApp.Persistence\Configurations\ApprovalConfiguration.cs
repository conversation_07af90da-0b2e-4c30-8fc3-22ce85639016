﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class ApprovalConfiguration : IEntityTypeConfiguration<Approval>
    {
        public void Configure(EntityTypeBuilder<Approval> builder)
        {
            builder.HasKey(a => a.Id);

            builder.Property(a => a.UserId).IsRequired();

            builder.Property(a => a.ApprovalType).IsRequired();

            builder.Property(a => a.IsApproved).IsRequired(false);

            builder.Property(a => a.RejectionReason).HasMaxLength(500);

            builder.Property(a => a.ApprovalData).HasColumnType("jsonb");

            builder.Property(a => a.Notes).HasMaxLength(1000);

            // Configure relationships
            builder
                .HasOne(a => a.User)
                .WithMany()
                .HasForeignKey(a => a.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasOne(a => a.Processor)
                .WithMany()
                .HasForeignKey(a => a.ProcessedBy)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
