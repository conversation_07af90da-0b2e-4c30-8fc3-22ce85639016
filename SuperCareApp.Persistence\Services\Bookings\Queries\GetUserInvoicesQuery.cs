using Microsoft.EntityFrameworkCore;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

public record GetUserInvoicesQuery(Guid UserId) : IQuery<Result<IEnumerable<InvoiceResponse>>>;

public sealed class GetUserInvoicesQueryHandler
    : IQueryHandler<GetUserInvoicesQuery, Result<IEnumerable<InvoiceResponse>>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetUserInvoicesQueryHandler(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<IEnumerable<InvoiceResponse>>> Handle(
        GetUserInvoicesQuery request,
        CancellationToken cancellationToken
    )
    {
        // 1. Guard (unchanged)
        if (request.UserId == Guid.Empty)
            return Result<IEnumerable<InvoiceResponse>>.Failure(
                Error.Validation("User ID is required.")
            );

        if (!await _dbContext.Users.AnyAsync(u => u.Id == request.UserId, cancellationToken))
            return Result<IEnumerable<InvoiceResponse>>.Failure(Error.NotFound("User not found."));

        // 2. Single query: fetch invoices OR empty list if not a provider
        var invoices = await _dbContext
            .Invoices.AsNoTracking()
            .Where(i =>
                // Client invoices
                i.BookingWindow.Booking.ClientId == request.UserId
                ||
                // Provider invoices
                _dbContext.CareProviderProfiles.Any(cp =>
                    cp.UserId == request.UserId && cp.Id == i.BookingWindow.Booking.ProviderId
                )
            )
            .Select(i => new InvoiceResponse(
                i.Id,
                i.InvoiceNumber,
                i.BookingWindowId,
                i.BookingWindow.Booking.ClientId,
                $"{i.BookingWindow.Booking.Client.UserProfile.FirstName} {i.BookingWindow.Booking.Client.UserProfile.LastName}",
                i.BookingWindow.Booking.ProviderId,
                $"{i.BookingWindow.Booking.Provider.User.UserProfile.FirstName} {i.BookingWindow.Booking.Provider.User.UserProfile.LastName}",
                i.Amount,
                i.Currency,
                i.BookingWindow.Booking.Category.Name,
                i.InvoiceDate.ToString("yyyy-MM-dd"),
                i.Status.ToString(),
                i.FileName,
                i.FileUrl
            ))
            .ToListAsync(cancellationToken);

        return invoices.Count == 0
            ? Result<IEnumerable<InvoiceResponse>>.Failure(
                Error.NotFound("No invoices found for the specified user.")
            )
            : Result<IEnumerable<InvoiceResponse>>.Success(invoices);
    }
}
