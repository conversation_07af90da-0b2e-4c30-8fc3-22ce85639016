using System.ComponentModel.DataAnnotations;
using FluentValidation;
#nullable disable
namespace SuperCareApp.Application.Common.Models.Otp
{
    public class ResetPasswordRequest
    {
        public string Email { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }
        public string ResetToken { get; set; }
    }

    public class ResetPasswordRequestValidator : AbstractValidator<ResetPasswordRequest>
    {
        public ResetPasswordRequestValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty()
                .WithMessage("Email is required")
                .Matches(@"^[^@\s]+@[^@\s]+\.[^@\s]+$")
                .WithMessage("Invalid email address format");

            RuleFor(x => x.NewPassword)
                .NotEmpty()
                .WithMessage("New password is required")
                .MinimumLength(8)
                .WithMessage("Password must be at least 8 characters")
                .MaximumLength(100)
                .WithMessage("Password cannot exceed 100 characters")
                .Matches(@"^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d\W_]+$")
                .WithMessage(
                    "Password must contain at least one letter and one number, and can include special characters."
                );

            RuleFor(x => x.ConfirmPassword)
                .NotEmpty()
                .WithMessage("Confirm password is required")
                .Equal(x => x.NewPassword)
                .WithMessage("Passwords do not match");

            RuleFor(x => x.ResetToken).NotEmpty().WithMessage("Reset token is required");
        }
    }
}
