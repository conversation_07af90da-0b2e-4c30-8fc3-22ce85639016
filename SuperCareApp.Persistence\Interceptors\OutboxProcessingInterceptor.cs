using Microsoft.EntityFrameworkCore.Diagnostics;

namespace SuperCareApp.Persistence.Interceptors;

public sealed class OutboxProcessingInterceptor : SaveChangesInterceptor
{
    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default
    )
    {
        // TODO: enqueue domain events into outbox table here.        return base.SavingChangesAsync(eventData, result, cancellationToken);
        return new ValueTask<InterceptionResult<int>>(result);
    }
}
