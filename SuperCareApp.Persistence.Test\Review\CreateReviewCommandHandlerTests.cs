using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using SuperCare.Persistence.Services.Reviews.Commands;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Reviews.Commands;

namespace SuperCareApp.Tests.Persistence.Services.Reviews.Commands
{
    public class CreateReviewCommandHandlerTests
    {
        private ApplicationDbContext GetInMemoryDbContext()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            return new ApplicationDbContext(options);
        }

        [Fact]
        public async Task Handle_SameClientCanCreateReviewsForDifferentBookings_ShouldSucceed()
        {
            // Arrange
            using var context = GetInMemoryDbContext();
            var mockCurrentUserService = new Mock<ICurrentUserService>();
            var mockLogger = new Mock<ILogger<CreateReviewCommandHandler>>();

            var clientId = Guid.NewGuid();
            var providerId1 = Guid.NewGuid();
            var providerId2 = Guid.NewGuid();
            var categoryId = Guid.NewGuid();

            // Setup current user as authenticated client
            mockCurrentUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockCurrentUserService.Setup(x => x.UserId).Returns(clientId);

            // Create test users
            var clientUser = new ApplicationUser
            {
                Id = clientId,
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                IsDeleted = false,
            };

            var providerUser1 = new ApplicationUser
            {
                Id = providerId1,
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                IsDeleted = false,
            };

            var providerUser2 = new ApplicationUser
            {
                Id = providerId2,
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                IsDeleted = false,
            };

            await context.Users.AddRangeAsync(clientUser, providerUser1, providerUser2);

            // Create care provider profiles
            var providerProfile1 = new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = providerId1,
                IsDeleted = false,
            };

            var providerProfile2 = new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = providerId2,
                IsDeleted = false,
            };

            await context.CareProviderProfiles.AddRangeAsync(providerProfile1, providerProfile2);

            // Create care category
            var category = new CareCategory
            {
                Id = categoryId,
                Name = "Test Category",
                IsDeleted = false,
            };

            await context.CareCategories.AddAsync(category);

            // Create two completed bookings for the same client with different providers
            var booking1 = new Booking
            {
                Id = Guid.NewGuid(),
                ClientId = clientId,
                ProviderId = providerProfile1.Id,
                CategoryId = categoryId,
                Provider = providerProfile1,
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
            };

            var booking2 = new Booking
            {
                Id = Guid.NewGuid(),
                ClientId = clientId,
                ProviderId = providerProfile2.Id,
                CategoryId = categoryId,
                Provider = providerProfile2,
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
            };

            await context.Bookings.AddRangeAsync(booking1, booking2);

            // Set booking statuses to completed
            var bookingStatus1 = new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = booking1.Id,
                Status = BookingStatusType.Completed,
                IsDeleted = false,
            };

            var bookingStatus2 = new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = booking2.Id,
                Status = BookingStatusType.Completed,
                IsDeleted = false,
            };

            await context.BookingStatuses.AddRangeAsync(bookingStatus1, bookingStatus2);

            // Update bookings with status references
            booking1.Status = bookingStatus1;
            booking2.Status = bookingStatus2;

            await context.SaveChangesAsync();

            var handler = new CreateReviewCommandHandler(
                context,
                mockCurrentUserService.Object,
                mockLogger.Object
            );

            // Act - Create review for first booking
            var command1 = new CreateReviewCommand(booking1.Id, "Great service from provider 1", 5);
            var result1 = await handler.Handle(command1, CancellationToken.None);

            // Act - Create review for second booking
            var command2 = new CreateReviewCommand(
                booking2.Id,
                "Excellent service from provider 2",
                4.5m
            );
            var result2 = await handler.Handle(command2, CancellationToken.None);

            // Assert
            Assert.True(result1.IsSuccess);
            Assert.True(result2.IsSuccess);

            Assert.NotNull(result1.Value);
            Assert.NotNull(result2.Value);

            // Verify first review
            Assert.Equal(booking1.Id, result1.Value.BookingId);
            Assert.Equal(clientId, result1.Value.ReviewerId);
            Assert.Equal(providerId1, result1.Value.RevieweeId);
            Assert.Equal(5, result1.Value.Rating);
            Assert.Equal("Great service from provider 1", result1.Value.Comment);

            // Verify second review
            Assert.Equal(booking2.Id, result2.Value.BookingId);
            Assert.Equal(clientId, result2.Value.ReviewerId);
            Assert.Equal(providerId2, result2.Value.RevieweeId);
            Assert.Equal(4.5m, result2.Value.Rating);
            Assert.Equal("Excellent service from provider 2", result2.Value.Comment);

            // Verify reviews are persisted in database
            var reviewsInDb = await context.Reviews.ToListAsync();
            Assert.Equal(2, reviewsInDb.Count);

            var review1InDb = reviewsInDb.FirstOrDefault(r => r.BookingId == booking1.Id);
            var review2InDb = reviewsInDb.FirstOrDefault(r => r.BookingId == booking2.Id);

            Assert.NotNull(review1InDb);
            Assert.NotNull(review2InDb);

            // Verify both reviews are from the same client
            Assert.Equal(clientId, review1InDb.ReviewerId);
            Assert.Equal(clientId, review2InDb.ReviewerId);

            // Verify reviews are for different providers
            Assert.Equal(providerId1, review1InDb.RevieweeId);
            Assert.Equal(providerId2, review2InDb.RevieweeId);
        }

        [Fact]
        public async Task Handle_SameClientSameProviderDifferentBookings_ShouldSucceed()
        {
            // Arrange
            using var context = GetInMemoryDbContext();
            var mockCurrentUserService = new Mock<ICurrentUserService>();
            var mockLogger = new Mock<ILogger<CreateReviewCommandHandler>>();

            var clientId = Guid.NewGuid();
            var providerId = Guid.NewGuid();
            var categoryId = Guid.NewGuid();

            // Setup current user as authenticated client
            mockCurrentUserService.Setup(x => x.IsAuthenticated).Returns(true);
            mockCurrentUserService.Setup(x => x.UserId).Returns(clientId);

            // Create test users
            var clientUser = new ApplicationUser
            {
                Id = clientId,
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                IsDeleted = false,
            };

            var providerUser = new ApplicationUser
            {
                Id = providerId,
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                IsDeleted = false,
            };

            await context.Users.AddRangeAsync(clientUser, providerUser);

            // Create care provider profile
            var providerProfile = new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = providerId,
                IsDeleted = false,
            };

            await context.CareProviderProfiles.AddAsync(providerProfile);

            // Create care category
            var category = new CareCategory
            {
                Id = categoryId,
                Name = "Test Category",
                IsDeleted = false,
            };

            await context.CareCategories.AddAsync(category);

            // Create two completed bookings for the same client with the same provider
            var booking1 = new Booking
            {
                Id = Guid.NewGuid(),
                ClientId = clientId,
                ProviderId = providerProfile.Id,
                CategoryId = categoryId,
                Provider = providerProfile,
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow.AddDays(-10),
            };

            var booking2 = new Booking
            {
                Id = Guid.NewGuid(),
                ClientId = clientId,
                ProviderId = providerProfile.Id,
                CategoryId = categoryId,
                Provider = providerProfile,
                IsDeleted = false,
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                UpdatedAt = DateTime.UtcNow.AddDays(-5),
            };

            await context.Bookings.AddRangeAsync(booking1, booking2);

            // Set booking statuses to completed
            var bookingStatus1 = new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = booking1.Id,
                Status = BookingStatusType.Completed,
                IsDeleted = false,
            };

            var bookingStatus2 = new BookingStatus
            {
                Id = Guid.NewGuid(),
                BookingId = booking2.Id,
                Status = BookingStatusType.Completed,
                IsDeleted = false,
            };

            await context.BookingStatuses.AddRangeAsync(bookingStatus1, bookingStatus2);

            // Update bookings with status references
            booking1.Status = bookingStatus1;
            booking2.Status = bookingStatus2;

            await context.SaveChangesAsync();

            var handler = new CreateReviewCommandHandler(
                context,
                mockCurrentUserService.Object,
                mockLogger.Object
            );

            // Act - Create review for first booking
            var command1 = new CreateReviewCommand(booking1.Id, "First service was good", 4);
            var result1 = await handler.Handle(command1, CancellationToken.None);

            // Act - Create review for second booking with same provider
            var command2 = new CreateReviewCommand(
                booking2.Id,
                "Second service was even better",
                5
            );
            var result2 = await handler.Handle(command2, CancellationToken.None);

            // Assert
            Assert.True(result1.IsSuccess);
            Assert.True(result2.IsSuccess);

            Assert.NotNull(result1.Value);
            Assert.NotNull(result2.Value);

            // Verify first review
            Assert.Equal(booking1.Id, result1.Value.BookingId);
            Assert.Equal(clientId, result1.Value.ReviewerId);
            Assert.Equal(providerId, result1.Value.RevieweeId);
            Assert.Equal(4, result1.Value.Rating);
            Assert.Equal("First service was good", result1.Value.Comment);

            // Verify second review
            Assert.Equal(booking2.Id, result2.Value.BookingId);
            Assert.Equal(clientId, result2.Value.ReviewerId);
            Assert.Equal(providerId, result2.Value.RevieweeId);
            Assert.Equal(5, result2.Value.Rating);
            Assert.Equal("Second service was even better", result2.Value.Comment);

            // Verify reviews are persisted in database
            var reviewsInDb = await context.Reviews.ToListAsync();
            Assert.Equal(2, reviewsInDb.Count);

            var review1InDb = reviewsInDb.FirstOrDefault(r => r.BookingId == booking1.Id);
            var review2InDb = reviewsInDb.FirstOrDefault(r => r.BookingId == booking2.Id);

            Assert.NotNull(review1InDb);
            Assert.NotNull(review2InDb);

            // Verify both reviews are from the same client
            Assert.Equal(clientId, review1InDb.ReviewerId);
            Assert.Equal(clientId, review2InDb.ReviewerId);

            // Verify both reviews are for the same provider
            Assert.Equal(providerId, review1InDb.RevieweeId);
            Assert.Equal(providerId, review2InDb.RevieweeId);

            // Verify the reviews are for different bookings
            Assert.NotEqual(review1InDb.BookingId, review2InDb.BookingId);
        }
    }
}
