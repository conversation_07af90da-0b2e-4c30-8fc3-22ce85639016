﻿using System.Globalization;
using Microsoft.AspNetCore.Hosting;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

public record GenerateInvoicesForBookingCommand(Guid BookingWindowId)
    : ICommand<Result<InvoiceResponse>>;

internal sealed class GenerateInvoicesForBookingCommandHandler
    : ICommandHandler<GenerateInvoicesForBookingCommand, Result<InvoiceResponse>>
{
    private readonly ApplicationDbContext _db;
    private readonly IFileStorageService _storage;
    private readonly ILogger<GenerateInvoicesForBookingCommandHandler> _logger;
    private readonly InvoicePdfBuilder _pdfBuilder;

    public GenerateInvoicesForBookingCommandHandler(
        ApplicationDbContext db,
        IFileStorageService storage,
        ILogger<GenerateInvoicesForBookingCommandHandler> logger,
        IWebHostEnvironment env
    )
    {
        _db = db;
        _storage = storage;
        _logger = logger;
        _pdfBuilder = new InvoicePdfBuilder(env);
    }

    public async Task<Result<InvoiceResponse>> Handle(
        GenerateInvoicesForBookingCommand request,
        CancellationToken ct
    )
    {
        // Alternative approach using AsSplitQuery for better performance with complex includes

        var bookingData = await _db
            .BookingWindows.AsNoTracking()
            .AsSplitQuery()
            .Where(bw => bw.Id == request.BookingWindowId)
            .Include(bw => bw.Booking)
            .ThenInclude(b => b.Client)
            .ThenInclude(c => c.UserProfile)
            .Include(bw => bw.Booking)
            .ThenInclude(b => b.Provider)
            .ThenInclude(p => p.User)
            .ThenInclude(u => u.UserProfile)
            .Include(bw => bw.Booking)
            .ThenInclude(b => b.Category)
            .ThenInclude(c => c.CareProviderCategories)
            .Select(bw => new BookingWindowDto
            {
                Id = bw.Id,
                DailyRate = bw.DailyRate.Value,
                StartTime = bw.StartTime,
                EndTime = bw.EndTime,
                Date = bw.Date,
                Status = bw.Status.Value,
                ClientId = bw.Booking.ClientId,
                ProviderId = bw.Booking.ProviderId,
                ClientFirstName = bw.Booking.Client.UserProfile.FirstName,
                ClientLastName = bw.Booking.Client.UserProfile.LastName,
                ClientEmail = bw.Booking.Client.Email,
                ClientPhoneNumber = bw.Booking.Client.PhoneNumber,
                ProviderFirstName = bw.Booking.Provider.User.UserProfile.FirstName,
                ProviderLastName = bw.Booking.Provider.User.UserProfile.LastName,
                ProviderEmail = bw.Booking.Provider.User.Email,
                ProviderPhoneNumber = bw.Booking.Provider.User.PhoneNumber,
                ProviderYearsExperience = bw.Booking.Provider.YearsExperience,
                HourlyRate = Math.Round(
                    bw.Booking.Provider.CareProviderCategories.FirstOrDefault(c =>
                        c.CategoryId == bw.Booking.CategoryId
                    ).HourlyRate
                ),
                PlatformFee = bw
                    .Booking.Provider.CareProviderCategories.FirstOrDefault(c =>
                        c.CategoryId == bw.Booking.CategoryId
                    )
                    .CareCategory.PlatformFee,
                CategoryName = bw.Booking.Category.Name,
            })
            .FirstOrDefaultAsync(ct);

        if (bookingData is null)
            return Result.Failure<InvoiceResponse>(Error.NotFound("Booking window not found"));

        if (bookingData.Status != BookingWindowStatus.Completed)
        {
            return Result.Failure<InvoiceResponse>(
                Error.BadRequest("Cannot generate invoice report for incomplete booking window")
            );
        }

        if (bookingData.StartTime >= bookingData.EndTime)
            return Result.Failure<InvoiceResponse>(Error.BadRequest("Invalid time range"));

        // 2. Create invoice (same logic)
        var hours = (decimal)(bookingData.EndTime - bookingData.StartTime).TotalHours;
        var amount = hours * bookingData.HourlyRate;
        var tax = amount * bookingData.PlatformFee;
        var total = amount + tax;

        var invoice = new Invoice
        {
            Id = Guid.NewGuid(),
            BookingWindowId = bookingData.Id,
            InvoiceNumber = $"INV-{DateTime.UtcNow:yyyyMMdd}-{bookingData.Id.ToString()[..4]}",
            InvoiceDate = DateTime.UtcNow,
            DueDate = DateTime.UtcNow.AddDays(14),
            Status = InvoiceStatus.Paid,
            Amount = amount,
            Tax = tax,
            TotalAmount = total,
            Currency = "USD",
        };

        _db.Invoices.Add(invoice);

        // 3. PDF & upload (same logic)
        var pdfBytes = await _pdfBuilder.BuildAsync(invoice, bookingData);
        if (pdfBytes is null or { Length: 0 })
            return Result.Failure<InvoiceResponse>(Error.Internal("PDF generation failed"));

        await using var pdfStream = new MemoryStream(pdfBytes);
        var fileName = $"{invoice.InvoiceNumber}.pdf";
        var upload = await _storage.UploadFileToLocalStorageAsync(
            pdfStream,
            "invoices",
            fileName,
            "application/pdf"
        );

        if (upload.IsFailure)
        {
            _logger.LogError("Upload failed for invoice {Id}: {Error}", invoice.Id, upload.Error);
            return Result.Failure<InvoiceResponse>(upload.Error);
        }

        // 4. Final update (same logic)
        invoice.FileName = upload.Value.StoredFileName;
        invoice.FileUrl = upload.Value.FileUrl;
        await _db.SaveChangesAsync(ct);

        // 5. Response with flattened data
        return Result.Success(
            new InvoiceResponse(
                invoice.Id,
                invoice.InvoiceNumber,
                bookingData.Id,
                bookingData.ClientId,
                $"{bookingData.ClientFirstName} {bookingData.ClientLastName}".Trim(),
                bookingData.ProviderId,
                $"{bookingData.ProviderFirstName} {bookingData.ProviderLastName}".Trim(),
                invoice.TotalAmount,
                invoice.Currency,
                bookingData.CategoryName,
                invoice.InvoiceDate.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                invoice.Status.ToString(),
                invoice.FileName,
                invoice.FileUrl
            )
        );
    }
}

internal class BookingWindowDto
{
    public Guid Id { get; set; }
    public decimal? DailyRate { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public DateOnly Date { get; set; }
    public BookingWindowStatus? Status { get; set; }

    // Client data (flattened)
    public Guid ClientId { get; set; }
    public string ClientFirstName { get; set; } = string.Empty;
    public string ClientLastName { get; set; } = string.Empty;
    public string ClientEmail { get; set; } = string.Empty;
    public string? ClientPhoneNumber { get; set; }
    public Domain.Entities.Address? ClientPrimaryAddress { get; set; }

    // Provider data (flattened)
    public Guid ProviderId { get; set; }
    public string ProviderFirstName { get; set; } = string.Empty;
    public string ProviderLastName { get; set; } = string.Empty;
    public string ProviderEmail { get; set; } = string.Empty;
    public string? ProviderPhoneNumber { get; set; }
    public int ProviderYearsExperience { get; set; }
    public decimal HourlyRate { get; set; } = 0;
    public decimal PlatformFee { get; set; } = 0;

    // Category data
    public string CategoryName { get; set; } = string.Empty;
}
