﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to activate a care category
    /// </summary>
    public record ActivateCareCategoryCommand(Guid CategoryId, Guid AdminId)
        : ICommand<Result<CareCategoryResponse>>;

    /// <summary>
    /// Handler for the ActivateCareCategoryCommand
    /// </summary>
    public sealed class ActivateCareCategoryCommandHandler
        : ICommandHandler<ActivateCareCategoryCommand, Result<CareCategoryResponse>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<ActivateCareCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public ActivateCareCategoryCommandHandler(
            ICareCategoryService categoryService,
            ILogger<ActivateCareCategoryCommandHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result<CareCategoryResponse>> Handle(
            ActivateCareCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Create an update request that only sets IsActive to true
                var updateRequest = new UpdateCareCategoryRequest { IsActive = true };

                return await _categoryService.UpdateCategoryAsync(
                    request.CategoryId,
                    updateRequest,
                    request.AdminId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error activating care category with ID {CategoryId}",
                    request.CategoryId
                );
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }
    }
}
