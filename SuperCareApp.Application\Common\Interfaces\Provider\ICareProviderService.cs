using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Provider;

public interface ICareProviderService
{
    Task<Result<IEnumerable<CareProviderProfileResponse>>> GetAllProvidersAsync();
    Task<Result<PagedCareProviderList>> GetPagedProvidersAsync(CareProviderListParams parameters);
    Task<Result<CareProviderProfileResponse>> GetProfileAsync(Guid userId);
    Task<Result<Guid>> CreateProfileAsync(Guid userId, CreateCareProviderProfileRequest request);
    Task<Result> UpdateProfileAsync(Guid userId, UpdateCareProviderProfileRequest request);
}
