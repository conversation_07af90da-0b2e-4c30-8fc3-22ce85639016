﻿namespace SuperCareApp.Application.Common.Interfaces
{
    /// <summary>
    /// Service to get the current user information
    /// </summary>
    public interface ICurrentUserService
    {
        /// <summary>
        /// Gets the current user id
        /// </summary>
        Guid? UserId { get; }

        /// <summary>
        /// Checks if the user is authenticated
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// Clears the current user information
        /// </summary>
        void Clear();

        /// <summary>
        ///Checks if the user is in a specific role
        /// </summary>
        /// <param name="roleName"></param>
        bool IsInRole(string roleName);
    }
}
