using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Shared.Utility;

namespace SuperCareApp.Application.Common.Extensions
{
    /// <summary>
    /// Extension methods for paged lists
    /// </summary>
    public static class PagedListExtensions
    {
        /// <summary>
        /// Converts a PagedProfileList to a PagedList<ProfileResponse>
        /// </summary>
        public static PagedList<ProfileResponse> ToPagedList(this PagedProfileList source)
        {
            return new PagedList<ProfileResponse>(
                source.Profiles,
                source.TotalCount,
                source.PageNumber,
                source.PageSize
            );
        }

        /// <summary>
        /// Converts a PagedList<ProfileResponse> to a PagedProfileList
        /// </summary>
        public static PagedProfileList ToPagedProfileList(this PagedList<ProfileResponse> source)
        {
            var meta = source.ToMetadata();
            return new PagedProfileList
            {
                Profiles = source.Items,
                PageNumber = meta.CurrentPage,
                PageSize = meta.PageSize,
                TotalCount = meta.TotalCount,
                TotalPages = meta.TotalPages,
            };
        }
    }
}
