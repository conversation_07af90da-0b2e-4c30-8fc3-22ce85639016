# Design Document

## Overview

The invoice generation system will transform the current basic Invoice entity into a comprehensive billing solution that automatically generates invoices based on completed booking windows, manages invoice status through a proper workflow, and generates professional PDF documents using PeachPDF library. The system will integrate seamlessly with existing booking and payment workflows while providing robust financial reporting and client/provider portals.

The design follows Clean Architecture principles with CQRS implementation, providing specialized services for invoice generation, PDF creation, and financial reporting. The system will maintain backward compatibility while adding comprehensive invoice management capabilities.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
│              InvoicesController + ReportsController         │
├─────────────────────────────────────────────────────────────┤
│              Application Layer (CQRS Handlers)              │
│  Commands: Generate, Update, Void, Pay                     │
│  Queries: GetInvoices, GetInvoiceById, GetReports          │
├─────────────────────────────────────────────────────────────┤
│              Application Services (Specialized)             │
│  IInvoiceGenerationService, IPdfGenerationService          │
│  IInvoiceNotificationService, IInvoiceReportingService     │
├─────────────────────────────────────────────────────────────┤
│                   Domain Layer (Enhanced)                   │
│  Invoice (Enhanced), InvoiceLineItem, InvoiceStatus        │
├─────────────────────────────────────────────────────────────┤
│                   External Services                         │
│  PeachPDF Library, Email Service, File Storage             │
└─────────────────────────────────────────────────────────────┘
```

### Integration Points

1. **Existing Booking System**: Integrates with BookingWindow completion events
2. **Payment System**: Updates invoice status based on payment events
3. **File Storage**: Stores generated PDF invoices using existing storage service
4. **Notification System**: Uses existing email infrastructure for notifications
5. **Reporting System**: Integrates with existing analytics and reporting

## Components and Interfaces

### Enhanced Domain Model

#### Enhanced Invoice Entity

```csharp
public class Invoice : BaseEntity
{
    // Basic properties
    public Guid BookingId { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public DateTime InvoiceDate { get; set; }
    public DateTime? DueDate { get; set; }
    public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
    
    // Financial details
    public decimal SubtotalAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = "USD";
    
    // Client and provider information
    public Guid ClientId { get; set; }
    public Guid ProviderId { get; set; }
    public string ClientName { get; set; } = string.Empty;
    public string ClientEmail { get; set; } = string.Empty;
    public string ProviderName { get; set; } = string.Empty;
    
    // Address information
    public string BillingAddressJson { get; set; } = string.Empty; // JSON serialized address
    public string ServiceAddressJson { get; set; } = string.Empty; // JSON serialized address
    
    // PDF and file information
    public string? PdfFilePath { get; set; }
    public string? PdfFileUrl { get; set; }
    public DateTime? PdfGeneratedAt { get; set; }
    public int PdfVersion { get; set; } = 1;
    
    // Payment tracking
    public DateTime? PaidAt { get; set; }
    public Guid? PaidBy { get; set; }
    public string? PaymentReference { get; set; }
    
    // Audit and notes
    public string? Notes { get; set; }
    public string? InternalNotes { get; set; }
    
    // Navigation properties
    public Booking Booking { get; set; } = null!;
    public ApplicationUser Client { get; set; } = null!;
    public CareProviderProfile Provider { get; set; } = null!;
    public ICollection<InvoiceLineItem> LineItems { get; set; } = new List<InvoiceLineItem>();
    public ICollection<InvoiceStatusHistory> StatusHistory { get; set; } = new List<InvoiceStatusHistory>();
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
    
    // Computed properties
    public bool IsPaid => Status == InvoiceStatus.Paid;
    public bool IsOverdue => Status == InvoiceStatus.Pending && DueDate.HasValue && DueDate < DateTime.UtcNow;
    public decimal PaidAmount => Payments.Where(p => p.Status == PaymentStatus.Completed).Sum(p => p.Amount);
    public decimal OutstandingAmount => TotalAmount - PaidAmount;
    public int DaysOverdue => IsOverdue ? (DateTime.UtcNow - DueDate!.Value).Days : 0;
    
    // Business methods
    public Result MoveToPending(Guid userId, DateTime? dueDate = null)
    {
        if (Status != InvoiceStatus.Draft)
            return Result.Failure(Error.BusinessRule($"Cannot move invoice from {Status} to Pending"));
        
        Status = InvoiceStatus.Pending;
        DueDate = dueDate ?? DateTime.UtcNow.AddDays(30); // Default 30 days
        
        AddStatusHistory(InvoiceStatus.Pending, userId, "Invoice moved to pending");
        return Result.Success();
    }
    
    public Result MarkAsPaid(Guid userId, string? paymentReference = null)
    {
        if (Status != InvoiceStatus.Pending)
            return Result.Failure(Error.BusinessRule($"Cannot mark invoice as paid from {Status} status"));
        
        Status = InvoiceStatus.Paid;
        PaidAt = DateTime.UtcNow;
        PaidBy = userId;
        PaymentReference = paymentReference;
        
        AddStatusHistory(InvoiceStatus.Paid, userId, $"Invoice paid. Reference: {paymentReference}");
        return Result.Success();
    }
    
    public Result VoidInvoice(Guid userId, string reason)
    {
        if (Status == InvoiceStatus.Paid)
            return Result.Failure(Error.BusinessRule("Cannot void a paid invoice"));
        
        Status = InvoiceStatus.Void;
        AddStatusHistory(InvoiceStatus.Void, userId, $"Invoice voided. Reason: {reason}");
        return Result.Success();
    }
    
    public Result AddLineItem(InvoiceLineItem lineItem)
    {
        if (Status != InvoiceStatus.Draft)
            return Result.Failure(Error.BusinessRule("Cannot modify invoice that is not in draft status"));
        
        LineItems.Add(lineItem);
        RecalculateAmounts();
        return Result.Success();
    }
    
    public void RecalculateAmounts()
    {
        SubtotalAmount = LineItems.Sum(li => li.TotalAmount);
        // Tax calculation logic would go here
        TotalAmount = SubtotalAmount + TaxAmount - DiscountAmount;
    }
    
    private void AddStatusHistory(InvoiceStatus status, Guid userId, string notes)
    {
        StatusHistory.Add(new InvoiceStatusHistory
        {
            InvoiceId = Id,
            Status = status,
            ChangedBy = userId,
            ChangedAt = DateTime.UtcNow,
            Notes = notes
        });
    }
}
```

#### New Supporting Entities

```csharp
public enum InvoiceStatus
{
    Draft,
    Pending,
    Paid,
    Void
}

public class InvoiceLineItem : BaseEntity
{
    public Guid InvoiceId { get; set; }
    public Guid? BookingWindowId { get; set; }
    public string Description { get; set; } = string.Empty;
    public DateTime ServiceDate { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public decimal Quantity { get; set; } // Hours or units
    public decimal UnitPrice { get; set; }
    public decimal TotalAmount { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public Invoice Invoice { get; set; } = null!;
    public BookingWindow? BookingWindow { get; set; }
    
    // Computed properties
    public TimeSpan Duration => EndTime - StartTime;
    public decimal HourlyRate => Quantity > 0 ? TotalAmount / Quantity : 0;
}

public class InvoiceStatusHistory : BaseEntity
{
    public Guid InvoiceId { get; set; }
    public InvoiceStatus Status { get; set; }
    public Guid ChangedBy { get; set; }
    public DateTime ChangedAt { get; set; }
    public string? Notes { get; set; }
    
    // Navigation properties
    public Invoice Invoice { get; set; } = null!;
    public ApplicationUser ChangedByUser { get; set; } = null!;
}

public class InvoiceTemplate : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
    public string TemplateJson { get; set; } = string.Empty; // JSON configuration
    public string? LogoUrl { get; set; }
    public string? HeaderHtml { get; set; }
    public string? FooterHtml { get; set; }
    public string? StylesCss { get; set; }
}

// Value objects for addresses
public record BillingAddress(
    string Name,
    string AddressLine1,
    string AddressLine2,
    string City,
    string State,
    string PostalCode,
    string Country,
    string? Email = null,
    string? Phone = null
);

public record ServiceAddress(
    string AddressLine1,
    string AddressLine2,
    string City,
    string State,
    string PostalCode,
    string Country
);
```

### Application Services

#### Invoice Generation Service

```csharp
public interface IInvoiceGenerationService
{
    Task<Result<Invoice>> GenerateInvoiceFromBookingAsync(Guid bookingId, CancellationToken ct = default);
    Task<Result<Invoice>> GenerateInvoiceFromBookingWindowsAsync(Guid bookingId, List<Guid> bookingWindowIds, CancellationToken ct = default);
    Task<Result<string>> GenerateInvoiceNumberAsync(CancellationToken ct = default);
    Task<Result<Invoice>> RegenerateInvoiceAsync(Guid invoiceId, CancellationToken ct = default);
}

internal class InvoiceGenerationService : IInvoiceGenerationService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<InvoiceGenerationService> _logger;
    private readonly IOptions<InvoiceSettings> _settings;

    public InvoiceGenerationService(
        ApplicationDbContext context,
        ILogger<InvoiceGenerationService> logger,
        IOptions<InvoiceSettings> settings)
    {
        _context = context;
        _logger = logger;
        _settings = settings;
    }

    public async Task<Result<Invoice>> GenerateInvoiceFromBookingAsync(Guid bookingId, CancellationToken ct = default)
    {
        try
        {
            // Load booking with all related data
            var booking = await _context.Bookings
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserProfile)
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserAddresses.Where(ua => ua.IsPrimary))
                    .ThenInclude(ua => ua.Address)
                .Include(b => b.Provider)
                    .ThenInclude(p => p.User)
                    .ThenInclude(u => u.UserProfile)
                .Include(b => b.Category)
                .Include(b => b.BookingWindows.Where(bw => bw.Status == BookingWindowStatus.Completed))
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, ct);

            if (booking == null)
                return Result.Failure<Invoice>(Error.NotFound("Booking not found"));

            var completedWindows = booking.BookingWindows.Where(bw => bw.Status == BookingWindowStatus.Completed).ToList();
            if (!completedWindows.Any())
                return Result.Failure<Invoice>(Error.BusinessRule("No completed booking windows found for invoice generation"));

            // Check if invoice already exists
            var existingInvoice = await _context.Invoices
                .FirstOrDefaultAsync(i => i.BookingId == bookingId && i.Status != InvoiceStatus.Void, ct);
            
            if (existingInvoice != null)
                return Result.Failure<Invoice>(Error.Conflict("Invoice already exists for this booking"));

            // Generate invoice
            var invoice = await CreateInvoiceFromBookingAsync(booking, completedWindows, ct);
            
            await _context.Invoices.AddAsync(invoice, ct);
            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("Invoice {InvoiceNumber} generated for booking {BookingId}", 
                invoice.InvoiceNumber, bookingId);

            return Result.Success(invoice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating invoice for booking {BookingId}", bookingId);
            return Result.Failure<Invoice>(Error.Internal("Error generating invoice"));
        }
    }

    public async Task<Result<Invoice>> GenerateInvoiceFromBookingWindowsAsync(Guid bookingId, List<Guid> bookingWindowIds, CancellationToken ct = default)
    {
        try
        {
            var booking = await _context.Bookings
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserProfile)
                .Include(b => b.Client)
                    .ThenInclude(c => c.UserAddresses.Where(ua => ua.IsPrimary))
                    .ThenInclude(ua => ua.Address)
                .Include(b => b.Provider)
                    .ThenInclude(p => p.User)
                    .ThenInclude(u => u.UserProfile)
                .Include(b => b.Category)
                .Include(b => b.BookingWindows.Where(bw => bookingWindowIds.Contains(bw.Id)))
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, ct);

            if (booking == null)
                return Result.Failure<Invoice>(Error.NotFound("Booking not found"));

            var selectedWindows = booking.BookingWindows.ToList();
            if (selectedWindows.Count != bookingWindowIds.Count)
                return Result.Failure<Invoice>(Error.NotFound("Some booking windows not found"));

            // Validate all windows are completed
            var incompletedWindows = selectedWindows.Where(bw => bw.Status != BookingWindowStatus.Completed).ToList();
            if (incompletedWindows.Any())
                return Result.Failure<Invoice>(Error.BusinessRule("All booking windows must be completed for invoice generation"));

            var invoice = await CreateInvoiceFromBookingAsync(booking, selectedWindows, ct);
            
            await _context.Invoices.AddAsync(invoice, ct);
            await _context.SaveChangesAsync(ct);

            return Result.Success(invoice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating invoice for booking windows {BookingWindowIds}", string.Join(",", bookingWindowIds));
            return Result.Failure<Invoice>(Error.Internal("Error generating invoice"));
        }
    }

    public async Task<Result<string>> GenerateInvoiceNumberAsync(CancellationToken ct = default)
    {
        try
        {
            var settings = _settings.Value;
            var year = DateTime.UtcNow.Year;
            var prefix = settings.InvoiceNumberPrefix ?? "INV";
            
            // Get the next sequence number for this year
            var lastInvoice = await _context.Invoices
                .Where(i => i.InvoiceNumber.StartsWith($"{prefix}-{year}-"))
                .OrderByDescending(i => i.InvoiceNumber)
                .FirstOrDefaultAsync(ct);

            int nextSequence = 1;
            if (lastInvoice != null)
            {
                var lastNumberPart = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumberPart, out var lastNumber))
                {
                    nextSequence = lastNumber + 1;
                }
            }

            var invoiceNumber = $"{prefix}-{year}-{nextSequence:D6}";
            return Result.Success(invoiceNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating invoice number");
            return Result.Failure<string>(Error.Internal("Error generating invoice number"));
        }
    }

    public async Task<Result<Invoice>> RegenerateInvoiceAsync(Guid invoiceId, CancellationToken ct = default)
    {
        try
        {
            var invoice = await _context.Invoices
                .Include(i => i.Booking)
                    .ThenInclude(b => b.BookingWindows)
                .Include(i => i.LineItems)
                .FirstOrDefaultAsync(i => i.Id == invoiceId, ct);

            if (invoice == null)
                return Result.Failure<Invoice>(Error.NotFound("Invoice not found"));

            if (invoice.Status != InvoiceStatus.Draft)
                return Result.Failure<Invoice>(Error.BusinessRule("Can only regenerate draft invoices"));

            // Clear existing line items
            _context.InvoiceLineItems.RemoveRange(invoice.LineItems);

            // Regenerate line items from booking windows
            var completedWindows = invoice.Booking.BookingWindows
                .Where(bw => bw.Status == BookingWindowStatus.Completed)
                .ToList();

            foreach (var window in completedWindows)
            {
                var lineItem = CreateLineItemFromBookingWindow(window, invoice.Id);
                invoice.LineItems.Add(lineItem);
            }

            invoice.RecalculateAmounts();
            await _context.SaveChangesAsync(ct);

            return Result.Success(invoice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating invoice {InvoiceId}", invoiceId);
            return Result.Failure<Invoice>(Error.Internal("Error regenerating invoice"));
        }
    }

    private async Task<Invoice> CreateInvoiceFromBookingAsync(Booking booking, List<BookingWindow> bookingWindows, CancellationToken ct)
    {
        var invoiceNumberResult = await GenerateInvoiceNumberAsync(ct);
        var invoiceNumber = invoiceNumberResult.IsSuccess ? invoiceNumberResult.Value : Guid.NewGuid().ToString();

        var primaryAddress = booking.Client.UserAddresses?.FirstOrDefault(ua => ua.IsPrimary);
        
        var billingAddress = new BillingAddress(
            $"{booking.Client.UserProfile?.FirstName} {booking.Client.UserProfile?.LastName}".Trim(),
            primaryAddress?.Address?.StreetAddress ?? "",
            "",
            primaryAddress?.Address?.City ?? "",
            primaryAddress?.Address?.State ?? "",
            primaryAddress?.Address?.PostalCode ?? "",
            booking.Client.UserProfile?.Country ?? "",
            booking.Client.Email,
            booking.Client.PhoneNumber
        );

        var invoice = new Invoice
        {
            BookingId = booking.Id,
            InvoiceNumber = invoiceNumber,
            InvoiceDate = DateTime.UtcNow,
            Status = InvoiceStatus.Draft,
            ClientId = booking.ClientId,
            ProviderId = booking.ProviderId,
            ClientName = $"{booking.Client.UserProfile?.FirstName} {booking.Client.UserProfile?.LastName}".Trim(),
            ClientEmail = booking.Client.Email ?? "",
            ProviderName = $"{booking.Provider.User.UserProfile?.FirstName} {booking.Provider.User.UserProfile?.LastName}".Trim(),
            BillingAddressJson = JsonSerializer.Serialize(billingAddress),
            Currency = _settings.Value.DefaultCurrency ?? "USD",
            CreatedAt = DateTime.UtcNow
        };

        // Create line items from booking windows
        foreach (var window in bookingWindows)
        {
            var lineItem = CreateLineItemFromBookingWindow(window, invoice.Id);
            invoice.LineItems.Add(lineItem);
        }

        invoice.RecalculateAmounts();

        // Add initial status history
        invoice.StatusHistory.Add(new InvoiceStatusHistory
        {
            InvoiceId = invoice.Id,
            Status = InvoiceStatus.Draft,
            ChangedBy = booking.CreatedBy ?? Guid.Empty,
            ChangedAt = DateTime.UtcNow,
            Notes = "Invoice created"
        });

        return invoice;
    }

    private static InvoiceLineItem CreateLineItemFromBookingWindow(BookingWindow window, Guid invoiceId)
    {
        var duration = window.Duration;
        var hours = (decimal)duration.TotalHours;
        var hourlyRate = window.DailyRate ?? 0; // This might need adjustment based on your rate structure

        return new InvoiceLineItem
        {
            InvoiceId = invoiceId,
            BookingWindowId = window.Id,
            Description = $"Care services on {window.Date:yyyy-MM-dd}",
            ServiceDate = window.Date.ToDateTime(window.StartTime),
            StartTime = window.StartTime,
            EndTime = window.EndTime,
            Quantity = hours,
            UnitPrice = hourlyRate,
            TotalAmount = hours * hourlyRate,
            Notes = window.DaySpecialInstructions
        };
    }
}
```

#### PDF Generation Service

```csharp
public interface IPdfGenerationService
{
    Task<Result<PdfGenerationResult>> GenerateInvoicePdfAsync(Guid invoiceId, CancellationToken ct = default);
    Task<Result<PdfGenerationResult>> RegeneratePdfAsync(Guid invoiceId, CancellationToken ct = default);
    Task<Result<byte[]>> GetPdfBytesAsync(Guid invoiceId, CancellationToken ct = default);
}

public record PdfGenerationResult(
    string FilePath,
    string FileUrl,
    long FileSize,
    DateTime GeneratedAt
);

internal class PdfGenerationService : IPdfGenerationService
{
    private readonly ApplicationDbContext _context;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<PdfGenerationService> _logger;
    private readonly IOptions<InvoiceSettings> _settings;

    public PdfGenerationService(
        ApplicationDbContext context,
        IFileStorageService fileStorageService,
        ILogger<PdfGenerationService> logger,
        IOptions<InvoiceSettings> settings)
    {
        _context = context;
        _fileStorageService = fileStorageService;
        _logger = logger;
        _settings = settings;
    }

    public async Task<Result<PdfGenerationResult>> GenerateInvoicePdfAsync(Guid invoiceId, CancellationToken ct = default)
    {
        try
        {
            var invoice = await LoadInvoiceWithDetailsAsync(invoiceId, ct);
            if (invoice == null)
                return Result.Failure<PdfGenerationResult>(Error.NotFound("Invoice not found"));

            // Generate HTML content
            var htmlContent = await GenerateInvoiceHtmlAsync(invoice, ct);
            
            // Generate PDF using PeachPDF
            var pdfBytes = await GeneratePdfFromHtmlAsync(htmlContent);
            
            // Save PDF to storage
            var fileName = $"invoice-{invoice.InvoiceNumber}.pdf";
            var containerName = $"invoices/{invoice.InvoiceDate.Year}/{invoice.InvoiceDate.Month:D2}";
            
            using var pdfStream = new MemoryStream(pdfBytes);
            var formFile = new FormFile(pdfStream, 0, pdfBytes.Length, "file", fileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/pdf"
            };

            var uploadResult = await _fileStorageService.UploadFileAsync(formFile, containerName, fileName);
            if (!uploadResult.IsSuccess)
                return Result.Failure<PdfGenerationResult>(uploadResult.Error);

            // Update invoice with PDF information
            invoice.PdfFilePath = uploadResult.Value.RelativePath;
            invoice.PdfFileUrl = uploadResult.Value.FileUrl;
            invoice.PdfGeneratedAt = DateTime.UtcNow;
            invoice.PdfVersion++;

            await _context.SaveChangesAsync(ct);

            var result = new PdfGenerationResult(
                uploadResult.Value.RelativePath,
                uploadResult.Value.FileUrl,
                uploadResult.Value.FileSize,
                DateTime.UtcNow
            );

            _logger.LogInformation("PDF generated for invoice {InvoiceNumber}: {FileUrl}", 
                invoice.InvoiceNumber, result.FileUrl);

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF for invoice {InvoiceId}", invoiceId);
            return Result.Failure<PdfGenerationResult>(Error.Internal("Error generating PDF"));
        }
    }

    public async Task<Result<PdfGenerationResult>> RegeneratePdfAsync(Guid invoiceId, CancellationToken ct = default)
    {
        try
        {
            var invoice = await LoadInvoiceWithDetailsAsync(invoiceId, ct);
            if (invoice == null)
                return Result.Failure<PdfGenerationResult>(Error.NotFound("Invoice not found"));

            // Delete old PDF if exists
            if (!string.IsNullOrEmpty(invoice.PdfFilePath))
            {
                await _fileStorageService.DeleteFileAsync(invoice.PdfFilePath);
            }

            // Generate new PDF
            return await GenerateInvoicePdfAsync(invoiceId, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating PDF for invoice {InvoiceId}", invoiceId);
            return Result.Failure<PdfGenerationResult>(Error.Internal("Error regenerating PDF"));
        }
    }

    public async Task<Result<byte[]>> GetPdfBytesAsync(Guid invoiceId, CancellationToken ct = default)
    {
        try
        {
            var invoice = await _context.Invoices
                .FirstOrDefaultAsync(i => i.Id == invoiceId, ct);

            if (invoice == null)
                return Result.Failure<byte[]>(Error.NotFound("Invoice not found"));

            if (string.IsNullOrEmpty(invoice.PdfFilePath))
                return Result.Failure<byte[]>(Error.NotFound("PDF not generated for this invoice"));

            // Get PDF from storage service
            if (_fileStorageService is DigitalOceanSpacesStorageService spacesService)
            {
                var streamResult = await spacesService.GetFileStreamAsync(invoice.PdfFilePath.TrimStart('/'));
                if (!streamResult.IsSuccess)
                    return Result.Failure<byte[]>(streamResult.Error);

                using var stream = streamResult.Value;
                using var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream, ct);
                return Result.Success(memoryStream.ToArray());
            }

            return Result.Failure<byte[]>(Error.Internal("Unable to retrieve PDF"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting PDF bytes for invoice {InvoiceId}", invoiceId);
            return Result.Failure<byte[]>(Error.Internal("Error retrieving PDF"));
        }
    }

    private async Task<Invoice?> LoadInvoiceWithDetailsAsync(Guid invoiceId, CancellationToken ct)
    {
        return await _context.Invoices
            .Include(i => i.Client)
                .ThenInclude(c => c.UserProfile)
            .Include(i => i.Provider)
                .ThenInclude(p => p.User)
                .ThenInclude(u => u.UserProfile)
            .Include(i => i.Booking)
                .ThenInclude(b => b.Category)
            .Include(i => i.LineItems)
                .ThenInclude(li => li.BookingWindow)
            .FirstOrDefaultAsync(i => i.Id == invoiceId, ct);
    }

    private async Task<string> GenerateInvoiceHtmlAsync(Invoice invoice, CancellationToken ct)
    {
        // Load template
        var template = await GetInvoiceTemplateAsync(ct);
        
        // Deserialize addresses
        var billingAddress = JsonSerializer.Deserialize<BillingAddress>(invoice.BillingAddressJson);
        
        // Generate HTML using template engine (could use Razor, Handlebars, etc.)
        var html = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Invoice {invoice.InvoiceNumber}</title>
    <style>
        {template.StylesCss ?? DefaultInvoiceStyles}
    </style>
</head>
<body>
    <div class='invoice-container'>
        <header class='invoice-header'>
            {template.HeaderHtml ?? GenerateDefaultHeader(template.LogoUrl)}
            <div class='invoice-info'>
                <h1>INVOICE</h1>
                <p><strong>Invoice #:</strong> {invoice.InvoiceNumber}</p>
                <p><strong>Date:</strong> {invoice.InvoiceDate:yyyy-MM-dd}</p>
                {(invoice.DueDate.HasValue ? $"<p><strong>Due Date:</strong> {invoice.DueDate:yyyy-MM-dd}</p>" : "")}
            </div>
        </header>
        
        <div class='invoice-addresses'>
            <div class='bill-to'>
                <h3>Bill To:</h3>
                <p>{billingAddress?.Name}</p>
                <p>{billingAddress?.AddressLine1}</p>
                {(!string.IsNullOrEmpty(billingAddress?.AddressLine2) ? $"<p>{billingAddress.AddressLine2}</p>" : "")}
                <p>{billingAddress?.City}, {billingAddress?.State} {billingAddress?.PostalCode}</p>
                <p>{billingAddress?.Country}</p>
                {(!string.IsNullOrEmpty(billingAddress?.Email) ? $"<p>Email: {billingAddress.Email}</p>" : "")}
            </div>
            
            <div class='service-provider'>
                <h3>Service Provider:</h3>
                <p>{invoice.ProviderName}</p>
            </div>
        </div>
        
        <table class='invoice-table'>
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Hours</th>
                    <th>Rate</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                {string.Join("", invoice.LineItems.Select(li => $@"
                <tr>
                    <td>{li.Description}</td>
                    <td>{li.ServiceDate:yyyy-MM-dd}</td>
                    <td>{li.StartTime:hh\\:mm} - {li.EndTime:hh\\:mm}</td>
                    <td>{li.Quantity:F2}</td>
                    <td>${li.UnitPrice:F2}</td>
                    <td>${li.TotalAmount:F2}</td>
                </tr>"))}
            </tbody>
            <tfoot>
                <tr class='subtotal'>
                    <td colspan='5'>Subtotal:</td>
                    <td>${invoice.SubtotalAmount:F2}</td>
                </tr>
                {(invoice.TaxAmount > 0 ? $@"
                <tr class='tax'>
                    <td colspan='5'>Tax:</td>
                    <td>${invoice.TaxAmount:F2}</td>
                </tr>" : "")}
                {(invoice.DiscountAmount > 0 ? $@"
                <tr class='discount'>
                    <td colspan='5'>Discount:</td>
                    <td>-${invoice.DiscountAmount:F2}</td>
                </tr>" : "")}
                <tr class='total'>
                    <td colspan='5'><strong>Total:</strong></td>
                    <td><strong>${invoice.TotalAmount:F2}</strong></td>
                </tr>
            </tfoot>
        </table>
        
        {(!string.IsNullOrEmpty(invoice.Notes) ? $@"
        <div class='invoice-notes'>
            <h3>Notes:</h3>
            <p>{invoice.Notes}</p>
        </div>" : "")}
        
        <footer class='invoice-footer'>
            {template.FooterHtml ?? GenerateDefaultFooter()}
        </footer>
    </div>
</body>
</html>";

        return html;
    }

    private async Task<byte[]> GeneratePdfFromHtmlAsync(string htmlContent)
    {
        // Using PeachPDF library
        var pdfDocument = new PdfDocument();
        var pdfPage = pdfDocument.AddPage();
        
        // Configure PDF generation options
        var options = new PdfGenerationOptions
        {
            PageSize = PageSize.A4,
            Orientation = PageOrientation.Portrait,
            MarginTop = 20,
            MarginBottom = 20,
            MarginLeft = 20,
            MarginRight = 20
        };

        // Generate PDF from HTML
        var pdfBytes = await PeachPdf.GeneratePdfFromHtmlAsync(htmlContent, options);
        
        return pdfBytes;
    }

    private async Task<InvoiceTemplate> GetInvoiceTemplateAsync(CancellationToken ct)
    {
        var template = await _context.InvoiceTemplates
            .FirstOrDefaultAsync(t => t.IsDefault, ct);

        return template ?? new InvoiceTemplate
        {
            Name = "Default",
            IsDefault = true,
            StylesCss = DefaultInvoiceStyles
        };
    }

    private static string GenerateDefaultHeader(string? logoUrl)
    {
        return $@"
        <div class='company-info'>
            {(!string.IsNullOrEmpty(logoUrl) ? $"<img src='{logoUrl}' alt='Company Logo' class='logo'>" : "")}
            <h2>SuperCare Services</h2>
            <p>Professional Care Services</p>
        </div>";
    }

    private static string GenerateDefaultFooter()
    {
        return @"
        <div class='footer-content'>
            <p>Thank you for choosing SuperCare Services!</p>
            <p>For questions about this invoice, please contact our billing department.</p>
        </div>";
    }

    private const string DefaultInvoiceStyles = @"
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .invoice-container { max-width: 800px; margin: 0 auto; }
        .invoice-header { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .company-info h2 { margin: 0; color: #333; }
        .invoice-info { text-align: right; }
        .invoice-addresses { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .bill-to, .service-provider { width: 45%; }
        .invoice-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
        .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .invoice-table th { background-color: #f2f2f2; font-weight: bold; }
        .invoice-table tfoot td { font-weight: bold; }
        .total { background-color: #f9f9f9; }
        .invoice-notes { margin-bottom: 30px; }
        .invoice-footer { border-top: 1px solid #ddd; padding-top: 20px; text-align: center; color: #666; }
        .logo { max-height: 60px; }
    ";
}
```

### Command and Query Handlers

#### Generate Invoice Command

```csharp
public record GenerateInvoiceCommand(
    Guid BookingId,
    List<Guid>? BookingWindowIds = null
) : ICommand<Result<GenerateInvoiceResponse>>;

public record GenerateInvoiceResponse(
    Guid InvoiceId,
    string InvoiceNumber,
    InvoiceStatus Status,
    decimal TotalAmount,
    DateTime InvoiceDate,
    int LineItemCount
);

internal sealed class GenerateInvoiceCommandHandler : ICommandHandler<GenerateInvoiceCommand, Result<GenerateInvoiceResponse>>
{
    private readonly IInvoiceGenerationService _invoiceGenerationService;
    private readonly IPdfGenerationService _pdfGenerationService;
    private readonly ILogger<GenerateInvoiceCommandHandler> _logger;

    public GenerateInvoiceCommandHandler(
        IInvoiceGenerationService invoiceGenerationService,
        IPdfGenerationService pdfGenerationService,
        ILogger<GenerateInvoiceCommandHandler> logger)
    {
        _invoiceGenerationService = invoiceGenerationService;
        _pdfGenerationService = pdfGenerationService;
        _logger = logger;
    }

    public async Task<Result<GenerateInvoiceResponse>> Handle(GenerateInvoiceCommand command, CancellationToken ct)
    {
        try
        {
            // Generate invoice
            var invoiceResult = command.BookingWindowIds?.Any() == true
                ? await _invoiceGenerationService.GenerateInvoiceFromBookingWindowsAsync(command.BookingId, command.BookingWindowIds, ct)
                : await _invoiceGenerationService.GenerateInvoiceFromBookingAsync(command.BookingId, ct);

            if (!invoiceResult.IsSuccess)
                return Result.Failure<GenerateInvoiceResponse>(invoiceResult.Error);

            var invoice = invoiceResult.Value;

            // Generate PDF (optional, can be done later)
            try
            {
                await _pdfGenerationService.GenerateInvoicePdfAsync(invoice.Id, ct);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "PDF generation failed for invoice {InvoiceId}, but invoice was created successfully", invoice.Id);
            }

            var response = new GenerateInvoiceResponse(
                invoice.Id,
                invoice.InvoiceNumber,
                invoice.Status,
                invoice.TotalAmount,
                invoice.InvoiceDate,
                invoice.LineItems.Count
            );

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating invoice for booking {BookingId}", command.BookingId);
            return Result.Failure<GenerateInvoiceResponse>(Error.Internal("Error generating invoice"));
        }
    }
}
```

### Configuration

```csharp
public class InvoiceSettings
{
    public string InvoiceNumberPrefix { get; set; } = "INV";
    public string DefaultCurrency { get; set; } = "USD";
    public int DefaultDueDays { get; set; } = 30;
    public decimal DefaultTaxRate { get; set; } = 0.0m;
    public bool AutoGeneratePdf { get; set; } = true;
    public bool SendNotificationOnGeneration { get; set; } = true;
    public string CompanyName { get; set; } = "SuperCare Services";
    public string CompanyAddress { get; set; } = "";
    public string CompanyPhone { get; set; } = "";
    public string CompanyEmail { get; set; } = "";
    public string LogoUrl { get; set; } = "";
}
```

## Error Handling

### Centralized Error Types

```csharp
public static class InvoiceErrors
{
    public static Error InvoiceNotFound(Guid invoiceId) => 
        Error.NotFound($"Invoice {invoiceId} not found");
    
    public static Error InvalidStatusTransition(InvoiceStatus from, InvoiceStatus to) => 
        Error.BusinessRule($"Cannot transition invoice status from {from} to {to}");
    
    public static Error InvoiceAlreadyExists(Guid bookingId) => 
        Error.Conflict($"Invoice already exists for booking {bookingId}");
    
    public static Error NoCompletedBookingWindows(Guid bookingId) => 
        Error.BusinessRule($"No completed booking windows found for booking {bookingId}");
    
    public static Error PdfGenerationFailed(string reason) => 
        Error.Internal($"PDF generation failed: {reason}");
    
    public static Error InvoiceNumberGenerationFailed() => 
        Error.Internal("Failed to generate unique invoice number");
    
    public static Error CannotModifyNonDraftInvoice(InvoiceStatus status) => 
        Error.BusinessRule($"Cannot modify invoice with status {status}");
}
```

This design provides a comprehensive invoice generation system that integrates seamlessly with the existing booking system while providing professional PDF generation, proper status management, and robust financial tracking capabilities.