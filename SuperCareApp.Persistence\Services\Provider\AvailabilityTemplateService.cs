using Microsoft.EntityFrameworkCore;
using SuperCareApp.Application.Common.Interfaces.Provider;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Constants;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Services.Provider
{
    /// <summary>
    /// Service for managing availability templates for care providers
    /// </summary>
    public class AvailabilityTemplateService : IAvailabilityTemplateService
    {
        private readonly ApplicationDbContext _context;

        public AvailabilityTemplateService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Creates a default availability template for a new care provider
        /// </summary>
        public async Task<Result> CreateDefaultAvailabilityTemplateAsync(Guid providerId)
        {
            try
            {
                var existingAvailabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .ToListAsync();

                if (existingAvailabilities.Any())
                {
                    return Result.Failure(
                        Error.Conflict("Availability template already exists for this provider")
                    );
                }

                // Create availability records for all days of the week
                var availabilities = new List<Availability>();

                foreach (var day in AvailabilityConstants.AllDays)
                {
                    var availability = new Availability
                    {
                        Id = Guid.NewGuid(),
                        ProviderId = providerId,
                        DayOfWeek = day,
                        IsAvailable = AvailabilityConstants.DefaultTemplate.IsAvailable,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    };

                    availabilities.Add(availability);
                }

                // Add all availability records
                await _context.Availabilities.AddRangeAsync(availabilities);
                await _context.SaveChangesAsync();

                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(
                    Error.Internal($"Failed to create default availability template: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Gets the availability template for a care provider
        /// </summary>
        public async Task<Result<AvailabilityTemplateDto>> GetAvailabilityTemplateAsync(
            Guid providerId
        )
        {
            try
            {
                var availabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .Include(a => a.AvailabilitySlots)
                    .OrderBy(a => Array.IndexOf(AvailabilityConstants.AllDays, a.DayOfWeek))
                    .ToListAsync();

                if (!availabilities.Any())
                {
                    return Result.Failure<AvailabilityTemplateDto>(
                        Error.NotFound("No availability template found for this provider")
                    );
                }

                var template = new AvailabilityTemplateDto
                {
                    ProviderId = providerId,
                    DayAvailabilities = availabilities
                        .Select(a => new DayAvailabilityDto
                        {
                            Id = a.Id,
                            DayOfWeek = a.DayOfWeek,
                            IsAvailable = a.IsAvailable,
                            SlotCount = a.AvailabilitySlots?.Count ?? 0,
                            CreatedAt = a.CreatedAt,
                            UpdatedAt = a.UpdatedAt.Value,
                        })
                        .ToList(),
                };

                return Result.Success(template);
            }
            catch (Exception ex)
            {
                return Result.Failure<AvailabilityTemplateDto>(
                    Error.Internal($"Failed to get availability template: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Updates the availability template for a care provider
        /// </summary>
        public async Task<Result> UpdateAvailabilityTemplateAsync(
            Guid providerId,
            AvailabilityTemplateDto template
        )
        {
            try
            {
                var existingAvailabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .ToListAsync();

                if (!existingAvailabilities.Any())
                {
                    return Result.Failure(
                        Error.NotFound("No availability template found for this provider")
                    );
                }

                // Update each day's availability
                foreach (var dayUpdate in template.DayAvailabilities)
                {
                    var existingAvailability = existingAvailabilities.FirstOrDefault(a =>
                        a.DayOfWeek == dayUpdate.DayOfWeek
                    );

                    if (existingAvailability != null)
                    {
                        existingAvailability.IsAvailable = dayUpdate.IsAvailable;
                        existingAvailability.UpdatedAt = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(
                    Error.Internal($"Failed to update availability template: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Resets the availability template to default (all days unavailable)
        /// </summary>
        public async Task<Result> ResetToDefaultTemplateAsync(Guid providerId)
        {
            try
            {
                var existingAvailabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .ToListAsync();

                if (!existingAvailabilities.Any())
                {
                    // Create default template if none exists
                    return await CreateDefaultAvailabilityTemplateAsync(providerId);
                }

                // Reset all days to unavailable
                foreach (var availability in existingAvailabilities)
                {
                    availability.IsAvailable = AvailabilityConstants.DefaultTemplate.IsAvailable;
                    availability.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(
                    Error.Internal($"Failed to reset availability template: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Bulk updates multiple days availability status
        /// </summary>
        public async Task<Result> BulkUpdateDaysAvailabilityAsync(
            Guid providerId,
            Dictionary<string, bool> dayUpdates
        )
        {
            try
            {
                // Validate all day names
                var invalidDays = dayUpdates
                    .Keys.Where(day => !AvailabilityConstants.IsValidDay(day))
                    .ToList();
                if (invalidDays.Any())
                {
                    return Result.Failure(
                        Error.BadRequest($"Invalid day names: {string.Join(", ", invalidDays)}")
                    );
                }

                var existingAvailabilities = await _context
                    .Availabilities.Where(a => a.ProviderId == providerId && !a.IsDeleted)
                    .ToListAsync();

                if (!existingAvailabilities.Any())
                {
                    return Result.Failure(
                        Error.NotFound("No availability template found for this provider")
                    );
                }

                var updatedCount = 0;

                // Update each specified day
                foreach (var dayUpdate in dayUpdates)
                {
                    var normalizedDay = AvailabilityConstants.GetNormalizedDay(dayUpdate.Key);
                    if (normalizedDay == null)
                        continue;

                    var existingAvailability = existingAvailabilities.FirstOrDefault(a =>
                        a.DayOfWeek == normalizedDay
                    );

                    if (existingAvailability != null)
                    {
                        existingAvailability.IsAvailable = dayUpdate.Value;
                        existingAvailability.UpdatedAt = DateTime.UtcNow;
                        updatedCount++;
                    }
                }

                if (updatedCount > 0)
                {
                    await _context.SaveChangesAsync();
                }

                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Failure(
                    Error.Internal($"Failed to bulk update days availability: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Sets weekdays (Monday-Friday) availability status
        /// </summary>
        public async Task<Result> SetWeekdaysAvailabilityAsync(Guid providerId, bool isAvailable)
        {
            var weekdayUpdates = AvailabilityConstants.Weekdays.ToDictionary(
                day => day,
                _ => isAvailable
            );

            return await BulkUpdateDaysAvailabilityAsync(providerId, weekdayUpdates);
        }

        /// <summary>
        /// Sets weekend (Saturday-Sunday) availability status
        /// </summary>
        public async Task<Result> SetWeekendAvailabilityAsync(Guid providerId, bool isAvailable)
        {
            var weekendUpdates = AvailabilityConstants.WeekendDays.ToDictionary(
                day => day,
                _ => isAvailable
            );

            return await BulkUpdateDaysAvailabilityAsync(providerId, weekendUpdates);
        }

        /// <summary>
        /// Validates if a care provider has a complete availability template
        /// </summary>
        public async Task<Result<bool>> ValidateAvailabilityTemplateAsync(Guid providerId)
        {
            try
            {
                var availabilityCount = await _context.Availabilities.CountAsync(a =>
                    a.ProviderId == providerId && !a.IsDeleted
                );

                var isComplete = availabilityCount == AvailabilityConstants.AllDays.Length;
                return Result.Success(isComplete);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool>(
                    Error.Internal($"Failed to validate availability template: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Checks if a care provider has any availability set up
        /// </summary>
        public async Task<Result<bool>> HasAnyAvailabilityAsync(Guid providerId)
        {
            try
            {
                var hasAvailability = await _context.Availabilities.AnyAsync(a =>
                    a.ProviderId == providerId && !a.IsDeleted && a.IsAvailable
                );

                return Result.Success(hasAvailability);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool>(
                    Error.Internal($"Failed to check provider availability: {ex.Message}")
                );
            }
        }
    }
}
