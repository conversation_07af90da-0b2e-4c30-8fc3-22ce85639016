# 📁 DigitalOcean Spaces Document Storage Implementation Plan

## Overview

This document outlines the implementation plan for integrating DigitalOcean Spaces as the primary document storage solution for the SuperCare application. This will handle profile pictures, care provider documents, certifications, and other file uploads.

## 🎯 Objectives

- **Scalable File Storage**: Handle large volumes of documents and images
- **CDN Integration**: Fast global content delivery via DigitalOcean CDN
- **Security**: Secure file uploads with proper access controls
- **Cost Optimization**: Efficient storage with lifecycle management
- **Document Management**: Comprehensive document tracking and versioning
- **Image Processing**: Automatic image optimization and resizing

## 🏗️ Architecture Overview

```
Client App → API Gateway → Document Service → DigitalOcean Spaces
                ↓                              ↓
         Database (Metadata)              CDN (Content Delivery)
                ↓
         Background Jobs (Processing)
```

## 📋 Implementation Phases

### Phase 1: Core Storage Infrastructure (Week 1-2)

#### 1.1 DigitalOcean Spaces Configuration

```csharp
// appsettings.json
{
  "DigitalOceanSpaces": {
    "AccessKey": "DO_ACCESS_KEY",
    "SecretKey": "DO_SECRET_KEY",
    "BucketName": "supercare-documents",
    "Region": "nyc3",
    "Endpoint": "https://nyc3.digitaloceanspaces.com",
    "CdnEndpoint": "https://supercare-documents.nyc3.cdn.digitaloceanspaces.com",
    "MaxFileSize": 10485760, // 10MB
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"],
    "ImageQuality": 85,
    "ThumbnailSizes": [150, 300, 600]
  }
}
```

#### 1.2 Domain Models

```csharp
public class Document : BaseEntity
{
    public Guid UserId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string StoragePath { get; set; } = string.Empty;
    public string CdnUrl { get; set; } = string.Empty;
    public DocumentType DocumentType { get; set; }
    public DocumentCategory Category { get; set; }
    public VerificationStatus VerificationStatus { get; set; }
    public bool IsPublic { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string? ThumbnailPath { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Navigation properties
    public ApplicationUser User { get; set; } = null!;
    public List<DocumentVersion> Versions { get; set; } = new();
}

public class DocumentVersion : BaseEntity
{
    public Guid DocumentId { get; set; }
    public string VersionNumber { get; set; } = string.Empty;
    public string StoragePath { get; set; } = string.Empty;
    public string CdnUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string UploadedBy { get; set; } = string.Empty;
    public string? ChangeDescription { get; set; }

    // Navigation properties
    public Document Document { get; set; } = null!;
}

public enum DocumentType
{
    ProfilePicture,
    IdentificationDocument,
    Certification,
    License,
    Insurance,
    BackgroundCheck,
    Reference,
    Other
}

public enum DocumentCategory
{
    UserProfile,
    CareProviderVerification,
    Legal,
    Medical,
    Administrative
}
```

#### 1.3 Service Interfaces

```csharp
public interface IDocumentStorageService
{
    Task<Result<DocumentUploadResponse>> UploadDocumentAsync(DocumentUploadRequest request);
    Task<Result<DocumentResponse>> GetDocumentAsync(Guid documentId);
    Task<Result<string>> GetPresignedUrlAsync(Guid documentId, TimeSpan expiry);
    Task<Result> DeleteDocumentAsync(Guid documentId);
    Task<Result<DocumentResponse>> UpdateDocumentAsync(Guid documentId, UpdateDocumentRequest request);
    Task<Result<List<DocumentResponse>>> GetUserDocumentsAsync(Guid userId, DocumentFilter filter);
}

public interface IImageProcessingService
{
    Task<Result<ProcessedImageResponse>> ProcessImageAsync(Stream imageStream, ImageProcessingOptions options);
    Task<Result<List<ThumbnailResponse>>> GenerateThumbnailsAsync(Stream imageStream, List<int> sizes);
    Task<Result<OptimizedImageResponse>> OptimizeImageAsync(Stream imageStream, int quality = 85);
}

public interface IDocumentValidationService
{
    Task<Result<ValidationResponse>> ValidateDocumentAsync(Stream documentStream, DocumentType documentType);
    Task<Result<bool>> IsValidFileTypeAsync(string fileName, string contentType);
    Task<Result<bool>> IsFileSizeValidAsync(long fileSize);
}
```

### Phase 2: Advanced Document Management (Week 3-4)

#### 2.1 Document Versioning

- **Version Control**: Track document versions and changes
- **Rollback Capability**: Ability to revert to previous versions
- **Change History**: Detailed audit trail of document modifications

#### 2.2 Document Processing Pipeline

```csharp
public class DocumentProcessingPipeline
{
    private readonly IImageProcessingService _imageProcessing;
    private readonly IDocumentValidationService _validation;
    private readonly IVirusScanService _virusScanning;
    private readonly IBackgroundJobService _backgroundJobs;

    public async Task<Result> ProcessDocumentAsync(Guid documentId)
    {
        // 1. Virus scanning
        // 2. Document validation
        // 3. Image optimization (if applicable)
        // 4. Thumbnail generation
        // 5. Metadata extraction
        // 6. CDN distribution
    }
}
```

### Phase 3: Security & Compliance (Week 5-6)

#### 3.1 Access Control

- **Private Documents**: Secure access with signed URLs
- **Role-based Access**: Different access levels for different user types
- **Temporary Access**: Time-limited document access

#### 3.2 Compliance Features

- **HIPAA Compliance**: Secure handling of medical documents
- **Data Retention**: Automatic document lifecycle management
- **Audit Logging**: Comprehensive access and modification logs

## 🔧 Technical Implementation

### Database Schema

```sql
-- Documents Table
CREATE TABLE Documents (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    UserId UNIQUEIDENTIFIER NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    ContentType NVARCHAR(100) NOT NULL,
    FileSize BIGINT NOT NULL,
    StoragePath NVARCHAR(500) NOT NULL,
    CdnUrl NVARCHAR(500) NOT NULL,
    DocumentType INT NOT NULL,
    Category INT NOT NULL,
    VerificationStatus INT NOT NULL DEFAULT 0,
    IsPublic BIT NOT NULL DEFAULT 0,
    ExpiryDate DATETIME2 NULL,
    ThumbnailPath NVARCHAR(500) NULL,
    Metadata NVARCHAR(MAX) NULL, -- JSON
    CreatedAt DATETIME2 NOT NULL,
    UpdatedAt DATETIME2 NOT NULL,
    IsDeleted BIT NOT NULL DEFAULT 0,

    FOREIGN KEY (UserId) REFERENCES Users(Id),
    INDEX IX_Documents_UserId (UserId),
    INDEX IX_Documents_DocumentType (DocumentType),
    INDEX IX_Documents_VerificationStatus (VerificationStatus)
);

-- Document Versions Table
CREATE TABLE DocumentVersions (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    DocumentId UNIQUEIDENTIFIER NOT NULL,
    VersionNumber NVARCHAR(20) NOT NULL,
    StoragePath NVARCHAR(500) NOT NULL,
    CdnUrl NVARCHAR(500) NOT NULL,
    FileSize BIGINT NOT NULL,
    UploadedBy NVARCHAR(255) NOT NULL,
    ChangeDescription NVARCHAR(500) NULL,
    CreatedAt DATETIME2 NOT NULL,

    FOREIGN KEY (DocumentId) REFERENCES Documents(Id) ON DELETE CASCADE,
    INDEX IX_DocumentVersions_DocumentId (DocumentId)
);

-- Document Access Logs
CREATE TABLE DocumentAccessLogs (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    DocumentId UNIQUEIDENTIFIER NOT NULL,
    UserId UNIQUEIDENTIFIER NOT NULL,
    AccessType NVARCHAR(50) NOT NULL, -- View, Download, Upload, Delete
    IpAddress NVARCHAR(45) NULL,
    UserAgent NVARCHAR(500) NULL,
    AccessedAt DATETIME2 NOT NULL,

    FOREIGN KEY (DocumentId) REFERENCES Documents(Id),
    FOREIGN KEY (UserId) REFERENCES Users(Id),
    INDEX IX_DocumentAccessLogs_DocumentId (DocumentId),
    INDEX IX_DocumentAccessLogs_UserId (UserId),
    INDEX IX_DocumentAccessLogs_AccessedAt (AccessedAt)
);
```

### Service Implementation

```csharp
public class DigitalOceanSpacesService : IDocumentStorageService
{
    private readonly AmazonS3Client _s3Client; // DO Spaces is S3-compatible
    private readonly IDocumentRepository _documentRepository;
    private readonly IImageProcessingService _imageProcessing;
    private readonly DigitalOceanSpacesConfiguration _config;
    private readonly ILogger<DigitalOceanSpacesService> _logger;

    public async Task<Result<DocumentUploadResponse>> UploadDocumentAsync(DocumentUploadRequest request)
    {
        try
        {
            // 1. Validate file
            var validation = await ValidateFileAsync(request);
            if (validation.IsFailure) return Result.Failure<DocumentUploadResponse>(validation.Error);

            // 2. Generate unique file path
            var filePath = GenerateFilePath(request.UserId, request.FileName, request.DocumentType);

            // 3. Upload to Spaces
            var uploadRequest = new PutObjectRequest
            {
                BucketName = _config.BucketName,
                Key = filePath,
                InputStream = request.FileStream,
                ContentType = request.ContentType,
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                CannedACL = request.IsPublic ? S3CannedACL.PublicRead : S3CannedACL.Private
            };

            var uploadResponse = await _s3Client.PutObjectAsync(uploadRequest);

            // 4. Generate CDN URL
            var cdnUrl = GenerateCdnUrl(filePath);

            // 5. Save metadata to database
            var document = new Document
            {
                UserId = request.UserId,
                FileName = Path.GetFileName(filePath),
                OriginalFileName = request.FileName,
                ContentType = request.ContentType,
                FileSize = request.FileStream.Length,
                StoragePath = filePath,
                CdnUrl = cdnUrl,
                DocumentType = request.DocumentType,
                Category = request.Category,
                IsPublic = request.IsPublic,
                ExpiryDate = request.ExpiryDate
            };

            await _documentRepository.AddAsync(document);

            // 6. Process document asynchronously (thumbnails, optimization, etc.)
            await _backgroundJobService.EnqueueAsync<DocumentProcessingJob>(
                job => job.ProcessDocumentAsync(document.Id));

            return Result.Success(new DocumentUploadResponse
            {
                DocumentId = document.Id,
                CdnUrl = cdnUrl,
                FileName = document.FileName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for user {UserId}", request.UserId);
            return Result.Failure<DocumentUploadResponse>(Error.Internal("Failed to upload document"));
        }
    }

    private string GenerateFilePath(Guid userId, string fileName, DocumentType documentType)
    {
        var extension = Path.GetExtension(fileName);
        var uniqueFileName = $"{Guid.NewGuid()}{extension}";
        var datePath = DateTime.UtcNow.ToString("yyyy/MM/dd");

        return $"{documentType.ToString().ToLower()}/{userId}/{datePath}/{uniqueFileName}";
    }

    private string GenerateCdnUrl(string filePath)
    {
        return $"{_config.CdnEndpoint}/{filePath}";
    }
}
```

## 🔒 Security Implementation

### 1. Secure File Upload

```csharp
public class SecureFileUploadMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        if (context.Request.Path.StartsWithSegments("/api/documents/upload"))
        {
            // 1. Validate file size
            if (context.Request.ContentLength > _maxFileSize)
            {
                context.Response.StatusCode = 413; // Payload Too Large
                return;
            }

            // 2. Validate content type
            var contentType = context.Request.ContentType;
            if (!IsAllowedContentType(contentType))
            {
                context.Response.StatusCode = 415; // Unsupported Media Type
                return;
            }

            // 3. Scan for malicious content
            // Implementation depends on chosen antivirus solution
        }

        await next(context);
    }
}
```

### 2. Presigned URLs for Secure Access

```csharp
public async Task<Result<string>> GetPresignedUrlAsync(Guid documentId, TimeSpan expiry)
{
    var document = await _documentRepository.GetByIdAsync(documentId);
    if (document == null)
        return Result.Failure<string>(Error.NotFound("Document not found"));

    // Check user permissions
    if (!await _authorizationService.CanAccessDocumentAsync(document, _currentUser.UserId))
        return Result.Failure<string>(Error.Forbidden("Access denied"));

    var request = new GetPreSignedUrlRequest
    {
        BucketName = _config.BucketName,
        Key = document.StoragePath,
        Expires = DateTime.UtcNow.Add(expiry),
        Verb = HttpVerb.GET
    };

    var presignedUrl = await _s3Client.GetPreSignedURLAsync(request);

    // Log access
    await _auditService.LogDocumentAccessAsync(documentId, _currentUser.UserId, "PresignedUrlGenerated");

    return Result.Success(presignedUrl);
}
```

## 📱 Frontend Integration

### File Upload Component

```typescript
// React/TypeScript example
interface FileUploadProps {
  documentType: DocumentType;
  onUploadComplete: (response: DocumentUploadResponse) => void;
  onUploadError: (error: string) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  documentType,
  onUploadComplete,
  onUploadError,
}) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleFileUpload = async (file: File) => {
    setUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentType", documentType.toString());

      const response = await fetch("/api/documents/upload", {
        method: "POST",
        body: formData,
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        onUploadComplete(result.payload);
      } else {
        const error = await response.json();
        onUploadError(error.message);
      }
    } catch (error) {
      onUploadError("Upload failed");
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="file-upload">
      <input
        type="file"
        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
        onChange={(e) =>
          e.target.files?.[0] && handleFileUpload(e.target.files[0])
        }
        disabled={uploading}
      />
      {uploading && (
        <div className="upload-progress">
          <div className="progress-bar" style={{ width: `${progress}%` }} />
          <span>Uploading... {progress}%</span>
        </div>
      )}
    </div>
  );
};
```

## 🧪 Testing Strategy

### 1. Unit Tests

```csharp
[Test]
public async Task UploadDocument_ValidFile_ShouldReturnSuccess()
{
    // Arrange
    var request = new DocumentUploadRequest
    {
        UserId = Guid.NewGuid(),
        FileName = "test.pdf",
        ContentType = "application/pdf",
        FileStream = new MemoryStream(Encoding.UTF8.GetBytes("test content")),
        DocumentType = DocumentType.Certification
    };

    // Act
    var result = await _documentStorageService.UploadDocumentAsync(request);

    // Assert
    Assert.That(result.IsSuccess, Is.True);
    Assert.That(result.Value.DocumentId, Is.Not.EqualTo(Guid.Empty));
}
```

### 2. Integration Tests

- DigitalOcean Spaces connectivity
- File upload/download operations
- CDN URL generation and access

### 3. Performance Tests

- Large file upload handling
- Concurrent upload scenarios
- CDN response times

## 📊 Monitoring & Analytics

### Key Metrics

- **Storage Usage**: Track total storage consumption
- **Upload Success Rate**: Monitor failed uploads
- **CDN Performance**: Track content delivery speeds
- **Cost Analysis**: Monitor storage and bandwidth costs

### Alerts

- High error rates on uploads
- Storage quota approaching limits
- Unusual access patterns
- CDN performance degradation

## 🚀 Deployment Considerations

### Environment Setup

```bash
# DigitalOcean Spaces setup
doctl spaces create supercare-documents --region nyc3
doctl spaces cors set supercare-documents --cors-rules cors-rules.json
```

### CDN Configuration

- Enable CDN for faster global delivery
- Configure cache headers for optimal performance
- Set up custom domain for branded URLs

## 📈 Future Enhancements

1. **Advanced Image Processing**: AI-powered image analysis and tagging
2. **Document OCR**: Extract text from uploaded documents
3. **Video Support**: Handle video file uploads and streaming
4. **Collaborative Editing**: Real-time document collaboration
5. **Advanced Search**: Full-text search across documents
6. **Automated Backup**: Cross-region backup and disaster recovery

## 🔗 Dependencies

- **AWS SDK for .NET**: S3-compatible API client
- **ImageSharp**: Image processing library
- **Background Jobs**: For asynchronous processing
- **Antivirus Integration**: For malware scanning
- **Logging**: Comprehensive operation logging

## 💰 Cost Optimization

### Storage Lifecycle Management

```csharp
public class DocumentLifecycleService
{
    public async Task ArchiveOldDocumentsAsync()
    {
        // Move documents older than 2 years to cheaper storage class
        var oldDocuments = await _documentRepository
            .GetDocumentsOlderThanAsync(DateTime.UtcNow.AddYears(-2));

        foreach (var doc in oldDocuments)
        {
            await MoveToArchiveStorageAsync(doc);
        }
    }

    public async Task DeleteExpiredDocumentsAsync()
    {
        // Delete documents past their expiry date
        var expiredDocs = await _documentRepository
            .GetExpiredDocumentsAsync();

        foreach (var doc in expiredDocs)
        {
            await DeleteDocumentAsync(doc.Id);
        }
    }
}
```

### Bandwidth Optimization

- **Image Compression**: Automatic compression for web delivery
- **Progressive Loading**: Load images progressively for better UX
- **Lazy Loading**: Load images only when needed
- **WebP Format**: Use modern image formats for better compression

## 📚 Resources

- [DigitalOcean Spaces Documentation](https://docs.digitalocean.com/products/spaces/)
- [AWS S3 SDK for .NET](https://docs.aws.amazon.com/sdk-for-net/v3/developer-guide/s3-apis-intro.html)
- [CDN Best Practices](https://docs.digitalocean.com/products/spaces/how-to/enable-cdn/)
- [File Upload Security Guidelines](https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload)
