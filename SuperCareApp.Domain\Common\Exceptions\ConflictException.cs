﻿namespace SuperCareApp.Domain.Common.Exceptions
{
    /// <summary>
    /// Exception thrown when there is a conflict with the current state of a resource
    /// </summary>
    public class ConflictException : SuperCareException
    {
        public ConflictException(string message)
            : base(message) { }

        public ConflictException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}
