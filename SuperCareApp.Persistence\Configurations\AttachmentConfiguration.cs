﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class AttachmentConfiguration : IEntityTypeConfiguration<Attachment>
    {
        public void Configure(EntityTypeBuilder<Attachment> builder)
        {
            builder.HasKey(a => a.Id);

            builder.Property(a => a.FileUrl).IsRequired().HasMaxLength(512);

            builder.Property(a => a.FileType).IsRequired().HasMaxLength(50);

            builder.Property(a => a.FileName).IsRequired().HasMaxLength(256);

            builder.Property(a => a.FileSize).IsRequired();

            // Relationships
            builder
                .HasOne(a => a.Message)
                .WithMany(m => m.Attachments)
                .HasForeignKey(a => a.MessageId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
