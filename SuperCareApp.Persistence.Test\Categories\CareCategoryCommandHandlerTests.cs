using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.Services.Categories.Commands;
using SuperCareApp.Persistence.UnitOfWork;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Comprehensive unit tests for all care category command handlers
/// </summary>
public class CareCategoryCommandHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly ICareCategoryService _service;

    public CareCategoryCommandHandlerTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        var repository = new CareCategoryRepository(_context);
        var unitOfWork = new UnitOfWork.UnitOfWork(_context);
        _service = new CareCategoryService(
            repository,
            _context,
            unitOfWork,
            NullLogger<CareCategoryService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region CreateCareCategoryCommandHandler Tests

    [Fact]
    public async Task CreateCareCategoryCommandHandler_WithValidRequest_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateCareCategoryRequest
        {
            Name = "New Category",
            Description = "New Description",
            IsActive = true,
            PlatformFee = 25.00m,
            Icon = "fas fa-new",
            Color = "#123456"
        };

        var handler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
        var command = new CreateCareCategoryCommand(request, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(request.Name, result.Value.Name);
        Assert.Equal(request.Description, result.Value.Description);
        Assert.Equal(request.IsActive, result.Value.IsActive);
        Assert.Equal(request.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(request.Icon, result.Value.Icon);
        Assert.Equal(request.Color, result.Value.Color);

        // Verify in database
        var categoryInDb = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == result.Value.Id);
        Assert.NotNull(categoryInDb);
        Assert.Equal(userId, categoryInDb.CreatedBy);
        Assert.False(categoryInDb.IsDeleted);
    }

    [Fact]
    public async Task CreateCareCategoryCommandHandler_WithDuplicateName_ShouldReturnConflict()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        var request = new CreateCareCategoryRequest
        {
            Name = "Existing Category",
            Description = "Duplicate name",
            IsActive = true,
            PlatformFee = 15.00m
        };

        var handler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
        var command = new CreateCareCategoryCommand(request, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("already exists", result.Error.Message);
    }

    [Fact]
    public async Task CreateCareCategoryCommandHandler_WithMinimalData_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateCareCategoryRequest
        {
            Name = "Minimal Category",
            Description = null,
            IsActive = false,
            PlatformFee = 0m,
            Icon = null,
            Color = null
        };

        var handler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
        var command = new CreateCareCategoryCommand(request, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(request.Name, result.Value.Name);
        Assert.Null(result.Value.Description);
        Assert.False(result.Value.IsActive);
        Assert.Equal(0m, result.Value.PlatformFee);
        Assert.Null(result.Value.Icon);
        Assert.Null(result.Value.Color);
    }

    #endregion

    #region UpdateCareCategoryCommandHandler Tests

    [Fact]
    public async Task UpdateCareCategoryCommandHandler_WithValidRequest_ShouldUpdateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-old",
            Color = "#000000",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Description = "Updated Description",
            IsActive = false,
            PlatformFee = 20.00m,
            Icon = "fas fa-new",
            Color = "#FFFFFF"
        };

        var handler = new UpdateCareCategoryCommandHandler(_service, NullLogger<UpdateCareCategoryCommandHandler>.Instance);
        var command = new UpdateCareCategoryCommand(category.Id, updateRequest, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(updateRequest.Name, result.Value.Name);
        Assert.Equal(updateRequest.Description, result.Value.Description);
        Assert.Equal(updateRequest.IsActive, result.Value.IsActive);
        Assert.Equal(updateRequest.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(updateRequest.Icon, result.Value.Icon);
        Assert.Equal(updateRequest.Color, result.Value.Color);

        // Verify in database
        var updatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == category.Id);
        Assert.NotNull(updatedCategory);
        Assert.Equal(userId, updatedCategory.UpdatedBy);
        Assert.NotNull(updatedCategory.UpdatedAt);
    }

    [Fact]
    public async Task UpdateCareCategoryCommandHandler_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category"
        };

        var handler = new UpdateCareCategoryCommandHandler(_service, NullLogger<UpdateCareCategoryCommandHandler>.Instance);
        var command = new UpdateCareCategoryCommand(nonExistentId, updateRequest, Guid.NewGuid());

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task UpdateCareCategoryCommandHandler_WithDuplicateName_ShouldReturnConflict()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Category 1" // Trying to rename category2 to category1's name
        };

        var handler = new UpdateCareCategoryCommandHandler(_service, NullLogger<UpdateCareCategoryCommandHandler>.Instance);
        var command = new UpdateCareCategoryCommand(category2.Id, updateRequest, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("already exists", result.Error.Message);
    }

    #endregion

    #region DeleteCareCategoryCommandHandler Tests

    [Fact]
    public async Task DeleteCareCategoryCommandHandler_WithValidId_ShouldSoftDeleteSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category to Delete",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var handler = new DeleteCareCategoryCommandHandler(_service, NullLogger<DeleteCareCategoryCommandHandler>.Instance);
        var command = new DeleteCareCategoryCommand(category.Id, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);

        // Verify soft delete in database
        var deletedCategory = await _context.CareCategories
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(c => c.Id == category.Id);
        Assert.NotNull(deletedCategory);
        Assert.True(deletedCategory.IsDeleted);
        Assert.NotNull(deletedCategory.DeletedAt);
        Assert.Equal(userId, deletedCategory.DeletedBy);

        // Verify category is not returned in normal queries
        var getResult = await _service.GetCategoryByIdAsync(category.Id);
        Assert.True(getResult.IsFailure);
    }

    [Fact]
    public async Task DeleteCareCategoryCommandHandler_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var handler = new DeleteCareCategoryCommandHandler(_service, NullLogger<DeleteCareCategoryCommandHandler>.Instance);
        var command = new DeleteCareCategoryCommand(nonExistentId, Guid.NewGuid());

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task DeleteCareCategoryCommandHandler_WithAlreadyDeletedCategory_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Already Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        var handler = new DeleteCareCategoryCommandHandler(_service, NullLogger<DeleteCareCategoryCommandHandler>.Instance);
        var command = new DeleteCareCategoryCommand(deletedCategory.Id, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion

    #region ActivateCareCategoryCommandHandler Tests

    [Fact]
    public async Task ActivateCareCategoryCommandHandler_WithInactiveCategory_ShouldActivateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(inactiveCategory);
        await _context.SaveChangesAsync();

        var handler = new ActivateCareCategoryCommandHandler(_service, NullLogger<ActivateCareCategoryCommandHandler>.Instance);
        var command = new ActivateCareCategoryCommand(inactiveCategory.Id, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.IsActive);

        // Verify in database
        var activatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == inactiveCategory.Id);
        Assert.NotNull(activatedCategory);
        Assert.True(activatedCategory.IsActive);
        Assert.Equal(userId, activatedCategory.UpdatedBy);
        Assert.NotNull(activatedCategory.UpdatedAt);
    }

    [Fact]
    public async Task ActivateCareCategoryCommandHandler_WithActiveCategory_ShouldRemainActive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(activeCategory);
        await _context.SaveChangesAsync();

        var handler = new ActivateCareCategoryCommandHandler(_service, NullLogger<ActivateCareCategoryCommandHandler>.Instance);
        var command = new ActivateCareCategoryCommand(activeCategory.Id, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.IsActive);
    }

    [Fact]
    public async Task ActivateCareCategoryCommandHandler_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var handler = new ActivateCareCategoryCommandHandler(_service, NullLogger<ActivateCareCategoryCommandHandler>.Instance);
        var command = new ActivateCareCategoryCommand(nonExistentId, Guid.NewGuid());

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion

    #region DeactivateCareCategoryCommandHandler Tests

    [Fact]
    public async Task DeactivateCareCategoryCommandHandler_WithActiveCategory_ShouldDeactivateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(activeCategory);
        await _context.SaveChangesAsync();

        var handler = new DeactivateCareCategoryCommandHandler(_service, NullLogger<DeactivateCareCategoryCommandHandler>.Instance);
        var command = new DeactivateCareCategoryCommand(activeCategory.Id, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value.IsActive);

        // Verify in database
        var deactivatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == activeCategory.Id);
        Assert.NotNull(deactivatedCategory);
        Assert.False(deactivatedCategory.IsActive);
        Assert.Equal(userId, deactivatedCategory.UpdatedBy);
        Assert.NotNull(deactivatedCategory.UpdatedAt);
    }

    [Fact]
    public async Task DeactivateCareCategoryCommandHandler_WithInactiveCategory_ShouldRemainInactive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(inactiveCategory);
        await _context.SaveChangesAsync();

        var handler = new DeactivateCareCategoryCommandHandler(_service, NullLogger<DeactivateCareCategoryCommandHandler>.Instance);
        var command = new DeactivateCareCategoryCommand(inactiveCategory.Id, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value.IsActive);
    }

    [Fact]
    public async Task DeactivateCareCategoryCommandHandler_WithNonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();
        var handler = new DeactivateCareCategoryCommandHandler(_service, NullLogger<DeactivateCareCategoryCommandHandler>.Instance);
        var command = new DeactivateCareCategoryCommand(nonExistentId, Guid.NewGuid());

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion

    #region BulkUpdateCareCategoriesCommandHandler Tests

    [Fact]
    public async Task BulkUpdateCareCategoriesCommandHandler_WithValidRequest_ShouldUpdateAllCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 1",
            Description = "Original Description 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 2",
            Description = "Original Description 2",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2);
        await _context.SaveChangesAsync();

        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = category1.Id,
                    Name = "Updated Category 1",
                    Description = "Updated Description 1",
                    IsActive = false,
                    PlatformFee = 20.00m
                },
                new CareCategoryUpdateItem
                {
                    Id = category2.Id,
                    Name = "Updated Category 2",
                    Description = "Updated Description 2",
                    IsActive = true,
                    PlatformFee = 25.00m
                }
            }
        };

        var handler = new BulkUpdateCareCategoriesCommandHandler(_service, NullLogger<BulkUpdateCareCategoriesCommandHandler>.Instance);
        var command = new BulkUpdateCareCategoriesCommand(bulkUpdateRequest, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());

        var updatedCategories = result.Value.ToList();
        var updatedCat1 = updatedCategories.First(c => c.Id == category1.Id);
        Assert.Equal("Updated Category 1", updatedCat1.Name);
        Assert.Equal("Updated Description 1", updatedCat1.Description);
        Assert.False(updatedCat1.IsActive);
        Assert.Equal(20.00m, updatedCat1.PlatformFee);

        var updatedCat2 = updatedCategories.First(c => c.Id == category2.Id);
        Assert.Equal("Updated Category 2", updatedCat2.Name);
        Assert.Equal("Updated Description 2", updatedCat2.Description);
        Assert.True(updatedCat2.IsActive);
        Assert.Equal(25.00m, updatedCat2.PlatformFee);
    }

    [Fact]
    public async Task BulkUpdateCareCategoriesCommandHandler_WithPartialFailures_ShouldReturnPartialSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(existingCategory);
        await _context.SaveChangesAsync();

        var nonExistentId = Guid.NewGuid();
        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = existingCategory.Id,
                    Name = "Updated Existing Category"
                },
                new CareCategoryUpdateItem
                {
                    Id = nonExistentId,
                    Name = "Non-existent Category"
                }
            }
        };

        var handler = new BulkUpdateCareCategoriesCommandHandler(_service, NullLogger<BulkUpdateCareCategoriesCommandHandler>.Instance);
        var command = new BulkUpdateCareCategoriesCommand(bulkUpdateRequest, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess); // Partial success
        Assert.Single(result.Value); // Only one category updated
        Assert.Equal("Updated Existing Category", result.Value.First().Name);
    }

    [Fact]
    public async Task BulkUpdateCareCategoriesCommandHandler_WithAllFailures_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var nonExistentId1 = Guid.NewGuid();
        var nonExistentId2 = Guid.NewGuid();

        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = new List<CareCategoryUpdateItem>
            {
                new CareCategoryUpdateItem
                {
                    Id = nonExistentId1,
                    Name = "Non-existent Category 1"
                },
                new CareCategoryUpdateItem
                {
                    Id = nonExistentId2,
                    Name = "Non-existent Category 2"
                }
            }
        };

        var handler = new BulkUpdateCareCategoriesCommandHandler(_service, NullLogger<BulkUpdateCareCategoriesCommandHandler>.Instance);
        var command = new BulkUpdateCareCategoriesCommand(bulkUpdateRequest, userId);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("Bulk update failed", result.Error.Message);
    }

    #endregion
}