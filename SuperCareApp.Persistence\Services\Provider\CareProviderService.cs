using System.Linq.Dynamic.Core;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Provider;

public class CareProviderService : ICareProviderService
{
    private readonly ApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAvailabilityTemplateService _availabilityTemplateService;

    public CareProviderService(
        ApplicationDbContext context,
        ICurrentUserService currentUserService,
        IAvailabilityTemplateService availabilityTemplateService
    )
    {
        _context = context;
        _currentUserService = currentUserService;
        _availabilityTemplateService = availabilityTemplateService;
    }

    public async Task<Result<IEnumerable<CareProviderProfileResponse>>> GetAllProvidersAsync()
    {
        try
        {
            // Query to join ApplicationUser, UserProfile, and CareProviderProfile
            // Use a projection to directly map to the required response format
            var careProviders = await _context
                .Set<CareProviderProfile>()
                .AsNoTracking()
                .Where(cp => !cp.IsDeleted) // Filter out deleted records
                .Select(cp => new
                {
                    Id = cp.UserId,
                    User = cp.User,
                    UserProfile = cp.User.UserProfile,
                    CareProvider = cp,
                })
                .ToListAsync();

            // Map to response objects
            var providerResponses = careProviders
                .Select(cp => new CareProviderProfileResponse
                {
                    Id = cp.User.Id,
                    Name =
                        cp.UserProfile != null
                            ? $"{cp.UserProfile.FirstName} {cp.UserProfile.LastName}".Trim()
                            : string.Empty,
                    Email = cp.User.Email ?? string.Empty,
                    PhoneNumber =
                        (cp.UserProfile?.PhoneNumber ?? cp.User.PhoneNumber) ?? string.Empty,
                    Gender = cp.UserProfile?.Gender ?? string.Empty,
                    YearsExperience = cp.CareProvider.YearsExperience,
                    DateOfBirth = cp.UserProfile?.DateOfBirth,
                })
                .ToList();

            // Return the result wrapped in a Result object
            return Result<IEnumerable<CareProviderProfileResponse>>.Success(providerResponses);
        }
        catch (Exception ex)
        {
            // Handle exceptions and return a failure result
            return Result<IEnumerable<CareProviderProfileResponse>>.Failure(
                Error.Internal($"Error retrieving all care providers: {ex.Message}")
            );
        }
    }

    public async Task<Result<PagedCareProviderList>> GetPagedProvidersAsync(
        CareProviderListParams parameters
    )
    {
        try
        {
            // Validate parameters
            if (parameters.PageNumber < 1)
            {
                return Result<PagedCareProviderList>.Failure(
                    Error.Internal("Page number should be greater than 1")
                );
            }

            // Create base query with joins
            var query = _context
                .Set<CareProviderProfile>()
                .AsNoTracking()
                .Where(cp => !cp.IsDeleted)
                .Select(cp => new
                {
                    Id = cp.Id,
                    UserId = cp.UserId,
                    UserEmail = cp.User.Email,
                    UserPhoneNumber = cp.User.PhoneNumber,
                    FirstName = cp.User.UserProfile != null
                        ? cp.User.UserProfile.FirstName
                        : string.Empty,
                    LastName = cp.User.UserProfile != null
                        ? cp.User.UserProfile.LastName
                        : string.Empty,
                    ProfilePhoneNumber = cp.User.UserProfile != null
                        ? cp.User.UserProfile.PhoneNumber
                        : string.Empty,
                    Gender = cp.User.UserProfile != null
                        ? cp.User.UserProfile.Gender
                        : string.Empty,
                    DateOfBirth = cp.User.UserProfile != null
                        ? cp.User.UserProfile.DateOfBirth
                        : null,
                    YearsExperience = cp.YearsExperience,
                    Rating = cp.Rating,
                });

            // Apply sorting if specified
            if (!string.IsNullOrWhiteSpace(parameters.SortBy))
            {
                // Map the sortBy parameter to the appropriate property name in our query
                string sortProperty = parameters.SortBy.ToLower() switch
                {
                    "name" => parameters.SortDescending
                        ? "LastName DESC, FirstName DESC"
                        : "LastName, FirstName",
                    "email" => parameters.SortDescending ? "UserEmail DESC" : "UserEmail",
                    "experience" => parameters.SortDescending
                        ? "YearsExperience DESC"
                        : "YearsExperience",
                    "rating" => parameters.SortDescending ? "Rating DESC" : "Rating",
                    _ => parameters.SortDescending ? "LastName DESC" : "LastName", // Default sort
                };

                // Apply sorting using Dynamic LINQ
                query = query.OrderBy(sortProperty);
            }
            else
            {
                // Default sorting by name
                query = parameters.SortDescending
                    ? query
                        .OrderByDescending(cp => cp.LastName)
                        .ThenByDescending(cp => cp.FirstName)
                    : query.OrderBy(cp => cp.LastName).ThenBy(cp => cp.FirstName);
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Calculate pagination values
            var totalPages = (int)Math.Ceiling(totalCount / (double)parameters.PageSize);
            var skip = (parameters.PageNumber - 1) * parameters.PageSize;

            // Apply pagination
            var pagedData = await query.Skip(skip).Take(parameters.PageSize).ToListAsync();

            // Map to response objects
            var providers = pagedData
                .Select(cp => new CareProviderProfileResponse
                {
                    Id = cp.Id,
                    UserId = cp.Id, // Set UserId to the same value as Id since this is the user ID
                    Name = $"{cp.FirstName} {cp.LastName}".Trim(),
                    Email = cp.UserEmail ?? string.Empty,
                    PhoneNumber = cp.ProfilePhoneNumber ?? cp.UserPhoneNumber ?? string.Empty,
                    Gender = cp.Gender ?? string.Empty,
                    YearsExperience = cp.YearsExperience,
                    DateOfBirth = cp.DateOfBirth,
                })
                .ToList();

            // Build paged response
            var result = new PagedCareProviderList
            {
                Providers = providers,
                PageNumber = parameters.PageNumber,
                PageSize = parameters.PageSize,
                TotalCount = totalCount,
                TotalPages = totalPages,
            };

            return Result<PagedCareProviderList>.Success(result);
        }
        catch (Exception ex)
        {
            return Result<PagedCareProviderList>.Failure(
                Error.Internal($"Error retrieving care providers: {ex.Message}")
            );
        }
    }

    public async Task<Result<CareProviderProfileResponse>> GetProfileAsync(Guid userId)
    {
        var careProviderProfile = await _context
            .Set<CareProviderProfile>()
            .AsNoTracking()
            .Where(cp => cp.UserId == userId && !cp.IsDeleted) // Filter out deleted records
            .Select(cp => new
            {
                User = cp.User,
                UserProfile = cp.User.UserProfile,
                CareProvider = cp,
            })
            .FirstOrDefaultAsync();

        if (careProviderProfile == null)
            return null;

        // Create the simplified response object with only needed fields
        var response = new CareProviderProfileResponse
        {
            // Set the ID and UserId
            Id = careProviderProfile.CareProvider.Id,
            UserId = careProviderProfile.User.Id,

            // Combine first and last name if available
            Name =
                careProviderProfile.UserProfile != null
                    ? $"{careProviderProfile.UserProfile.FirstName} {careProviderProfile.UserProfile.LastName}".Trim()
                    : string.Empty,

            // Get email from user
            Email = careProviderProfile.User.Email ?? string.Empty,

            // Get phone number - first try user profile, then fallback to identity user
            PhoneNumber =
                (
                    careProviderProfile.UserProfile?.PhoneNumber
                    ?? careProviderProfile.User.PhoneNumber
                ) ?? string.Empty,

            // Get gender from user profile
            Gender = careProviderProfile.UserProfile?.Gender ?? string.Empty,

            // Get experience from care provider profile
            YearsExperience = careProviderProfile.CareProvider.YearsExperience,

            // Get date of birth from user profile
            DateOfBirth = careProviderProfile.UserProfile?.DateOfBirth,
        };

        return response;
    }

    public async Task<Result<Guid>> CreateProfileAsync(
        Guid userId,
        CreateCareProviderProfileRequest request
    )
    {
        // Use transaction to ensure both profile and availability creation succeed or fail together
        using var transaction = await _context.Database.BeginTransactionAsync();

        try
        {
            var careProvider = new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Bio = "Example placeholder bio will be updated later based on requirement",
                YearsExperience = request.YearsExperience,
                ProvidesOvernight = false,
                ProvidesLiveIn = false,
                //Send empty json below
                Qualifications = "{}",
                VerificationStatus = VerificationStatus.Verified,
                Rating = 5,
                RatingCount = 5,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
            };

            await _context.CareProviderProfiles.AddAsync(careProvider);
            await _context.SaveChangesAsync();

            // Create default availability template for the new provider
            var availabilityResult =
                await _availabilityTemplateService.CreateDefaultAvailabilityTemplateAsync(userId);

            if (availabilityResult.IsFailure)
            {
                // Rollback transaction if availability template creation fails
                await transaction.RollbackAsync();
                return Result<Guid>.Failure(
                    Error.Internal(
                        $"Failed to create availability template: {availabilityResult.Error.Message}"
                    )
                );
            }

            // Commit transaction if both operations succeed
            await transaction.CommitAsync();
            return Result<Guid>.Success(careProvider.Id);
        }
        catch (Exception ex)
        {
            // Rollback transaction on any exception
            await transaction.RollbackAsync();
            return Result<Guid>.Failure(
                Error.Internal($"Failed to create care provider profile: {ex.Message}")
            );
        }
    }

    public async Task<Result> UpdateProfileAsync(
        Guid userId,
        UpdateCareProviderProfileRequest request
    )
    {
        var existingProfile = _context.CareProviderProfiles.FirstOrDefault(x =>
            x.UserId == userId && !x.IsDeleted
        );
        if (existingProfile == null)
        {
            return Result.Failure(Error.NotFound("Care provider profile not found"));
        }
        existingProfile.YearsExperience = request.YearsExperience;
        existingProfile.UpdatedAt = DateTime.UtcNow;
        _context.CareProviderProfiles.Update(existingProfile);
        await _context.SaveChangesAsync();
        return Result.Success();
    }
}
