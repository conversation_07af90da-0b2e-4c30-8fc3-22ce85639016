﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace SuperCareApp.Persistence.Extensions
{
    /// <summary>
    /// Handles database initialization with a "clean slate" approach for development
    /// </summary>
    public static class DatabaseInitializer
    {
        /// <summary>
        /// Initializes the database - creates if it doesn't exist, optionally drops and recreates if it exists
        /// </summary>
        /// <param name="host">The host to initialize the database for</param>
        /// <param name="developmentOnly">Only initialize in development environment</param>
        /// <param name="dropDatabase">Whether to drop the database before creating if it already exists</param>
        /// <returns>The host</returns>
        public static async Task<IHost> InitializeDatabaseAsync<TContext>(
            this IHost host,
            bool developmentOnly = true,
            bool dropDatabase = true
        )
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var environment = services.GetRequiredService<IHostEnvironment>();

                // Skip initialization if not in development and developmentOnly is true
                if (developmentOnly && !environment.IsDevelopment())
                {
                    logger.LogInformation(
                        "Skipping database initialization in non-development environment"
                    );
                    return host;
                }

                // Try to resolve the context - this might fail if there are dependency issues
                var context = services.GetService<TContext>();
                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}. Skipping database initialization.",
                        typeof(TContext).Name
                    );
                    return host;
                }

                await InitializeDatabaseCoreAsync(context, logger, dropDatabase);
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(
                    ex,
                    "An error occurred while initializing the database for context {DbContextName}",
                    typeof(TContext).Name
                );

                // Re-throw in development to make issues visible, log and continue in other environments
                var environment = services.GetService<IHostEnvironment>();
                if (environment?.IsDevelopment() == true)
                {
                    throw;
                }
            }

            return host;
        }

        /// <summary>
        /// Synchronous version of database initialization
        /// </summary>
        public static IHost InitializeDatabase<TContext>(
            this IHost host,
            bool developmentOnly = true,
            bool dropDatabase = true
        )
            where TContext : DbContext
        {
            return host.InitializeDatabaseAsync<TContext>(developmentOnly, dropDatabase)
                .GetAwaiter()
                .GetResult();
        }

        private static async Task InitializeDatabaseCoreAsync<TContext>(
            TContext context,
            ILogger logger,
            bool dropDatabase
        )
            where TContext : DbContext
        {
            // Check if database exists
            var canConnect = await context.Database.CanConnectAsync();

            if (canConnect && dropDatabase)
            {
                // Database exists and we want to drop it
                logger.LogWarning(
                    "DEVELOPMENT ONLY: Dropping existing database for clean slate approach"
                );
                await context.Database.EnsureDeletedAsync();
                canConnect = false; // Database no longer exists after deletion
            }

            if (!canConnect)
            {
                // Database doesn't exist, create it
                logger.LogInformation("Database doesn't exist, creating new database");
                await context.Database.EnsureCreatedAsync();
                logger.LogInformation("Database created successfully");
            }
            else
            {
                // Database exists and dropDatabase is false
                logger.LogInformation(
                    "Database already exists, skipping creation (dropDatabase = false)"
                );
            }
        }

        /// <summary>
        /// Alternative initialization method that uses migrations instead of EnsureCreated
        /// </summary>
        public static async Task<IHost> InitializeDatabaseWithMigrationsAsync<TContext>(
            this IHost host,
            bool developmentOnly = true,
            bool dropDatabase = false
        )
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var environment = services.GetRequiredService<IHostEnvironment>();

                // Skip initialization if not in development and developmentOnly is true
                if (developmentOnly && !environment.IsDevelopment())
                {
                    logger.LogInformation(
                        "Skipping database migration in non-development environment"
                    );
                    return host;
                }

                var context = services.GetService<TContext>();
                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}. Skipping database migration.",
                        typeof(TContext).Name
                    );
                    return host;
                }

                if (dropDatabase)
                {
                    logger.LogWarning("DEVELOPMENT ONLY: Dropping database before migration");
                    await context.Database.EnsureDeletedAsync();
                }

                logger.LogInformation("Applying database migrations");
                await context.Database.MigrateAsync();

                logger.LogInformation("Database migrations applied successfully");
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(
                    ex,
                    "An error occurred while migrating the database for context {DbContextName}",
                    typeof(TContext).Name
                );

                var environment = services.GetService<IHostEnvironment>();
                if (environment?.IsDevelopment() == true)
                {
                    throw;
                }
            }

            return host;
        }
    }
}
