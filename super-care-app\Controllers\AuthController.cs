﻿using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using super_care_app.Shared.Utility;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.Otp;
using SuperCareApp.Application.Common.Models.User;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Identity.Commands;
using Swashbuckle.AspNetCore.Annotations;

namespace super_care_app.Controllers
{
    /// <summary>
    /// Handles authentication and user registration
    /// </summary>
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<AuthResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<Error>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<Error>)
    )]
    public class AuthController : BaseController
    {
        private readonly IMediator _mediator;
        private readonly ICurrentUserService _currentUserService;
        private readonly RequestValidator _requestValidator;

        public AuthController(
            IMediator mediator,
            ICurrentUserService currentUserService,
            RequestValidator requestValidator
        )
        {
            _mediator = mediator;
            _currentUserService = currentUserService;
            _requestValidator = requestValidator;
        }

        /// <summary>
        /// Authenticates a user and returns authentication tokens
        /// </summary>
        /// <param name="request">Login credentials</param>
        /// <returns>Authentication tokens and expiration details</returns>
        [HttpPost(ApiRoutes.Auth.Login)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<AuthResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Authenticates a user",
            Description = "Authenticates a user with email and password and returns JWT tokens",
            OperationId = "Auth_Login",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> Login(AuthRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new AuthRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<AuthResponse>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new LoginCommand(request.Email, request.Password);
            var authResponse = await _mediator.Send(command);
            if (authResponse.IsFailure)
            {
                return ErrorResponseFromError<AuthResponse>(authResponse.Error);
            }

            return SuccessResponse(authResponse.Value, "Authentication successful");
        }

        /// <summary>
        /// Logs out a user by invalidating their authentication tokens
        /// </summary>
        /// <param name="request">Access token to identify the user</param>
        /// <returns>Confirmation of successful logout</returns>
        [HttpPost(ApiRoutes.Auth.Logout)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Logs out a user",
            Description = "Invalidates the user's access and refresh tokens using the provided access token",
            OperationId = "Auth_Logout",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> Logout([FromBody] LogoutRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new LogoutRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<object>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new LogoutCommand(request.token);
            _currentUserService.Clear();
            var result = await _mediator.Send(command);
            if (result.IsFailure)
            {
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Logout successful");
        }

        /// <summary>
        /// Registers a new user and returns authentication tokens
        /// </summary>
        /// <param name="request">User registration details</param>
        /// <returns>Authentication tokens and expiration details</returns>
        [HttpPost(ApiRoutes.Auth.Register)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<AuthResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [SwaggerOperation(
            Summary = "Registers a new user",
            Description = "Creates a new user account with the provided details and returns JWT tokens",
            OperationId = "Auth_Register",
            Tags = new[] { "Authentication" }
        )]
        [Consumes("application/json")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new RegisterRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<AuthResponse>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new RegisterUserCommand(
                request.Email,
                request.Password,
                request.PhoneNumber,
                request.IsCareProvider,
                request.FirstName,
                request.LastName
            );
            var otpResult = await _mediator.Send(command);
            if (otpResult.IsFailure)
            {
                return ErrorResponseFromError<AuthResponse>(otpResult.Error);
            }

            return SuccessResponse(otpResult.Value, "User registered successfully");
        }

        /// <summary>
        /// Refreshes the user's authentication tokens
        /// </summary>
        /// <param name="request">Refresh token request</param>
        /// <returns>New authentication tokens</returns>
        [HttpPost(ApiRoutes.Auth.RefreshToken)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<AuthResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [SwaggerOperation(
            Summary = "Refreshes an access token",
            Description = "Exchanges a valid refresh token for a new access token and refresh token pair",
            OperationId = "Auth_RefreshToken",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new RefreshTokenRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<AuthResponse>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new ExchangeTokenCommand(request.RefreshToken);
            var authResponse = await _mediator.Send(command);
            if (authResponse.IsFailure)
            {
                return ErrorResponseFromError<AuthResponse>(authResponse.Error);
            }

            return SuccessResponse(authResponse.Value, "Token refreshed successfully");
        }

        /// <summary>
        /// Resets the user's password using the OTP code sent to their email
        /// </summary>
        [HttpPut(ApiRoutes.Auth.ResetPassword)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [SwaggerOperation(
            Summary = "Resets password",
            Description = "Resets the user's password using the OTP code sent to their email",
            OperationId = "Auth_ResetPassword",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new ResetPasswordRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<ResetPasswordResponse>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new ResetPasswordCommand(
                request.Email,
                request.NewPassword,
                request.ResetToken
            );

            var resetPasswordResult = await _mediator.Send(command);
            if (resetPasswordResult.IsFailure)
            {
                return ErrorResponseFromError<ResetPasswordResponse>(resetPasswordResult.Error);
            }

            return SuccessResponse(resetPasswordResult.Value, "Password reset successfully");
        }

        /// <summary>
        /// Changes the user's password using the current password and new password
        /// </summary>
        [HttpPost(ApiRoutes.Auth.ChangePassword)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [SwaggerOperation(
            Summary = "Change password",
            Description = "Changes the user's password using the current password and new password",
            OperationId = "Auth_ChangePassword",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            var validationResult = await _requestValidator.ValidateAsync(
                request,
                new ChangePasswordRequestValidator()
            );
            if (!validationResult.IsSuccess)
            {
                return ErrorResponseFromError<object>(
                    Error.Validation("Validation failed", validationResult.Error.ValidationErrors)
                );
            }

            var userId = _currentUserService.UserId;
            if (userId == null)
            {
                return UnauthorizedResponse<object>("User not found");
            }

            var command = new ChangePasswordCommand(
                userId.Value,
                request.CurrentPassword,
                request.NewPassword,
                request.ConfirmNewPassword
            );

            var changePasswordResult = await _mediator.Send(command);
            if (changePasswordResult.IsFailure)
            {
                return ErrorResponseFromError<object>(changePasswordResult.Error);
            }

            return SuccessResponse(changePasswordResult.Value, "Password changed successfully");
        }

        /// <summary>
        /// Generates an OTP code and sends it to the user via the specified delivery method
        /// </summary>
        [HttpPost(ApiRoutes.Auth.GenerateOtp)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<OtpResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [SwaggerOperation(
            Summary = "Generate OTP codes",
            Description = "Generates OTP codes and sends them to the user via email and/or SMS. Can generate either one or both.",
            OperationId = "Auth_GenerateOtp",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> GenerateOtp([FromBody] GenerateOtpRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new GenerateOtpRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<OtpResponse>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new GenerateOtpCommand(
                request.Email,
                request.PhoneNumber,
                request.DeliveryMethod.ToOtpDeliveryMethod(),
                request.ActionType
            );

            var otpResult = await _mediator.Send(command);
            if (otpResult.IsFailure)
            {
                return ErrorResponseFromError<OtpResponse>(otpResult.Error);
            }

            return SuccessResponse(otpResult.Value, "OTP code generated successfully");
        }

        /// <summary>
        /// Verifies OTP codes sent to the user via email and/or SMS
        /// </summary>
        [HttpPost(ApiRoutes.Auth.VerifyOtp)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<VerifyOtpResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<Error>)
        )]
        [SwaggerOperation(
            Summary = "Verify OTP codes",
            Description = "Verifies OTP codes sent to the user via email and/or SMS. Can verify either one or both. For verificationType use either Registration or PasswordReset",
            OperationId = "Auth_VerifyOtp",
            Tags = new[] { "Authentication" }
        )]
        public async Task<IActionResult> VerifyOtp([FromBody] VerifyOtpRequest request)
        {
            var validator = await _requestValidator.ValidateAsync(
                request,
                new VerifyOtpRequestValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<VerifyOtpResponse>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var command = new VerifyOtpCommand(
                request.Email,
                request.PhoneNumber,
                request.EmailOtpCode,
                request.PhoneOtpCode,
                request.VerificationType
            );

            var verifyResult = await _mediator.Send(command);
            if (verifyResult.IsFailure)
            {
                return ErrorResponseFromError<VerifyOtpResponse>(verifyResult.Error);
            }

            return SuccessResponse(verifyResult.Value, "OTP code verification successful");
        }
    }
}
