﻿namespace SuperCareApp.Application.Common.Models.Bookings;

public class UserPaymentMethod
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string PaymentMethodId { get; set; }
    public string Type { get; set; }
    public string LastFour { get; set; }
    public int ExpMonth { get; set; }
    public int ExpYear { get; set; }
    public bool IsDefault { get; set; }

    public static List<UserPaymentMethod> GetSeedData(Guid userId, int count = 3)
    {
        var random = new Random();
        var cardTypes = new[] { "visa", "mastercard", "amex", "discover" };
        var paymentMethods = new List<UserPaymentMethod>();

        for (int i = 0; i < count; i++)
        {
            paymentMethods.Add(
                new UserPaymentMethod
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    PaymentMethodId = $"pm_{Guid.NewGuid().ToString("N").ToLower()}",
                    Type = cardTypes[random.Next(cardTypes.Length)],
                    LastFour = random.Next(1000, 10000).ToString(),
                    ExpMonth = random.Next(1, 13),
                    ExpYear = DateTime.Now.Year + random.Next(1, 6),
                    IsDefault = (i == 0),
                }
            );
        }
        return paymentMethods;
    }
}
