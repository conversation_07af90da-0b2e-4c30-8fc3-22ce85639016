﻿using System.ComponentModel.DataAnnotations;

namespace SuperCareApp.Application.Common.Models.Documents;

/// <summary>
/// Request model for document approval
/// </summary>
public class DocumentApprovalRequest
{
    /// <summary>
    /// Optional notes about the approval
    /// </summary>
    public string? Notes { get; set; }
}

/// <summary>
/// Request model for document rejection
/// </summary>
public class DocumentRejectionRequest
{
    /// <summary>
    /// Reason for rejecting the document
    /// </summary>
    [Required(ErrorMessage = "Rejection reason is required")]
    public string RejectionReason { get; set; } = string.Empty;

    /// <summary>
    /// Optional additional notes
    /// </summary>
    public string? Notes { get; set; }
}
