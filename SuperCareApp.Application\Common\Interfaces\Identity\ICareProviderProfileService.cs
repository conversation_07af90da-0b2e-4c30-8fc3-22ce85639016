using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Identity;

public interface ICareProviderProfileService
{
    Task<Result<Guid>> CreateAsync(CareProviderProfile profile);
    Task<Result<CareProviderProfile?>> GetByUserIdAsync(Guid userId);
    Task<Result<CareProviderProfile?>> GetByUserIdNoFilterAsync(Guid userId);
    Task<Result<Guid>> UpdateAsync(Guid userId, CareProviderProfile profile);
    Task<Result> DeleteAsync(Guid userId);
}
