using SuperCareApp.Application.Common.Interfaces.Mediator;

namespace SuperCareApp.Application.Common.Interfaces.Messages.Command;

/// <summary>
/// Represents a command interface that inherits from IRequest.
/// Used for command operations that do not return a value.
/// </summary>
public interface ICommand : IRequest { }

/// <summary>
/// Represents a generic command interface that inherits from IRequest<T>.
/// Used for command operations that return a value of type T.
/// </summary>
/// <typeparam name="TResponse">The type of the value returned by the command</typeparam>
public interface ICommand<TResponse> : IRequest<TResponse> { }

/// <summary>
/// Represents a command handler interface that processes commands of type TCommand.
/// Implements IRequestHandler to handle the command and return a result of type TResponse.
/// </summary>
/// <typeparam name="TCommand">The type of the command being handled</typeparam>
/// <typeparam name="TResponse">The type of the value returned by the command handler</typeparam>
public interface ICommandHandler<TCommand, TResponse> : IRequestHandler<TCommand, TResponse>
    where TCommand : ICommand<TResponse> { }

/// <summary>
/// Represents a command handler interface that processes commands of type TCommand.
/// Implements IRequestHandler to handle the command without returning a value.
/// </summary>
/// <typeparam name="TCommand">The type of the command being handled</typeparam>
public interface ICommandHandler<TCommand> : IRequestHandler<TCommand>
    where TCommand : ICommand { }
