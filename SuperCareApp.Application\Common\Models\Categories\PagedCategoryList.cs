using SuperCareApp.Application.Shared.Utility;

namespace SuperCareApp.Application.Common.Models.Categories
{
    /// <summary>
    /// Paged list of care categories
    /// </summary>
    public class PagedCategoryList
    {
        /// <summary>
        /// List of care categories
        /// </summary>
        public List<CareCategoryResponse> Categories { get; set; } =
            new List<CareCategoryResponse>();

        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; } = 0;

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; } = 0;

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// Converts this paged list to a PaginationMetadata object
        /// </summary>
        public PaginationMetadata ToMetadata()
        {
            return new PaginationMetadata(PageNumber, TotalPages, TotalCount, PageSize);
        }
    }
}
