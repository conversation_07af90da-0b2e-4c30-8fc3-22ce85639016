using SuperCareApp.Application.Common.Interfaces.Mediator;

namespace SuperCareApp.Application.Common.Interfaces;

/// <summary>
/// Represents an integration event that can be published and handled asynchronously.
/// </summary>
/// <remarks>
/// This interface extends INotification to allow for easy integration with the Mediator pattern.
/// </remarks>
public interface IIntegrationEvent : INotification
{
    Guid Id { get; }
    DateTime CreatedAt { get; }
    public string EventType { get; }
    public string EventName { get; }
    public string Data { get; }
}

/// <summary>
/// Represents a handler for processing integration events.
/// </summary>
/// <typeparam name="TIntegrationEvent"></typeparam> <summary>
/// Represents a handler for processing integration events.
/// </summary>
/// <typeparam name="TIntegrationEvent"></typeparam>
public interface IIntegrationEventHandler<in TIntegrationEvent>
    : INotificationHandler<TIntegrationEvent>
    where TIntegrationEvent : IIntegrationEvent { }
