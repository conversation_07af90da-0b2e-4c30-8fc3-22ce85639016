namespace SuperCareApp.Persistence.Services;

/// <summary>
/// Default implementation of audit configuration
/// </summary>
public class AuditConfiguration : IAuditConfiguration
{
    private readonly string[] _excludedEntityTypes = new[]
    {
        "AuditLog",
        "ApplicationUserToken",
        "ApplicationUserLogin",
        "ApplicationUserClaim",
        "ApplicationRoleClaim",
        "OtpCode" // Temporary codes shouldn't be audited
        ,
    };

    private readonly string[] _sensitiveProperties = new[]
    {
        "Password",
        "PasswordHash",
        "SecurityStamp",
        "ConcurrencyStamp",
        "Token",
        "RefreshToken",
        "AccessToken",
        "Secret",
        "Key",
        "Code", // OTP codes
        "Salt",
        "Hash",
    };

    private readonly Dictionary<string, string[]> _entitySpecificExclusions =
        new()
        {
            {
                "ApplicationUser",
                new[]
                {
                    "PasswordHash",
                    "SecurityStamp",
                    "ConcurrencyStamp",
                    "AccessFailedCount",
                    "LockoutEnd",
                    "LockoutEnabled",
                    "TwoFactorEnabled",
                }
            },
            {
                "Payment",
                new[]
                {
                    "TransactionId" // Might contain sensitive payment info
                    ,
                }
            },
        };

    public bool ShouldAuditEntity(string entityType)
    {
        return !_excludedEntityTypes.Contains(entityType, StringComparer.OrdinalIgnoreCase);
    }

    public bool ShouldAuditProperty(string entityType, string propertyName)
    {
        // Check global sensitive properties
        if (
            _sensitiveProperties.Any(sensitive =>
                propertyName.Contains(sensitive, StringComparison.OrdinalIgnoreCase)
            )
        )
        {
            return false;
        }

        // Check entity-specific exclusions
        if (_entitySpecificExclusions.TryGetValue(entityType, out var exclusions))
        {
            return !exclusions.Contains(propertyName, StringComparer.OrdinalIgnoreCase);
        }

        return true;
    }

    public string[] GetSensitiveProperties()
    {
        return _sensitiveProperties;
    }

    public string[] GetExcludedEntityTypes()
    {
        return _excludedEntityTypes;
    }
}
