﻿namespace SuperCareApp.Application.Common.Models.Bookings;

/// <summary>
/// Response model for a leave
/// </summary>
public class LeaveResponse
{
    /// <summary>
    /// The unique identifier of the leave
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The ID of the care provider
    /// </summary>
    public Guid ProviderId { get; set; }

    /// <summary>
    /// The start date of the leave
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// The end date of the leave
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Optional reason for the leave
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// When the leave was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
