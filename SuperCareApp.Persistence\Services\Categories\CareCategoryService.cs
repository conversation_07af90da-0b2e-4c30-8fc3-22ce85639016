﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Categories
{
    /// <summary>
    /// Service implementation for care categories
    /// </summary>
    public class CareCategoryService : ICareCategoryService
    {
        private readonly ICareCategoryRepository _categoryRepository;
        private readonly ApplicationDbContext _context;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<CareCategoryService> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CareCategoryService(
            ICareCategoryRepository categoryRepository,
            ApplicationDbContext context,
            IUnitOfWork unitOfWork,
            ILogger<CareCategoryService> logger
        )
        {
            _categoryRepository = categoryRepository;
            _context = context;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<Result<IEnumerable<CareCategoryResponse>>> GetAllCategoriesAsync(
            bool includeInactive = false,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var categoriesResult = includeInactive
                    ? await _categoryRepository.GetAllAsync(cancellationToken)
                    : await _categoryRepository.GetActiveAsync(cancellationToken);

                if (categoriesResult.IsFailure)
                {
                    return Result.Failure<IEnumerable<CareCategoryResponse>>(
                        categoriesResult.Error
                    );
                }

                var categoryResponses = categoriesResult.Value.Select(MapToResponse).ToList();

                return Result.Success<IEnumerable<CareCategoryResponse>>(categoryResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all care categories");
                return Result.Failure<IEnumerable<CareCategoryResponse>>(
                    Error.Internal(ex.Message)
                );
            }
        }

        /// <inheritdoc />
        public async Task<Result<PagedCategoryList>> GetPaginatedCategoriesAsync(
            int pageNumber,
            int pageSize,
            bool includeInactive = false,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Validate pagination parameters
                if (pageNumber < 1)
                    pageNumber = 1;
                if (pageSize < 1)
                    pageSize = 10;
                if (pageSize > 100)
                    pageSize = 100; // Limit max page size

                var paginatedResult = await _categoryRepository.GetPaginatedAsync(
                    pageNumber,
                    pageSize,
                    includeInactive,
                    cancellationToken
                );

                if (paginatedResult.IsFailure)
                {
                    return Result.Failure<PagedCategoryList>(paginatedResult.Error);
                }

                var (categories, totalCount) = paginatedResult.Value;
                var categoryResponses = categories.Select(MapToResponse).ToList();

                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var pagedList = new PagedCategoryList
                {
                    Categories = categoryResponses,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                };

                return Result.Success(pagedList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paginated care categories");
                return Result.Failure<PagedCategoryList>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<bool>> ValidateUniqueNameAsync(
            string name,
            Guid? excludeId = null,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                {
                    return Result.Failure<bool>(Error.Validation("Category name cannot be empty"));
                }

                var existsResult = await _categoryRepository.ExistsByNameAsync(
                    name,
                    excludeId,
                    cancellationToken
                );

                if (existsResult.IsFailure)
                {
                    return Result.Failure<bool>(existsResult.Error);
                }

                // Return true if name is unique (doesn't exist), false if duplicate
                return Result.Success(!existsResult.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error validating category name uniqueness for name: {Name}",
                    name
                );
                return Result.Failure<bool>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<CareCategoryResponse>> GetCategoryByIdAsync(
            Guid id,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var categoryResult = await _categoryRepository.GetByIdAsync(id, cancellationToken);
                if (categoryResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(categoryResult.Error);
                }

                var response = MapToResponse(categoryResult.Value);
                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting care category with ID {CategoryId}", id);
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<CareCategoryResponse>> CreateCategoryAsync(
            CreateCareCategoryRequest request,
            Guid userId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Validate that the category name is unique
                var uniqueNameResult = await ValidateUniqueNameAsync(
                    request.Name,
                    null,
                    cancellationToken
                );
                if (uniqueNameResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(uniqueNameResult.Error);
                }

                if (!uniqueNameResult.Value)
                {
                    return Result.Failure<CareCategoryResponse>(
                        Error.Conflict(
                            $"A care category with the name '{request.Name}' already exists."
                        )
                    );
                }

                // Create the new category
                var category = new CareCategory
                {
                    Id = Guid.NewGuid(),
                    Name = request.Name,
                    Description = request.Description,
                    IsActive = request.IsActive,
                    PlatformFee = request.PlatformFee,
                    Icon = request.Icon,
                    Color = request.Color,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = userId,
                };

                // Add the category to the repository
                var addResult = await _categoryRepository.AddAsync(category, cancellationToken);
                if (addResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(addResult.Error);
                }

                // Save changes
                var saveResult = await _unitOfWork.SaveChangesAsync(cancellationToken);
                if (saveResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(saveResult.Error);
                }

                // Map to response
                var response = MapToResponse(category);
                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating care category");
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result<CareCategoryResponse>> UpdateCategoryAsync(
            Guid id,
            UpdateCareCategoryRequest request,
            Guid userId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // 1. Get existing category
                var categoryResult = await _categoryRepository.GetByIdAsync(id, cancellationToken);
                if (categoryResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(categoryResult.Error);
                }

                var category = categoryResult.Value;

                // 2. Check name uniqueness if it's being changed
                if (!string.IsNullOrWhiteSpace(request.Name) && request.Name != category.Name)
                {
                    var uniqueNameResult = await ValidateUniqueNameAsync(
                        request.Name,
                        id,
                        cancellationToken
                    );
                    if (uniqueNameResult.IsFailure)
                    {
                        return Result.Failure<CareCategoryResponse>(uniqueNameResult.Error);
                    }

                    if (!uniqueNameResult.Value)
                    {
                        return Result.Failure<CareCategoryResponse>(
                            Error.Conflict(
                                $"A care category with the name '{request.Name}' already exists."
                            )
                        );
                    }

                    category.Name = request.Name;
                }

                // 3. Apply other optional updates using null checks
                category.Description ??= string.Empty;
                category.Description = request.Description ?? category.Description;
                category.IsActive = request.IsActive ?? category.IsActive;
                category.PlatformFee = request.PlatformFee.GetValueOrDefault(category.PlatformFee);
                category.Icon = request.Icon ?? category.Icon;
                category.Color = request.Color ?? category.Color;

                // 4. Update audit fields
                category.UpdatedAt = DateTime.UtcNow;
                category.UpdatedBy = userId;

                // 5. Update and save
                var updateResult = await _categoryRepository.UpdateAsync(
                    category,
                    cancellationToken
                );
                if (updateResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(updateResult.Error);
                }

                var saveResult = await _unitOfWork.SaveChangesAsync(cancellationToken);
                if (saveResult.IsFailure)
                {
                    return Result.Failure<CareCategoryResponse>(saveResult.Error);
                }

                // 6. Return response
                return Result.Success(MapToResponse(category));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating care category with ID {CategoryId}", id);
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<Result> DeleteCategoryAsync(
            Guid id,
            Guid userId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                // Soft delete the category
                var deleteResult = await _categoryRepository.SoftDeleteAsync(
                    id,
                    userId,
                    cancellationToken
                );
                if (deleteResult.IsFailure)
                {
                    return Result.Failure(deleteResult.Error);
                }

                // Save changes
                var saveResult = await _unitOfWork.SaveChangesAsync(cancellationToken);
                if (saveResult.IsFailure)
                {
                    return Result.Failure(saveResult.Error);
                }

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting care category with ID {CategoryId}", id);
                return Result.Failure(Error.Internal(ex.Message));
            }
        }

        /// <inheritdoc />
        public async Task<
            Result<IEnumerable<CareProviderCategoryResponse>>
        > GetCategoriesByProviderIdAsync(
            Guid providerId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var providerCategoriesResult = await _context
                    .CareProviderCategories.Where(pc =>
                        pc.ProviderId == providerId && pc.HourlyRate > 0 && !pc.IsDeleted
                    )
                    .Include(pc => pc.CareCategory)
                    .ToListAsync(cancellationToken);

                if (providerCategoriesResult == null! || !providerCategoriesResult.Any())
                {
                    return Result.Failure<IEnumerable<CareProviderCategoryResponse>>(
                        Error.NotFound(
                            $"No care categories found for provider with ID {providerId}"
                        )
                    );
                }
                var categoryResponses = providerCategoriesResult
                    .Select(pc => new CareProviderCategoryResponse
                    {
                        Id = pc.CareCategory.Id,
                        Name = pc.CareCategory.Name,
                        Description = pc.ProviderSpecificDescription,
                        IsActive = pc.CareCategory.IsActive,
                        PlatformFee = pc.CareCategory.PlatformFee,
                        Icon = pc.CareCategory.Icon,
                        Color = pc.CareCategory.Color,
                        HourlyRate = pc.HourlyRate,
                        ExperienceYears = pc.ExperienceYears ?? 0,
                        CreatedAt = pc.CareCategory.CreatedAt,
                        UpdatedAt = pc.CareCategory.UpdatedAt,
                    })
                    .ToList();

                return Result.Success<IEnumerable<CareProviderCategoryResponse>>(categoryResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting care categories for provider with ID {ProviderId}",
                    providerId
                );
                return Result.Failure<IEnumerable<CareProviderCategoryResponse>>(
                    Error.Internal(ex.Message)
                );
            }
        }

        /// <inheritdoc />
        public async Task<Result<IEnumerable<CareCategoryResponse>>> BulkUpdateCategoriesAsync(
            BulkUpdateCareCategoriesRequest request,
            Guid userId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                _logger.LogInformation(
                    "Starting bulk update of {Count} care categories by user {UserId}",
                    request.Categories.Count,
                    userId
                );

                var updatedCategories = new List<CareCategoryResponse>();
                var errors = new List<string>();

                // Process each category update
                foreach (var updateItem in request.Categories)
                {
                    try
                    {
                        // Get existing category
                        var categoryResult = await _categoryRepository.GetByIdAsync(
                            updateItem.Id,
                            cancellationToken
                        );

                        if (categoryResult.IsFailure)
                        {
                            errors.Add($"Category with ID {updateItem.Id} not found");
                            continue;
                        }

                        var category = categoryResult.Value;

                        // Check name uniqueness if it's being changed
                        if (
                            !string.IsNullOrWhiteSpace(updateItem.Name)
                            && updateItem.Name != category.Name
                        )
                        {
                            var uniqueNameResult = await ValidateUniqueNameAsync(
                                updateItem.Name,
                                updateItem.Id,
                                cancellationToken
                            );
                            if (uniqueNameResult.IsFailure)
                            {
                                errors.Add(
                                    $"Category {updateItem.Id}: {uniqueNameResult.Error.Message}"
                                );
                                continue;
                            }

                            if (!uniqueNameResult.Value)
                            {
                                errors.Add(
                                    $"Category {updateItem.Id}: A category with the name '{updateItem.Name}' already exists."
                                );
                                continue;
                            }

                            category.Name = updateItem.Name;
                        }

                        // Apply partial updates only for provided fields
                        if (!string.IsNullOrWhiteSpace(updateItem.Description))
                        {
                            category.Description = updateItem.Description;
                        }

                        if (updateItem.IsActive.HasValue)
                        {
                            category.IsActive = updateItem.IsActive.Value;
                        }

                        if (updateItem.PlatformFee.HasValue)
                        {
                            category.PlatformFee = updateItem.PlatformFee.Value;
                        }

                        // Update audit fields
                        category.UpdatedAt = DateTime.UtcNow;
                        category.UpdatedBy = userId;

                        // Update the category
                        var updateResult = await _categoryRepository.UpdateAsync(
                            category,
                            cancellationToken
                        );
                        if (updateResult.IsFailure)
                        {
                            errors.Add(
                                $"Failed to update category {updateItem.Id}: {updateResult.Error.Message}"
                            );
                            continue;
                        }

                        updatedCategories.Add(MapToResponse(category));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error updating category {CategoryId}", updateItem.Id);
                        errors.Add($"Error updating category {updateItem.Id}: {ex.Message}");
                    }
                }

                // If there were errors and no successful updates, return failure
                if (errors.Any() && !updatedCategories.Any())
                {
                    var validationErrors = new Dictionary<string, string[]>
                    {
                        { "Categories", errors.ToArray() },
                    };

                    return Result.Failure<IEnumerable<CareCategoryResponse>>(
                        Error.Validation("Bulk update failed", validationErrors)
                    );
                }

                // Save all changes
                var saveResult = await _unitOfWork.SaveChangesAsync(cancellationToken);
                if (saveResult.IsFailure)
                {
                    return Result.Failure<IEnumerable<CareCategoryResponse>>(saveResult.Error);
                }

                _logger.LogInformation(
                    "Bulk update completed. Updated {UpdatedCount} categories, {ErrorCount} errors",
                    updatedCategories.Count,
                    errors.Count
                );

                // If there were partial errors, log them but still return success for updated categories
                if (errors.Any())
                {
                    _logger.LogWarning(
                        "Bulk update had partial failures: {Errors}",
                        string.Join("; ", errors)
                    );
                }

                return Result.Success<IEnumerable<CareCategoryResponse>>(updatedCategories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk update of care categories");
                return Result.Failure<IEnumerable<CareCategoryResponse>>(
                    Error.Internal(ex.Message)
                );
            }
        }

        /// <summary>
        /// Maps a CareCategory entity to a CareCategoryResponse
        /// </summary>
        private static CareCategoryResponse MapToResponse(CareCategory category)
        {
            return new CareCategoryResponse
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                IsActive = category.IsActive,
                PlatformFee = category.PlatformFee,
                Icon = category.Icon,
                Color = category.Color,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt,
            };
        }
    }
}
