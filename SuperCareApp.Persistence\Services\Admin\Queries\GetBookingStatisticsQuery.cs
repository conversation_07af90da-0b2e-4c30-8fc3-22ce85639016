﻿using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin.Queries;

public record GetBookingStatisticsQuery : IQuery<Result<BookingStatisticsResponse>>;

internal sealed class GetBookingStatisticsQueryHandler
    : IQueryHandler<GetBookingStatisticsQuery, Result<BookingStatisticsResponse>>
{
    private readonly ApplicationDbContext _context;

    public GetBookingStatisticsQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<BookingStatisticsResponse>> Handle(
        GetBookingStatisticsQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Fetch all required stats in as few queries as possible
            var bookings = _context.Bookings.Where(b => !b.IsDeleted);

            // Query 1: Total Bookings
            var totalBookings = await bookings.CountAsync(cancellationToken);

            // Query 2: Booking Status Counts
            var statusCounts = await bookings
                .Select(b => b.Status.Status)
                .GroupBy(status => status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count, cancellationToken);

            var pendingBookings = statusCounts.GetValueOrDefault(BookingStatusType.Requested, 0);
            var completedBookings = statusCounts.GetValueOrDefault(BookingStatusType.Completed, 0);
            var confirmedBookings = statusCounts.GetValueOrDefault(BookingStatusType.Confirmed, 0);

            // Build response
            var response = new BookingStatisticsResponse
            {
                TotalBookings = totalBookings,
                PendingBookings = pendingBookings,
                CompletedBookings = completedBookings,
                ConfirmedBookings = confirmedBookings,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            return Result.Failure<BookingStatisticsResponse>(
                Error.Internal($"Error retrieving booking statistics: {ex.Message}")
            );
        }
    }
}
