﻿namespace super_care_app.Shared.Constants;

public static class ApiRoutes
{
    public static class Auth
    {
        public const string Login = "login";
        public const string Logout = "logout";
        public const string Register = "register";
        public const string RegisterValidate = "register-validate";
        public const string RefreshToken = "refresh-token";
        public const string ForgotPassword = "forgot-password";
        public const string ValidateOtp = "validate-otp";
        public const string ResetPassword = "reset-password";
        public const string ChangePassword = "change-password";
        public const string GenerateOtp = "generate-otp";
        public const string VerifyOtp = "verify-otp";
    }

    public static class Account
    {
        public const string GetProfiles = "profiles";
        public const string GetProfile = "users/{userId:guid}/profile";
        public const string CreateProfile = "users/{userId:guid}/profile";
        public const string UpdateProfile = "users/{userId:guid}/profile";
        public const string DeleteProfile = "users/{userId:guid}/profile";

        // Address endpoints
        public const string GetAddresses = "users/{userId:guid}/addresses";
        public const string GetAddress = "users/{userId:guid}/address/{addressId:guid}";
        public const string CreateAddress = "users/{userId:guid}/address";
        public const string UpdateAddress = "users/{userId:guid}/address/{addressId:guid}";
        public const string DeleteAddress = "users/{userId:guid}/address/{addressId:guid}";
        public const string SetPrimaryAddress =
            "users/{userId:guid}/address/{addressId:guid}/primary";

        // Care category endpoints
        public const string AddCategory = "users/{userId:guid}/category";
        public const string AddCategories = "users/{userId:guid}/categories";
        public const string RemoveCategory = "users/{userId:guid}/category";

        // Document endpoints
        public const string GetDocuments = "users/{userId:guid}/documents";
        public const string GetDocument = "users/{userId:guid}/document/{documentId:guid}";
        public const string UploadDocument = "users/{userId:guid}/document";
        public const string UpdateDocument = "users/{userId:guid}/document/{documentId:guid}";
        public const string DeleteDocument = "users/{userId:guid}/document/{documentId:guid}";

        //Payment method endpoints
        public const string GetPaymentMethods = "users/{userId:guid}/payment-methods";
        public const string AddPaymentMethod = "users/{userId:guid}/payment-method";
    }

    public static class User
    {
        public const string ProfileUpdate = "{id:guid}";
        public const string GetById = "{id:guid}";
        public const string AddRole = "{id:guid}/roles";
        public const string RemoveRole = "{id:guid}/roles/{role}";
    }

    public static class CareProvider
    {
        public const string GetAll = "all";
        public const string GetById = "{id:guid}";
        public const string Create = "create";
        public const string Update = "{userId:guid}";
        public const string Delete = "{id:guid}";
    }

    public static class Bookings
    {
        // Availability endpoints
        public const string CreateAvailability = "availability";
        public const string UpdateAvailability = "availability/{availabilityId:guid}";
        public const string DeleteAvailability = "availability/{availabilityId:guid}";
        public const string GetAvailability = "providers/{providerId:guid}/availability";
        public const string GetAvailabilityForDate =
            "providers/{providerId:guid}/availability/{date:datetime}";
        public const string GetAvailabilityTemplate =
            "providers/{providerId:guid}/availability-template";
        public const string UpdateAvailabilityStatus = "availability/{availabilityId:guid}/status";
        public const string GetAvailableTimeSlots =
            "providers/{providerId:guid}/available-timeslots";
        public const string GetAllAvailabilities = "availabilities";
        public const string IsDateAvailable = "providers/{providerId:guid}/availability-check";
        public const string BulkUpdateAvailability = "availabilities/{providerId:guid}/bulk-update";

        // Leave endpoints
        public const string CreateLeave = "providers/{providerId:guid}/leave";
        public const string UpdateLeave = "providers/{providerId:guid}/leave/{leaveId:guid}";
        public const string DeleteLeave = "providers/{providerId:guid}/leave/{leaveId:guid}";
        public const string GetLeave = "providers/{providerId:guid}/leave/{leaveId:guid}";
        public const string GetProviderLeaves = "providers/{providerId:guid}/leaves";
        public const string GetMyLeaves = "leaves/my";
        public const string GetAllLeaves = "leaves";

        // Booking endpoints
        public const string CreateBooking = "create";
        public const string UpdateBooking = "{bookingId:guid}";
        public const string DeleteBooking = "{bookingId:guid}";
        public const string CancelBooking = "{bookingId:guid}/cancel";
        public const string GetBookingById = "{bookingId:guid}";
        public const string GetAllBookings = "all";
        public const string UpdateBookingStatus = "{bookingId:guid}/status";

        //Invoice endpoints
        public const string GetInvoice = "invoice/{invoiceId:guid}";
        public const string GetInvoicesForBooking = "booking/{bookingId:guid}/invoices";
        public const string GenerateInvoicesForBooking =
            "booking/{bookingWindowId:guid}/generate-invoices";
        public const string MarkInvoiceAsPaid = "invoice/{invoiceId:guid}/mark-paid";

        public const string GetUserInvoices = "invoices";
    }

    public static class Admin
    {
        public const string BookingStatistics = "booking-statistics";
        public const string CareProviderStatistics = "care-provider-statistics";
        public const string ClientStatistics = "client-statistics";
        public const string GetStatistics = "statistics";

        public const string GetUsers = "users";
        public const string GetUserProfiles = "user-profiles";
        public const string GetApprovalRequests = "approval-requests";
        public const string GetApprovalRequest = "approval-requests/{requestId:guid}";
        public const string ApproveRequest = "approval-requests/{requestId:guid}/approve";
        public const string RejectRequest = "approval-requests/{requestId:guid}/reject";

        // Document approval endpoints
        public const string GetPendingDocuments = "documents/pending";
        public const string ApproveDocument = "documents/{documentId:guid}/approve";
        public const string RejectDocument = "documents/{documentId:guid}/reject";
        public const string GetCareProviders = "care-providers";

        // Care provider management endpoints
        public const string SuspendProvider = "providers/{providerId:guid}/suspend";
        public const string RemoveSuspension = "providers/{providerId:guid}/remove-suspension";
        public const string ResetPassword = "reset-password";

        // Care category management endpoints
        public const string BulkUpdateCareCategories = "care-categories/bulk-update";

        // Admin profile management
        public const string GetProfile = "profile";
        public const string UpdateProfile = "profile";

        // Audit log endpoints
        public const string GetEntityAuditLogs = "audit/entity/{entityType}/{entityId:guid}";
        public const string GetUserAuditLogs = "audit/user/{userId:guid}";
        public const string GetAuditLogsByDateRange = "audit/logs";
        public const string GetAuditStatistics = "audit/statistics";

        // Dashboard endpoints
        public const string GetDashboardStatistics = "dashboard/statistics";
        public const string GetSystemOverview = "dashboard/overview";
        public const string GetUserAnalytics = "dashboard/user-analytics";
        public const string GetBookingAnalytics = "dashboard/booking-analytics";
        public const string GetFinancialAnalytics = "dashboard/financial-analytics";
        public const string GetNotificationAnalytics = "dashboard/notification-analytics";
        public const string GetWebSocketStatistics = "dashboard/websocket-stats";
        public const string GetTrendingData = "dashboard/trending/{metric}";
        public const string GetComparativeStatistics = "dashboard/comparative";
    }

    public static class Example
    {
        public const string Success = "success";
        public const string NotFound = "not-found";
    }

    public static class Categories
    {
        public const string GetAll = "";
        public const string GetById = "{id:guid}";
        public const string GetByProviderId = "provider/{providerId:guid}";
        public const string Create = "";
        public const string Update = "{id:guid}";
        public const string Delete = "{id:guid}";
        public const string Activate = "{id:guid}/activate";
        public const string Deactivate = "{id:guid}/deactivate";
        public const string UpdateByProviderId = "provider/{providerId:guid}/update/{id:guid}";
    }

    public static class Documents
    {
        public const string Upload = "";
        public const string GetAll = "";
        public const string GetAllCached = "cached";
        public const string GetById = "{documentId:guid}";
        public const string Update = "{documentId:guid}";
        public const string Delete = "{documentId:guid}";
    }

    /// <summary>
    /// Calendar API routes
    /// </summary>
    public static class Calendar
    {
        // Calendar endpoints
        public const string GetMonthlyCalendar = "monthly/{providerId:guid}/{monthsAhead:int}";
        public const string GetCalendarRange = "range/{providerId:guid}";
        public const string GetDayAvailability = "day/{providerId:guid}/{date:datetime}";
        public const string GetNextAvailableSlots = "next-available/{providerId:guid}";
        public const string CheckAvailability = "check-availability/{providerId:guid}";
        public const string GetCalendarSummary = "summary/{providerId:guid}/{year:int}/{month:int}";
        public const string GetAvailableProviders = "available-providers";
        public const string GetFilteredCalendar =
            "filtered/{providerId:guid}/{year:int}/{month:int}";
    }

    public static class TrackingSessions
    {
        // Tracking session management endpoints
        public const string StartSession = "bookings/{bookingId:guid}/start";
        public const string UpdateSession = "{sessionId:guid}";
        public const string EndSession = "{sessionId:guid}/end";
        public const string PauseSession = "{sessionId:guid}/pause";
        public const string ResumeSession = "{sessionId:guid}/resume";
        public const string GetSession = "{sessionId:guid}";
        public const string GetSessionsByBooking = "bookings/{bookingId:guid}";
        public const string GetSessionsByProvider = "providers/{providerId:guid}";
        public const string GetMySessions = "my-sessions";
    }

    public static class AvailabilityTemplate
    {
        // Availability template management endpoints
        public const string GetTemplate = "providers/{providerId:guid}/template";
        public const string GetMyTemplate = "my-template";
        public const string UpdateTemplate = "providers/{providerId:guid}/template";
        public const string ResetTemplate = "providers/{providerId:guid}/template/reset";
        public const string BulkUpdate = "providers/{providerId:guid}/template/bulk-update";
        public const string SetWeekdays = "providers/{providerId:guid}/template/weekdays";
        public const string SetWeekend = "providers/{providerId:guid}/template/weekend";
        public const string ValidateTemplate = "providers/{providerId:guid}/template/validate";
        public const string HasAvailability =
            "providers/{providerId:guid}/template/has-availability";
        public const string CreateDefault = "providers/{providerId:guid}/template/create-default";
    }

    public static class Reviews
    {
        public const string GetReview = "{providerId:guid}";
        public const string CreateReview = "{bookingId:guid}";
        public const string GetMyReviews = "my";
    }
}
