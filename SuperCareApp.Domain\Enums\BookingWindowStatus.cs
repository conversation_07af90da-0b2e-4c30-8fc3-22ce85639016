﻿namespace SuperCareApp.Domain.Enums;

public enum BookingWindowStatus
{
    Upcoming,
    Pending,
    Completed,
    Cancelled,
}

public static class BookingWindowStatusEnumExtensions
{
    public static string ToString(this BookingWindowStatus bookingWindowStatus)
    {
        return bookingWindowStatus switch
        {
            BookingWindowStatus.Upcoming => "Upcoming",
            BookingWindowStatus.Pending => "Pending",
            BookingWindowStatus.Completed => "Completed",
            BookingWindowStatus.Cancelled => "Cancelled",
            _ => throw new ArgumentOutOfRangeException(
                nameof(bookingWindowStatus),
                bookingWindowStatus,
                null
            ),
        };
    }

    public static BookingWindowStatus FromString(string statusString)
    {
        return statusString?.Trim() switch
        {
            "Upcoming" => BookingWindowStatus.Upcoming,
            "Pending" => BookingWindowStatus.Pending,
            "Completed" => BookingWindowStatus.Completed,
            "Cancelled" => BookingWindowStatus.Cancelled,
            _ => throw new ArgumentException(
                $"Invalid booking window status: '{statusString}'",
                nameof(statusString)
            ),
        };
    }
}
