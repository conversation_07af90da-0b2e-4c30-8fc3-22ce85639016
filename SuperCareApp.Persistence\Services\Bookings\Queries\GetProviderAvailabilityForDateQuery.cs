using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Entities.ValueObjects;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

/// <summary>
/// Query to get provider availability for a specific date
/// </summary>
/// <param name="ProviderId">The provider ID</param>
/// <param name="Date">The specific date to check availability for</param>
public record GetProviderAvailabilityForDateQuery(Guid ProviderId, DateOnly Date)
    : IQuery<Result<AvailabilityResponse>>;

/// <summary>
/// Handler for GetProviderAvailabilityForDateQuery
/// </summary>
public sealed class GetProviderAvailabilityForDateQueryHandler
    : IQueryHandler<GetProviderAvailabilityForDateQuery, Result<AvailabilityResponse>>
{
    private readonly ApplicationDbContext _db;
    private readonly ILogger<GetProviderAvailabilityForDateQueryHandler> _logger;
    private const string TimeFormat = "HH:mm";

    public GetProviderAvailabilityForDateQueryHandler(
        ApplicationDbContext db,
        ILogger<GetProviderAvailabilityForDateQueryHandler> logger
    )
    {
        _db = db;
        _logger = logger;
    }

    public async Task<Result<AvailabilityResponse>> Handle(
        GetProviderAvailabilityForDateQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var date = request.Date;
            // Fix 1.1: Use case-insensitive day comparison
            var dayOfWeek = date.DayOfWeek.ToString().ToLowerInvariant();

            // Fix 2.1: Use split query to prevent cartesian explosion
            var result = await (
                from provider in _db.Set<CareProviderProfile>().AsSplitQuery()
                where provider.Id == request.ProviderId
                select new
                {
                    ProviderId = provider.Id,
                    BufferDuration = provider.BufferDuration,

                    Availability = _db.Set<Availability>()
                        .Where(a =>
                            a.ProviderId == provider.Id && a.DayOfWeek.ToLower() == dayOfWeek
                        )
                        .Select(a => new
                        {
                            a.IsAvailable,
                            Slots = a.AvailabilitySlots.Select(s => new { s.StartTime, s.EndTime }),
                        })
                        .FirstOrDefault(),

                    BookingWindows = _db.Set<BookingWindow>()
                        .Where(b => b.Booking.ProviderId == provider.Id && b.Date == date)
                        .Select(b => new { b.StartTime, b.EndTime })
                        .ToList(),

                    Leaves = _db.Set<Leave>()
                        .Where(l =>
                            l.ProviderId == provider.Id
                            && date >= DateOnly.FromDateTime(l.StartDate)
                            && date <= DateOnly.FromDateTime(l.EndDate)
                        )
                        .Select(l => new { l.StartDate, l.EndDate })
                        .ToList(),
                }
            ).FirstOrDefaultAsync(cancellationToken);

            // Fix 3.2: Simplify null check
            if (result?.Availability?.IsAvailable != true)
                return Result<AvailabilityResponse>.Success(new AvailabilityResponse());

            // Get raw available ranges without any buffer reduction
            var availableRanges = result
                .Availability.Slots.Select(s => new TimeRange(s.StartTime, s.EndTime))
                .ToList();

            // Fix 1.2: Leaves should block entire calendar day
            var leaveRanges = result
                .Leaves.Where(leave =>
                    date >= DateOnly.FromDateTime(leave.StartDate)
                    && date <= DateOnly.FromDateTime(leave.EndDate)
                )
                .Select(_ => new TimeRange(TimeOnly.MinValue, TimeOnly.MaxValue))
                .ToList();

            // Fix: Apply buffer only to actual bookings, not to available ranges
            var bookingRangesWithBuffer = result
                .BookingWindows.Select(b => new TimeRange(
                    b.StartTime.AddMinutes(-result.BufferDuration), // Buffer before booking
                    b.EndTime.AddMinutes(result.BufferDuration) // Buffer after booking
                ))
                .ToList();

            // Combine all blockages (bookings with buffer + leaves)
            var allBlockages = bookingRangesWithBuffer.Concat(leaveRanges).ToList();

            // Subtract blockages from available ranges to get truly free time slots
            var freeRanges = TimeRange.SubtractMultiple(availableRanges, allBlockages);

            var sortedFreeRanges = freeRanges.OrderBy(slot => slot.Start).ToList();

            var response = new AvailabilityResponse
            {
                Id = result.ProviderId,
                Day = date.DayOfWeek.ToString(),
                Available = sortedFreeRanges.Any(),
                Slots = sortedFreeRanges
                    .Select(slot => new TimeSlotResponse
                    {
                        StartTime = slot.Start.ToString(TimeFormat),
                        EndTime = slot.End.ToString(TimeFormat),
                    })
                    .ToList(),
            };

            _logger.LogInformation(
                "Availability retrieved successfully for provider {ProviderId} on {Date}",
                request.ProviderId,
                request.Date
            );

            return Result<AvailabilityResponse>.Success(response);
        }
        catch (Exception ex)
        {
            // Fix 3.4: Use structured logging
            _logger.LogError(
                ex,
                "An error occurred while retrieving availability for provider {ProviderId} on {Date}",
                request.ProviderId,
                request.Date
            );
            return Result.Failure<AvailabilityResponse>(
                Error.Internal(
                    $"An internal error occurred while retrieving availability: {ex.Message}"
                )
            );
        }
    }
}
