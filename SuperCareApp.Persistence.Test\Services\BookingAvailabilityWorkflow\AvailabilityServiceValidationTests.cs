using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;
using AvailabilityEntity = SuperCareApp.Domain.Entities.Availability;

namespace SuperCareApp.Persistence.Test.Services.BookingAvailabilityWorkflow;

public class AvailabilityServiceValidationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly AvailabilityService _service;
    private readonly Mock<IBookingManagementService> _mockBookingService;
    private readonly Guid _providerId;
    private readonly Guid _userId;

    public AvailabilityServiceValidationTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _mockBookingService = new Mock<IBookingManagementService>();
        _service = new AvailabilityService(
            _context,
            _mockBookingService.Object,
            NullLogger<AvailabilityService>.Instance
        );

        _providerId = Guid.NewGuid();
        _userId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.CareProviderProfiles.Add(providerProfile);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Availability Validation Tests

    [Fact]
    public async Task AddAvailabilityAsync_WithInvalidDayOfWeek_ShouldFail()
    {
        // Arrange
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot { StartTime = new TimeOnly(9, 0), EndTime = new TimeOnly(17, 0) },
        };

        // Act
        var result = await _service.AddAvailabilityAsync(_userId, "InvalidDay", true, slots);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Invalid day of week", result.Error.Message);
    }

    [Fact]
    public async Task AddAvailabilityAsync_WithInvalidTimeSlot_ShouldFail()
    {
        // Arrange - End time before start time
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot
            {
                StartTime = new TimeOnly(17, 0),
                EndTime = new TimeOnly(9, 0), // Invalid: end before start
            },
        };

        // Act
        var result = await _service.AddAvailabilityAsync(_userId, "Monday", true, slots);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("start of the interval cannot be after its end", result.Error.Message);
    }

    [Fact]
    public async Task AddAvailabilityAsync_WithNonExistentProvider_ShouldFail()
    {
        // Arrange
        var nonExistentUserId = Guid.NewGuid();
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot { StartTime = new TimeOnly(9, 0), EndTime = new TimeOnly(17, 0) },
        };

        // Act
        var result = await _service.AddAvailabilityAsync(nonExistentUserId, "Monday", true, slots);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Provider profile not found", result.Error.Message);
    }

    [Fact]
    public async Task AddAvailabilityAsync_WithValidData_ShouldSucceed()
    {
        // Arrange
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot { StartTime = new TimeOnly(9, 0), EndTime = new TimeOnly(12, 0) },
            new AvailabilitySlot { StartTime = new TimeOnly(13, 0), EndTime = new TimeOnly(17, 0) },
        };

        // Act
        var result = await _service.AddAvailabilityAsync(_userId, "Monday", true, slots);

        // Assert
        Assert.True(result.IsSuccess);

        var availability = await _context
            .Availabilities.Include(a => a.AvailabilitySlots)
            .FirstOrDefaultAsync(a => a.Id == result.Value);

        Assert.NotNull(availability);
        Assert.Equal(_providerId, availability.ProviderId);
        Assert.Equal("Monday", availability.DayOfWeek);
        Assert.True(availability.IsAvailable);
        Assert.Equal(2, availability.AvailabilitySlots.Count);
    }

    [Fact]
    public async Task UpdateAvailabilityAsync_WithInvalidTimeSlot_ShouldFail()
    {
        // Arrange
        var availability = await CreateTestAvailability();

        var invalidSlots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot
            {
                StartTime = new TimeOnly(17, 0),
                EndTime = new TimeOnly(9, 0), // Invalid: end before start
            },
        };

        // Act
        var result = await _service.UpdateAvailabilityAsync(
            _userId,
            availability.Id,
            true,
            invalidSlots
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("start of the interval cannot be after its end", result.Error.Message);
    }

    [Fact]
    public async Task UpdateAvailabilityAsync_WithNonExistentAvailability_ShouldFail()
    {
        // Arrange
        var nonExistentAvailabilityId = Guid.NewGuid();
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot { StartTime = new TimeOnly(9, 0), EndTime = new TimeOnly(17, 0) },
        };

        // Act
        var result = await _service.UpdateAvailabilityAsync(
            _userId,
            nonExistentAvailabilityId,
            true,
            slots
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Availability not found", result.Error.Message);
    }

    [Fact]
    public async Task UpdateAvailabilityAsync_WithValidData_ShouldSucceed()
    {
        // Arrange
        var availability = await CreateTestAvailability();

        var updatedSlots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot { StartTime = new TimeOnly(8, 0), EndTime = new TimeOnly(18, 0) },
        };

        // Act
        var result = await _service.UpdateAvailabilityAsync(
            _userId,
            availability.Id,
            false,
            updatedSlots
        );

        // Assert
        Assert.True(result.IsSuccess);

        var updatedAvailability = await _context
            .Availabilities.Include(a => a.AvailabilitySlots)
            .FirstOrDefaultAsync(a => a.Id == availability.Id);

        Assert.NotNull(updatedAvailability);
        Assert.False(updatedAvailability.IsAvailable);
        Assert.Single(updatedAvailability.AvailabilitySlots);
        Assert.Equal(new TimeOnly(8, 0), updatedAvailability.AvailabilitySlots.First().StartTime);
        Assert.Equal(new TimeOnly(18, 0), updatedAvailability.AvailabilitySlots.First().EndTime);
    }

    #endregion

    #region Bulk Update Validation Tests

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithInvalidDayOfWeek_ShouldFail()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "InvalidDay",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            30,
            null,
            null
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Invalid day of week", result.Error.Message);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithNegativeBufferDuration_ShouldFail()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            -10,
            null,
            null
        ); // Negative buffer

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Buffer duration cannot be negative", result.Error.Message);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithNegativeWorkingHours_ShouldFail()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            30,
            null,
            -5
        ); // Negative working hours

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Working hours per day cannot be negative", result.Error.Message);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithInvalidTimeSlots_ShouldFail()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(17, 0),
                        EndTime = new TimeOnly(9, 0), // Invalid: end before start
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            30,
            null,
            null
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("start of the interval cannot be after its end", result.Error.Message);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithNonExistentProvider_ShouldFail()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            nonExistentProviderId,
            availabilityUpdates,
            30,
            null,
            null
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Provider profile not found", result.Error.Message);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithValidData_ShouldSucceed()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(12, 0),
                    },
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(13, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
            ("Tuesday", false, new List<AvailabilitySlot>()),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            45,
            true,
            10
        );

        // Assert
        Assert.True(result.IsSuccess);

        // Verify Monday availability
        var mondayAvailability = await _context
            .Availabilities.Include(a => a.AvailabilitySlots)
            .FirstOrDefaultAsync(a => a.ProviderId == _providerId && a.DayOfWeek == "Monday");

        Assert.NotNull(mondayAvailability);
        Assert.True(mondayAvailability.IsAvailable);
        Assert.Equal(2, mondayAvailability.AvailabilitySlots.Count);

        // Verify Tuesday availability
        var tuesdayAvailability = await _context
            .Availabilities.Include(a => a.AvailabilitySlots)
            .FirstOrDefaultAsync(a => a.ProviderId == _providerId && a.DayOfWeek == "Tuesday");

        Assert.NotNull(tuesdayAvailability);
        Assert.False(tuesdayAvailability.IsAvailable);

        // Verify provider profile updates
        var updatedProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
            p.Id == _providerId
        );

        Assert.NotNull(updatedProfile);
        Assert.Equal(45, updatedProfile.BufferDuration);
        Assert.Equal(10, updatedProfile.WorkingHours);
    }

    #endregion

    #region Leave Validation Tests

    [Fact]
    public async Task AddLeaveAsync_WithInvalidDateRange_ShouldFail()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(3),
            EndDate = DateTime.Today.AddDays(1), // End before start
            Reason = "Invalid leave",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("End date must be after start date", result.Error.Message);
    }

    [Fact]
    public async Task AddLeaveAsync_WithPastStartDate_ShouldFail()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(-1), // Past date
            EndDate = DateTime.Today.AddDays(1),
            Reason = "Past leave",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Start date cannot be in the past", result.Error.Message);
    }

    [Fact]
    public async Task AddLeaveAsync_WithTodayAsStartDate_ShouldSucceed()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today, // Today should be valid
            EndDate = DateTime.Today.AddDays(1),
            Reason = "Emergency leave",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task AddLeaveAsync_WithSameDayLeave_ShouldSucceed()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(1), // Same day leave
            Reason = "Doctor appointment",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.True(result.IsSuccess);

        var leave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == result.Value);
        Assert.NotNull(leave);
        Assert.Equal(leaveRequest.StartDate, leave.StartDate);
        Assert.Equal(leaveRequest.EndDate, leave.EndDate);
    }

    [Fact]
    public async Task AddLeaveAsync_WithEmptyReason_ShouldSucceed()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = null, // Empty reason should be allowed
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.True(result.IsSuccess);

        var leave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == result.Value);
        Assert.NotNull(leave);
        Assert.Null(leave.Reason);
    }

    [Fact]
    public async Task AddLeaveAsync_WithLongReason_ShouldSucceed()
    {
        // Arrange
        var longReason = new string('A', 500); // Very long reason
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = longReason,
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.True(result.IsSuccess);

        var leave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == result.Value);
        Assert.NotNull(leave);
        Assert.Equal(longReason, leave.Reason);
    }

    #endregion

    #region Edge Cases and Boundary Tests

    [Fact]
    public async Task AddAvailabilityAsync_WithMidnightTimes_ShouldSucceed()
    {
        // Arrange
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot
            {
                StartTime = new TimeOnly(0, 0), // Midnight
                EndTime = new TimeOnly(23, 59), // End of day
            },
        };

        // Act
        var result = await _service.AddAvailabilityAsync(_userId, "Monday", true, slots);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task AddAvailabilityAsync_WithSameStartAndEndTime_ShouldSucceed()
    {
        // Arrange
        var slots = new List<AvailabilitySlot>
        {
            new AvailabilitySlot
            {
                StartTime = new TimeOnly(12, 0),
                EndTime = new TimeOnly(12, 0), // Same time (zero duration)
            },
        };

        // Act
        var result = await _service.AddAvailabilityAsync(_userId, "Monday", true, slots);

        // Assert
        Assert.True(result.IsSuccess);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithZeroBufferDuration_ShouldSucceed()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            0,
            null,
            null
        ); // Zero buffer

        // Assert
        Assert.True(result.IsSuccess);

        var updatedProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
            p.Id == _providerId
        );

        Assert.NotNull(updatedProfile);
        Assert.Equal(0, updatedProfile.BufferDuration);
    }

    [Fact]
    public async Task BulkUpdateAvailabilityByDayOfWeekAsync_WithZeroWorkingHours_ShouldSucceed()
    {
        // Arrange
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            _providerId,
            availabilityUpdates,
            30,
            null,
            0
        ); // Zero working hours

        // Assert
        Assert.True(result.IsSuccess);

        var updatedProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
            p.Id == _providerId
        );

        Assert.NotNull(updatedProfile);
        Assert.Equal(0, updatedProfile.WorkingHours);
    }

    #endregion

    #region Helper Methods

    private async Task<AvailabilityEntity> CreateTestAvailability()
    {
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Monday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);
        await _context.SaveChangesAsync();

        return availability;
    }

    #endregion
}
