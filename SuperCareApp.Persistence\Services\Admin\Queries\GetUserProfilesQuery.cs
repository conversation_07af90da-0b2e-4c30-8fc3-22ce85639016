﻿using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin.Queries
{
    /// <summary>
    /// Query to get user profiles for admin
    /// </summary>
    public record GetUserProfilesQuery(UserProfileListParams Parameters)
        : IQuery<Result<PagedUserProfileList>>;

    /// <summary>
    /// Handler for GetUserProfilesQuery
    /// </summary>
    public class GetUserProfilesQueryHandler
        : IQueryHandler<GetUserProfilesQuery, Result<PagedUserProfileList>>
    {
        private readonly ApplicationDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<GetUserProfilesQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetUserProfilesQueryHandler(
            ApplicationDbContext context,
            IHttpContextAccessor httpContextAccessor,
            ILogger<GetUserProfilesQueryHandler> logger
        )
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        /// <summary>
        /// Handle the query
        /// </summary>
        public async Task<Result<PagedUserProfileList>> Handle(
            GetUserProfilesQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var parameters = request.Parameters;

                // Start with a query for all non-deleted users
                var query = _context.Users.Include(u => u.UserProfile).Where(u => !u.IsDeleted);

                // Apply search filter if provided
                if (!string.IsNullOrWhiteSpace(parameters.SearchTerm))
                {
                    var searchTerm = parameters.SearchTerm.ToLower();
                    query = query.Where(u =>
                        u.Email.ToLower().Contains(searchTerm)
                        || u.UserName.ToLower().Contains(searchTerm)
                        || (
                            u.UserProfile != null
                            && (
                                (
                                    u.UserProfile.FirstName != null
                                    && u.UserProfile.FirstName.ToLower().Contains(searchTerm)
                                )
                                || (
                                    u.UserProfile.LastName != null
                                    && u.UserProfile.LastName.ToLower().Contains(searchTerm)
                                )
                                || (
                                    u.UserProfile.PhoneNumber != null
                                    && u.UserProfile.PhoneNumber.Contains(searchTerm)
                                )
                            )
                        )
                    );
                }

                // Always filter to only include care providers
                query = query.Where(u =>
                    _context.CareProviderProfiles.Any(cp => cp.UserId == u.Id && !cp.IsDeleted)
                );

                // Apply verification status filter if provided
                if (parameters.VerificationStatus.HasValue)
                {
                    query = query.Where(u =>
                        _context.CareProviderProfiles.Any(cp =>
                            cp.UserId == u.Id
                            && !cp.IsDeleted
                            && cp.VerificationStatus == parameters.VerificationStatus.Value
                        )
                    );
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(parameters.SortBy))
                {
                    string sortExpression;
                    switch (parameters.SortBy.ToLower())
                    {
                        case "name":
                            sortExpression = parameters.SortDescending
                                ? "UserProfile.FirstName DESC, UserProfile.LastName DESC"
                                : "UserProfile.FirstName ASC, UserProfile.LastName ASC";
                            break;
                        case "email":
                            sortExpression = parameters.SortDescending ? "Email DESC" : "Email ASC";
                            break;
                        case "createdat":
                            sortExpression = parameters.SortDescending
                                ? "CreatedAt DESC"
                                : "CreatedAt ASC";
                            break;
                        case "lastlogin":
                            sortExpression = parameters.SortDescending
                                ? "LastLogin DESC"
                                : "LastLogin ASC";
                            break;
                        default:
                            sortExpression = parameters.SortDescending
                                ? "CreatedAt DESC"
                                : "CreatedAt ASC";
                            break;
                    }
                    query = query.OrderBy(sortExpression);
                }
                else
                {
                    // Default sorting by creation date (newest first)
                    query = query.OrderByDescending(u => u.CreatedAt);
                }

                // Get total count for pagination
                var totalCount = await query.CountAsync(cancellationToken);

                // Apply pagination
                var users = await query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync(cancellationToken);

                // Create response
                var response = new PagedUserProfileList
                {
                    PageNumber = parameters.PageNumber,
                    PageSize = parameters.PageSize,
                    TotalCount = totalCount,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)parameters.PageSize),
                };

                // Map users to response model
                foreach (var user in users)
                {
                    var profileResponse = new AdminUserProfileResponse
                    {
                        UserId = user.Id,
                        Email = user.Email,
                        EmailVerified = user.EmailVerified,
                        IsActive = user.IsActive,
                        LastLogin = user.LastLogin,
                        CreatedAt = user.CreatedAt,
                        Roles = new List<string>(), // Get roles from UserManager in a real implementation
                    };

                    // Add user profile data if available
                    if (user.UserProfile != null)
                    {
                        profileResponse.Name =
                            $"{user.UserProfile.FirstName} {user.UserProfile.LastName}".Trim();
                        profileResponse.PhoneNumber = user.UserProfile.PhoneNumber;
                        profileResponse.Gender = user.UserProfile.Gender;
                        profileResponse.DateOfBirth = user.UserProfile.DateOfBirth;

                        // Add profile picture URL if available
                        if (!string.IsNullOrEmpty(user.UserProfile.ImagePath))
                        {
                            var httpRequest = _httpContextAccessor.HttpContext?.Request;
                            if (httpRequest != null)
                            {
                                var baseUrl =
                                    $"{httpRequest.Scheme}://{httpRequest.Host}{httpRequest.PathBase}";
                                profileResponse.ProfilePictureUrl =
                                    $"{baseUrl}/{user.UserProfile.ImagePath}";
                            }
                        }
                    }

                    // Check if user is a care provider and add provider-specific data
                    var careProvider = await _context
                        .CareProviderProfiles.Include(cp =>
                            cp.CareProviderCategories.Where(cpc => cpc.HourlyRate > 0)
                        )
                        .ThenInclude(cpc => cpc.CareCategory)
                        .AsSplitQuery()
                        .FirstOrDefaultAsync(
                            cp => cp.UserId == user.Id && !cp.IsDeleted,
                            cancellationToken
                        );

                    if (careProvider != null)
                    {
                        profileResponse.IsCareProvider = true;
                        profileResponse.ProviderId = careProvider.Id;
                        profileResponse.YearsExperience = careProvider.YearsExperience;
                        profileResponse.VerificationStatus =
                            careProvider.VerificationStatus.GetDescription();
                    }
                    else
                    {
                        profileResponse.IsCareProvider = false;
                    }

                    response.Profiles.Add(profileResponse);
                }

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user profiles");
                return Result.Failure<PagedUserProfileList>(
                    Error.Internal("Error retrieving user profiles")
                );
            }
        }
    }
}
