﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Address
{
    public class UpdateAddressRequest
    {
        public Guid AddressId { get; set; }
        public string? StreetAddress { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool? IsPrimary { get; set; }
        public string? Label { get; set; }
    }

    public class UpdateAddressRequestValidator : AbstractValidator<UpdateAddressRequest>
    {
        public UpdateAddressRequestValidator()
        {
            RuleFor(x => x.AddressId).NotEmpty().WithMessage("Address ID is required.");

            RuleFor(x => x.StreetAddress)
                .MaximumLength(200)
                .WithMessage("Street address cannot exceed 200 characters.")
                .When(x => !string.IsNullOrEmpty(x.StreetAddress));

            RuleFor(x => x.City)
                .MaximumLength(100)
                .WithMessage("City cannot exceed 100 characters.")
                .When(x => !string.IsNullOrEmpty(x.City));

            RuleFor(x => x.State)
                .MaximumLength(100)
                .WithMessage("State cannot exceed 100 characters.")
                .When(x => !string.IsNullOrEmpty(x.State));

            RuleFor(x => x.PostalCode)
                .Matches(@"^\d{5}(-\d{4})?$")
                .WithMessage("Postal code must be a valid format (e.g., 12345 or 12345-6789).")
                .When(x => !string.IsNullOrEmpty(x.PostalCode));

            RuleFor(x => x.Latitude)
                .InclusiveBetween(-90, 90)
                .WithMessage("Latitude must be between -90 and 90 degrees.")
                .When(x => x.Latitude.HasValue);

            RuleFor(x => x.Longitude)
                .InclusiveBetween(-180, 180)
                .WithMessage("Longitude must be between -180 and 180 degrees.")
                .When(x => x.Longitude.HasValue);

            RuleFor(x => x.Label)
                .MaximumLength(50)
                .WithMessage("Label cannot exceed 50 characters.")
                .When(x => !string.IsNullOrEmpty(x.Label));
        }
    }
}
