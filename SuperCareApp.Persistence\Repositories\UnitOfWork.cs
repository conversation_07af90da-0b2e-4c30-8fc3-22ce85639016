﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Repositories
{
    /// <summary>
    /// Unit of work implementation for managing transactions and repositories
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UnitOfWork> _logger;
        private IDbContextTransaction? _transaction;
        private readonly Dictionary<Type, object> _repositories = new();
        private bool _disposed;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">The database context</param>
        /// <param name="logger">The logger</param>
        public UnitOfWork(ApplicationDbContext context, ILogger<UnitOfWork> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Gets a repository for the specified entity type
        /// </summary>
        /// <typeparam name="TEntity">The entity type</typeparam>
        /// <returns>The repository</returns>
        public IRepository<TEntity> Repository<TEntity>()
            where TEntity : BaseEntity
        {
            if (_repositories.ContainsKey(typeof(TEntity)))
            {
                return (IRepository<TEntity>)_repositories[typeof(TEntity)];
            }

            var repository = new Repository<TEntity, ApplicationDbContext>(_context);
            _repositories.Add(typeof(TEntity), repository);
            return repository;
        }

        /// <summary>
        /// Begins a transaction
        /// </summary>
        public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        }

        /// <summary>
        /// Commits the transaction
        /// </summary>
        public async Task<Result> CommitTransactionAsync(
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                if (_transaction == null)
                {
                    return Result.Failure(Error.Internal("No active transaction to commit"));
                }

                await _transaction.CommitAsync(cancellationToken);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error committing transaction");
                return Result.Failure(
                    Error.Internal($"Error committing transaction: {ex.Message}")
                );
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                    _transaction = null;
                }
            }
        }

        /// <summary>
        /// Rolls back the transaction
        /// </summary>
        public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (_transaction != null)
                {
                    await _transaction.RollbackAsync(cancellationToken);
                }
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                    _transaction = null;
                }
            }
        }

        /// <summary>
        /// Saves all changes made in this context to the database
        /// </summary>
        public async Task<Result<int>> SaveChangesAsync(
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var result = await _context.SaveChangesAsync(cancellationToken);
                return Result.Success(result);
            }
            catch (DbUpdateConcurrencyException ex)
            {
                _logger.LogError(ex, "Concurrency error occurred while saving changes");
                return Result.Failure<int>(
                    Error.Conflict("A concurrency error occurred while saving changes")
                );
            }
            catch (DbUpdateException ex)
            {
                _logger.LogError(ex, "Database error occurred while saving changes");
                return Result.Failure<int>(
                    Error.Internal(
                        $"Database error occurred while saving changes: {ex.InnerException?.Message ?? ex.Message}"
                    )
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while saving changes");
                return Result.Failure<int>(
                    Error.Internal($"Error occurred while saving changes: {ex.Message}")
                );
            }
        }

        /// <summary>
        /// Disposes the unit of work
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the unit of work
        /// </summary>
        /// <param name="disposing">Whether to dispose managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _transaction?.Dispose();
                    _context.Dispose();
                }

                _disposed = true;
            }
        }
    }
}
