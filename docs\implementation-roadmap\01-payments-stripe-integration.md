# 💳 Stripe Payment Integration Implementation Plan

## Overview

This document outlines the implementation plan for integrating Stripe payment processing into the SuperCare application. The integration will handle secure payments between clients and care providers with platform fee collection.

## 🎯 Objectives

- **Secure Payment Processing**: Handle credit card payments securely through Stripe
- **Platform Fee Collection**: Automatically collect platform fees from transactions
- **Multi-party Payments**: Split payments between platform and care providers
- **Payment Tracking**: Comprehensive payment history and status tracking
- **Refund Management**: Handle refunds and disputes
- **Subscription Support**: Support for recurring payments (future enhancement)

## 🏗️ Architecture Overview

```
Client App → API Gateway → Payment Service → Stripe API
                ↓
         Database (Payment Records)
                ↓
         Webhook Handler ← Stripe Webhooks
```

## 📋 Implementation Phases

### Phase 1: Core Payment Infrastructure (Week 1-2)

#### 1.1 Stripe Configuration
```csharp
// appsettings.json
{
  "Stripe": {
    "PublishableKey": "pk_test_...",
    "SecretKey": "sk_test_...",
    "WebhookSecret": "whsec_...",
    "ConnectClientId": "ca_...",
    "PlatformFeePercentage": 10.0,
    "Currency": "USD"
  }
}
```

#### 1.2 Domain Models
- **Payment Entity**: Core payment record
- **PaymentIntent Entity**: Stripe payment intent tracking
- **PaymentMethod Entity**: Stored payment methods
- **Refund Entity**: Refund tracking
- **StripeAccount Entity**: Connected account management

#### 1.3 Service Interfaces
```csharp
public interface IPaymentService
{
    Task<Result<PaymentIntentResponse>> CreatePaymentIntentAsync(CreatePaymentRequest request);
    Task<Result<PaymentResponse>> ProcessPaymentAsync(ProcessPaymentRequest request);
    Task<Result<RefundResponse>> ProcessRefundAsync(RefundRequest request);
    Task<Result<PaymentMethodResponse>> SavePaymentMethodAsync(SavePaymentMethodRequest request);
}

public interface IStripeWebhookService
{
    Task<Result> HandleWebhookAsync(string payload, string signature);
}
```

### Phase 2: Stripe Connect Integration (Week 3-4)

#### 2.1 Connected Accounts for Care Providers
- **Account Creation**: Automatic Stripe Connect account creation for providers
- **Onboarding Flow**: KYC/verification process integration
- **Account Management**: Update account information and capabilities

#### 2.2 Split Payments
```csharp
public class SplitPaymentRequest
{
    public decimal TotalAmount { get; set; }
    public decimal PlatformFeeAmount { get; set; }
    public string ConnectedAccountId { get; set; }
    public string PaymentMethodId { get; set; }
    public Guid BookingId { get; set; }
}
```

### Phase 3: Advanced Features (Week 5-6)

#### 3.1 Payment Methods Management
- **Card Storage**: Securely store customer payment methods
- **Multiple Cards**: Support multiple payment methods per user
- **Default Payment Method**: Set and manage default payment options

#### 3.2 Subscription Support
- **Recurring Bookings**: Support for regular care schedules
- **Subscription Management**: Create, update, cancel subscriptions
- **Proration**: Handle mid-cycle changes

## 🔧 Technical Implementation

### Database Schema

```sql
-- Payment Intent Table
CREATE TABLE PaymentIntents (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    StripePaymentIntentId NVARCHAR(255) NOT NULL,
    BookingId UNIQUEIDENTIFIER NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    ProviderId UNIQUEIDENTIFIER NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    PlatformFee DECIMAL(18,2) NOT NULL,
    ProviderAmount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(3) NOT NULL DEFAULT 'USD',
    Status NVARCHAR(50) NOT NULL,
    CreatedAt DATETIME2 NOT NULL,
    UpdatedAt DATETIME2 NOT NULL,
    
    FOREIGN KEY (BookingId) REFERENCES Bookings(Id),
    FOREIGN KEY (ClientId) REFERENCES Users(Id),
    FOREIGN KEY (ProviderId) REFERENCES Users(Id)
);

-- Stripe Connected Accounts
CREATE TABLE StripeConnectedAccounts (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    UserId UNIQUEIDENTIFIER NOT NULL,
    StripeAccountId NVARCHAR(255) NOT NULL,
    AccountStatus NVARCHAR(50) NOT NULL,
    ChargesEnabled BIT NOT NULL DEFAULT 0,
    PayoutsEnabled BIT NOT NULL DEFAULT 0,
    OnboardingCompleted BIT NOT NULL DEFAULT 0,
    CreatedAt DATETIME2 NOT NULL,
    UpdatedAt DATETIME2 NOT NULL,
    
    FOREIGN KEY (UserId) REFERENCES Users(Id),
    UNIQUE(StripeAccountId)
);

-- Payment Methods
CREATE TABLE PaymentMethods (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    UserId UNIQUEIDENTIFIER NOT NULL,
    StripePaymentMethodId NVARCHAR(255) NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    Last4 NVARCHAR(4),
    Brand NVARCHAR(50),
    ExpiryMonth INT,
    ExpiryYear INT,
    IsDefault BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL,
    
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);
```

### Service Implementation Structure

```csharp
public class StripePaymentService : IPaymentService
{
    private readonly StripeClient _stripeClient;
    private readonly IPaymentRepository _paymentRepository;
    private readonly IStripeConnectService _connectService;
    private readonly ILogger<StripePaymentService> _logger;
    private readonly StripeConfiguration _config;

    // Core payment processing methods
    public async Task<Result<PaymentIntentResponse>> CreatePaymentIntentAsync(CreatePaymentRequest request)
    {
        // 1. Validate booking and amounts
        // 2. Calculate platform fee
        // 3. Create Stripe PaymentIntent with application_fee
        // 4. Store payment intent in database
        // 5. Return client secret for frontend
    }

    public async Task<Result<PaymentResponse>> ProcessPaymentAsync(ProcessPaymentRequest request)
    {
        // 1. Confirm PaymentIntent
        // 2. Update payment status
        // 3. Trigger booking confirmation
        // 4. Send notifications
    }
}
```

## 🔒 Security Considerations

### 1. PCI Compliance
- **No Card Data Storage**: Never store raw card data
- **Stripe Elements**: Use Stripe's secure card input elements
- **Tokenization**: All card data handled via Stripe tokens

### 2. Webhook Security
```csharp
public class StripeWebhookController : BaseController
{
    [HttpPost("stripe/webhook")]
    public async Task<IActionResult> HandleWebhook()
    {
        var payload = await new StreamReader(Request.Body).ReadToEndAsync();
        var signature = Request.Headers["Stripe-Signature"];
        
        try
        {
            var stripeEvent = EventUtility.ConstructEvent(
                payload, signature, _webhookSecret);
            
            await _webhookService.HandleWebhookAsync(stripeEvent);
            return Ok();
        }
        catch (StripeException)
        {
            return BadRequest();
        }
    }
}
```

### 3. API Security
- **Rate Limiting**: Implement rate limits on payment endpoints
- **Idempotency**: Use idempotency keys for payment operations
- **Audit Logging**: Log all payment-related activities

## 📱 Frontend Integration

### Payment Flow
1. **Client selects service** → Booking creation
2. **Payment method selection** → Stripe Elements integration
3. **Payment confirmation** → PaymentIntent confirmation
4. **Success/Failure handling** → Booking status update

### Stripe Elements Integration
```javascript
// Frontend payment form
const stripe = Stripe('pk_test_...');
const elements = stripe.elements();
const cardElement = elements.create('card');

// Handle payment submission
const handlePayment = async (paymentIntentClientSecret) => {
    const {error, paymentIntent} = await stripe.confirmCardPayment(
        paymentIntentClientSecret,
        {
            payment_method: {
                card: cardElement,
                billing_details: {
                    name: 'Customer Name'
                }
            }
        }
    );
    
    if (error) {
        // Handle error
    } else if (paymentIntent.status === 'succeeded') {
        // Payment successful
    }
};
```

## 🧪 Testing Strategy

### 1. Unit Tests
- Payment service methods
- Webhook handling logic
- Fee calculation algorithms

### 2. Integration Tests
- Stripe API integration
- Database operations
- End-to-end payment flows

### 3. Test Cards
```csharp
public static class StripeTestCards
{
    public const string VisaSuccess = "****************";
    public const string VisaDeclined = "****************";
    public const string MastercardSuccess = "****************";
    public const string AmexSuccess = "***************";
}
```

## 📊 Monitoring & Analytics

### Key Metrics
- **Payment Success Rate**: Track successful vs failed payments
- **Platform Revenue**: Monitor platform fee collection
- **Provider Earnings**: Track provider payment amounts
- **Refund Rate**: Monitor refund frequency and amounts

### Dashboards
- Real-time payment processing status
- Daily/monthly revenue reports
- Provider payout summaries
- Failed payment analysis

## 🚀 Deployment Considerations

### Environment Configuration
- **Development**: Use Stripe test keys
- **Staging**: Use Stripe test keys with production-like data
- **Production**: Use Stripe live keys with proper security

### Webhook Endpoints
- Configure webhook endpoints in Stripe Dashboard
- Ensure webhook endpoint security and reliability
- Implement webhook retry logic for failed deliveries

## 📈 Future Enhancements

1. **Multi-currency Support**: Support payments in different currencies
2. **Alternative Payment Methods**: Apple Pay, Google Pay, ACH transfers
3. **Marketplace Features**: Advanced split payment scenarios
4. **Subscription Management**: Recurring payment plans
5. **Dispute Management**: Automated chargeback handling
6. **Analytics Dashboard**: Advanced payment analytics and reporting

## 🔗 Dependencies

- **Stripe.net**: Official Stripe .NET SDK
- **Entity Framework**: Database operations
- **Background Jobs**: For webhook processing and retries
- **Logging**: Comprehensive payment operation logging
- **Monitoring**: Payment processing metrics and alerts

## 📚 Resources

- [Stripe Connect Documentation](https://stripe.com/docs/connect)
- [Stripe Payment Intents API](https://stripe.com/docs/payments/payment-intents)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
- [PCI Compliance Guidelines](https://stripe.com/docs/security)
