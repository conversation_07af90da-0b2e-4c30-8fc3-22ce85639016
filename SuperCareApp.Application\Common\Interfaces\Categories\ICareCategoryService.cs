﻿using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Categories
{
    /// <summary>
    /// Service interface for care categories
    /// </summary>
    public interface ICareCategoryService
    {
        /// <summary>
        /// Gets all care categories
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of care categories</returns>
        Task<Result<IEnumerable<CareCategoryResponse>>> GetAllCategoriesAsync(
            bool includeInactive = false,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets care categories with pagination
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="includeInactive">Whether to include inactive categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paginated list of care categories</returns>
        Task<Result<PagedCategoryList>> GetPaginatedCategoriesAsync(
            int pageNumber,
            int pageSize,
            bool includeInactive = false,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Validates if a category name is unique
        /// /// </summary>
        /// <param name="name">The category name to validate</param>
        /// <param name="excludeId">Optional ID to exclude from validation (for updates)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result</returns>
        Task<Result<bool>> ValidateUniqueNameAsync(
            string name,
            Guid? excludeId = null,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets a care category by ID
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The care category if found</returns>
        Task<Result<CareCategoryResponse>> GetCategoryByIdAsync(
            Guid id,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Creates a new care category
        /// </summary>
        /// <param name="request">The create category request</param>
        /// <param name="userId">The ID of the user creating the category</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created care category</returns>
        Task<Result<CareCategoryResponse>> CreateCategoryAsync(
            CreateCareCategoryRequest request,
            Guid userId,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Updates an existing care category
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <param name="request">The update category request</param>
        /// <param name="userId">The ID of the user updating the category</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated care category</returns>
        Task<Result<CareCategoryResponse>> UpdateCategoryAsync(
            Guid id,
            UpdateCareCategoryRequest request,
            Guid userId,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Deletes a care category
        /// </summary>
        /// <param name="id">The category ID</param>
        /// <param name="userId">The ID of the user deleting the category</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success or failure result</returns>
        Task<Result> DeleteCategoryAsync(
            Guid id,
            Guid userId,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets care categories for a specific provider
        /// </summary>
        /// <param name="providerId">The provider ID</param>
        /// /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of care categories for the provider</returns>
        Task<Result<IEnumerable<CareProviderCategoryResponse>>> GetCategoriesByProviderIdAsync(
            Guid providerId,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Bulk updates multiple care categories
        /// </summary>
        /// <param name="request">The bulk update request containing multiple category updates</param>
        /// <param name="userId">The ID of the user performing the bulk update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of updated care categories</returns>
        Task<Result<IEnumerable<CareCategoryResponse>>> BulkUpdateCategoriesAsync(
            BulkUpdateCareCategoriesRequest request,
            Guid userId,
            CancellationToken cancellationToken = default
        );
    }
}
