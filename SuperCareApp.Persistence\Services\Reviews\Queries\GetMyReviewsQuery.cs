using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Reviews;

namespace SuperCareApp.Persistence.Services.Reviews.Queries;

public record GetMyReviewsQuery : IQuery<Result<List<ReviewResponse>>>;

public sealed class GetMyReviewsQueryHandler
    : IQueryHandler<GetMyReviewsQuery, Result<List<ReviewResponse>>>
{
    private readonly ApplicationDbContext _db;
    private readonly ILogger<GetMyReviewsQueryHandler> _logger;
    private readonly ICurrentUserService _currentUserService;

    public GetMyReviewsQueryHandler(
        ApplicationDbContext db,
        ILogger<GetMyReviewsQueryHandler> logger,
        ICurrentUserService currentUserService
    )
    {
        _db = db;
        _logger = logger;
        _currentUserService = currentUserService;
    }

    public async Task<Result<List<ReviewResponse>>> Handle(
        GetMyReviewsQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            if (_currentUserService is not { IsAuthenticated: true, UserId: not null })
            {
                _logger.LogWarning("Unauthenticated user attempted to access reviews");
                return Result.Failure<List<ReviewResponse>>(
                    Error.Unauthorized("User must be authenticated to view reviews")
                );
            }

            var currentUserId = _currentUserService.UserId.Value;

            _logger.LogInformation("Fetching reviews for current user: {UserId}", currentUserId);

            // Get reviews where the current user is either the reviewer or reviewee
            var reviews = await _db
                .Reviews.Where(r =>
                    (r.ReviewerId == currentUserId || r.RevieweeId == currentUserId) && !r.IsDeleted
                )
                .Include(r => r.Reviewer)
                .ThenInclude(u => u.UserProfile)
                .Include(r => r.Reviewee)
                .ThenInclude(u => u.UserProfile)
                .Include(r => r.Booking)
                .ThenInclude(b => b.Category)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync(cancellationToken);

            if (!reviews.Any())
            {
                _logger.LogInformation("No reviews found for user: {UserId}", currentUserId);
            }

            var reviewResponses = reviews
                .Select(review => new ReviewResponse(
                    Id: review.Id.ToString(),
                    Rating: review.Rating,
                    Review: review.Comment ?? string.Empty,
                    Category: new ReviewCategoryResponse(
                        Id: review.Booking.Category.Id.ToString(),
                        Name: review.Booking.Category.Name
                    ),
                    ReviewBy: new ReviewerResponse(
                        Name: GetReviewerName(review.Reviewer),
                        ProfileImage: review.Reviewer.UserProfile?.ImagePath ?? string.Empty
                    ),
                    CreatedAt: review.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                ))
                .ToList();

            _logger.LogInformation(
                "Successfully retrieved {Count} reviews for user: {UserId}",
                reviewResponses.Count,
                currentUserId
            );

            return Result.Success(reviewResponses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving reviews for current user");

            return Result.Failure<List<ReviewResponse>>(
                Error.Internal("An error occurred while retrieving reviews.")
            );
        }
    }

    private static string GetReviewerName(ApplicationUser reviewer)
    {
        if (reviewer?.UserProfile != null)
        {
            var firstName = reviewer.UserProfile.FirstName?.Trim();
            var lastName = reviewer.UserProfile.LastName?.Trim();

            if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName))
                return $"{firstName} {lastName}";

            if (!string.IsNullOrEmpty(firstName))
                return firstName;

            if (!string.IsNullOrEmpty(lastName))
                return lastName;
        }

        return reviewer?.Email ?? "Unknown User";
    }
}
