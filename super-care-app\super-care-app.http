### Environment Variables
@super_care_app_HostAddress = http://localhost:5221
@provider_id = b0c1d2e3-f4a5-6677-8899-334455667788
@booking_id = b0c1d2e3-f4a5-6677-8899-334455667788
@authToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.levfHBGO1qAANxr3uKICuoNd2YNOIykGNNLtmG0YEDo
@rotateToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************.k24KKT6IY8Q6hoYED7efCcD25Z7eLAwl-yEDZITDv9c
### Health Checks

# Basic health check
GET {{super_care_app_HostAddress}}/health
Accept: application/json

### Authentication

# @name login
POST {{super_care_app_HostAddress}}/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin@2345"
}

### Refresh Token
POST {{super_care_app_HostAddress}}/api/v1/auth/refresh-token
Content-Type: application/json
Accept: application/json
Authorization: Bearer {{authToken}}

{
  "refreshToken" : "{{rotateToken}}}"
}


### Register a care provider
POST {{super_care_app_HostAddress}}/api/v1/auth/register
Content-Type: application/json
Accept: application/json
Authorization: Bearer {{authToken}}

{
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "phoneNumber": "+919876543210",
  "isCareProvider": true,
  "firstName": "Souvik",
  "lastName": "Kundu"
}

### Get provider availability for a date
GET {{super_care_app_HostAddress}}/api/v1/bookings/providers/{{provider_id}}/availability/2025-07-19
Accept: application/json
Authorization: Bearer {{authToken}}

### Get provider availability templates
GET {{super_care_app_HostAddress}}/api/v1/bookings/providers/{{provider_id}}/availability-template
Accept: application/json
Authorization: Bearer {{authToken}}

### Get all bookings
GET {{super_care_app_HostAddress}}/api/v1/bookings/all
Accept: application/json
Authorization: Bearer {{authToken}}
