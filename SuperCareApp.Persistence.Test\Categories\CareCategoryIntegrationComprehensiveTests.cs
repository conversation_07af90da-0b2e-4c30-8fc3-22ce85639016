using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.Services.Categories.Commands;
using SuperCareApp.Persistence.Services.Categories.Queries;
using SuperCareApp.Persistence.UnitOfWork;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Comprehensive integration tests that verify the complete flow from API to database
/// for all care category operations including soft delete scenarios
/// </summary>
public class CareCategoryIntegrationComprehensiveTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CareCategoryService _service;

    public CareCategoryIntegrationComprehensiveTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        var repository = new CareCategoryRepository(_context);
        var unitOfWork = new UnitOfWork.UnitOfWork(_context);
        _service = new CareCategoryService(
            repository,
            _context,
            unitOfWork,
            NullLogger<CareCategoryService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Complete CRUD Workflow Tests

    [Fact]
    public async Task CompleteWorkflow_CreateUpdateGetDelete_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();

        // 1. Create category
        var createRequest = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 25.00m,
            Icon = "fas fa-test",
            Color = "#123456"
        };

        var createHandler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
        var createCommand = new CreateCareCategoryCommand(createRequest, userId);
        var createResult = await createHandler.Handle(createCommand, CancellationToken.None);

        Assert.True(createResult.IsSuccess);
        var categoryId = createResult.Value.Id;

        // 2. Get category by ID
        var getByIdHandler = new GetCategoryByIdQueryHandler(_service, NullLogger<GetCategoryByIdQueryHandler>.Instance);
        var getByIdQuery = new GetCategoryByIdQuery(categoryId);
        var getByIdResult = await getByIdHandler.Handle(getByIdQuery, CancellationToken.None);

        Assert.True(getByIdResult.IsSuccess);
        Assert.Equal(createRequest.Name, getByIdResult.Value.Name);
        Assert.Equal(createRequest.Description, getByIdResult.Value.Description);
        Assert.Equal(createRequest.Icon, getByIdResult.Value.Icon);
        Assert.Equal(createRequest.Color, getByIdResult.Value.Color);

        // 3. Update category
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Test Category",
            Description = "Updated Description",
            IsActive = false,
            PlatformFee = 35.00m,
            Icon = "fas fa-updated",
            Color = "#654321"
        };

        var updateHandler = new UpdateCareCategoryCommandHandler(_service, NullLogger<UpdateCareCategoryCommandHandler>.Instance);
        var updateCommand = new UpdateCareCategoryCommand(categoryId, updateRequest, userId);
        var updateResult = await updateHandler.Handle(updateCommand, CancellationToken.None);

        Assert.True(updateResult.IsSuccess);
        Assert.Equal(updateRequest.Name, updateResult.Value.Name);
        Assert.Equal(updateRequest.Description, updateResult.Value.Description);
        Assert.Equal(updateRequest.Icon, updateResult.Value.Icon);
        Assert.Equal(updateRequest.Color, updateResult.Value.Color);

        // 4. Verify update in database
        var updatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == categoryId);
        Assert.NotNull(updatedCategory);
        Assert.Equal(updateRequest.Name, updatedCategory.Name);
        Assert.Equal(userId, updatedCategory.UpdatedBy);
        Assert.NotNull(updatedCategory.UpdatedAt);

        // 5. Get all categories (should include updated category)
        var getAllHandler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var getAllQuery = new GetAllCategoriesQuery(IncludeInactive: true);
        var getAllResult = await getAllHandler.Handle(getAllQuery, CancellationToken.None);

        Assert.True(getAllResult.IsSuccess);
        Assert.Contains(getAllResult.Value, c => c.Id == categoryId && c.Name == updateRequest.Name);

        // 6. Soft delete category
        var deleteHandler = new DeleteCareCategoryCommandHandler(_service, NullLogger<DeleteCareCategoryCommandHandler>.Instance);
        var deleteCommand = new DeleteCareCategoryCommand(categoryId, userId);
        var deleteResult = await deleteHandler.Handle(deleteCommand, CancellationToken.None);

        Assert.True(deleteResult.IsSuccess);

        // 7. Verify soft delete in database
        var deletedCategory = await _context.CareCategories
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(c => c.Id == categoryId);
        Assert.NotNull(deletedCategory);
        Assert.True(deletedCategory.IsDeleted);
        Assert.NotNull(deletedCategory.DeletedAt);
        Assert.Equal(userId, deletedCategory.DeletedBy);

        // 8. Verify category is not returned in normal queries
        var getDeletedResult = await getByIdHandler.Handle(getByIdQuery, CancellationToken.None);
        Assert.True(getDeletedResult.IsFailure);

        // 9. Verify category is not in GetAll results
        var getAllAfterDeleteResult = await getAllHandler.Handle(getAllQuery, CancellationToken.None);
        Assert.True(getAllAfterDeleteResult.IsSuccess);
        Assert.DoesNotContain(getAllAfterDeleteResult.Value, c => c.Id == categoryId);
    }

    [Fact]
    public async Task CompleteWorkflow_ActivateDeactivate_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();

        // 1. Create inactive category
        var createRequest = new CreateCareCategoryRequest
        {
            Name = "Inactive Category",
            Description = "Initially inactive",
            IsActive = false,
            PlatformFee = 15.00m
        };

        var createHandler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
        var createCommand = new CreateCareCategoryCommand(createRequest, userId);
        var createResult = await createHandler.Handle(createCommand, CancellationToken.None);

        Assert.True(createResult.IsSuccess);
        Assert.False(createResult.Value.IsActive);
        var categoryId = createResult.Value.Id;

        // 2. Activate category
        var activateHandler = new ActivateCareCategoryCommandHandler(_service, NullLogger<ActivateCareCategoryCommandHandler>.Instance);
        var activateCommand = new ActivateCareCategoryCommand(categoryId, userId);
        var activateResult = await activateHandler.Handle(activateCommand, CancellationToken.None);

        Assert.True(activateResult.IsSuccess);
        Assert.True(activateResult.Value.IsActive);

        // 3. Verify activation in database
        var activatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == categoryId);
        Assert.NotNull(activatedCategory);
        Assert.True(activatedCategory.IsActive);
        Assert.Equal(userId, activatedCategory.UpdatedBy);

        // 4. Deactivate category
        var deactivateHandler = new DeactivateCareCategoryCommandHandler(_service, NullLogger<DeactivateCareCategoryCommandHandler>.Instance);
        var deactivateCommand = new DeactivateCareCategoryCommand(categoryId, userId);
        var deactivateResult = await deactivateHandler.Handle(deactivateCommand, CancellationToken.None);

        Assert.True(deactivateResult.IsSuccess);
        Assert.False(deactivateResult.Value.IsActive);

        // 5. Verify deactivation in database
        var deactivatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c => c.Id == categoryId);
        Assert.NotNull(deactivatedCategory);
        Assert.False(deactivatedCategory.IsActive);
        Assert.Equal(userId, deactivatedCategory.UpdatedBy);
    }

    #endregion

    #region Bulk Operations Integration Tests

    [Fact]
    public async Task BulkUpdateWorkflow_ShouldUpdateMultipleCategoriesCorrectly()
    {
        var userId = Guid.NewGuid();

        // 1. Create multiple categories
        var categories = new List<Guid>();
        for (int i = 1; i <= 3; i++)
        {
            var createRequest = new CreateCareCategoryRequest
            {
                Name = $"Category {i}",
                Description = $"Description {i}",
                IsActive = true,
                PlatformFee = i * 10.00m
            };

            var createHandler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
            var createCommand = new CreateCareCategoryCommand(createRequest, userId);
            var createResult = await createHandler.Handle(createCommand, CancellationToken.None);

            Assert.True(createResult.IsSuccess);
            categories.Add(createResult.Value.Id);
        }

        // 2. Bulk update categories
        var bulkUpdateRequest = new BulkUpdateCareCategoriesRequest
        {
            Categories = categories.Select((id, index) => new CareCategoryUpdateItem
            {
                Id = id,
                Name = $"Updated Category {index + 1}",
                Description = $"Updated Description {index + 1}",
                IsActive = false,
                PlatformFee = (index + 1) * 20.00m
            }).ToList()
        };

        var bulkUpdateHandler = new BulkUpdateCareCategoriesCommandHandler(_service, NullLogger<BulkUpdateCareCategoriesCommandHandler>.Instance);
        var bulkUpdateCommand = new BulkUpdateCareCategoriesCommand(bulkUpdateRequest, userId);
        var bulkUpdateResult = await bulkUpdateHandler.Handle(bulkUpdateCommand, CancellationToken.None);

        Assert.True(bulkUpdateResult.IsSuccess);
        Assert.Equal(3, bulkUpdateResult.Value.Count());

        // 3. Verify updates in database
        var updatedCategories = await _context.CareCategories
            .Where(c => categories.Contains(c.Id))
            .ToListAsync();

        Assert.Equal(3, updatedCategories.Count);
        foreach (var category in updatedCategories)
        {
            Assert.StartsWith("Updated Category", category.Name);
            Assert.StartsWith("Updated Description", category.Description);
            Assert.False(category.IsActive);
            Assert.Equal(userId, category.UpdatedBy);
            Assert.NotNull(category.UpdatedAt);
        }

        // 4. Verify through GetAll query
        var getAllHandler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var getAllQuery = new GetAllCategoriesQuery(IncludeInactive: true);
        var getAllResult = await getAllHandler.Handle(getAllQuery, CancellationToken.None);

        Assert.True(getAllResult.IsSuccess);
        var allCategories = getAllResult.Value.ToList();
        
        foreach (var categoryId in categories)
        {
            var category = allCategories.First(c => c.Id == categoryId);
            Assert.StartsWith("Updated Category", category.Name);
            Assert.False(category.IsActive);
        }
    }

    #endregion

    #region Provider Categories Integration Tests

    [Fact]
    public async Task ProviderCategoriesWorkflow_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        // 1. Create categories
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 1",
            Description = "General Description 1",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-provider1",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 2",
            Description = "General Description 2",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = "fas fa-provider2",
            Color = "#33FF57",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2);
        await _context.SaveChangesAsync();

        // 2. Create provider categories
        var providerCategory1 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category1.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            ProviderSpecificDescription = "Provider specific description 1"
        };

        var providerCategory2 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category2.Id,
            HourlyRate = 60.00m,
            ExperienceYears = 3,
            ProviderSpecificDescription = "Provider specific description 2"
        };

        _context.CareProviderCategories.AddRange(providerCategory1, providerCategory2);
        await _context.SaveChangesAsync();

        // 3. Get categories by provider ID
        var getByProviderHandler = new GetCategoriesByProviderIdQueryHandler(_service, NullLogger<GetCategoriesByProviderIdQueryHandler>.Instance);
        var getByProviderQuery = new GetCategoriesByProviderIdQuery(providerId);
        var getByProviderResult = await getByProviderHandler.Handle(getByProviderQuery, CancellationToken.None);

        Assert.True(getByProviderResult.IsSuccess);
        Assert.Equal(2, getByProviderResult.Value.Count());

        var providerCategories = getByProviderResult.Value.ToList();
        var providerCat1 = providerCategories.First(c => c.Name == "Provider Category 1");
        Assert.Equal(50.00m, providerCat1.HourlyRate);
        Assert.Equal(5, providerCat1.ExperienceYears);
        Assert.Equal("Provider specific description 1", providerCat1.Description);
        Assert.Equal("fas fa-provider1", providerCat1.Icon);
        Assert.Equal("#FF5733", providerCat1.Color);

        // 4. Get all categories with provider ID (should return provider-specific data)
        var getAllHandler = new GetAllCategoriesQueryHandler(_service, NullLogger<GetAllCategoriesQueryHandler>.Instance, _context);
        var getAllQuery = new GetAllCategoriesQuery(IncludeInactive: false, ProviderId: providerId);
        var getAllResult = await getAllHandler.Handle(getAllQuery, CancellationToken.None);

        Assert.True(getAllResult.IsSuccess);
        var allCategories = getAllResult.Value.ToList();
        
        var cat1 = allCategories.First(c => c.Name == "Provider Category 1");
        Assert.Equal("Provider specific description 1", cat1.Description);
        Assert.Equal(50.00m, cat1.HourlyRate);
        Assert.Equal(5, cat1.ExperienceYears);

        // 5. Soft delete provider category
        providerCategory1.IsDeleted = true;
        providerCategory1.DeletedAt = DateTime.UtcNow;
        providerCategory1.DeletedBy = userId;
        await _context.SaveChangesAsync();

        // 6. Verify deleted provider category is excluded
        var getByProviderAfterDeleteResult = await getByProviderHandler.Handle(getByProviderQuery, CancellationToken.None);
        Assert.True(getByProviderAfterDeleteResult.IsSuccess);
        Assert.Single(getByProviderAfterDeleteResult.Value);
        Assert.Equal("Provider Category 2", getByProviderAfterDeleteResult.Value.First().Name);

        // 7. Verify GetAll with provider ID excludes deleted provider category
        var getAllAfterDeleteResult = await getAllHandler.Handle(getAllQuery, CancellationToken.None);
        Assert.True(getAllAfterDeleteResult.IsSuccess);
        
        var cat1AfterDelete = getAllAfterDeleteResult.Value.First(c => c.Name == "Provider Category 1");// Should use general description
        Assert.Equal(0m, cat1AfterDelete.HourlyRate); // Should be 0 since provider category is deleted
        Assert.Equal(0, cat1AfterDelete.ExperienceYears); // Should be 0 since provider category is deleted
    }

    #endregion

    #region Pagination Integration Tests

    [Fact]
    public async Task PaginationWorkflow_ShouldWorkCorrectly()
    {
        var userId = Guid.NewGuid();

        // 1. Create multiple categories
        var categories = new List<Guid>();
        for (int i = 1; i <= 15; i++)
        {
            var createRequest = new CreateCareCategoryRequest
            {
                Name = $"Category {i:D2}",
                Description = $"Description {i}",
                IsActive = i % 2 == 1, // Alternate active/inactive
                PlatformFee = i * 5.00m
            };

            var createHandler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
            var createCommand = new CreateCareCategoryCommand(createRequest, userId);
            var createResult = await createHandler.Handle(createCommand, CancellationToken.None);

            Assert.True(createResult.IsSuccess);
            categories.Add(createResult.Value.Id);
        }

        // 2. Test paginated query - first page, active only
        var paginatedHandler = new GetPaginatedCategoriesQueryHandler(_service, NullLogger<GetPaginatedCategoriesQueryHandler>.Instance);
        var paginatedQuery = new GetPaginatedCategoriesQuery(PageNumber: 1, PageSize: 5, IncludeInactive: false);
        var paginatedResult = await paginatedHandler.Handle(paginatedQuery, CancellationToken.None);

        Assert.True(paginatedResult.IsSuccess);
        Assert.Equal(5, paginatedResult.Value.Categories.Count());
        Assert.Equal(8, paginatedResult.Value.TotalCount); // 8 active categories (odd numbers)
        Assert.Equal(2, paginatedResult.Value.TotalPages); // Ceiling(8/5) = 2
        Assert.Equal(1, paginatedResult.Value.PageNumber);
        Assert.Equal(5, paginatedResult.Value.PageSize);

        // 3. Test paginated query - second page, active only
        var paginatedQuery2 = new GetPaginatedCategoriesQuery(PageNumber: 2, PageSize: 5, IncludeInactive: false);
        var paginatedResult2 = await paginatedHandler.Handle(paginatedQuery2, CancellationToken.None);

        Assert.True(paginatedResult2.IsSuccess);
        Assert.Equal(3, paginatedResult2.Value.Categories.Count()); // Remaining 3 active categories
        Assert.Equal(8, paginatedResult2.Value.TotalCount);
        Assert.Equal(2, paginatedResult2.Value.TotalPages);
        Assert.Equal(2, paginatedResult2.Value.PageNumber);

        // 4. Test paginated query - include inactive
        var paginatedQueryAll = new GetPaginatedCategoriesQuery(PageNumber: 1, PageSize: 10, IncludeInactive: true);
        var paginatedResultAll = await paginatedHandler.Handle(paginatedQueryAll, CancellationToken.None);

        Assert.True(paginatedResultAll.IsSuccess);
        Assert.Equal(10, paginatedResultAll.Value.Categories.Count());
        Assert.Equal(15, paginatedResultAll.Value.TotalCount); // All categories
        Assert.Equal(2, paginatedResultAll.Value.TotalPages); // Ceiling(15/10) = 2

        // 5. Soft delete some categories and verify pagination
        var categoriesToDelete = categories.Take(3).ToList();
        foreach (var categoryId in categoriesToDelete)
        {
            var deleteHandler = new DeleteCareCategoryCommandHandler(_service, NullLogger<DeleteCareCategoryCommandHandler>.Instance);
            var deleteCommand = new DeleteCareCategoryCommand(categoryId, userId);
            await deleteHandler.Handle(deleteCommand, CancellationToken.None);
        }

        // 6. Test pagination after soft delete
        var paginatedAfterDeleteResult = await paginatedHandler.Handle(paginatedQueryAll, CancellationToken.None);
        Assert.True(paginatedAfterDeleteResult.IsSuccess);
        Assert.Equal(10, paginatedAfterDeleteResult.Value.Categories.Count());
        Assert.Equal(12, paginatedAfterDeleteResult.Value.TotalCount); // 15 - 3 deleted = 12
        Assert.Equal(2, paginatedAfterDeleteResult.Value.TotalPages); // Ceiling(12/10) = 2
    }

    #endregion

    #region Validation Integration Tests

    [Fact]
    public async Task ValidationWorkflow_ShouldPreventDuplicateNames()
    {
        var userId = Guid.NewGuid();

        // 1. Create first category
        var createRequest1 = new CreateCareCategoryRequest
        {
            Name = "Unique Category",
            Description = "First category",
            IsActive = true,
            PlatformFee = 10.00m
        };

        var createHandler = new CreateCareCategoryCommandHandler(_service, NullLogger<CreateCareCategoryCommandHandler>.Instance);
        var createCommand1 = new CreateCareCategoryCommand(createRequest1, userId);
        var createResult1 = await createHandler.Handle(createCommand1, CancellationToken.None);

        Assert.True(createResult1.IsSuccess);

        // 2. Try to create second category with same name
        var createRequest2 = new CreateCareCategoryRequest
        {
            Name = "Unique Category",
            Description = "Duplicate name",
            IsActive = true,
            PlatformFee = 15.00m
        };

        var createCommand2 = new CreateCareCategoryCommand(createRequest2, userId);
        var createResult2 = await createHandler.Handle(createCommand2, CancellationToken.None);

        Assert.True(createResult2.IsFailure);
        Assert.Contains("already exists", createResult2.Error.Message);

        // 3. Try case-insensitive duplicate
        var createRequest3 = new CreateCareCategoryRequest
        {
            Name = "UNIQUE CATEGORY",
            Description = "Case insensitive duplicate",
            IsActive = true,
            PlatformFee = 20.00m
        };

        var createCommand3 = new CreateCareCategoryCommand(createRequest3, userId);
        var createResult3 = await createHandler.Handle(createCommand3, CancellationToken.None);

        Assert.True(createResult3.IsFailure);
        Assert.Contains("already exists", createResult3.Error.Message);

        // 4. Update to duplicate name should fail
        var category1Id = createResult1.Value.Id;
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Another Category"
        };

        // First create another category
        var anotherCreateRequest = new CreateCareCategoryRequest
        {
            Name = "Another Category",
            IsActive = true,
            PlatformFee = 25.00m
        };

        var anotherCreateCommand = new CreateCareCategoryCommand(anotherCreateRequest, userId);
        var anotherCreateResult = await createHandler.Handle(anotherCreateCommand, CancellationToken.None);
        Assert.True(anotherCreateResult.IsSuccess);

        // Now try to update first category to have the same name as the second
        var updateHandler = new UpdateCareCategoryCommandHandler(_service, NullLogger<UpdateCareCategoryCommandHandler>.Instance);
        var updateCommand = new UpdateCareCategoryCommand(category1Id, updateRequest, userId);
        var updateResult = await updateHandler.Handle(updateCommand, CancellationToken.None);

        Assert.True(updateResult.IsFailure);
        Assert.Contains("already exists", updateResult.Error.Message);

        // 5. Update to same name should succeed (no change)
        var sameNameUpdateRequest = new UpdateCareCategoryRequest
        {
            Name = "Unique Category",
            Description = "Updated description"
        };

        var sameNameUpdateCommand = new UpdateCareCategoryCommand(category1Id, sameNameUpdateRequest, userId);
        var sameNameUpdateResult = await updateHandler.Handle(sameNameUpdateCommand, CancellationToken.None);

        Assert.True(sameNameUpdateResult.IsSuccess);
        Assert.Equal("Updated description", sameNameUpdateResult.Value.Description);
    }

    #endregion

    #region Error Handling Integration Tests

    [Fact]
    public async Task ErrorHandlingWorkflow_ShouldHandleNonExistentEntities()
    {
        var userId = Guid.NewGuid();
        var nonExistentId = Guid.NewGuid();

        // 1. Get non-existent category
        var getByIdHandler = new GetCategoryByIdQueryHandler(_service, NullLogger<GetCategoryByIdQueryHandler>.Instance);
        var getByIdQuery = new GetCategoryByIdQuery(nonExistentId);
        var getByIdResult = await getByIdHandler.Handle(getByIdQuery, CancellationToken.None);

        Assert.True(getByIdResult.IsFailure);
        Assert.Contains("was not found", getByIdResult.Error.Message);

        // 2. Update non-existent category
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Name"
        };

        var updateHandler = new UpdateCareCategoryCommandHandler(_service, NullLogger<UpdateCareCategoryCommandHandler>.Instance);
        var updateCommand = new UpdateCareCategoryCommand(nonExistentId, updateRequest, userId);
        var updateResult = await updateHandler.Handle(updateCommand, CancellationToken.None);

        Assert.True(updateResult.IsFailure);
        Assert.Contains("was not found", updateResult.Error.Message);

        // 3. Delete non-existent category
        var deleteHandler = new DeleteCareCategoryCommandHandler(_service, NullLogger<DeleteCareCategoryCommandHandler>.Instance);
        var deleteCommand = new DeleteCareCategoryCommand(nonExistentId, userId);
        var deleteResult = await deleteHandler.Handle(deleteCommand, CancellationToken.None);

        Assert.True(deleteResult.IsFailure);
        Assert.Contains("was not found", deleteResult.Error.Message);

        // 4. Activate non-existent category
        var activateHandler = new ActivateCareCategoryCommandHandler(_service, NullLogger<ActivateCareCategoryCommandHandler>.Instance);
        var activateCommand = new ActivateCareCategoryCommand(nonExistentId, userId);
        var activateResult = await activateHandler.Handle(activateCommand, CancellationToken.None);

        Assert.True(activateResult.IsFailure);
        Assert.Contains("was not found", activateResult.Error.Message);

        // 5. Deactivate non-existent category
        var deactivateHandler = new DeactivateCareCategoryCommandHandler(_service, NullLogger<DeactivateCareCategoryCommandHandler>.Instance);
        var deactivateCommand = new DeactivateCareCategoryCommand(nonExistentId, userId);
        var deactivateResult = await deactivateHandler.Handle(deactivateCommand, CancellationToken.None);

        Assert.True(deactivateResult.IsFailure);
        Assert.Contains("was not found", deactivateResult.Error.Message);
    }

    #endregion
}