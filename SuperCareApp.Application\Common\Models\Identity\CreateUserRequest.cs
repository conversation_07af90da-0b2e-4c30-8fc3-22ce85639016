﻿using System.Globalization;
using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace SuperCareApp.Application.Common.Models.Identity;

public class CreateUserRequest
{
    public string Name { get; set; } = string.Empty;

    public string PhoneNumber { get; set; } = string.Empty;

    public string Gender { get; set; } = string.Empty;

    public int? YearsExperience { get; set; }

    public string DateOfBirth { get; set; } = string.Empty;

    /// <summary>
    /// Profile picture file (optional)
    /// </summary>
    public IFormFile? ProfilePicture { get; set; }
}

public class CreateUserRequestValidator : AbstractValidator<CreateUserRequest>
{
    public CreateUserRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required.")
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.");

        RuleFor(x => x.PhoneNumber)
            .NotEmpty()
            .WithMessage("Phone number is required.")
            .Matches(@"^\+?1?\d{9,15}$")
            .WithMessage("Phone number must be a valid format (e.g., +1234567890 or 1234567890).");

        RuleFor(x => x.Gender)
            .NotEmpty()
            .WithMessage("Gender is required.")
            .Must(g => g is "Male" or "Female" or "Other")
            .WithMessage("Gender must be Male, Female, or Other.");

        RuleFor(x => x.YearsExperience)
            .InclusiveBetween(0, 100)
            .When(x => x.YearsExperience.HasValue)
            .WithMessage("Years of experience must be between 0 and 100.");

        RuleFor(x => x.DateOfBirth)
            .NotEmpty()
            .WithMessage("Date of birth is required.")
            .Must(BeValidDate)
            .WithMessage("Date of birth must be a valid date in the format YYYY-MM-DD.");
    }

    private bool BeValidDate(string date)
    {
        return DateTime.TryParseExact(
            date,
            "yyyy-MM-dd",
            CultureInfo.InvariantCulture,
            DateTimeStyles.None,
            out _
        );
    }
}
