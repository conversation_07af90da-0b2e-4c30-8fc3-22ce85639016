﻿namespace SuperCareApp.Domain.Common.Exceptions
{
    /// <summary>
    /// Exception thrown when validation fails
    /// </summary>
    public class ValidationException : SuperCareException
    {
        public IDictionary<string, string[]> Errors { get; }

        public ValidationException()
            : base("One or more validation failures have occurred.")
        {
            Errors = new Dictionary<string, string[]>();
        }

        public ValidationException(IDictionary<string, string[]> errors)
            : base("One or more validation failures have occurred.")
        {
            Errors = errors;
        }

        public ValidationException(string propertyName, string errorMessage)
            : base($"Validation failed: {propertyName} - {errorMessage}")
        {
            Errors = new Dictionary<string, string[]> { { propertyName, new[] { errorMessage } } };
        }

        public ValidationException(IEnumerable<(string PropertyName, string ErrorMessage)> failures)
            : base("One or more validation failures have occurred.")
        {
            var errors = failures
                .GroupBy(e => e.PropertyName, e => e.ErrorMessage)
                .ToDictionary(
                    failureGroup => failureGroup.Key,
                    failureGroup => failureGroup.ToArray()
                );

            Errors = errors;
        }
    }
}
