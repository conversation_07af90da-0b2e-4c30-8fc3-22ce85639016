﻿using SuperCareApp.Application.Shared.Utility;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Paged list of approval requests
    /// </summary>
    public class PagedApprovalRequestList
    {
        /// <summary>
        /// List of approval requests
        /// </summary>
        public List<ApprovalRequestResponse> Requests { get; set; } =
            new List<ApprovalRequestResponse>();

        /// <summary>
        /// Current page number
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// Converts this paged list to a PaginationMetadata object
        /// </summary>
        public PaginationMetadata ToMetadata()
        {
            return new PaginationMetadata(PageNumber, TotalPages, TotalCount, PageSize);
        }
    }
}
