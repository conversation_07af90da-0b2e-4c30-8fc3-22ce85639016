﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

public class PaymentConfiguration : IEntityTypeConfiguration<Payment>
{
    public void Configure(EntityTypeBuilder<Payment> builder)
    {
        builder.HasKey(p => p.Id);

        builder.Property(p => p.Amount).IsRequired().HasColumnType("decimal(18,2)");

        builder.Property(p => p.PaymentMethod).IsRequired().HasMaxLength(50);

        builder.Property(p => p.TransactionId).HasMaxLength(100);

        builder.Property(p => p.Status).IsRequired().HasConversion<string>();

        builder.Property(p => p.PaymentDateTime).IsRequired();

        builder.Property(p => p.InvoiceNumber).HasMaxLength(50);

        // Relationships
        builder
            .HasOne(p => p.Invoice)
            .WithMany(b => b.Payments)
            .HasForeignKey(p => p.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(p => p.TransactionId);
        builder.HasIndex(p => p.InvoiceNumber);
    }
}
