﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Configurations
{
    public class DocumentConfiguration : IEntityTypeConfiguration<Document>
    {
        public void Configure(EntityTypeBuilder<Document> builder)
        {
            builder.HasKey(d => d.Id);

            builder.Property(d => d.UserId).IsRequired();

            builder.Property(d => d.DocumentType).IsRequired().HasMaxLength(100);

            builder.Property(d => d.DocumentUrl).IsRequired().HasMaxLength(512);

            builder.Property(d => d.VerificationStatus).IsRequired().HasConversion<string>();

            builder.Property(d => d.UploadedAt).IsRequired();

            builder.Property(d => d.RejectionReason).HasMaxLength(500);

            // Configure new properties for country-specific certifications
            builder
                .Property(d => d.Country)
                .IsRequired()
                .HasDefaultValue(Country.Other)
                .HasConversion<string>();

            builder.Property(d => d.CertificationType).HasConversion<string>();

            builder.Property(d => d.OtherCertificationType).HasMaxLength(100);

            builder.Property(d => d.CertificationNumber).HasMaxLength(50);

            builder.Property(d => d.ExpiryDate);

            // Ignore computed property
            builder.Ignore(d => d.IsExpired);

            // Relationships
            builder
                .HasOne(d => d.UserProfile)
                .WithMany(p => p.Documents)
                .HasForeignKey(d => d.UserId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
