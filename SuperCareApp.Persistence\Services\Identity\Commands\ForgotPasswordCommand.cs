﻿using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Otp;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record ForgotPasswordCommand(string Email) : ICommand<Result<OtpResponse>>;

internal sealed class ForgotPasswordCommandHandler
    : ICommandHandler<ForgotPasswordCommand, Result<OtpResponse>>
{
    private readonly IAuthService _authService;

    public ForgotPasswordCommandHandler(IAuthService authService)
    {
        _authService = authService;
    }

    public async Task<Result<OtpResponse>> Handle(
        ForgotPasswordCommand request,
        CancellationToken cancellationToken
    )
    {
        var result = await _authService.GenerateOtpAsync(request.Email, "Reset Password");
        if (result.IsFailure)
        {
            return Result.Failure<OtpResponse>(result.Error);
        }
        return Result.Success(result.Value);
    }
}
