﻿using System.ComponentModel.DataAnnotations;
using FluentValidation;
using PhoneNumbers;

namespace SuperCareApp.Application.Common.Models.Identity;

/// <summary>
/// User registration request model
/// </summary>
public class RegisterRequest
{
    /// <summary>
    /// Email address for the user account
    /// </summary>
    [Required]
    //[EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Password for the user account
    /// </summary>
    [Required]
    [MinLength(6)]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Confirm password (must match Password)
    /// </summary>
    [Required]
    [Compare("Password")]
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// Phone number for the user
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Whether the user is registering as a care provider
    /// </summary>
    [Required]
    public bool IsCareProvider { get; set; } = false;

    /// <summary>
    /// First name of the user (optional)
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name of the user (optional)
    /// </summary>
    public string? LastName { get; set; }
}

public class RegisterRequestValidator : AbstractValidator<RegisterRequest>
{
    public RegisterRequestValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty()
            .WithMessage("Email is required.")
            .EmailAddress()
            .WithMessage("A valid email address is required.")
            .MaximumLength(254)
            .WithMessage("Email cannot exceed 254 characters.");

        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("Password is required.")
            .MinimumLength(8)
            .WithMessage("Password must be at least 8 characters.")
            .MaximumLength(128)
            .WithMessage("Password cannot exceed 128 characters.")
            .Matches(@"^(?=.*[a-zA-Z])(?=.*\d)")
            .WithMessage("Password must contain at least one letter and one number.");

        RuleFor(x => x.ConfirmPassword)
            .NotEmpty()
            .WithMessage("Confirm password is required.")
            .Equal(x => x.Password)
            .WithMessage("Passwords do not match.");

        RuleFor(x => x.PhoneNumber)
            .Must(phone => string.IsNullOrWhiteSpace(phone) || BeAValidPhoneNumber(phone))
            .WithMessage("Please provide a valid international phone number starting with +.")
            .Matches(@"^\+\d{7,15}$")
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber))
            .WithMessage("Phone number must start with '+' followed by 7-15 digits.");

        RuleFor(x => x.FirstName)
            .MaximumLength(50)
            .WithMessage("First name cannot exceed 50 characters.")
            .Matches(@"^[a-zA-Z\s'-]+$")
            .When(x => !string.IsNullOrWhiteSpace(x.FirstName))
            .WithMessage("First name can only contain letters, spaces, hyphens, or apostrophes.");

        RuleFor(x => x.LastName)
            .MaximumLength(50)
            .WithMessage("Last name cannot exceed 50 characters.")
            .Matches(@"^[a-zA-Z\s'-]+$")
            .When(x => !string.IsNullOrWhiteSpace(x.LastName))
            .WithMessage("Last name can only contain letters, spaces, hyphens, or apostrophes.");
    }

    private bool BeAValidPhoneNumber(string? number)
    {
        if (string.IsNullOrWhiteSpace(number))
            return true;

        try
        {
            var phoneUtil = PhoneNumberUtil.GetInstance();
            var parsed = phoneUtil.Parse(number, null);

            // Check if it's a valid format, even if not strictly valid
            var region = phoneUtil.GetRegionCodeForNumber(parsed);
            if (string.IsNullOrEmpty(region))
            {
                // If no region, at least check basic format
                return number.Length >= 8 && number.Length <= 16 && number.StartsWith("+");
            }

            // For UK numbers, we might want to be more lenient
            if (region == "GB")
            {
                // UK numbers: +44 followed by 10-12 digits
                var nationalNumber = phoneUtil.GetNationalSignificantNumber(parsed);
                return nationalNumber.Length >= 10 && nationalNumber.Length <= 12;
            }

            return phoneUtil.IsValidNumber(parsed);
        }
        catch (NumberParseException)
        {
            return false;
        }
    }
}
