﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Application.Common.Models.User;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Repositories.User
{
    public class UserProfileRepository
        : Repository<UserProfile, ApplicationDbContext>,
            IUserProfileRepository
    {
        private readonly ILogger<UserProfileRepository> _logger;
        private readonly IUnitOfWork _unitOfWork;

        public UserProfileRepository(
            ApplicationDbContext context,
            ILogger<UserProfileRepository> logger,
            IUnitOfWork unitOfWork
        )
            : base(context)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        public async Task<Result<UserProfile>> GetByUserIdAsync(
            Guid userId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var profile = await Context
                    .Set<UserProfile>()
                    .FirstOrDefaultAsync(p => p.ApplicationUserId == userId, cancellationToken);

                if (profile == null)
                {
                    _logger.LogWarning("User profile not found for UserId: {UserId}", userId);
                    return Result.Failure<UserProfile>(Error.NotFound("User profile not found"));
                }

                return Result.Success(profile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user profile for UserId: {UserId}", userId);
                return Result.Failure<UserProfile>(
                    Error.Internal("Failed to retrieve user profile")
                );
            }
        }

        public async Task<Result<UserProfile>> UpdateProfileAsync(
            Guid userId,
            UpdateUserProfileRequest request,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                await _unitOfWork.BeginTransactionAsync(cancellationToken);

                var profileResult = await GetByUserIdAsync(userId, cancellationToken);
                if (profileResult.IsFailure)
                {
                    return profileResult;
                }

                var profile = profileResult.Value;
                UpdateProfileProperties(profile, request);

                var updateResult = await UpdateAsync(profile, cancellationToken);
                if (updateResult.IsFailure)
                {
                    await _unitOfWork.RollbackTransactionAsync(cancellationToken);
                    return updateResult;
                }

                var saveResult = await _unitOfWork.SaveChangesAsync(cancellationToken);
                if (saveResult.IsFailure)
                {
                    await _unitOfWork.RollbackTransactionAsync(cancellationToken);
                    return Result.Failure<UserProfile>(saveResult.Error);
                }

                await _unitOfWork.CommitTransactionAsync(cancellationToken);

                _logger.LogInformation("Updated user profile for UserId: {UserId}", userId);
                return Result.Success(profile);
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync(cancellationToken);
                _logger.LogError(ex, "Error updating user profile for UserId: {UserId}", userId);
                return Result.Failure<UserProfile>(Error.Internal("Failed to update user profile"));
            }
        }

        private static void UpdateProfileProperties(
            UserProfile profile,
            UpdateUserProfileRequest request
        )
        {
            if (request.FirstName != null)
                profile.FirstName = request.FirstName;
            if (request.LastName != null)
                profile.LastName = request.LastName;
            if (request.PhoneNumber != null)
                profile.PhoneNumber = request.PhoneNumber;
            if (request.ProfilePicture != null)
                profile.ImageName = request.ProfilePicture;
            if (request.Country != null)
                profile.Country = request.Country;
            if (request.DateOfBirth.HasValue)
                profile.DateOfBirth = request.DateOfBirth;
            if (request.Preferences != null)
                profile.Preferences = request.Preferences;

            profile.UpdatedAt = DateTime.UtcNow;
        }
    }
}
