using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    /// <summary>
    /// Request model for cancelling a booking
    /// </summary>
    public class CancelBookingRequest
    {
        /// <summary>
        /// Optional reason for cancelling the booking
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Whether to notify the other party about the cancellation
        /// </summary>
        public bool NotifyOtherParty { get; set; } = true;

        /// <summary>
        /// Whether to allow cancellation even if it's within the cancellation window
        /// </summary>
        public bool ForceCancel { get; set; } = false;
    }

    /// <summary>
    /// Validator for CancelBookingRequest
    /// </summary>
    public class CancelBookingRequestValidator : AbstractValidator<CancelBookingRequest>
    {
        public CancelBookingRequestValidator()
        {
            RuleFor(x => x.Reason)
                .MaximumLength(500)
                .WithMessage("Cancellation reason cannot exceed 500 characters")
                .When(x => !string.IsNullOrWhiteSpace(x.Reason));
        }
    }
}
