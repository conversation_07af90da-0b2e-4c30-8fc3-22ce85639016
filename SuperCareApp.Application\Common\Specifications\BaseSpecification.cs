﻿using System.Linq.Expressions;
using SuperCareApp.Application.Common.Interfaces.Persistence;

namespace SuperCareApp.Application.Common.Specifications
{
    /// <summary>
    /// Base implementation of the specification pattern
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    public abstract class BaseSpecification<T> : ISpecification<T>
    {
        /// <summary>
        /// Default constructor
        /// </summary>
        protected BaseSpecification() { }

        /// <summary>
        /// Constructor with criteria
        /// </summary>
        /// <param name="criteria">The filter criteria</param>
        protected BaseSpecification(Expression<Func<T, bool>> criteria)
        {
            Criteria = criteria;
        }

        /// <summary>
        /// The filter criteria
        /// </summary>
        public Expression<Func<T, bool>>? Criteria { get; private set; }

        /// <summary>
        /// Include expressions for eager loading
        /// </summary>
        public List<Expression<Func<T, object>>> Includes { get; } =
            new List<Expression<Func<T, object>>>();

        /// <summary>
        /// Ordering expressions
        /// </summary>
        public List<OrderingExpression<T>> OrderingExpressions { get; } =
            new List<OrderingExpression<T>>();

        /// <summary>
        /// Number of entities to take (for pagination)
        /// </summary>
        public int? Take { get; private set; }

        /// <summary>
        /// Number of entities to skip (for pagination)
        /// </summary>
        public int? Skip { get; private set; }

        /// <summary>
        /// Adds an include expression
        /// </summary>
        /// <param name="includeExpression">The include expression</param>
        protected void AddInclude(Expression<Func<T, object>> includeExpression)
        {
            Includes.Add(includeExpression);
        }

        /// <summary>
        /// Adds an ordering expression (ascending)
        /// </summary>
        /// <param name="orderByExpression">The ordering expression</param>
        protected void AddOrderBy(Expression<Func<T, object>> orderByExpression)
        {
            OrderingExpressions.Add(
                new OrderingExpression<T>
                {
                    OrderingKeySelector = orderByExpression,
                    Direction = OrderingDirection.Ascending,
                }
            );
        }

        /// <summary>
        /// Adds an ordering expression (descending)
        /// </summary>
        /// <param name="orderByDescendingExpression">The ordering expression</param>
        protected void AddOrderByDescending(Expression<Func<T, object>> orderByDescendingExpression)
        {
            OrderingExpressions.Add(
                new OrderingExpression<T>
                {
                    OrderingKeySelector = orderByDescendingExpression,
                    Direction = OrderingDirection.Descending,
                }
            );
        }

        /// <summary>
        /// Applies paging
        /// </summary>
        /// <param name="skip">Number of entities to skip</param>
        /// <param name="take">Number of entities to take</param>
        protected void ApplyPaging(int skip, int take)
        {
            Skip = skip;
            Take = take;
        }
    }
}
