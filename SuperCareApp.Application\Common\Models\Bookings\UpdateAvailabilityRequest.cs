using System.Data;
using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings;

public class UpdateAvailabilityRequest
{
    public bool IsAvailable { get; set; } = true;
    public List<CreateAvailabilitySlotRequest> Slots { get; set; } =
        new List<CreateAvailabilitySlotRequest>();
}

public class UpdateAvailabilityRequestValidator : AbstractValidator<UpdateAvailabilityRequest>
{
    public UpdateAvailabilityRequestValidator()
    {
        RuleFor(x => x.IsAvailable).NotNull().WithMessage("Availability status is required.");

        When(
            x => x.IsAvailable,
            () =>
            {
                RuleFor(x => x.Slots)
                    .NotEmpty()
                    .WithMessage(
                        "At least one availability slot is required when set to available."
                    );

                RuleForEach(x => x.Slots)
                    .SetValidator(new CreateAvailabilitySlotRequestValidator());

                RuleFor(x => x.Slots)
                    .Must(NotHaveOverlappingSlots)
                    .WithMessage("Availability slots cannot overlap.");
            }
        );

        When(
            x => !x.IsAvailable,
            () =>
            {
                RuleFor(x => x.Slots)
                    .Empty()
                    .WithMessage("Slots must be empty when not available.");
            }
        );
    }

    private bool NotHaveOverlappingSlots(List<CreateAvailabilitySlotRequest> slots)
    {
        if (slots == null || !slots.Any())
        {
            return true;
        }

        var orderedSlots = slots.OrderBy(s => s.StartTime).ToList();

        for (int i = 0; i < orderedSlots.Count - 1; i++)
        {
            // If the end time of the current slot is after the start time of the next slot, they overlap.
            if (orderedSlots[i].EndTime > orderedSlots[i + 1].StartTime)
            {
                return false;
            }
        }

        return true;
    }
}
