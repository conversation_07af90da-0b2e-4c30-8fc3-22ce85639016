﻿using SuperCareApp.Domain.Common;

namespace SuperCareApp.Domain.Entities
{
    public class Address : BaseEntity
    {
        public string StreetAddress { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        // Navigation properties
        public ICollection<UserAddress> UserAddresses { get; set; } = new List<UserAddress>();
    }
}
