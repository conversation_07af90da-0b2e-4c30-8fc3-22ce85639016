using System.Threading;
using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Interfaces.Storage;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Identity.Enums;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record UpdateProfileCommand(Guid UserId, UserType UserType, UpdateUserRequest Request)
    : ICommand<Result>;

public sealed class UpdateProfileCommandHandler : ICommandHandler<UpdateProfileCommand, Result>
{
    private readonly IUserProfileService _userProfileService;
    private readonly ICareProviderProfileService _careProviderProfileService;
    private readonly ILogger<UpdateProfileCommandHandler> _logger;
    private readonly IFileStorageService _fileStorageService;
    private readonly IMediator _mediator;
    private readonly ApplicationDbContext _dbContext;

    public UpdateProfileCommandHandler(
        IUserProfileService userProfileService,
        ICareProviderProfileService careProviderProfileService,
        IFileStorageService fileStorageService,
        IMediator mediator,
        ILogger<UpdateProfileCommandHandler> logger,
        ApplicationDbContext dbContext
    )
    {
        _userProfileService = userProfileService;
        _careProviderProfileService = careProviderProfileService;
        _fileStorageService = fileStorageService;
        _mediator = mediator;
        _logger = logger;
        _dbContext = dbContext;
    }

    public async Task<Result> Handle(
        UpdateProfileCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Handle profile picture upload if provided
            if (request.Request.ProfilePicture != null)
            {
                await HandleProfilePictureUpload(
                    request.UserId,
                    request.Request.ProfilePicture,
                    cancellationToken
                );
            }

            switch (request.UserType)
            {
                case UserType.Client:
                    return await UpdateUserProfile(request.UserId, request.Request);

                case UserType.CareProvider:
                    var response = await UpdateUserProfile(request.UserId, request.Request);
                    if (response.IsFailure)
                    {
                        return response;
                    }
                    return await UpdateCareProviderProfile(request.UserId, request.Request);

                default:
                    _logger.LogWarning("Unsupported user type: {UserType}", request.UserType);
                    return Result.Failure(Error.BadRequest("Unsupported user type"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating profile for user {UserId}", request.UserId);
            return Result.Failure(Error.Internal($"Error updating profile: {ex.Message}"));
        }
    }

    private async Task<Result<ProfilePictureResponse>> HandleProfilePictureUpload(
        Guid userId,
        IFormFile profilePicture,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var uploadCommand = new UploadProfilePictureCommand(profilePicture, userId);
            var result = await _mediator.Send(uploadCommand, cancellationToken);

            if (result.IsFailure)
            {
                _logger.LogWarning(
                    "Failed to upload profile picture for user {UserId}: {Error}",
                    userId,
                    result.Error.Message
                );
            }
            else
            {
                _logger.LogInformation(
                    "Profile picture uploaded successfully for user {UserId}",
                    userId
                );
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading profile picture for user {UserId}", userId);
            return Result.Failure<ProfilePictureResponse>(
                Error.Internal("An error occurred while uploading the profile picture")
            );
        }
    }

    private async Task<Result> UpdateUserProfile(Guid userId, object requestObj)
    {
        var updateUserRequest = requestObj as UpdateUserRequest;
        if (updateUserRequest == null)
        {
            return Result.Failure(Error.BadRequest("Invalid request data"));
        }

        var existingUserResult = await _userProfileService.GetByUserIdAsync(userId);
        if (existingUserResult.IsFailure || existingUserResult.Value == null)
        {
            return Result.Failure(Error.NotFound("User profile not found"));
        }

        var existingUser = existingUserResult.Value;

        // Check if the user's email is verified
        if (existingUser.User != null && !existingUser.User.EmailVerified)
        {
            _logger.LogWarning("Attempt to update profile for unverified user {UserId}", userId);
            return Result.Failure(Error.Forbidden("Cannot update profile until email is verified"));
        }

        // Only update fields that are explicitly mentioned in the request
        bool hasChanges = false;

        // Update name if provided
        if (!string.IsNullOrWhiteSpace(updateUserRequest.FirstName))
        {
            hasChanges = true;
            existingUser.FirstName = updateUserRequest.FirstName;
        }

        if (!string.IsNullOrWhiteSpace(updateUserRequest.LastName))
        {
            hasChanges = true;
            existingUser.LastName = updateUserRequest.LastName;
        }

        // Update phone number if provided
        if (!string.IsNullOrWhiteSpace(updateUserRequest.PhoneNumber))
        {
            hasChanges = true;
            existingUser.PhoneNumber = updateUserRequest.PhoneNumber;
        }

        // Update email in ApplicationUser if provided
        if (!string.IsNullOrWhiteSpace(updateUserRequest.Email) && existingUser.User != null)
        {
            hasChanges = true;
            existingUser.User.Email = updateUserRequest.Email;
        }

        // Update gender if provided
        if (!string.IsNullOrWhiteSpace(updateUserRequest.Gender))
        {
            hasChanges = true;
            existingUser.Gender = updateUserRequest.Gender;
        }

        // Update date of birth if provided
        if (!string.IsNullOrWhiteSpace(updateUserRequest.DateOfBirth))
        {
            hasChanges = true;
            var dateOfBirth = DateTime.Parse(updateUserRequest.DateOfBirth);
            // Check if it's not in UTC format then convert it to UTC format
            if (dateOfBirth.Kind != DateTimeKind.Utc)
            {
                dateOfBirth = DateTime.SpecifyKind(dateOfBirth, DateTimeKind.Utc);
            }
            existingUser.DateOfBirth = dateOfBirth;
        }

        // Update primary address if provided
        if (updateUserRequest.PrimaryAddress != null && existingUser.User != null)
        {
            hasChanges = true;
            var address = new Domain.Entities.Address
            {
                StreetAddress = updateUserRequest.PrimaryAddress.StreetAddress ?? string.Empty,
                City = updateUserRequest.PrimaryAddress.City ?? string.Empty,
                State = updateUserRequest.PrimaryAddress.State ?? string.Empty,
                PostalCode = updateUserRequest.PrimaryAddress.PostalCode ?? string.Empty,
                Latitude = updateUserRequest.PrimaryAddress.Latitude,
                Longitude = updateUserRequest.PrimaryAddress.Longitude,
                UserAddresses = new List<UserAddress>
                {
                    new UserAddress
                    {
                        UserId = userId,
                        Label = updateUserRequest.PrimaryAddress.Label,
                    },
                },
            };
            // Assume _userProfileService handles adding or updating the address in the Address table
            existingUser.User.UserAddresses.Clear(); // Clear existing primary address
            existingUser.User.UserAddresses.Add(
                new UserAddress
                {
                    UserId = userId,
                    Address = address,
                    Label = updateUserRequest.PrimaryAddress.Label,
                    IsPrimary = true, // Explicitly set as primary
                }
            );
        }

        // Only update if there are changes
        if (!hasChanges)
        {
            return Result.Success();
        }

        var result = await _userProfileService.UpdateAsync(userId, existingUser);
        return result.IsSuccess ? Result.Success() : Result.Failure(result.Error);
    }

    private async Task<Result> UpdateCareProviderProfile(Guid userId, object requestObj)
    {
        var updateProviderRequest = requestObj as UpdateUserRequest;
        if (updateProviderRequest == null)
        {
            return Result.Failure(Error.BadRequest("Invalid request data"));
        }

        var existingProvider = await _careProviderProfileService.GetByUserIdAsync(userId);
        if (existingProvider.IsFailure || existingProvider.Value == null)
        {
            return Result.Failure(Error.NotFound("Care provider profile not found"));
        }

        // Check if the profile is verified
        if (existingProvider.Value.VerificationStatus != VerificationStatus.Verified)
        {
            _logger.LogWarning("Attempt to update unverified profile for user {UserId}", userId);
            return Result.Failure(
                Error.Forbidden("Cannot update profile until verification is complete")
            );
        }

        // Only update fields that are explicitly mentioned in the request
        bool hasChanges = false;

        //Update BufferDuration if provided
        if (updateProviderRequest.BufferDuration.HasValue)
        {
            hasChanges = true;
            existingProvider.Value.BufferDuration = updateProviderRequest.BufferDuration.Value;
        }

        // Update years of experience if provided
        if (updateProviderRequest.YearsExperience.HasValue)
        {
            hasChanges = true;
            existingProvider.Value.YearsExperience = updateProviderRequest.YearsExperience.Value;
        }

        // Update primary address if provided
        if (updateProviderRequest.PrimaryAddress != null)
        {
            hasChanges = true;
            var address = new Domain.Entities.Address
            {
                StreetAddress = updateProviderRequest.PrimaryAddress.StreetAddress ?? string.Empty,
                City = updateProviderRequest.PrimaryAddress.City ?? string.Empty,
                State = updateProviderRequest.PrimaryAddress.State ?? string.Empty,
                PostalCode = updateProviderRequest.PrimaryAddress.PostalCode ?? string.Empty,
                Latitude = updateProviderRequest.PrimaryAddress.Latitude,
                Longitude = updateProviderRequest.PrimaryAddress.Longitude,
                UserAddresses = new List<UserAddress>
                {
                    new UserAddress
                    {
                        UserId = userId,
                        Label = updateProviderRequest.PrimaryAddress.Label,
                    },
                },
            };
            // Add address to the Addresses table
            await _dbContext.Addresses.AddAsync(address);

            // Clear existing primary address for the user
            var existingUserAddresses = await _dbContext
                .UserAddresses.Where(ua => ua.UserId == userId)
                .ToListAsync();
            _dbContext.UserAddresses.RemoveRange(existingUserAddresses);

            // Add new UserAddress to link the user and the new address
            var userAddress = new UserAddress
            {
                UserId = userId,
                Address = address,
                Label = updateProviderRequest.PrimaryAddress.Label,
                IsPrimary = true, // Explicitly set as primary
            };
            await _dbContext.UserAddresses.AddAsync(userAddress);
        }

        // Only update if there are changes
        if (!hasChanges)
        {
            return Result.Success();
        }

        var result = await _careProviderProfileService.UpdateAsync(userId, existingProvider.Value);
        return result.IsSuccess ? Result.Success() : Result.Failure(result.Error);
    }
}
