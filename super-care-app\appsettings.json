{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=supercare;Username=********;Password=********"}, "JwtSettings": {"Secret": "SuperSecureKeyWithAtLeast32Characters123", "Issuer": "SuperCareApp", "Audience": "SuperCareAppUsers", "ExpiryMinutes": 60}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Smtp": {"Host": "smtp.gmail.com", "Port": "587", "EnableSsl": "true", "Password": "getnxkiacfvcwtiy", "FromEmail": "<EMAIL>"}, "OtpSettings": {"UseDefaultOtpForDevelopment": "true", "DefaultOtp": "123456", "OtpExpiryMinutes": "10"}, "Twilio": {"AccountSid": "**********************************", "AuthToken": "01d24b0d3f9ed1e79cddac516016b02d", "PhoneNumber": "+************"}, "PerformanceMonitoring": {"ThresholdInMs": "500"}, "CacheSettings": {"DefaultExpirationSeconds": 300, "SizeLimitMegabytes": 100, "EnableCompression": false}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5221"}}}}