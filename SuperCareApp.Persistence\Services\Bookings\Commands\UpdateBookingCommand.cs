﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;

namespace SuperCareApp.Persistence.Services.Bookings.Commands
{
    public record UpdateBookingCommand(
        Guid Id,
        Guid BookingId,
        Guid CategoryId,
        DateTime BookingDate,
        TimeOnly StartTime,
        TimeOnly EndTime,
        string? SpecialInstructions
    ) : ICommand<Result>;

    public sealed class UpdateBookingCommandHandler : ICommandHandler<UpdateBookingCommand, Result>
    {
        private readonly IBookingService _bookingService;

        public UpdateBookingCommandHandler(IBookingService bookingService)
        {
            _bookingService = bookingService;
        }

        public async Task<Result> Handle(
            UpdateBookingCommand request,
            CancellationToken cancellationToken
        )
        {
            return await _bookingService.UpdateBookingAsync(
                request.Id,
                request.BookingId,
                request.CategoryId,
                request.BookingDate,
                request.StartTime,
                request.EndTime,
                request.SpecialInstructions
            );
        }
    }
}
