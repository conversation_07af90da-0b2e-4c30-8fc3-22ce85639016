﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

// Command
public record CreateAvailabilityCommand(
    Guid UserId,
    string DayOfWeek,
    bool IsAvailable,
    List<AvailabilitySlot> Slots
) : ICommand<Result<Guid>>;

// Handler
public class CreateAvailabilityCommandHandler
    : ICommandHandler<CreateAvailabilityCommand, Result<Guid>>
{
    private readonly IAvailabilityService _availabilityService;

    public CreateAvailabilityCommandHandler(IAvailabilityService availabilityService)
    {
        _availabilityService = availabilityService;
    }

    public async Task<Result<Guid>> Handle(CreateAvailabilityCommand request, CancellationToken ct)
    {
        return await _availabilityService.AddAvailabilityAsync(
            request.UserId,
            request.DayOfWeek,
            request.IsAvailable,
            request.Slots
        );
    }
}
