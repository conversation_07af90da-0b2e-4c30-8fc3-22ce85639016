﻿using System.ComponentModel.DataAnnotations;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Request model for suspending a care provider
    /// </summary>
    public class SuspendProviderRequest
    {
        /// <summary>
        /// Optional reason for suspension
        /// </summary>
        public string? SuspensionReason { get; set; }

        /// <summary>
        /// Optional additional notes
        /// </summary>
        public string? Notes { get; set; }
    }
}
