﻿using System.Linq.Expressions;

namespace SuperCareApp.Application.Common.Interfaces.Persistence
{
    /// <summary>
    /// Base repository interface for generic CRUD operations
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    public interface IRepositoryBase<TEntity>
        where TEntity : class
    {
        /// <summary>
        /// Gets all entities
        /// </summary>
        /// <returns>All entities</returns>
        IEnumerable<TEntity> ListAll();

        /// <summary>
        /// Gets all entities asynchronously
        /// </summary>
        /// <returns>All entities</returns>
        Task<IEnumerable<TEntity>> ListAllAsync();

        /// <summary>
        /// Gets entities based on specification
        /// </summary>
        /// <param name="specification">The specification to apply</param>
        /// <returns>Filtered entities</returns>
        IReadOnlyList<TEntity> List(ISpecification<TEntity> specification);

        /// <summary>
        /// Gets entities based on specification asynchronously
        /// </summary>
        /// <param name="specification">The specification to apply</param>
        /// <returns>Filtered entities</returns>
        Task<IReadOnlyList<TEntity>> ListAsync(ISpecification<TEntity> specification);

        /// <summary>
        /// Gets an entity by its ID
        /// </summary>
        /// <param name="id">The entity ID</param>
        /// <returns>The entity or null if not found</returns>
        TEntity? GetById(object id);

        /// <summary>
        /// Gets an entity by its ID asynchronously
        /// </summary>
        /// <param name="id">The entity ID</param>
        /// <returns>The entity or null if not found</returns>
        Task<TEntity?> GetByIdAsync(object id);

        /// <summary>
        /// Finds entities based on a predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>Filtered entities</returns>
        IEnumerable<TEntity> Find(Expression<Func<TEntity, bool>> predicate);

        /// <summary>
        /// Finds entities based on a predicate asynchronously
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>Filtered entities</returns>
        Task<IEnumerable<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate);

        /// <summary>
        /// Adds a new entity
        /// </summary>
        /// <param name="entity">The entity to add</param>
        /// <returns>The added entity</returns>
        TEntity Add(TEntity entity);

        /// <summary>
        /// Adds a new entity asynchronously
        /// </summary>
        /// <param name="entity">The entity to add</param>
        /// <returns>The added entity</returns>
        Task<TEntity> AddAsync(TEntity entity);

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <returns>The updated entity</returns>
        TEntity Update(TEntity entity);

        /// <summary>
        /// Updates an existing entity asynchronously
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <returns>The updated entity</returns>
        Task<TEntity> UpdateAsync(TEntity entity);

        /// <summary>
        /// Checks if an entity exists based on a predicate
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>True if an entity exists, false otherwise</returns>
        bool CheckExists(Expression<Func<TEntity, bool>> predicate);

        /// <summary>
        /// Checks if an entity exists based on a predicate asynchronously
        /// </summary>
        /// <param name="predicate">The filter predicate</param>
        /// <returns>True if an entity exists, false otherwise</returns>
        Task<bool> CheckExistsAsync(Expression<Func<TEntity, bool>> predicate);

        /// <summary>
        /// Deletes an entity
        /// </summary>
        /// <param name="entity">The entity to delete</param>
        void Delete(TEntity entity);

        /// <summary>
        /// Deletes an entity asynchronously
        /// </summary>
        /// <param name="entity">The entity to delete</param>
        Task DeleteAsync(TEntity entity);
    }
}
