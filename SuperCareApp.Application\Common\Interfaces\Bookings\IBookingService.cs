﻿using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Bookings;

public interface IBookingService
{
    Task<Result<Guid>> CreateBookingAsync(
        Guid userId,
        Guid providerId,
        Guid categoryId,
        DateTime startDate,
        DateTime endDate,
        TimeOnly? startTime,
        TimeOnly? endTime,
        string? specialInstructions,
        int workingHours = 8
    );

    Task<Result> UpdateBookingAsync(
        Guid userId,
        Guid bookingId,
        Guid categoryId,
        DateTime startDate,
        TimeOnly startTime,
        TimeOnly endTime,
        string? specialInstructions
    );

    Task<Result<BookingResponse>> GetBookingByIdAsync(Guid bookingId);
    Task<Result> DeleteBookingAsync(Guid userId, Guid bookingId);
    Task<Result<PagedBookingList>> GetAllBookingsAsync(
        BookingsRequest request,
        BookingListParams parameters,
        CancellationToken cancellationToken
    );

    Task<Result> CancelBookingAsync(
        Guid userId,
        Guid bookingId,
        string? reason = null,
        bool notifyOtherParty = true,
        bool forceCancel = false
    );

    Task<Result<Guid>> CreateBookingWithWindowsAsync(
        Guid userId,
        Guid providerId,
        Guid categoryId,
        DateTime startDate,
        DateTime endDate,
        bool isRecurring,
        string? specialInstructions,
        List<BookingWindowRequest> bookingWindows
    );

    Task<Result<UpdateBookingResponse>> UpdateBookingWithWindowsAsync(
        Guid userId,
        Guid bookingId,
        UpdateBookingWithWindowsRequest request,
        CancellationToken cancellationToken = default
    );
}
