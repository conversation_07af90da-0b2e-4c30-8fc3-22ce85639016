﻿using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Address.Commands
{
    public record SetPrimaryAddressCommand(Guid UserId, Guid AddressId) : ICommand<Result<Guid>>;

    internal sealed class SetPrimaryAddressCommandHandler
        : ICommandHandler<SetPrimaryAddressCommand, Result<Guid>>
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<SetPrimaryAddressCommandHandler> _logger;

        public SetPrimaryAddressCommandHandler(
            IAddressService addressService,
            ILogger<SetPrimaryAddressCommandHandler> logger
        )
        {
            _addressService = addressService;
            _logger = logger;
        }

        public async Task<Result<Guid>> Handle(
            SetPrimaryAddressCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _addressService.SetPrimaryAddressAsync(
                    request.UserId,
                    request.AddressId
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error setting primary address {AddressId} for user {UserId}",
                    request.AddressId,
                    request.UserId
                );
                return Result.Failure<Guid>(
                    Error.Internal($"Error setting primary address: {ex.Message}")
                );
            }
        }
    }
}
