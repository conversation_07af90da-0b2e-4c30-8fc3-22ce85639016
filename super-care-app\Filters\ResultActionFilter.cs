﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;

namespace super_care_app.Filters
{
    /// <summary>
    /// Action filter to handle Result pattern responses
    /// </summary>
    public class ResultActionFilter : IActionFilter
    {
        private readonly IWebHostEnvironment _env;

        public ResultActionFilter(IWebHostEnvironment env)
        {
            _env = env;
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            // No action needed before execution
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
            if (context.Result is ObjectResult objectResult)
            {
                // Check if the result is already an ApiResponseModel or PaginatedResponseModel of any generic type to avoid double wrapping
                if (
                    objectResult.Value != null
                    && objectResult.Value.GetType().IsGenericType
                    && (
                        objectResult.Value.GetType().GetGenericTypeDefinition()
                            == typeof(ApiResponseModel<>)
                        || objectResult.Value.GetType().GetGenericTypeDefinition()
                            == typeof(PaginatedResponseModel<>)
                    )
                )
                {
                    return;
                }

                if (objectResult.Value is Result result)
                {
                    context.Result = CreateActionResultFromResult(result);
                }
                else if (objectResult.Value is IResult resultInterface)
                {
                    // Handle generic Result<T> through reflection
                    var resultType = resultInterface.GetType();
                    var isSuccessProperty = resultType.GetProperty("IsSuccess");
                    var errorProperty = resultType.GetProperty("Error");
                    var valueProperty = resultType.GetProperty("Value");

                    if (isSuccessProperty != null && errorProperty != null)
                    {
                        bool isSuccess = (bool)isSuccessProperty.GetValue(resultInterface)!;
                        if (!isSuccess)
                        {
                            var error = errorProperty.GetValue(resultInterface) as Error;
                            context.Result = CreateActionResultFromError(error!);
                        }
                        else if (valueProperty != null)
                        {
                            var value = valueProperty.GetValue(resultInterface);
                            var successResponse = new ApiResponseModel<object>(
                                ApiResponseStatusEnum.Success,
                                "The operation completed successfully.",
                                value
                            );
                            context.Result = new OkObjectResult(successResponse);
                        }
                    }
                }
            }
        }

        private IActionResult CreateActionResultFromResult(Result result)
        {
            if (result.IsSuccess)
            {
                var response = new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Success,
                    "The operation completed successfully.",
                    null
                );
                return new OkObjectResult(response);
            }

            return CreateActionResultFromError(result.Error);
        }

        private IActionResult CreateActionResultFromError(Error error)
        {
            var (statusCode, status) = error.Code switch
            {
                "NotFound" => ((int)HttpStatusCode.NotFound, ApiResponseStatusEnum.NotFound),
                "Validation" => ((int)HttpStatusCode.BadRequest, ApiResponseStatusEnum.BadRequest),
                "BadRequest" => ((int)HttpStatusCode.BadRequest, ApiResponseStatusEnum.BadRequest),
                "Unauthorized" => (
                    (int)HttpStatusCode.Unauthorized,
                    ApiResponseStatusEnum.Unauthorized
                ),
                "Forbidden" => ((int)HttpStatusCode.Forbidden, ApiResponseStatusEnum.Forbidden),
                "Conflict" => ((int)HttpStatusCode.Conflict, ApiResponseStatusEnum.Error),
                "ExternalService" => ((int)HttpStatusCode.BadGateway, ApiResponseStatusEnum.Error),
                _ => (
                    (int)HttpStatusCode.InternalServerError,
                    ApiResponseStatusEnum.InternalServerError
                ),
            };

            // Include validation errors in the response if available
            object payload = null;
            if (error.Code == "Validation" && error.ValidationErrors.Count > 0)
            {
                payload = error.ValidationErrors;
            }

            var response = new ApiResponseModel<object>(status, error.Message, payload);

            return new ObjectResult(response) { StatusCode = statusCode };
        }
    }
}
