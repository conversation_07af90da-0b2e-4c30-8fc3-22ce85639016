# Time Tracking API Integration Guide

## Table of Contents

- [Getting Started](#getting-started)
- [Authentication](#authentication)
- [SDK Examples](#sdk-examples)
- [Mobile Integration](#mobile-integration)
- [Web Application Integration](#web-application-integration)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)
- [Testing](#testing)
- [Performance Optimization](#performance-optimization)
- [Troubleshooting](#troubleshooting)

## Getting Started

### Base URL
```
Production: https://api.supercare.com
Staging: https://staging-api.supercare.com
Development: https://localhost:5001
```

### API Version
All endpoints use version 1: `/api/v1/tracking-sessions`

### Content Type
All requests must include: `Content-Type: application/json`

### Response Format
All responses follow the standardized `ApiResponseModel<T>` format:

```json
{
  "apiResponseId": "guid",
  "status": "success|error|badRequest|notFound|unauthorized",
  "statusCode": 200,
  "message": "Operation completed successfully",
  "payload": { /* response data */ },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## Authentication

### JWT Token
All API calls require a valid JWT token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Required Roles
- **CareProvider**: Can manage their own tracking sessions
- **Client**: Can view sessions for their bookings
- **Admin**: Can view all sessions

### Token Refresh
Tokens expire after 60 minutes. Implement automatic refresh:

```javascript
// Example token refresh
const refreshToken = async () => {
  const response = await fetch('/api/v1/auth/refresh-token', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${refreshToken}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (response.ok) {
    const data = await response.json();
    localStorage.setItem('token', data.payload.accessToken);
    return data.payload.accessToken;
  }
  
  throw new Error('Token refresh failed');
};
```

## SDK Examples

### JavaScript/TypeScript SDK

```typescript
interface LocationData {
  latitude: number;
  longitude: number;
  timestamp: string;
  accuracy?: number;
  altitude?: number;
  speed?: number;
  address?: string;
}

interface TrackingSession {
  id: string;
  bookingId: string;
  providerId: string;
  startTime: string;
  endTime?: string;
  status: 'Active' | 'Paused' | 'Completed' | 'Cancelled';
  notes?: string;
  totalHours?: number;
  trackingData?: LocationData[];
  isActive: boolean;
  isCompleted: boolean;
  duration?: string;
  activeDuration?: string;
}

class TrackingSessionAPI {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}/api/v1/tracking-sessions${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }

    return data.payload;
  }

  async startSession(bookingId: string, notes?: string, locationData?: LocationData): Promise<TrackingSession> {
    return this.request<TrackingSession>(`/bookings/${bookingId}/start`, {
      method: 'POST',
      body: JSON.stringify({
        notes,
        initialLocationData: locationData
      })
    });
  }

  async updateSession(sessionId: string, updates: {
    status?: 'Active' | 'Paused';
    notes?: string;
    locationData?: LocationData;
  }): Promise<TrackingSession> {
    return this.request<TrackingSession>(`/${sessionId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
  }

  async endSession(sessionId: string, notes?: string, locationData?: LocationData): Promise<TrackingSession> {
    return this.request<TrackingSession>(`/${sessionId}/end`, {
      method: 'POST',
      body: JSON.stringify({
        notes,
        finalLocationData: locationData
      })
    });
  }

  async pauseSession(sessionId: string): Promise<TrackingSession> {
    return this.request<TrackingSession>(`/${sessionId}/pause`, {
      method: 'POST'
    });
  }

  async resumeSession(sessionId: string): Promise<TrackingSession> {
    return this.request<TrackingSession>(`/${sessionId}/resume`, {
      method: 'POST'
    });
  }

  async getSession(sessionId: string): Promise<TrackingSession> {
    return this.request<TrackingSession>(`/${sessionId}`);
  }

  async getSessionsByBooking(bookingId: string): Promise<TrackingSession[]> {
    return this.request<TrackingSession[]>(`/bookings/${bookingId}`);
  }

  async getMySessions(params?: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }): Promise<TrackingSession[]> {
    const queryParams = new URLSearchParams();
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());

    const query = queryParams.toString();
    return this.request<TrackingSession[]>(`/my-sessions${query ? `?${query}` : ''}`);
  }
}

// Usage Example
const api = new TrackingSessionAPI('https://api.supercare.com', userToken);

// Start a session
const session = await api.startSession('booking-123', 'Starting morning care', {
  latitude: 51.5074,
  longitude: -0.1278,
  timestamp: new Date().toISOString(),
  accuracy: 5.0,
  address: '123 Oak Street, London'
});

console.log('Session started:', session.id);
```

### Python SDK

```python
import requests
from typing import Optional, Dict, List, Any
from datetime import datetime
import json

class LocationData:
    def __init__(self, latitude: float, longitude: float, 
                 timestamp: str = None, accuracy: float = None,
                 altitude: float = None, speed: float = None,
                 address: str = None):
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = timestamp or datetime.utcnow().isoformat() + 'Z'
        self.accuracy = accuracy
        self.altitude = altitude
        self.speed = speed
        self.address = address
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.__dict__.items() if v is not None}

class TrackingSessionAPI:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        })
    
    def _request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        url = f"{self.base_url}/api/v1/tracking-sessions{endpoint}"
        
        response = self.session.request(
            method=method,
            url=url,
            json=data if data else None
        )
        
        response_data = response.json()
        
        if not response.ok:
            raise Exception(f"API Error: {response_data.get('message', 'Unknown error')}")
        
        return response_data['payload']
    
    def start_session(self, booking_id: str, notes: str = None, 
                     location_data: LocationData = None) -> Dict[str, Any]:
        data = {}
        if notes:
            data['notes'] = notes
        if location_data:
            data['initialLocationData'] = location_data.to_dict()
        
        return self._request('POST', f'/bookings/{booking_id}/start', data)
    
    def update_session(self, session_id: str, status: str = None,
                      notes: str = None, location_data: LocationData = None) -> Dict[str, Any]:
        data = {}
        if status:
            data['status'] = status
        if notes:
            data['notes'] = notes
        if location_data:
            data['locationData'] = location_data.to_dict()
        
        return self._request('PUT', f'/{session_id}', data)
    
    def end_session(self, session_id: str, notes: str = None,
                   location_data: LocationData = None) -> Dict[str, Any]:
        data = {}
        if notes:
            data['notes'] = notes
        if location_data:
            data['finalLocationData'] = location_data.to_dict()
        
        return self._request('POST', f'/{session_id}/end', data)
    
    def pause_session(self, session_id: str) -> Dict[str, Any]:
        return self._request('POST', f'/{session_id}/pause')
    
    def resume_session(self, session_id: str) -> Dict[str, Any]:
        return self._request('POST', f'/{session_id}/resume')
    
    def get_session(self, session_id: str) -> Dict[str, Any]:
        return self._request('GET', f'/{session_id}')
    
    def get_sessions_by_booking(self, booking_id: str) -> List[Dict[str, Any]]:
        return self._request('GET', f'/bookings/{booking_id}')
    
    def get_my_sessions(self, start_date: str = None, end_date: str = None,
                       page: int = 1, page_size: int = 10) -> List[Dict[str, Any]]:
        params = []
        if start_date:
            params.append(f'startDate={start_date}')
        if end_date:
            params.append(f'endDate={end_date}')
        params.append(f'page={page}')
        params.append(f'pageSize={page_size}')
        
        query = '?' + '&'.join(params) if params else ''
        return self._request('GET', f'/my-sessions{query}')

# Usage Example
api = TrackingSessionAPI('https://api.supercare.com', user_token)

# Start a session
location = LocationData(
    latitude=51.5074,
    longitude=-0.1278,
    accuracy=5.0,
    address='123 Oak Street, London'
)

session = api.start_session(
    booking_id='booking-123',
    notes='Starting morning care',
    location_data=location
)

print(f"Session started: {session['id']}")
```

### C# SDK

```csharp
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

public class LocationData
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public double? Accuracy { get; set; }
    public double? Altitude { get; set; }
    public double? Speed { get; set; }
    public string? Address { get; set; }
}

public class TrackingSession
{
    public Guid Id { get; set; }
    public Guid BookingId { get; set; }
    public Guid ProviderId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Status { get; set; }
    public string? Notes { get; set; }
    public decimal? TotalHours { get; set; }
    public List<LocationData>? TrackingData { get; set; }
    public bool IsActive { get; set; }
    public bool IsCompleted { get; set; }
    public TimeSpan? Duration { get; set; }
    public TimeSpan? ActiveDuration { get; set; }
}

public class ApiResponse<T>
{
    public Guid ApiResponseId { get; set; }
    public string Status { get; set; }
    public int StatusCode { get; set; }
    public string Message { get; set; }
    public T Payload { get; set; }
    public DateTime Timestamp { get; set; }
}

public class TrackingSessionAPI
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;

    public TrackingSessionAPI(string baseUrl, string token)
    {
        _baseUrl = baseUrl;
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
    }

    private async Task<T> RequestAsync<T>(string endpoint, HttpMethod method, object? data = null)
    {
        var url = $"{_baseUrl}/api/v1/tracking-sessions{endpoint}";
        var request = new HttpRequestMessage(method, url);

        if (data != null)
        {
            var json = JsonSerializer.Serialize(data);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
        }

        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"API Error: {apiResponse?.Message ?? "Unknown error"}");
        }

        return apiResponse.Payload;
    }

    public async Task<TrackingSession> StartSessionAsync(Guid bookingId, string? notes = null, LocationData? locationData = null)
    {
        var data = new Dictionary<string, object>();
        if (!string.IsNullOrEmpty(notes))
            data["notes"] = notes;
        if (locationData != null)
            data["initialLocationData"] = locationData;

        return await RequestAsync<TrackingSession>($"/bookings/{bookingId}/start", HttpMethod.Post, data);
    }

    public async Task<TrackingSession> UpdateSessionAsync(Guid sessionId, string? status = null, string? notes = null, LocationData? locationData = null)
    {
        var data = new Dictionary<string, object>();
        if (!string.IsNullOrEmpty(status))
            data["status"] = status;
        if (!string.IsNullOrEmpty(notes))
            data["notes"] = notes;
        if (locationData != null)
            data["locationData"] = locationData;

        return await RequestAsync<TrackingSession>($"/{sessionId}", HttpMethod.Put, data);
    }

    public async Task<TrackingSession> EndSessionAsync(Guid sessionId, string? notes = null, LocationData? locationData = null)
    {
        var data = new Dictionary<string, object>();
        if (!string.IsNullOrEmpty(notes))
            data["notes"] = notes;
        if (locationData != null)
            data["finalLocationData"] = locationData;

        return await RequestAsync<TrackingSession>($"/{sessionId}/end", HttpMethod.Post, data);
    }

    public async Task<TrackingSession> PauseSessionAsync(Guid sessionId)
    {
        return await RequestAsync<TrackingSession>($"/{sessionId}/pause", HttpMethod.Post);
    }

    public async Task<TrackingSession> ResumeSessionAsync(Guid sessionId)
    {
        return await RequestAsync<TrackingSession>($"/{sessionId}/resume", HttpMethod.Post);
    }

    public async Task<TrackingSession> GetSessionAsync(Guid sessionId)
    {
        return await RequestAsync<TrackingSession>($"/{sessionId}", HttpMethod.Get);
    }

    public async Task<List<TrackingSession>> GetSessionsByBookingAsync(Guid bookingId)
    {
        return await RequestAsync<List<TrackingSession>>($"/bookings/{bookingId}", HttpMethod.Get);
    }

    public async Task<List<TrackingSession>> GetMySessionsAsync(DateTime? startDate = null, DateTime? endDate = null, int page = 1, int pageSize = 10)
    {
        var queryParams = new List<string> { $"page={page}", $"pageSize={pageSize}" };
        
        if (startDate.HasValue)
            queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
        if (endDate.HasValue)
            queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");

        var query = string.Join("&", queryParams);
        return await RequestAsync<List<TrackingSession>>($"/my-sessions?{query}", HttpMethod.Get);
    }
}

// Usage Example
var api = new TrackingSessionAPI("https://api.supercare.com", userToken);

// Start a session
var location = new LocationData
{
    Latitude = 51.5074,
    Longitude = -0.1278,
    Accuracy = 5.0,
    Address = "123 Oak Street, London"
};

var session = await api.StartSessionAsync(
    bookingId: Guid.Parse("booking-123"),
    notes: "Starting morning care",
    locationData: location
);

Console.WriteLine($"Session started: {session.Id}");
```

## Mobile Integration

### React Native Example

```javascript
import React, { useState, useEffect } from 'react';
import { View, Button, Text, Alert } from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import AsyncStorage from '@react-native-async-storage/async-storage';

const TrackingSessionScreen = ({ bookingId }) => {
  const [session, setSession] = useState(null);
  const [isTracking, setIsTracking] = useState(false);
  const [location, setLocation] = useState(null);

  useEffect(() => {
    // Check for existing active session on component mount
    checkActiveSession();
    
    // Start location tracking
    const watchId = Geolocation.watchPosition(
      (position) => {
        const newLocation = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          timestamp: new Date().toISOString(),
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude,
          speed: position.coords.speed
        };
        setLocation(newLocation);
        
        // Update session location if tracking
        if (session && isTracking) {
          updateSessionLocation(newLocation);
        }
      },
      (error) => console.error('Location error:', error),
      {
        enableHighAccuracy: true,
        distanceFilter: 10, // Update every 10 meters
        interval: 30000, // Update every 30 seconds
      }
    );

    return () => Geolocation.clearWatch(watchId);
  }, [session, isTracking]);

  const checkActiveSession = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const response = await fetch(`${API_BASE}/tracking-sessions/my-sessions?status=Active`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      const result = await response.json();
      if (result.status === 'success' && result.payload.length > 0) {
        const activeSession = result.payload.find(s => s.bookingId === bookingId);
        if (activeSession) {
          setSession(activeSession);
          setIsTracking(true);
        }
      }
    } catch (error) {
      console.error('Error checking active session:', error);
    }
  };

  const startSession = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const response = await fetch(`${API_BASE}/tracking-sessions/bookings/${bookingId}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: 'Session started from mobile app',
          initialLocationData: location
        })
      });

      const result = await response.json();
      if (result.status === 'success') {
        setSession(result.payload);
        setIsTracking(true);
        Alert.alert('Success', 'Tracking session started');
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to start session');
      console.error('Start session error:', error);
    }
  };

  const endSession = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const response = await fetch(`${API_BASE}/tracking-sessions/${session.id}/end`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: 'Session ended from mobile app',
          finalLocationData: location
        })
      });

      const result = await response.json();
      if (result.status === 'success') {
        setSession(null);
        setIsTracking(false);
        Alert.alert('Success', `Session ended. Total hours: ${result.payload.totalHours}`);
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to end session');
      console.error('End session error:', error);
    }
  };

  const pauseSession = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const response = await fetch(`${API_BASE}/tracking-sessions/${session.id}/pause`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();
      if (result.status === 'success') {
        setSession(result.payload);
        Alert.alert('Success', 'Session paused');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pause session');
    }
  };

  const resumeSession = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const response = await fetch(`${API_BASE}/tracking-sessions/${session.id}/resume`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();
      if (result.status === 'success') {
        setSession(result.payload);
        Alert.alert('Success', 'Session resumed');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to resume session');
    }
  };

  const updateSessionLocation = async (locationData) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      await fetch(`${API_BASE}/tracking-sessions/${session.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          locationData: locationData
        })
      });
    } catch (error) {
      console.error('Location update error:', error);
    }
  };

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 18, marginBottom: 20 }}>
        Time Tracking
      </Text>
      
      {session ? (
        <View>
          <Text>Session ID: {session.id}</Text>
          <Text>Status: {session.status}</Text>
          <Text>Started: {new Date(session.startTime).toLocaleString()}</Text>
          
          {session.status === 'Active' && (
            <View style={{ marginTop: 20 }}>
              <Button title="Pause Session" onPress={pauseSession} />
              <Button title="End Session" onPress={endSession} color="red" />
            </View>
          )}
          
          {session.status === 'Paused' && (
            <View style={{ marginTop: 20 }}>
              <Button title="Resume Session" onPress={resumeSession} />
              <Button title="End Session" onPress={endSession} color="red" />
            </View>
          )}
        </View>
      ) : (
        <Button title="Start Tracking" onPress={startSession} />
      )}
      
      {location && (
        <View style={{ marginTop: 20 }}>
          <Text>Current Location:</Text>
          <Text>Lat: {location.latitude.toFixed(6)}</Text>
          <Text>Lng: {location.longitude.toFixed(6)}</Text>
          <Text>Accuracy: {location.accuracy}m</Text>
        </View>
      )}
    </View>
  );
};

export default TrackingSessionScreen;
```

### Flutter Example

```dart
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class TrackingSessionScreen extends StatefulWidget {
  final String bookingId;
  
  const TrackingSessionScreen({Key? key, required this.bookingId}) : super(key: key);
  
  @override
  _TrackingSessionScreenState createState() => _TrackingSessionScreenState();
}

class _TrackingSessionScreenState extends State<TrackingSessionScreen> {
  TrackingSession? session;
  bool isTracking = false;
  Position? currentLocation;
  StreamSubscription<Position>? locationSubscription;
  Timer? locationUpdateTimer;

  @override
  void initState() {
    super.initState();
    checkActiveSession();
    startLocationTracking();
  }

  @override
  void dispose() {
    locationSubscription?.cancel();
    locationUpdateTimer?.cancel();
    super.dispose();
  }

  Future<void> startLocationTracking() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return;
      }
    }

    locationSubscription = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    ).listen((Position position) {
      setState(() {
        currentLocation = position;
      });
      
      // Update session location every 5 minutes if tracking
      if (session != null && isTracking) {
        locationUpdateTimer?.cancel();
        locationUpdateTimer = Timer(const Duration(minutes: 5), () {
          updateSessionLocation(position);
        });
      }
    });
  }

  Future<void> checkActiveSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('authToken');
      
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/tracking-sessions/my-sessions?status=Active'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'success') {
          final sessions = (data['payload'] as List)
              .map((s) => TrackingSession.fromJson(s))
              .toList();
          
          final activeSession = sessions
              .where((s) => s.bookingId == widget.bookingId)
              .firstOrNull;
          
          if (activeSession != null) {
            setState(() {
              session = activeSession;
              isTracking = true;
            });
          }
        }
      }
    } catch (e) {
      print('Error checking active session: $e');
    }
  }

  Future<void> startSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('authToken');
      
      final locationData = currentLocation != null ? {
        'latitude': currentLocation!.latitude,
        'longitude': currentLocation!.longitude,
        'timestamp': DateTime.now().toIso8601String(),
        'accuracy': currentLocation!.accuracy,
        'altitude': currentLocation!.altitude,
        'speed': currentLocation!.speed,
      } : null;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/tracking-sessions/bookings/${widget.bookingId}/start'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'notes': 'Session started from mobile app',
          'initialLocationData': locationData,
        }),
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200 && data['status'] == 'success') {
        setState(() {
          session = TrackingSession.fromJson(data['payload']);
          isTracking = true;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tracking session started')),
        );
      } else {
        throw Exception(data['message'] ?? 'Failed to start session');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> endSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('authToken');
      
      final locationData = currentLocation != null ? {
        'latitude': currentLocation!.latitude,
        'longitude': currentLocation!.longitude,
        'timestamp': DateTime.now().toIso8601String(),
        'accuracy': currentLocation!.accuracy,
      } : null;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/tracking-sessions/${session!.id}/end'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'notes': 'Session ended from mobile app',
          'finalLocationData': locationData,
        }),
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200 && data['status'] == 'success') {
        final endedSession = TrackingSession.fromJson(data['payload']);
        
        setState(() {
          session = null;
          isTracking = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Session ended. Total hours: ${endedSession.totalHours}'),
          ),
        );
      } else {
        throw Exception(data['message'] ?? 'Failed to end session');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> pauseSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('authToken');

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/tracking-sessions/${session!.id}/pause'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200 && data['status'] == 'success') {
        setState(() {
          session = TrackingSession.fromJson(data['payload']);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Session paused')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> resumeSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('authToken');

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/tracking-sessions/${session!.id}/resume'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200 && data['status'] == 'success') {
        setState(() {
          session = TrackingSession.fromJson(data['payload']);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Session resumed')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> updateSessionLocation(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('authToken');

      await http.put(
        Uri.parse('${ApiConfig.baseUrl}/tracking-sessions/${session!.id}'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'locationData': {
            'latitude': position.latitude,
            'longitude': position.longitude,
            'timestamp': DateTime.now().toIso8601String(),
            'accuracy': position.accuracy,
          },
        }),
      );
    } catch (e) {
      print('Location update error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Tracking'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (session != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Session ID: ${session!.id}'),
                      Text('Status: ${session!.status}'),
                      Text('Started: ${session!.startTime}'),
                      if (session!.totalHours != null)
                        Text('Total Hours: ${session!.totalHours}'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              if (session!.status == 'Active') ...[
                ElevatedButton(
                  onPressed: pauseSession,
                  child: const Text('Pause Session'),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: endSession,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('End Session'),
                ),
              ] else if (session!.status == 'Paused') ...[
                ElevatedButton(
                  onPressed: resumeSession,
                  child: const Text('Resume Session'),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: endSession,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('End Session'),
                ),
              ],
            ] else ...[
              ElevatedButton(
                onPressed: startSession,
                child: const Text('Start Tracking'),
              ),
            ],
            const SizedBox(height: 24),
            if (currentLocation != null) ...[
              const Text('Current Location:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Latitude: ${currentLocation!.latitude.toStringAsFixed(6)}'),
              Text('Longitude: ${currentLocation!.longitude.toStringAsFixed(6)}'),
              Text('Accuracy: ${currentLocation!.accuracy.toStringAsFixed(1)}m'),
            ],
          ],
        ),
      ),
    );
  }
}

class TrackingSession {
  final String id;
  final String bookingId;
  final String providerId;
  final String startTime;
  final String? endTime;
  final String status;
  final String? notes;
  final double? totalHours;
  final bool isActive;
  final bool isCompleted;

  TrackingSession({
    required this.id,
    required this.bookingId,
    required this.providerId,
    required this.startTime,
    this.endTime,
    required this.status,
    this.notes,
    this.totalHours,
    required this.isActive,
    required this.isCompleted,
  });

  factory TrackingSession.fromJson(Map<String, dynamic> json) {
    return TrackingSession(
      id: json['id'],
      bookingId: json['bookingId'],
      providerId: json['providerId'],
      startTime: json['startTime'],
      endTime: json['endTime'],
      status: json['status'],
      notes: json['notes'],
      totalHours: json['totalHours']?.toDouble(),
      isActive: json['isActive'] ?? false,
      isCompleted: json['isCompleted'] ?? false,
    );
  }
}
```

## Web Application Integration

### React Example

```jsx
import React, { useState, useEffect, useCallback } from 'react';
import { TrackingSessionAPI } from './api/TrackingSessionAPI';

const TrackingSessionComponent = ({ bookingId, userToken }) => {
  const [api] = useState(() => new TrackingSessionAPI('https://api.supercare.com', userToken));
  const [session, setSession] = useState(null);
  const [isTracking, setIsTracking] = useState(false);
  const [location, setLocation] = useState(null);
  const [error, setError] = useState(null);

  // Get current location
  const getCurrentLocation = useCallback(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            timestamp: new Date().toISOString(),
            accuracy: position.coords.accuracy
          });
        },
        (error) => {
          console.error('Location error:', error);
          setError('Unable to get location');
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
      );
    }
  }, []);

  // Check for active session on mount
  useEffect(() => {
    const checkActiveSession = async () => {
      try {
        const sessions = await api.getMySessions();
        const activeSession = sessions.find(s => 
          s.bookingId === bookingId && (s.status === 'Active' || s.status === 'Paused')
        );
        
        if (activeSession) {
          setSession(activeSession);
          setIsTracking(true);
        }
      } catch (err) {
        console.error('Error checking active session:', err);
      }
    };

    checkActiveSession();
    getCurrentLocation();
  }, [api, bookingId, getCurrentLocation]);

  // Update location periodically during tracking
  useEffect(() => {
    let interval;
    
    if (isTracking && session) {
      interval = setInterval(() => {
        getCurrentLocation();
        
        // Update session location every 5 minutes
        if (location) {
          api.updateSession(session.id, { locationData: location })
            .catch(err => console.error('Location update failed:', err));
        }
      }, 300000); // 5 minutes
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTracking, session, location, api, getCurrentLocation]);

  const startSession = async () => {
    try {
      setError(null);
      const newSession = await api.startSession(
        bookingId,
        'Session started from web app',
        location
      );
      
      setSession(newSession);
      setIsTracking(true);
    } catch (err) {
      setError(err.message);
    }
  };

  const endSession = async () => {
    try {
      setError(null);
      const endedSession = await api.endSession(
        session.id,
        'Session ended from web app',
        location
      );
      
      setSession(null);
      setIsTracking(false);
      
      alert(`Session ended. Total hours: ${endedSession.totalHours}`);
    } catch (err) {
      setError(err.message);
    }
  };

  const pauseSession = async () => {
    try {
      setError(null);
      const pausedSession = await api.pauseSession(session.id);
      setSession(pausedSession);
    } catch (err) {
      setError(err.message);
    }
  };

  const resumeSession = async () => {
    try {
      setError(null);
      const resumedSession = await api.resumeSession(session.id);
      setSession(resumedSession);
    } catch (err) {
      setError(err.message);
    }
  };

  const formatDuration = (startTime, endTime) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diff = end - start;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="tracking-session">
      <h3>Time Tracking</h3>
      
      {error && (
        <div className="error-message" style={{ color: 'red', marginBottom: '1rem' }}>
          {error}
        </div>
      )}
      
      {session ? (
        <div className="session-info">
          <div className="session-details">
            <p><strong>Session ID:</strong> {session.id}</p>
            <p><strong>Status:</strong> {session.status}</p>
            <p><strong>Started:</strong> {new Date(session.startTime).toLocaleString()}</p>
            <p><strong>Duration:</strong> {formatDuration(session.startTime, session.endTime)}</p>
            {session.totalHours && (
              <p><strong>Total Hours:</strong> {session.totalHours}</p>
            )}
          </div>
          
          <div className="session-controls">
            {session.status === 'Active' && (
              <>
                <button onClick={pauseSession} className="btn btn-warning">
                  Pause Session
                </button>
                <button onClick={endSession} className="btn btn-danger">
                  End Session
                </button>
              </>
            )}
            
            {session.status === 'Paused' && (
              <>
                <button onClick={resumeSession} className="btn btn-success">
                  Resume Session
                </button>
                <button onClick={endSession} className="btn btn-danger">
                  End Session
                </button>
              </>
            )}
          </div>
        </div>
      ) : (
        <div className="start-session">
          <button onClick={startSession} className="btn btn-primary">
            Start Tracking
          </button>
        </div>
      )}
      
      {location && (
        <div className="location-info" style={{ marginTop: '1rem' }}>
          <h4>Current Location</h4>
          <p>Latitude: {location.latitude.toFixed(6)}</p>
          <p>Longitude: {location.longitude.toFixed(6)}</p>
          <p>Accuracy: {location.accuracy}m</p>
        </div>
      )}
    </div>
  );
};

export default TrackingSessionComponent;
```

## Error Handling

### Comprehensive Error Handling

```javascript
class TrackingSessionError extends Error {
  constructor(message, code, statusCode) {
    super(message);
    this.name = 'TrackingSessionError';
    this.code = code;
    this.statusCode = statusCode;
  }
}

const handleApiError = (response, data) => {
  const errorMap = {
    400: 'BadRequest',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'NotFound',
    409: 'Conflict',
    500: 'InternalServerError'
  };

  const errorCode = errorMap[response.status] || 'Unknown';
  throw new TrackingSessionError(
    data.message || 'An error occurred',
    errorCode,
    response.status
  );
};

// Usage in API calls
const apiRequest = async (url, options) => {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (!response.ok) {
      handleApiError(response, data);
    }
    
    return data.payload;
  } catch (error) {
    if (error instanceof TrackingSessionError) {
      // Handle specific API errors
      switch (error.code) {
        case 'Unauthorized':
          // Redirect to login or refresh token
          await refreshToken();
          break;
        case 'BadRequest':
          // Show validation errors to user
          showValidationErrors(error.message);
          break;
        case 'Conflict':
          // Handle business rule violations
          showConflictDialog(error.message);
          break;
        default:
          // Show generic error
          showErrorMessage(error.message);
      }
    } else {
      // Handle network or other errors
      showErrorMessage('Network error. Please check your connection.');
    }
    
    throw error;
  }
};
```

## Best Practices

### 1. Location Privacy
```javascript
// Always request user permission for location
const requestLocationPermission = async () => {
  if (navigator.permissions) {
    const permission = await navigator.permissions.query({ name: 'geolocation' });
    if (permission.state === 'denied') {
      throw new Error('Location permission denied');
    }
  }
};

// Provide option to disable location tracking
const startSessionWithoutLocation = async (bookingId, notes) => {
  return api.startSession(bookingId, notes, null);
};
```

### 2. Offline Support
```javascript
// Queue actions when offline
const actionQueue = [];

const queueAction = (action) => {
  actionQueue.push({
    ...action,
    timestamp: Date.now()
  });
  localStorage.setItem('queuedActions', JSON.stringify(actionQueue));
};

const processQueue = async () => {
  const queue = JSON.parse(localStorage.getItem('queuedActions') || '[]');
  
  for (const action of queue) {
    try {
      await executeAction(action);
    } catch (error) {
      console.error('Failed to process queued action:', error);
    }
  }
  
  localStorage.removeItem('queuedActions');
};

// Check online status
window.addEventListener('online', processQueue);
```

### 3. Battery Optimization
```javascript
// Reduce location update frequency when battery is low
const optimizeForBattery = () => {
  if (navigator.getBattery) {
    navigator.getBattery().then((battery) => {
      const updateInterval = battery.level < 0.2 ? 600000 : 300000; // 10min vs 5min
      
      setInterval(updateLocation, updateInterval);
    });
  }
};
```

### 4. Data Validation
```javascript
const validateLocationData = (location) => {
  if (!location) return false;
  
  const { latitude, longitude, accuracy } = location;
  
  // Basic validation
  if (latitude < -90 || latitude > 90) return false;
  if (longitude < -180 || longitude > 180) return false;
  if (accuracy && accuracy > 1000) return false; // Too inaccurate
  
  return true;
};

const validateSessionData = (session) => {
  if (!session.bookingId || !session.providerId) {
    throw new Error('Invalid session data');
  }
  
  if (session.endTime && new Date(session.endTime) <= new Date(session.startTime)) {
    throw new Error('End time must be after start time');
  }
};
```

## Testing

### Unit Tests Example (Jest)

```javascript
import { TrackingSessionAPI } from '../TrackingSessionAPI';

// Mock fetch
global.fetch = jest.fn();

describe('TrackingSessionAPI', () => {
  let api;
  
  beforeEach(() => {
    api = new TrackingSessionAPI('https://api.test.com', 'test-token');
    fetch.mockClear();
  });

  test('should start session successfully', async () => {
    const mockResponse = {
      status: 'success',
      payload: {
        id: 'session-123',
        bookingId: 'booking-456',
        status: 'Active',
        startTime: '2024-01-15T10:00:00Z'
      }
    };

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    const result = await api.startSession('booking-456', 'Test session');

    expect(fetch).toHaveBeenCalledWith(
      'https://api.test.com/api/v1/tracking-sessions/bookings/booking-456/start',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json'
        }),
        body: JSON.stringify({
          notes: 'Test session',
          initialLocationData: undefined
        })
      })
    );

    expect(result.id).toBe('session-123');
    expect(result.status).toBe('Active');
  });

  test('should handle API errors', async () => {
    const mockError = {
      status: 'badRequest',
      message: 'Active session already exists'
    };

    fetch.mockResolvedValueOnce({
      ok: false,
      status: 400,
      json: async () => mockError
    });

    await expect(api.startSession('booking-456')).rejects.toThrow('Active session already exists');
  });
});
```

### Integration Tests Example

```javascript
describe('Tracking Session Integration', () => {
  let api;
  let testBookingId;
  let testToken;

  beforeAll(async () => {
    // Setup test environment
    testToken = await getTestToken();
    testBookingId = await createTestBooking();
    api = new TrackingSessionAPI('https://staging-api.supercare.com', testToken);
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  });

  test('complete session workflow', async () => {
    // Start session
    const session = await api.startSession(testBookingId, 'Integration test session');
    expect(session.status).toBe('Active');

    // Pause session
    const pausedSession = await api.pauseSession(session.id);
    expect(pausedSession.status).toBe('Paused');

    // Resume session
    const resumedSession = await api.resumeSession(session.id);
    expect(resumedSession.status).toBe('Active');

    // End session
    const endedSession = await api.endSession(session.id, 'Test completed');
    expect(endedSession.status).toBe('Completed');
    expect(endedSession.totalHours).toBeGreaterThan(0);
  });
});
```

## Performance Optimization

### 1. Request Batching
```javascript
class BatchedLocationUpdater {
  constructor(api, sessionId, batchSize = 10, interval = 60000) {
    this.api = api;
    this.sessionId = sessionId;
    this.batchSize = batchSize;
    this.interval = interval;
    this.locationQueue = [];
    this.timer = null;
  }

  addLocation(location) {
    this.locationQueue.push(location);
    
    if (this.locationQueue.length >= this.batchSize) {
      this.flush();
    } else if (!this.timer) {
      this.timer = setTimeout(() => this.flush(), this.interval);
    }
  }

  async flush() {
    if (this.locationQueue.length === 0) return;
    
    const locations = [...this.locationQueue];
    this.locationQueue = [];
    
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    try {
      await this.api.updateSession(this.sessionId, {
        locationData: locations[locations.length - 1], // Send latest location
        notes: `Batch update: ${locations.length} locations`
      });
    } catch (error) {
      console.error('Batch location update failed:', error);
      // Re-queue failed locations
      this.locationQueue.unshift(...locations);
    }
  }
}
```

### 2. Caching
```javascript
class CachedTrackingSessionAPI extends TrackingSessionAPI {
  constructor(baseUrl, token, cacheTimeout = 300000) { // 5 minutes
    super(baseUrl, token);
    this.cache = new Map();
    this.cacheTimeout = cacheTimeout;
  }

  async getSession(sessionId) {
    const cacheKey = `session-${sessionId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const session = await super.getSession(sessionId);
    
    this.cache.set(cacheKey, {
      data: session,
      timestamp: Date.now()
    });

    return session;
  }

  invalidateCache(sessionId) {
    this.cache.delete(`session-${sessionId}`);
  }

  async updateSession(sessionId, updates) {
    const result = await super.updateSession(sessionId, updates);
    this.invalidateCache(sessionId);
    return result;
  }
}
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Location Not Working
```javascript
const diagnoseLocationIssues = async () => {
  // Check if geolocation is supported
  if (!navigator.geolocation) {
    return 'Geolocation not supported by browser';
  }

  // Check permissions
  if (navigator.permissions) {
    const permission = await navigator.permissions.query({ name: 'geolocation' });
    if (permission.state === 'denied') {
      return 'Location permission denied';
    }
  }

  // Test location access
  return new Promise((resolve) => {
    navigator.geolocation.getCurrentPosition(
      () => resolve('Location working'),
      (error) => {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            return resolve('Location permission denied');
          case error.POSITION_UNAVAILABLE:
            return resolve('Location unavailable');
          case error.TIMEOUT:
            return resolve('Location request timeout');
          default:
            return resolve('Unknown location error');
        }
      },
      { timeout: 10000 }
    );
  });
};
```

#### 2. Session State Issues
```javascript
const validateSessionState = (session) => {
  const issues = [];

  if (!session.id) issues.push('Missing session ID');
  if (!session.bookingId) issues.push('Missing booking ID');
  if (!session.startTime) issues.push('Missing start time');
  
  if (session.status === 'Completed' && !session.endTime) {
    issues.push('Completed session missing end time');
  }
  
  if (session.endTime && new Date(session.endTime) <= new Date(session.startTime)) {
    issues.push('End time before start time');
  }

  return issues;
};
```

#### 3. Network Issues
```javascript
const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      const delay = baseDelay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

// Usage
const startSessionWithRetry = (bookingId, notes, location) => {
  return retryWithBackoff(() => api.startSession(bookingId, notes, location));
};
```

---

*This integration guide provides comprehensive examples for implementing the Time Tracking API across different platforms and technologies. Adapt the examples to your specific requirements and technology stack.*