﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Storage;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Identity.Commands;

namespace SuperCareApp.Persistence.Test.Account
{
    public class UpdateProfileCommandHandlerTest : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly Mock<IUserProfileService> _userProfileServiceMock;
        private readonly Mock<ICareProviderProfileService> _careProviderProfileServiceMock;
        private readonly Mock<ILogger<UpdateProfileCommandHandler>> _loggerMock;
        private readonly Mock<IFileStorageService> _fileStorageServiceMock;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly UpdateProfileCommandHandler _handler;

        public UpdateProfileCommandHandlerTest()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _userProfileServiceMock = new Mock<IUserProfileService>();
            _careProviderProfileServiceMock = new Mock<ICareProviderProfileService>();
            _loggerMock = new Mock<ILogger<UpdateProfileCommandHandler>>();
            _fileStorageServiceMock = new Mock<IFileStorageService>();
            _mediatorMock = new Mock<IMediator>();

            _handler = new UpdateProfileCommandHandler(
                _userProfileServiceMock.Object,
                _careProviderProfileServiceMock.Object,
                _fileStorageServiceMock.Object,
                _mediatorMock.Object,
                _loggerMock.Object,
                _context
            );
        }

        public void Dispose()
        {
            _context.Database.EnsureDeleted();
            _context.Dispose();
        }

        #region Test Data Helpers

        private static UserProfile CreateTestUserProfile(Guid userId, bool emailVerified = true)
        {
            return new UserProfile
            {
                Id = Guid.NewGuid(),
                ApplicationUserId = userId,
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+**********",
                Gender = "Male",
                DateOfBirth = DateTime.UtcNow.AddYears(-30),
                User = new ApplicationUser
                {
                    Id = userId,
                    Email = "<EMAIL>",
                    EmailVerified = emailVerified,
                },
            };
        }

        private static CareProviderProfile CreateTestCareProviderProfile(
            Guid userId,
            VerificationStatus status = VerificationStatus.Verified
        )
        {
            return new CareProviderProfile
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                YearsExperience = 5,
                BufferDuration = 30,
                VerificationStatus = status,
                User = new ApplicationUser
                {
                    Id = userId,
                    Email = "<EMAIL>",
                    EmailVerified = true,
                },
            };
        }

        private static UpdateUserRequest CreateTestUpdateRequest()
        {
            return new UpdateUserRequest
            {
                FirstName = "Jane",
                LastName = "Smith",
                PhoneNumber = "+**********",
                Email = "<EMAIL>",
                Gender = "Female",
                DateOfBirth = "1990-05-15",
                YearsExperience = 7,
                BufferDuration = 45,
                PrimaryAddress = new AddressInfo
                {
                    StreetAddress = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Latitude = 40.7128m,
                    Longitude = -74.0060m,
                    Label = "Home",
                },
            };
        }

        private static Mock<IFormFile> CreateMockFormFile(
            string fileName = "test.jpg",
            long length = 1024
        )
        {
            var mockFile = new Mock<IFormFile>();
            mockFile.Setup(f => f.FileName).Returns(fileName);
            mockFile.Setup(f => f.Length).Returns(length);
            mockFile.Setup(f => f.ContentType).Returns("image/jpeg");
            return mockFile;
        }

        #endregion

        #region Validation Tests

        [Fact]
        public async Task Handle_UnsupportedUserType_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var request = CreateTestUpdateRequest();
            var command = new UpdateProfileCommand(userId, (UserType)999, request);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("BadRequest", result.Error.Code);
            Assert.Equal("Unsupported user type", result.Error.Message);
        }

        [Fact]
        public async Task Handle_UserProfileNotFound_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var request = CreateTestUpdateRequest();
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(
                    Result.Failure<UserProfile?>(Error.NotFound("User profile not found"))
                );

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("NotFound", result.Error.Code);
            Assert.Equal("User profile not found", result.Error.Message);
        }

        [Fact]
        public async Task Handle_UnverifiedEmail_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId, emailVerified: false);
            var request = CreateTestUpdateRequest();
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Forbidden", result.Error.Code);
            Assert.Equal("Cannot update profile until email is verified", result.Error.Message);
        }

        [Fact]
        public async Task Handle_CareProviderProfileNotFound_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = CreateTestUpdateRequest();
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(
                    Result.Failure<CareProviderProfile?>(
                        Error.NotFound("Care provider profile not found")
                    )
                );

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("NotFound", result.Error.Code);
            Assert.Equal("Care provider profile not found", result.Error.Message);
        }

        [Fact]
        public async Task Handle_UnverifiedCareProviderProfile_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var careProviderProfile = CreateTestCareProviderProfile(
                userId,
                VerificationStatus.Pending
            );
            var request = CreateTestUpdateRequest();
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Forbidden", result.Error.Code);
            Assert.Equal(
                "Cannot update profile until verification is complete",
                result.Error.Message
            );
        }

        [Fact]
        public async Task Handle_InvalidRequestData_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var command = new UpdateProfileCommand(userId, UserType.Client, null!);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            // The null request causes a NullReferenceException in Handle method when checking request.Request.ProfilePicture
            // This gets caught by the outer try-catch and returns Internal error
            Assert.Equal("Internal", result.Error.Code);
            Assert.Contains("Error updating profile", result.Error.Message);
        }

        #endregion

        #region Edge Case Tests

        [Fact]
        public async Task Handle_NoChangesProvided_ReturnsSuccess()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest(); // Empty request with no changes
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(It.IsAny<Guid>(), It.IsAny<UserProfile>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_EmptyStringValues_ReturnsSuccess()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "",
                LastName = "",
                PhoneNumber = "",
                Email = "",
                Gender = "",
                DateOfBirth = "",
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(It.IsAny<Guid>(), It.IsAny<UserProfile>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_WhitespaceOnlyValues_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "   ",
                LastName = "   ",
                PhoneNumber = "   ",
                Email = "   ",
                Gender = "   ",
                DateOfBirth = "   ",
            };

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            // Act

            var validator = new UpdateUserRequestValidator();
            var validationResult = await validator.ValidateAsync(request);

            // Assert

            Assert.False(validationResult.IsValid);
        }

        [Fact]
        public async Task Handle_InvalidDateFormat_ThrowsException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                DateOfBirth = "invalid-date",
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            // Act & Assert
            var result = await _handler.Handle(command, CancellationToken.None);
            Assert.False(result.IsSuccess);
            Assert.Equal("Internal", result.Error.Code);
            Assert.Contains("Error updating profile", result.Error.Message);
        }

        [Fact]
        public async Task Handle_UserProfileServiceUpdateFails_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest { FirstName = "Jane" };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Failure<Guid>(Error.Internal("Database error")));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Internal", result.Error.Code);
            Assert.Equal("Database error", result.Error.Message);
        }

        [Fact]
        public async Task Handle_CareProviderServiceUpdateFails_ReturnsFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var careProviderProfile = CreateTestCareProviderProfile(userId);
            var request = new UpdateUserRequest { YearsExperience = 10 };
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

            _careProviderProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()))
                .ReturnsAsync(Result.Failure<Guid>(Error.Internal("Provider update failed")));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Internal", result.Error.Code);
            Assert.Equal("Provider update failed", result.Error.Message);
        }

        [Fact]
        public async Task Handle_ProfilePictureUploadFails_ContinuesWithProfileUpdate()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var mockFile = CreateMockFormFile();
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                ProfilePicture = mockFile.Object,
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _mediatorMock
                .Setup(x =>
                    x.Send(It.IsAny<UploadProfilePictureCommand>(), It.IsAny<CancellationToken>())
                )
                .ReturnsAsync(
                    Result.Failure<ProfilePictureResponse>(Error.Internal("Upload failed"))
                );

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess); // Profile update should still succeed
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.IsAny<UserProfile>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_GeneralException_ReturnsInternalError()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var request = CreateTestUpdateRequest();
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Internal", result.Error.Code);
            Assert.Contains(
                "Error updating profile: Database connection failed",
                result.Error.Message
            );
        }

        #endregion

        #region Workflow Tests - Client Profile Updates

        [Fact]
        public async Task Handle_ClientProfileUpdate_BasicFields_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                LastName = "Smith",
                PhoneNumber = "+**********",
                Email = "<EMAIL>",
                Gender = "Female",
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x =>
                    x.UpdateAsync(
                        userId,
                        It.Is<UserProfile>(p =>
                            p.FirstName == "Jane"
                            && p.LastName == "Smith"
                            && p.PhoneNumber == "+**********"
                            && p.Gender == "Female"
                            && p.User.Email == "<EMAIL>"
                        )
                    ),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ClientProfileUpdate_WithDateOfBirth_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest { FirstName = "Jane", DateOfBirth = "1990-05-15" };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x =>
                    x.UpdateAsync(
                        userId,
                        It.Is<UserProfile>(p =>
                            p.FirstName == "Jane"
                            && p.DateOfBirth.HasValue
                            && p.DateOfBirth.Value.Date == new DateTime(1990, 5, 15).Date
                        )
                    ),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ClientProfileUpdate_WithAddress_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                PrimaryAddress = new AddressInfo
                {
                    StreetAddress = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Latitude = 40.7128m,
                    Longitude = -74.0060m,
                    Label = "Home",
                },
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x =>
                    x.UpdateAsync(
                        userId,
                        It.Is<UserProfile>(p =>
                            p.FirstName == "Jane"
                            && p.User.UserAddresses.Any(ua =>
                                ua.Address.StreetAddress == "123 Main St"
                                && ua.Address.City == "New York"
                                && ua.IsPrimary == true
                            )
                        )
                    ),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ClientProfileUpdate_PartialFields_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "Jane", // Only updating first name
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x =>
                    x.UpdateAsync(
                        userId,
                        It.Is<UserProfile>(p =>
                            p.FirstName == "Jane"
                            && p.LastName == "Doe"
                            && // Should remain unchanged
                            p.PhoneNumber == "+**********" // Should remain unchanged
                        )
                    ),
                Times.Once
            );
        }

        #endregion

        #region Workflow Tests - CareProvider Profile Updates

        [Fact]
        public async Task Handle_CareProviderProfileUpdate_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var careProviderProfile = CreateTestCareProviderProfile(userId);
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                LastName = "Smith",
                YearsExperience = 10,
                BufferDuration = 60,
            };
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

            _careProviderProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x =>
                    x.UpdateAsync(
                        userId,
                        It.Is<UserProfile>(p => p.FirstName == "Jane" && p.LastName == "Smith")
                    ),
                Times.Once
            );
            _careProviderProfileServiceMock.Verify(
                x =>
                    x.UpdateAsync(
                        userId,
                        It.Is<CareProviderProfile>(p =>
                            p.YearsExperience == 10 && p.BufferDuration == 60
                        )
                    ),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_CareProviderProfileUpdate_WithAddress_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var careProviderProfile = CreateTestCareProviderProfile(userId);
            var request = new UpdateUserRequest
            {
                YearsExperience = 8,
                PrimaryAddress = new AddressInfo
                {
                    StreetAddress = "456 Oak Ave",
                    City = "Los Angeles",
                    State = "CA",
                    PostalCode = "90210",
                    Label = "Office",
                },
            };
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

            _careProviderProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _careProviderProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.Is<CareProviderProfile>(p => p.YearsExperience == 8)),
                Times.Once
            );

            // For CareProvider address updates, the handler directly manipulates the DbContext
            // The address operations are performed but not saved to the in-memory database
            // unless SaveChanges is called. Since we're mocking the services, we can verify
            // that the correct service methods were called instead of checking the database state.
            _careProviderProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_CareProviderProfileUpdate_OnlyProviderFields_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var careProviderProfile = CreateTestCareProviderProfile(userId);
            var request = new UpdateUserRequest
            {
                BufferDuration = 90, // Only updating provider-specific field
            };
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

            _careProviderProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(It.IsAny<Guid>(), It.IsAny<UserProfile>()),
                Times.Never
            );
            _careProviderProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.Is<CareProviderProfile>(p => p.BufferDuration == 90)),
                Times.Once
            );
        }

        #endregion

        #region Workflow Tests - Profile Picture Upload

        [Fact]
        public async Task Handle_ProfilePictureUpload_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var mockFile = CreateMockFormFile();
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                ProfilePicture = mockFile.Object,
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            var profilePictureResponse = new ProfilePictureResponse
            {
                ImageUrl = "https://storage/profile-pictures/test.jpg",
                FileName = "test.jpg",
                UploadedAt = DateTime.UtcNow,
            };

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _mediatorMock
                .Setup(x =>
                    x.Send(It.IsAny<UploadProfilePictureCommand>(), It.IsAny<CancellationToken>())
                )
                .ReturnsAsync(Result.Success(profilePictureResponse));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _mediatorMock.Verify(
                x =>
                    x.Send(
                        It.Is<UploadProfilePictureCommand>(cmd =>
                            cmd.File == mockFile.Object && cmd.UserId == userId
                        ),
                        It.IsAny<CancellationToken>()
                    ),
                Times.Once
            );
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.IsAny<UserProfile>()),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_OnlyProfilePictureUpload_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var mockFile = CreateMockFormFile();
            var request = new UpdateUserRequest
            {
                ProfilePicture = mockFile.Object, // Only uploading picture, no other changes
            };
            var command = new UpdateProfileCommand(userId, UserType.Client, request);

            var profilePictureResponse = new ProfilePictureResponse
            {
                ImageUrl = "https://storage/profile-pictures/test.jpg",
                FileName = "test.jpg",
                UploadedAt = DateTime.UtcNow,
            };

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _mediatorMock
                .Setup(x =>
                    x.Send(It.IsAny<UploadProfilePictureCommand>(), It.IsAny<CancellationToken>())
                )
                .ReturnsAsync(Result.Success(profilePictureResponse));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _mediatorMock.Verify(
                x => x.Send(It.IsAny<UploadProfilePictureCommand>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(It.IsAny<Guid>(), It.IsAny<UserProfile>()),
                Times.Never
            );
        }

        [Fact]
        public async Task Handle_CareProviderWithProfilePicture_Success()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userProfile = CreateTestUserProfile(userId);
            var careProviderProfile = CreateTestCareProviderProfile(userId);
            var mockFile = CreateMockFormFile();
            var request = new UpdateUserRequest
            {
                FirstName = "Jane",
                YearsExperience = 12,
                ProfilePicture = mockFile.Object,
            };
            var command = new UpdateProfileCommand(userId, UserType.CareProvider, request);

            var profilePictureResponse = new ProfilePictureResponse
            {
                ImageUrl = "https://storage/profile-pictures/test.jpg",
                FileName = "test.jpg",
                UploadedAt = DateTime.UtcNow,
            };

            _userProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

            _userProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<UserProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _careProviderProfileServiceMock
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

            _careProviderProfileServiceMock
                .Setup(x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()))
                .ReturnsAsync(Result.Success(Guid.NewGuid()));

            _mediatorMock
                .Setup(x =>
                    x.Send(It.IsAny<UploadProfilePictureCommand>(), It.IsAny<CancellationToken>())
                )
                .ReturnsAsync(Result.Success(profilePictureResponse));

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            _mediatorMock.Verify(
                x => x.Send(It.IsAny<UploadProfilePictureCommand>(), It.IsAny<CancellationToken>()),
                Times.Once
            );
            _userProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.IsAny<UserProfile>()),
                Times.Once
            );
            _careProviderProfileServiceMock.Verify(
                x => x.UpdateAsync(userId, It.IsAny<CareProviderProfile>()),
                Times.Once
            );
        }

        #endregion
    }
}
