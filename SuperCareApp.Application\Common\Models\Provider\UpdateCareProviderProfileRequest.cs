using System.ComponentModel.DataAnnotations;

namespace SuperCareApp.Application.Common.Models.Provider
{
    public record UpdateCareProviderProfileRequest
    {
        [Required(ErrorMessage = "Name is required.")]
        [RegularExpression(
            @"^[a-zA-Z\s'-]{2,100}$",
            ErrorMessage = "Name must contain only letters, spaces, apostrophes, and hyphens (2–100 characters)."
        )]
        public string Name { get; set; }

        [Required(ErrorMessage = "Phone number is required.")]
        [RegularExpression(
            @"^\+?[1-9]\d{9,14}$",
            ErrorMessage = "Phone number must be numeric and 10–15 digits long (e.g., +**********)."
        )]
        public string PhoneNumber { get; set; }

        [Required(ErrorMessage = "Gender is required.")]
        [RegularExpression(
            @"^(Male|Female|Other)$",
            ErrorMessage = "Gender must be 'Male', 'Female', or 'Other'."
        )]
        public string Gender { get; set; }

        [Range(0, 100, ErrorMessage = "Years of experience must be between 0 and 100.")]
        public int YearsExperience { get; set; }

        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? DateOfBirth { get; set; }
    }
}
