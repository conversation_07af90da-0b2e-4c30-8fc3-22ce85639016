﻿using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Domain.Common.Resilience
{
    /// <summary>
    /// Provides timeout functionality for operations
    /// </summary>
    public static class Timeout
    {
        /// <summary>
        /// Executes an operation with a timeout
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="timeoutMilliseconds">The timeout in milliseconds</param>
        /// <returns>The result of the operation</returns>
        public static async Task<Result<T>> ExecuteWithTimeoutAsync<T>(
            Func<CancellationToken, Task<Result<T>>> operation,
            int timeoutMilliseconds = 30000
        )
        {
            using var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.CancelAfter(timeoutMilliseconds);

            try
            {
                return await operation(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
                when (cancellationTokenSource.IsCancellationRequested)
            {
                return Result.Failure<T>(
                    Error.ExternalService($"Operation timed out after {timeoutMilliseconds}ms")
                );
            }
            catch (Exception ex)
            {
                return Result.Failure<T>(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Executes an operation with a timeout
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <param name="timeoutMilliseconds">The timeout in milliseconds</param>
        /// <returns>The result of the operation</returns>
        public static async Task<Result> ExecuteWithTimeoutAsync(
            Func<CancellationToken, Task<Result>> operation,
            int timeoutMilliseconds = 30000
        )
        {
            using var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.CancelAfter(timeoutMilliseconds);

            try
            {
                return await operation(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
                when (cancellationTokenSource.IsCancellationRequested)
            {
                return Result.Failure(
                    Error.ExternalService($"Operation timed out after {timeoutMilliseconds}ms")
                );
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
        }

        /// <summary>
        /// Executes an operation with a timeout
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="timeoutMilliseconds">The timeout in milliseconds</param>
        /// <returns>The result of the operation or throws an exception</returns>
        public static async Task<T> ExecuteWithTimeoutAsync<T>(
            Func<CancellationToken, Task<T>> operation,
            int timeoutMilliseconds = 30000
        )
        {
            using var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.CancelAfter(timeoutMilliseconds);

            try
            {
                var task = operation(cancellationTokenSource.Token);
                var completedTask = await Task.WhenAny(
                    task,
                    Task.Delay(timeoutMilliseconds, cancellationTokenSource.Token)
                );

                if (completedTask == task)
                {
                    cancellationTokenSource.Cancel(); // Cancel the delay task
                    return await task;
                }
                else
                {
                    throw new TimeoutException(
                        $"Operation timed out after {timeoutMilliseconds}ms"
                    );
                }
            }
            catch (OperationCanceledException)
                when (cancellationTokenSource.IsCancellationRequested)
            {
                throw new TimeoutException($"Operation timed out after {timeoutMilliseconds}ms");
            }
        }
    }
}
