<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>super_care_app</RootNamespace>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference
      Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore"
      Version="8.0.14"
    />
    <PackageReference
      Include="Microsoft.Extensions.Options.ConfigurationExtensions"
      Version="9.0.0"
    />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SuperCareApp.Persistence\SuperCareApp.Persistence.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Controllers\UsersController.new.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Models\Doc\Auth\" />
    <Folder Include="wwwroot\images\" />
    <Folder Include="wwwroot\uploads\" />
  </ItemGroup>
</Project>
