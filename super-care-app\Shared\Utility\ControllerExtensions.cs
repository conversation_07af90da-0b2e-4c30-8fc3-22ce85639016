using Microsoft.AspNetCore.Mvc;

namespace SuperCareApp.Shared.Utility
{
    /// <summary>
    /// Extension methods for controllers to create standardized API responses
    /// </summary>
    public static class ControllerExtensions
    {
        /// <summary>
        /// Creates a success response
        /// </summary>
        public static IActionResult Success<T>(
            this ControllerBase controller,
            T? data = default,
            string? message = null
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.Success,
                message ?? "The operation completed successfully.",
                data
            );

            return controller.Ok(response);
        }

        /// <summary>
        /// Creates an error response
        /// </summary>
        public static IActionResult Error<T>(
            this ControllerBase controller,
            string message,
            T? data = default,
            int statusCode = 400
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.Error,
                message,
                data,
                statusCode.ToString()
            );

            return controller.StatusCode(statusCode, response);
        }

        /// <summary>
        /// Creates a not found response
        /// </summary>
        public static IActionResult NotFound<T>(
            this ControllerBase controller,
            string? message = null,
            T? data = default
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.NotFound,
                message ?? "The requested resource was not found.",
                data
            );

            return controller.NotFound(response);
        }

        /// <summary>
        /// Creates a bad request response
        /// </summary>
        public static IActionResult BadRequest<T>(
            this ControllerBase controller,
            string? message = null,
            T? data = default
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.BadRequest,
                message ?? "The request is invalid.",
                data
            );

            return controller.BadRequest(response);
        }

        /// <summary>
        /// Creates an unauthorized response
        /// </summary>
        public static IActionResult Unauthorized<T>(
            this ControllerBase controller,
            string? message = null,
            T? data = default
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.Unauthorized,
                message ?? "Unauthorized access.",
                data
            );

            return controller.StatusCode(401, response);
        }

        /// <summary>
        /// Creates a forbidden response
        /// </summary>
        public static IActionResult Forbidden<T>(
            this ControllerBase controller,
            string? message = null,
            T? data = default
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.Forbidden,
                message ?? "Access to the resource is forbidden.",
                data
            );

            return controller.StatusCode(403, response);
        }

        /// <summary>
        /// Creates an internal server error response
        /// </summary>
        public static IActionResult InternalServerError<T>(
            this ControllerBase controller,
            string? message = null,
            T? data = default
        )
        {
            var response = new ApiResponseModel<T>(
                ApiResponseStatusEnum.InternalServerError,
                message ?? "An internal server error occurred.",
                data
            );

            return controller.StatusCode(500, response);
        }

        /// <summary>
        /// Creates a paginated response
        /// </summary>
        public static IActionResult Paginated<T>(
            this ControllerBase controller,
            T data,
            int currentPage,
            int totalPages,
            int totalCount,
            int pageSize,
            string? message = null
        )
        {
            var response = new PaginatedResponseModel<T>(
                ApiResponseStatusEnum.Success,
                message ?? "The operation completed successfully.",
                data,
                currentPage,
                totalPages,
                totalCount,
                pageSize
            );

            return controller.Ok(response);
        }

        /// <summary>
        /// Creates a paginated response using a PaginationMetadata object
        /// </summary>
        public static IActionResult Paginated<T>(
            this ControllerBase controller,
            T data,
            PaginationMetadata meta,
            string? message = null
        )
        {
            var response = new PaginatedResponseModel<T>(
                ApiResponseStatusEnum.Success,
                message ?? "The operation completed successfully.",
                data,
                meta
            );

            return controller.Ok(response);
        }
    }
}
