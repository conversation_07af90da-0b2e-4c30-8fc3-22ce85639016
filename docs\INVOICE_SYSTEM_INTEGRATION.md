# Invoice System Integration for Multi-Day Bookings

**Version**: 1.0
**Date**: July 11, 2025
**Status**: Proposed

---

## 1. Overview

This document outlines a detailed plan to integrate an invoice system capable of handling multi-day bookings by generating separate invoices for each day (i.e., for each `BookingWindow`). This will provide clients with clear, itemized billing and streamline the payment process.

**Core Objective**: To create a robust invoicing system that automatically generates an `Invoice` for each `BookingWindow` within a `Booking`, manages their status, and links them to payments.

---

## 2. System Architecture and Design

The proposed solution involves extending the existing domain model, creating new application services for invoice management, and exposing this functionality through new API endpoints.

### 2.1. Domain Layer Enhancements

We will introduce a new `Invoice` entity and modify the existing `Booking` and `Payment` entities.

#### 2.1.1. `Invoice` Entity (Modified)

The `Invoice` entity will be modified to be associated with a `BookingWindow` instead of a `Booking`. This is the core change that enables per-day invoicing.

**File to Modify**: `SuperCareApp.Domain/Entities/Invoice.cs`

```csharp
using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities;

public sealed class Invoice : BaseEntity
{
    public Guid BookingWindowId { get; set; } // Changed from BookingId
    public string InvoiceNumber { get; set; } = null!;
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public InvoiceStatus Status { get; set; }
    public decimal Amount { get; set; }
    public decimal Tax { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = null!;

    // Navigation properties
    public BookingWindow BookingWindow { get; set; } = null!;
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
}
```

#### 2.1.2. `Booking` Entity (Modification)

The `Booking` entity will have its relationship with `Invoice` removed.

**File to Modify**: `SuperCareApp.Domain/Entities/Booking.cs`

```csharp
// In Booking.cs
// Remove the following line:
// public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
```

#### 2.1.3. `BookingWindow` Entity (Modification)

The `BookingWindow` entity will now have a one-to-one relationship with the `Invoice` entity.

**File to Modify**: `SuperCareApp.Domain/Entities/BookingWindow.cs`

```csharp
// In BookingWindow.cs
public class BookingWindow : BaseEntity
{
    // ... existing properties
    public Invoice? Invoice { get; set; }
}
```

#### 2.1.4. `Payment` Entity (Modification)

The `Payment` entity will now be associated with an `Invoice` instead of a `Booking`.

**File to Modify**: `SuperCareApp.Domain/Entities/Payment.cs`

```csharp
// In Payment.cs
public class Payment : BaseEntity
{
    public Guid InvoiceId { get; set; } // Changed from BookingId
    // ... other properties
    public Invoice Invoice { get; set; } = null!;
}
```

#### 2.1.5. `InvoiceStatus` Enum (New)

A new enum will be created to manage the status of an invoice.

**File to Create**: `SuperCareApp.Domain/Enums/InvoiceStatus.cs`

```csharp
namespace SuperCareApp.Domain.Enums
{
    public enum InvoiceStatus
    {
        Draft,
        Sent,
        Paid,
        Partial,
        Overdue,
        Void
    }
}
```

### 2.2. Application Layer

New services and interfaces will be created to handle the business logic for invoicing.

#### 2.2.1. `IInvoiceService` Interface (Removed/Replaced by CQRS)

With the adoption of Mediator and CQRS, the `IInvoiceService` interface will be replaced by distinct Command and Query objects and their handlers. The `InvoiceService` will become a thin wrapper around the Mediator, dispatching these commands and queries.



#### 2.2.2. CQRS Commands and Queries (New)

We will define specific commands for actions that modify the state (e.g., generating invoices, marking as paid) and queries for actions that retrieve data.

**File to Create**: `SuperCareApp.Application/Features/Invoices/Commands/GenerateInvoicesForBookingCommand.cs`

```csharp
using MediatR;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Features.Invoices.Commands;

public record GenerateInvoicesForBookingCommand(Guid BookingId) : IRequest<Result>;
```

**File to Create**: `SuperCareApp.Application/Features/Invoices/Commands/MarkInvoiceAsPaidCommand.cs`

```csharp
using MediatR;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Features.Invoices.Commands;

public record MarkInvoiceAsPaidCommand(Guid InvoiceId) : IRequest<Result>;
```

**File to Create**: `SuperCareApp.Application/Features/Invoices/Queries/GetInvoiceByIdQuery.cs`

```csharp
using MediatR;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Application.Common.Models.Invoices;

namespace SuperCareApp.Application.Features.Invoices.Queries;

public record GetInvoiceByIdQuery(Guid InvoiceId) : IRequest<Result<InvoiceResponse>>;
```

**File to Create**: `SuperCareApp.Application/Features/Invoices/Queries/GetInvoicesForBookingQuery.cs`

```csharp
using MediatR;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Application.Common.Models.Invoices;

namespace SuperCareApp.Application.Features.Invoices.Queries;

public record GetInvoicesForBookingQuery(Guid BookingId) : IRequest<Result<IEnumerable<InvoiceResponse>>>;
```

#### 2.2.3. CQRS Command and Query Handlers (New)

Each command and query will have a dedicated handler responsible for executing the business logic.

**File to Create**: `SuperCareApp.Application/Features/Invoices/Commands/GenerateInvoicesForBookingCommandHandler.cs`

```csharp
using MediatR;
using Microsoft.EntityFrameworkCore;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Application.Features.Invoices.Commands;

public class GenerateInvoicesForBookingCommandHandler : IRequestHandler<GenerateInvoicesForBookingCommand, Result>
{
    private readonly ApplicationDbContext _context;

    public GenerateInvoicesForBookingCommandHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(GenerateInvoicesForBookingCommand request, CancellationToken cancellationToken)
    {
        var booking = await _context.Bookings
            .Include(b => b.BookingWindows)
            .FirstOrDefaultAsync(b => b.Id == request.BookingId, cancellationToken);

        if (booking == null)
            return Result.Failure(Error.NotFound("Booking not found."));

        foreach (var window in booking.BookingWindows)
        {
            var invoice = new Invoice
            {
                BookingWindowId = window.Id,
                InvoiceNumber = $"INV-{DateTime.UtcNow:yyyyMMdd}-{window.Id.ToString().Substring(0, 4)}",
                InvoiceDate = DateTime.UtcNow,
                DueDate = DateTime.UtcNow.AddDays(14), // Or some other logic
                Status = InvoiceStatus.Draft,
                Amount = window.DailyRate ?? 0, // Or calculate based on hours
                Tax = 0, // Add tax logic if needed
                TotalAmount = window.DailyRate ?? 0,
                Currency = "USD"
            };
            await _context.Invoices.AddAsync(invoice, cancellationToken);
        }

        await _context.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
```

**File to Create**: `SuperCareApp.Application/Features/Invoices/Commands/MarkInvoiceAsPaidCommandHandler.cs`

```csharp
using MediatR;
using Microsoft.EntityFrameworkCore;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Application.Features.Invoices.Commands;

public class MarkInvoiceAsPaidCommandHandler : IRequestHandler<MarkInvoiceAsPaidCommand, Result>
{
    private readonly ApplicationDbContext _context;

    public MarkInvoiceAsPaidCommandHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(MarkInvoiceAsPaidCommand request, CancellationToken cancellationToken)
    {
        var invoice = await _context.Invoices.FirstOrDefaultAsync(i => i.Id == request.InvoiceId, cancellationToken);

        if (invoice == null)
            return Result.Failure(Error.NotFound("Invoice not found."));

        invoice.Status = InvoiceStatus.Paid;
        _context.Invoices.Update(invoice);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
```

**File to Create**: `SuperCareApp.Application/Features/Invoices/Queries/GetInvoiceByIdQueryHandler.cs`

```csharp
using MediatR;
using Microsoft.EntityFrameworkCore;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Application.Common.Models.Invoices;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Application.Features.Invoices.Queries;

public class GetInvoiceByIdQueryHandler : IRequestHandler<GetInvoiceByIdQuery, Result<InvoiceResponse>>
{
    private readonly ApplicationDbContext _context;

    public GetInvoiceByIdQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<InvoiceResponse>> Handle(GetInvoiceByIdQuery request, CancellationToken cancellationToken)
    {
        var invoice = await _context.Invoices
            .Where(i => i.Id == request.InvoiceId)
            .Select(i => new InvoiceResponse
            {
                Id = i.Id,
                InvoiceNumber = i.InvoiceNumber,
                InvoiceDate = i.InvoiceDate,
                DueDate = i.DueDate,
                Status = i.Status.ToString(),
                Amount = i.Amount,
                TotalAmount = i.TotalAmount,
                BookingWindowId = i.BookingWindowId
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (invoice == null)
            return Result.Failure<InvoiceResponse>(Error.NotFound("Invoice not found."));

        return Result.Success(invoice);
    }
}
```

**File to Create**: `SuperCareApp.Application/Features/Invoices/Queries/GetInvoicesForBookingQueryHandler.cs`

```csharp
using MediatR;
using Microsoft.EntityFrameworkCore;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Application.Common.Models.Invoices;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Application.Features.Invoices.Queries;

public class GetInvoicesForBookingQueryHandler : IRequestHandler<GetInvoicesForBookingQuery, Result<IEnumerable<InvoiceResponse>>>
{
    private readonly ApplicationDbContext _context;

    public GetInvoicesForBookingQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<IEnumerable<InvoiceResponse>>> Handle(GetInvoicesForBookingQuery request, CancellationToken cancellationToken)
    {
        var bookingWindows = await _context.BookingWindows
            .Where(bw => bw.BookingId == request.BookingId)
            .Select(bw => bw.Id)
            .ToListAsync(cancellationToken);

        if (!bookingWindows.Any())
            return Result.Failure<IEnumerable<InvoiceResponse>>(Error.NotFound("No booking windows found for this booking."));

        var invoices = await _context.Invoices
            .Where(i => bookingWindows.Contains(i.BookingWindowId))
            .Select(i => new InvoiceResponse
            {
                Id = i.Id,
                InvoiceNumber = i.InvoiceNumber,
                InvoiceDate = i.InvoiceDate,
                DueDate = i.DueDate,
                Status = i.Status.ToString(),
                Amount = i.Amount,
                TotalAmount = i.TotalAmount,
                BookingWindowId = i.BookingWindowId
            })
            .ToListAsync(cancellationToken);

        return Result.Success<IEnumerable<InvoiceResponse>>(invoices);
    }
}
```


### 2.3. Persistence Layer

With CQRS, the business logic previously encapsulated in `InvoiceService` is now distributed among the command and query handlers. The persistence layer will primarily involve the `ApplicationDbContext` and repositories used by these handlers.

#### 2.3.1. `InvoiceService` (Removed)

The `InvoiceService` will be removed as its functionality is now handled by the CQRS command and query handlers.

---

## 3. API Layer

New endpoints will be added to the `BookingsController` to manage invoices.

### 3.1. `InvoicesController` (Modified for CQRS)

The `InvoicesController` will now use `IMediator` to dispatch commands and queries.

**File to Modify**: `super-care-app/Controllers/InvoicesController.cs`

```csharp
using MediatR;
using Microsoft.AspNetCore.Mvc;
using SuperCareApp.Application.Features.Invoices.Commands;
using SuperCareApp.Application.Features.Invoices.Queries;

namespace SuperCareApp.Controllers;

[ApiController]
[Route("api/invoices")]
public class InvoicesController : BaseController
{
    private readonly IMediator _mediator;

    public InvoicesController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet("{invoiceId}")]
    public async Task<IActionResult> GetInvoice(Guid invoiceId)
    {
        var query = new GetInvoiceByIdQuery(invoiceId);
        var result = await _mediator.Send(query);
        return result.IsSuccess ? Ok(result.Value) : HandleFailure(result);
    }

    [HttpGet("booking/{bookingId}")]
    public async Task<IActionResult> GetInvoicesForBooking(Guid bookingId)
    {
        var query = new GetInvoicesForBookingQuery(bookingId);
        var result = await _mediator.Send(query);
        return result.IsSuccess ? Ok(result.Value) : HandleFailure(result);
    }

    [HttpPost("generate-for-booking/{bookingId}")]
    public async Task<IActionResult> GenerateInvoicesForBooking(Guid bookingId)
    {
        var command = new GenerateInvoicesForBookingCommand(bookingId);
        var result = await _mediator.Send(command);
        return result.IsSuccess ? Ok() : HandleFailure(result);
    }

    [HttpPut("{invoiceId}/mark-as-paid")]
    public async Task<IActionResult> MarkInvoiceAsPaid(Guid invoiceId)
    {
        var command = new MarkInvoiceAsPaidCommand(invoiceId);
        var result = await _mediator.Send(command);
        return result.IsSuccess ? Ok() : HandleFailure(result);
    }
}
```

---

## 4. Implementation Phases

### Phase 1: Domain and Persistence Setup

- **Task 1**: Modify `Invoice`, `Booking`, `BookingWindow`, and `Payment` entities.
- **Task 2**: Create the `InvoiceStatus` enum.
- **Task 3**: Create database migrations for the entity changes.

### Phase 2: Application and API Layer (CQRS Integration)

- **Task 1**: Create the `InvoiceResponse` DTO.
- **Task 2**: Define CQRS Commands (`GenerateInvoicesForBookingCommand`, `MarkInvoiceAsPaidCommand`) and Queries (`GetInvoiceByIdQuery`, `GetInvoicesForBookingQuery`).
- **Task 3**: Implement the corresponding CQRS Command and Query Handlers.
- **Task 4**: Modify the `InvoicesController` to use `IMediator` to dispatch these commands and queries.
- **Task 5**: Integrate the `GenerateInvoicesForBookingCommand` dispatch into the `BookingService`'s `CreateBookingWithWindowsAsync` method after a booking is successfully created.

### Phase 3: Payment Integration

- **Task 1**: Modify the `PaymentService` to work with `InvoiceId` instead of `BookingId`.
- **Task 2**: The `MarkInvoiceAsPaidCommand` and its handler will manage marking invoices as paid.
- **Task 3**: The `InvoicesController` will expose an endpoint to dispatch the `MarkInvoiceAsPaidCommand`.

---

## 5. Flowcharts

### 5.1. Invoice Generation Flow

```mermaid
graph TD
    A[Booking Created] --> B{CreateBookingWithWindowsAsync}
    B --> C[Booking and Windows Saved]
    C --> D[GenerateInvoicesForBookingAsync]
    D --> E{For each BookingWindow}
    E --> F[Create Invoice]
    F --> G[Save Invoice]
    E --> H[All windows processed]
    H --> I[Return Success]
```

### 5.2. Invoice Retrieval Flow

```mermaid
graph TD
    A[Client requests invoices for booking] --> B[GET /api/invoices/booking/:bookingId]
    B --> C[GetInvoicesForBooking]
    C --> D[GetInvoicesForBookingAsync]
    D --> E{Retrieve invoices from DB}
    E --> F[Map to InvoiceResponse]
    F --> G[Return Invoices]

```

---

## 6. Conclusion

This plan provides a comprehensive approach to integrating a per-day invoicing system. By following these steps, we can deliver a feature that enhances the platform's financial tracking capabilities and improves the user experience for both clients and care providers.
