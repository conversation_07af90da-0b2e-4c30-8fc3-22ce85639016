using System.Text.Json;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Application.Shared.Utility;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Example response models for CareProvider API documentation
    /// </summary>
    public static class CareProviderExamples
    {
        /// <summary>
        /// Gets an example of a successful care provider list response
        /// </summary>
        public static string GetAllProvidersExample()
        {
            var providers = new List<CareProviderProfileResponse>
            {
                new CareProviderProfileResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    YearsExperience = 5,
                    DateOfBirth = new DateTime(1985, 6, 15),
                },
                new CareProviderProfileResponse
                {
                    Id = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    Name = "Sarah Johnson",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Female",
                    YearsExperience = 8,
                    DateOfBirth = new DateTime(1980, 3, 22),
                },
                new CareProviderProfileResponse
                {
                    Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    UserId = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Name = "Michael Brown",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    YearsExperience = 3,
                    DateOfBirth = new DateTime(1990, 9, 10),
                },
            };

            var response = new ApiResponseModel<IEnumerable<CareProviderProfileResponse>>(
                ApiResponseStatusEnum.Success,
                "Care providers retrieved successfully",
                providers
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful paged care provider list response
        /// </summary>
        public static string GetPagedProvidersExample()
        {
            var providers = new List<CareProviderProfileResponse>
            {
                new CareProviderProfileResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Name = "John Smith",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    YearsExperience = 5,
                    DateOfBirth = new DateTime(1985, 6, 15),
                },
                new CareProviderProfileResponse
                {
                    Id = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    Name = "Sarah Johnson",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Female",
                    YearsExperience = 8,
                    DateOfBirth = new DateTime(1980, 3, 22),
                },
                new CareProviderProfileResponse
                {
                    Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    UserId = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Name = "Michael Brown",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    YearsExperience = 3,
                    DateOfBirth = new DateTime(1990, 9, 10),
                },
            };

            var pagedList = new PagedCareProviderList
            {
                Providers = providers.ToList(),
                PageNumber = 1,
                PageSize = 10,
                TotalCount = 25,
                TotalPages = 3,
            };

            var response = new PaginatedResponseModel<PagedCareProviderList>(
                ApiResponseStatusEnum.Success,
                "Care providers retrieved successfully",
                pagedList,
                currentPage: pagedList.PageNumber,
                totalPages: pagedList.TotalPages,
                totalCount: pagedList.TotalCount,
                pageSize: pagedList.PageSize
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful detailed care provider profile response
        /// </summary>
        public static string GetProviderByIdExample()
        {
            var provider = new DetailedCareProviderProfileResponse
            {
                Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                UserId = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d479"),
                Name = "John Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                HourlyRate = 25.50m,
                ProvidesOvernight = true,
                ProvidesLiveIn = false,
                Bio =
                    "Experienced care provider with a background in nursing and elderly care. Specializes in dementia care and physical therapy assistance.",
                Qualifications = JsonSerializer.Serialize(
                    new List<string>
                    {
                        "Certified Nursing Assistant (CNA)",
                        "CPR Certified",
                        "First Aid Certified",
                        "Dementia Care Specialist",
                    }
                ),
                VerificationStatus = "Verified",
                Rating = 4.8m,
                RatingCount = 24,
                DateOfBirth = new DateTime(1985, 6, 15),
                CreatedAt = DateTime.UtcNow.AddYears(-2),
                UpdatedAt = DateTime.UtcNow.AddMonths(-1),
                Categories = new List<string>
                {
                    "Elderly Care",
                    "Dementia Care",
                    "Physical Therapy",
                },
            };

            var response = new ApiResponseModel<DetailedCareProviderProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Care provider profile retrieved successfully",
                provider
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful care provider profile creation response
        /// </summary>
        public static string CreateProviderProfileExample()
        {
            var providerId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479");

            var response = new ApiResponseModel<Guid>(
                ApiResponseStatusEnum.Success,
                "Care provider profile created successfully",
                providerId
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful care provider profile update response
        /// </summary>
        public static string UpdateProviderProfileExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Success,
                "Care provider profile updated successfully",
                null
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a validation error response
        /// </summary>
        public static string ValidationErrorExample()
        {
            var validationErrors = new Dictionary<string, string[]>
            {
                { "Name", new[] { "Name is required." } },
                {
                    "PhoneNumber",
                    new[]
                    {
                        "Phone number must be numeric and 10–15 digits long (e.g., +**********).",
                    }
                },
                { "YearsExperience", new[] { "Years of experience must be between 0 and 100." } },
            };

            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Error,
                "Validation failed",
                validationErrors
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a not found error response
        /// </summary>
        public static string NotFoundErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Error,
                "Care provider profile not found",
                null
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Helper method to serialize objects to JSON
        /// </summary>
        private static string SerializeToJson<T>(T obj)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            };

            return JsonSerializer.Serialize(obj, options);
        }
    }
}
