using System.Text.Json.Serialization;

namespace SuperCareApp.Application.Shared.Utility
{
    /// <summary>
    /// Internal class to store pagination information
    /// </summary>
    internal class PagedListInternal<T>
    {
        /// <summary>
        /// List of items
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();

        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;
    }

    /// <summary>
    /// Generic paged list that can be used across different APIs
    /// </summary>
    /// <typeparam name="T">Type of items in the list</typeparam>
    public class PagedList<T>
    {
        private readonly PagedListInternal<T> _internal;

        /// <summary>
        /// List of items
        /// </summary>
        [JsonPropertyName("items")]
        public List<T> Items => _internal.Items;

        /// <summary>
        /// Default constructor
        /// </summary>
        public PagedList()
        {
            _internal = new PagedListInternal<T>();
        }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public PagedList(List<T> items, int count, int pageNumber, int pageSize)
        {
            _internal = new PagedListInternal<T>
            {
                Items = items,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalCount = count,
                TotalPages = (int)Math.Ceiling(count / (double)pageSize),
            };
        }

        /// <summary>
        /// Converts this paged list to a PaginationMetadata object
        /// </summary>
        public PaginationMetadata ToMetadata()
        {
            return new PaginationMetadata(
                _internal.PageNumber,
                _internal.TotalPages,
                _internal.TotalCount,
                _internal.PageSize
            );
        }
    }
}
