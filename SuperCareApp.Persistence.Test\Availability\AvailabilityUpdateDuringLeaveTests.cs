using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;

namespace SuperCareApp.Persistence.Test.Availability;

/// <summary>
/// Focused tests specifically for verifying that isAvailable can be updated
/// regardless of leave status - this is the core concern
/// </summary>
public class AvailabilityUpdateDuringLeaveTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly AvailabilityService _service;

    public AvailabilityUpdateDuringLeaveTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        var mockBookingService = new Mock<IBookingManagementService>();
        _service = new AvailabilityService(
            _context,
            mockBookingService.Object,
            NullLogger<AvailabilityService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task CanUpdateAvailability_FromFalseToTrue_WhileOnLeave()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        // Setup provider
        var providerProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            BufferDuration = 30,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.CareProviderProfiles.Add(providerProfile);

        // Create availability that is currently FALSE
        var availability = new Domain.Entities.Availability
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            DayOfWeek = "Monday",
            IsAvailable = false, // Currently unavailable
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Availabilities.Add(availability);

        // Create active leave period covering Monday
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            StartDate = DateTime.UtcNow.Date,
            EndDate = DateTime.UtcNow.Date.AddDays(7),
            Reason = "On vacation",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Prepare update to change availability from FALSE to TRUE while on leave
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            availabilityUpdates,
            bufferDuration: 30,
            providesRecurringBooking: null,
            workingHoursPerDay: null
        );

        // Assert
        Assert.True(
            result.IsSuccess,
            $"Update should succeed even during leave. Error: {result.Error?.Message}"
        );

        // Verify availability was updated to TRUE despite being on leave
        var updatedAvailability = await _context.Availabilities.FirstOrDefaultAsync(a =>
            a.ProviderId == providerId && a.DayOfWeek == "Monday"
        );

        Assert.NotNull(updatedAvailability);
        Assert.True(updatedAvailability.IsAvailable); // Should be TRUE now

        // Verify leave period is still active and unaffected
        var activeLeave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == leave.Id);
        Assert.NotNull(activeLeave);
        Assert.False(activeLeave.IsDeleted);

        // Key point: Availability can be set to TRUE even during leave
        // The leave will override this when computing final availability for clients
    }

    [Fact]
    public async Task CanUpdateAvailability_FromTrueToFalse_WhileOnLeave()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        // Setup provider
        var providerProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            BufferDuration = 30,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.CareProviderProfiles.Add(providerProfile);

        // Create availability that is currently TRUE
        var availability = new Domain.Entities.Availability
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            DayOfWeek = "Tuesday",
            IsAvailable = true, // Currently available
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Availabilities.Add(availability);
        await _context.SaveChangesAsync();

        // Add existing slot
        var existingSlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(8, 0),
            EndTime = new TimeOnly(16, 0),
        };
        _context.AvailabilitySlots.Add(existingSlot);

        // Create active leave period covering Tuesday
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            StartDate = DateTime.UtcNow.Date,
            EndDate = DateTime.UtcNow.Date.AddDays(5),
            Reason = "Medical leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Prepare update to change availability from TRUE to FALSE while on leave
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            ("Tuesday", false, new List<AvailabilitySlot>()), // No slots when unavailable
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            availabilityUpdates,
            bufferDuration: 30,
            providesRecurringBooking: null,
            workingHoursPerDay: null
        );

        // Assert
        Assert.True(
            result.IsSuccess,
            $"Update should succeed even during leave. Error: {result.Error?.Message}"
        );

        // Verify availability was updated to FALSE despite being on leave
        var updatedAvailability = await _context.Availabilities.FirstOrDefaultAsync(a =>
            a.ProviderId == providerId && a.DayOfWeek == "Tuesday"
        );

        Assert.NotNull(updatedAvailability);
        Assert.False(updatedAvailability.IsAvailable); // Should be FALSE now

        // Verify leave period is still active and unaffected
        var activeLeave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == leave.Id);
        Assert.NotNull(activeLeave);
        Assert.False(activeLeave.IsDeleted);

        // Key point: Availability can be set to FALSE even during leave
        // This allows providers to update their regular schedule even while temporarily away
    }

    [Fact]
    public async Task CanToggleAvailability_MultipleTimesWhileOnLeave()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        // Setup provider
        var providerProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            BufferDuration = 30,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            WorkingHours = 8,
        };
        _context.CareProviderProfiles.Add(providerProfile);

        // Create availability starting as FALSE
        var availability = new Domain.Entities.Availability
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            DayOfWeek = "Wednesday",
            IsAvailable = false,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Availabilities.Add(availability);

        // Create long leave period
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            StartDate = DateTime.UtcNow.Date,
            EndDate = DateTime.UtcNow.Date.AddDays(14), // 2 weeks
            Reason = "Extended leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Step 1: Update from FALSE to TRUE while on leave
        var updateToTrue = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Wednesday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(10, 0),
                        EndTime = new TimeOnly(14, 0),
                    },
                }
            ),
        };

        var result1 = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            updateToTrue,
            30,
            null,
            null
        );

        Assert.True(result1.IsSuccess);

        var availability1 = await _context.Availabilities.FirstOrDefaultAsync(a =>
            a.ProviderId == providerId && a.DayOfWeek == "Wednesday"
        );
        Assert.True(availability1.IsAvailable); // Should be TRUE

        // Step 2: Update from TRUE back to FALSE while still on leave
        var updateToFalse = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            ("Wednesday", false, new List<AvailabilitySlot>()),
        };

        var result2 = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            updateToFalse,
            30,
            true,
            8
        );

        Assert.True(result2.IsSuccess);

        var availability2 = await _context.Availabilities.FirstOrDefaultAsync(a =>
            a.ProviderId == providerId && a.DayOfWeek == "Wednesday"
        );
        Assert.False(availability2.IsAvailable); // Should be FALSE again

        // Step 3: Update from FALSE to TRUE again while still on leave
        var updateToTrueAgain = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Wednesday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(15, 0),
                    },
                }
            ),
        };

        var result3 = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            updateToTrueAgain,
            30,
            null,
            null
        );

        Assert.True(result3.IsSuccess);

        var availability3 = await _context.Availabilities.FirstOrDefaultAsync(a =>
            a.ProviderId == providerId && a.DayOfWeek == "Wednesday"
        );
        Assert.True(availability3.IsAvailable); // Should be TRUE again

        // Verify leave is still active throughout all updates
        var persistentLeave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == leave.Id);
        Assert.NotNull(persistentLeave);
        Assert.False(persistentLeave.IsDeleted);

        // Key insight: Availability updates are completely independent of leave status
        // Providers can modify their regular schedule even while on temporary leave
    }

    [Fact]
    public async Task AvailabilityUpdates_DoNotAffectLeaveStatus()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        var providerProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            BufferDuration = 30,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.CareProviderProfiles.Add(providerProfile);

        // Create multiple leave periods
        var leave1 = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            StartDate = DateTime.UtcNow.Date,
            EndDate = DateTime.UtcNow.Date.AddDays(3),
            Reason = "First leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        var leave2 = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            StartDate = DateTime.UtcNow.Date.AddDays(10),
            EndDate = DateTime.UtcNow.Date.AddDays(15),
            Reason = "Second leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        _context.Leaves.AddRange(leave1, leave2);
        await _context.SaveChangesAsync();

        // Update availability for multiple days while leaves are active
        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            (
                "Monday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                }
            ),
            ("Tuesday", false, new List<AvailabilitySlot>()),
            (
                "Wednesday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(8, 0),
                        EndTime = new TimeOnly(16, 0),
                    },
                }
            ),
            (
                "Thursday",
                true,
                new List<AvailabilitySlot>
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(10, 0),
                        EndTime = new TimeOnly(14, 0),
                    },
                }
            ),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            availabilityUpdates,
            bufferDuration: 45,
            providesRecurringBooking: true,
            workingHoursPerDay: 8
        );

        // Assert
        Assert.True(result.IsSuccess);

        // Verify all availability updates were applied
        var availabilities = await _context
            .Availabilities.Where(a => a.ProviderId == providerId)
            .ToListAsync();

        Assert.Equal(4, availabilities.Count);
        Assert.True(availabilities.Single(a => a.DayOfWeek == "Monday").IsAvailable);
        Assert.False(availabilities.Single(a => a.DayOfWeek == "Tuesday").IsAvailable);
        Assert.True(availabilities.Single(a => a.DayOfWeek == "Wednesday").IsAvailable);
        Assert.True(availabilities.Single(a => a.DayOfWeek == "Thursday").IsAvailable);

        // Verify ALL leave periods remain completely unaffected
        var leaves = await _context.Leaves.Where(l => l.ProviderId == providerId).ToListAsync();
        Assert.Equal(2, leaves.Count);
        Assert.All(leaves, leave => Assert.False(leave.IsDeleted));

        var persistentLeave1 = leaves.Single(l => l.Id == leave1.Id);
        Assert.Equal("First leave", persistentLeave1.Reason);
        Assert.Equal(DateTime.UtcNow.Date, persistentLeave1.StartDate);
        Assert.Equal(DateTime.UtcNow.Date.AddDays(3), persistentLeave1.EndDate);

        var persistentLeave2 = leaves.Single(l => l.Id == leave2.Id);
        Assert.Equal("Second leave", persistentLeave2.Reason);
        Assert.Equal(DateTime.UtcNow.Date.AddDays(10), persistentLeave2.StartDate);
        Assert.Equal(DateTime.UtcNow.Date.AddDays(15), persistentLeave2.EndDate);

        // Key verification: Leave and availability are completely independent
        // Updating availability does not modify, delete, or affect leave periods in any way
    }

    [Theory]
    [InlineData(true, false)] // Available to Unavailable
    [InlineData(false, true)] // Unavailable to Available
    [InlineData(true, true)] // Available to Available (with different slots)
    [InlineData(false, false)] // Unavailable to Unavailable
    public async Task AvailabilityCanBeUpdated_ToAnyState_RegardlessOfLeaveStatus(
        bool initialAvailability,
        bool targetAvailability
    )
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        var providerProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            BufferDuration = 30,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.CareProviderProfiles.Add(providerProfile);

        // Create initial availability
        var availability = new Domain.Entities.Availability
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            DayOfWeek = "Friday",
            IsAvailable = initialAvailability,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Availabilities.Add(availability);
        await _context.SaveChangesAsync();

        // Add initial slot if available
        if (initialAvailability)
        {
            var initialSlot = new AvailabilitySlot
            {
                Id = Guid.NewGuid(),
                AvailabilityId = availability.Id,
                StartTime = new TimeOnly(9, 0),
                EndTime = new TimeOnly(17, 0),
            };
            _context.AvailabilitySlots.Add(initialSlot);
            await _context.SaveChangesAsync();
        }

        // Create active leave period
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            StartDate = DateTime.UtcNow.Date,
            EndDate = DateTime.UtcNow.Date.AddDays(7),
            Reason = "Active leave during availability test",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };
        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Prepare update to target availability
        var slots = targetAvailability
            ? new List<AvailabilitySlot>
            {
                new AvailabilitySlot
                {
                    StartTime = new TimeOnly(10, 0),
                    EndTime = new TimeOnly(16, 0),
                },
            }
            : new List<AvailabilitySlot>();

        var availabilityUpdates = new List<(
            string DayOfWeek,
            bool IsAvailable,
            List<AvailabilitySlot> Slots
        )>
        {
            ("Friday", targetAvailability, slots),
        };

        // Act
        var result = await _service.BulkUpdateAvailabilityByDayOfWeekAsync(
            providerId,
            availabilityUpdates,
            bufferDuration: 30,
            providesRecurringBooking: null,
            workingHoursPerDay: null
        );

        // Assert
        Assert.True(
            result.IsSuccess,
            $"Should be able to update from {initialAvailability} to {targetAvailability} during leave. Error: {result.Error?.Message}"
        );

        // Verify availability was updated to target state
        var updatedAvailability = await _context.Availabilities.FirstOrDefaultAsync(a =>
            a.ProviderId == providerId && a.DayOfWeek == "Friday"
        );

        Assert.NotNull(updatedAvailability);
        Assert.Equal(targetAvailability, updatedAvailability.IsAvailable);

        // Verify leave is unaffected
        var persistentLeave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == leave.Id);
        Assert.NotNull(persistentLeave);
        Assert.False(persistentLeave.IsDeleted);

        // Core assertion: ANY availability state change is possible during leave
        // The leave status does not prevent or interfere with availability updates
    }
}
