﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class SubscriptionConfiguration : IEntityTypeConfiguration<Subscription>
    {
        public void Configure(EntityTypeBuilder<Subscription> builder)
        {
            builder.HasKey(s => s.Id);

            builder.Property(s => s.SubscriptionType).IsRequired().HasConversion<string>();

            builder.Property(s => s.StartDate).IsRequired();

            builder.Property(s => s.EndDate).IsRequired();

            builder.Property(s => s.Amount).IsRequired().HasColumnType("decimal(18,2)");

            builder.Property(s => s.Status).IsRequired().HasConversion<string>();

            builder.Property(s => s.PaymentMethod).IsRequired().HasMaxLength(50);

            // Relationships
            builder
                .HasOne(s => s.Provider)
                .WithMany()
                .HasForeignKey(s => s.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(s => new { s.ProviderId, s.EndDate });
        }
    }
}
