{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=supercare_dev;Username=********;Password=********"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Smtp": {"Host": "smtp.gmail.com", "Port": "587", "EnableSsl": "true", "Password": "getnxkiacfvcwtiy", "FromEmail": "<EMAIL>"}, "OtpSettings": {"UseDefaultOtpForDevelopment": "true", "DefaultOtp": "123456", "OtpExpiryMinutes": "10"}, "Twilio": {"AccountSid": "**********************************", "AuthToken": "01d24b0d3f9ed1e79cddac516016b02d", "PhoneNumber": "+************"}, "PerformanceMonitoring": {"ThresholdInMs": "200"}, "CacheSettings": {"DefaultExpirationSeconds": 60, "SizeLimitMegabytes": 50, "EnableCompression": false}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5221"}}}}