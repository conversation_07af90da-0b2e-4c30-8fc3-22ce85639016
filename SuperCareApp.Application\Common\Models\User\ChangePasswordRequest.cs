﻿using System.ComponentModel.DataAnnotations;
using FluentValidation;

namespace SuperCareApp.Application.Common.Models.User
{
    /// <summary>
    /// Request model for changing a user's password
    /// </summary>
    public class ChangePasswordRequest
    {
        public string CurrentPassword { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    public class ChangePasswordRequestValidator : AbstractValidator<ChangePasswordRequest>
    {
        public ChangePasswordRequestValidator()
        {
            RuleFor(x => x.CurrentPassword)
                .NotEmpty()
                .WithMessage("Current password is required.")
                .MinimumLength(8)
                .WithMessage("Current password must be at least 8 characters.")
                .MaximumLength(128)
                .WithMessage("Current password cannot exceed 128 characters.")
                .Matches(@"^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d\W_]{8,}$")
                .WithMessage(
                    "Password must contain at least one letter and one number, and can include special characters."
                );

            RuleFor(x => x.NewPassword)
                .NotEmpty()
                .WithMessage("New password is required.")
                .MinimumLength(8)
                .WithMessage("New password must be at least 8 characters.")
                .MaximumLength(128)
                .WithMessage("New password cannot exceed 128 characters.")
                .Matches(@"^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d\W_]{8,}$")
                .WithMessage(
                    "Password must contain at least one letter and one number, and can include special characters."
                );

            RuleFor(x => x.ConfirmNewPassword)
                .NotEmpty()
                .WithMessage("Confirm new password is required.")
                .Equal(x => x.NewPassword)
                .WithMessage("New passwords do not match.");
        }
    }
}
