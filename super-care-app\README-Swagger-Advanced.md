# Advanced Swagger Configuration Guide

This document provides information about advanced Swagger/OpenAPI configuration options and how to extend the current implementation with additional features.

## Table of Contents

1. [Advanced Configuration](#advanced-configuration)
2. [API Versioning](#api-versioning)
3. [Multiple Document Support](#multiple-document-support)
4. [Custom UI Themes](#custom-ui-themes)
5. [Code Generation](#code-generation)
6. [Integration with Other Tools](#integration-with-other-tools)
7. [Performance Optimization](#performance-optimization)
8. [Security Considerations](#security-considerations)

## Advanced Configuration

### Customizing the Swagger UI

The Swagger UI can be customized beyond the basic configuration:

```csharp
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SuperCare API v1");
    c.RoutePrefix = "api-docs"; // Change the URL path
    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List); // Default expansion setting
    c.DefaultModelsExpandDepth(1); // Expand models one level by default
    c.DisplayRequestDuration(); // Show request duration
    c.EnableDeepLinking(); // Enable deep linking for operations and tags
    c.EnableFilter(); // Enable filtering by tag
    c.ShowExtensions(); // Show vendor extensions
    c.EnableValidator(); // Enable validation
    c.SupportedSubmitMethods(SubmitMethod.Get, SubmitMethod.Post, SubmitMethod.Put, SubmitMethod.Delete); // Limit available methods
    c.InjectStylesheet("/custom-swagger-ui.css"); // Add custom CSS
    c.InjectJavascript("/custom-swagger-ui.js"); // Add custom JavaScript
});
```

### Custom Document Filters

You can create custom document filters to modify the entire Swagger document:

```csharp
public class CustomDocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Add custom info
        swaggerDoc.Info.Description += "\n\nAdditional information about the API.";
        
        // Add custom server
        swaggerDoc.Servers.Add(new OpenApiServer
        {
            Url = "https://staging-api.example.com",
            Description = "Staging server"
        });
        
        // Add custom extensions
        swaggerDoc.Extensions.Add("x-api-owner", new OpenApiString("SuperCare Team"));
    }
}
```

Register the filter in the Swagger configuration:

```csharp
services.AddSwaggerGen(c =>
{
    // Other configuration...
    c.DocumentFilter<CustomDocumentFilter>();
});
```

## API Versioning

### Setting Up API Versioning

To support multiple API versions:

1. Install the required packages:
```
dotnet add package Microsoft.AspNetCore.Mvc.Versioning
dotnet add package Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer
```

2. Configure API versioning in `Program.cs`:
```csharp
builder.Services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = new ApiVersion(1, 0);
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ReportApiVersions = true;
    options.ApiVersionReader = ApiVersionReader.Combine(
        new UrlSegmentApiVersionReader(),
        new HeaderApiVersionReader("X-API-Version"),
        new QueryStringApiVersionReader("api-version"));
});

builder.Services.AddVersionedApiExplorer(options =>
{
    options.GroupNameFormat = "'v'VVV";
    options.SubstituteApiVersionInUrl = true;
});
```

3. Update the Swagger configuration:
```csharp
builder.Services.AddSwaggerGen(options =>
{
    // Get all API versions
    var provider = builder.Services.BuildServiceProvider()
        .GetRequiredService<IApiVersionDescriptionProvider>();
    
    // Create a swagger document for each API version
    foreach (var description in provider.ApiVersionDescriptions)
    {
        options.SwaggerDoc(
            description.GroupName,
            new OpenApiInfo
            {
                Title = $"SuperCare API {description.GroupName}",
                Version = description.ApiVersion.ToString(),
                Description = description.IsDeprecated ? 
                    "This API version has been deprecated." : 
                    "API for SuperCare application"
            });
    }
    
    // Other configuration...
});
```

4. Configure the Swagger UI:
```csharp
app.UseSwaggerUI(options =>
{
    var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();
    
    foreach (var description in provider.ApiVersionDescriptions)
    {
        options.SwaggerEndpoint(
            $"/swagger/{description.GroupName}/swagger.json",
            $"SuperCare API {description.GroupName}");
    }
});
```

5. Apply versioning to controllers:
```csharp
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
public class UsersV1Controller : ControllerBase
{
    // V1 implementation
}

[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/[controller]")]
public class UsersV2Controller : ControllerBase
{
    // V2 implementation
}
```

## Multiple Document Support

You can create multiple Swagger documents for different parts of your API:

```csharp
services.AddSwaggerGen(c =>
{
    // Public API
    c.SwaggerDoc("public", new OpenApiInfo
    {
        Title = "SuperCare Public API",
        Version = "v1",
        Description = "Public endpoints for the SuperCare application"
    });
    
    // Admin API
    c.SwaggerDoc("admin", new OpenApiInfo
    {
        Title = "SuperCare Admin API",
        Version = "v1",
        Description = "Administrative endpoints for the SuperCare application"
    });
    
    // Document filter to include controllers in the right document
    c.DocInclusionPredicate((docName, apiDesc) =>
    {
        if (!apiDesc.TryGetMethodInfo(out var methodInfo)) return false;
        
        var controllerName = methodInfo.DeclaringType.Name;
        
        // Include controllers in the appropriate document
        return docName switch
        {
            "public" => !controllerName.Contains("Admin"),
            "admin" => controllerName.Contains("Admin"),
            _ => false
        };
    });
});
```

Configure the Swagger UI to show multiple documents:

```csharp
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/public/swagger.json", "Public API");
    c.SwaggerEndpoint("/swagger/admin/swagger.json", "Admin API");
});
```

## Custom UI Themes

### Customizing the Swagger UI Theme

You can customize the appearance of the Swagger UI by adding custom CSS:

1. Create a custom CSS file in the `wwwroot` directory:
```css
/* wwwroot/css/swagger-custom.css */
.swagger-ui .topbar {
    background-color: #2c3e50;
}

.swagger-ui .info .title {
    color: #3498db;
}

.swagger-ui .opblock-tag {
    background-color: #ecf0f1;
}

.swagger-ui .opblock.opblock-get {
    border-color: #2980b9;
    background-color: rgba(41, 128, 185, 0.1);
}

.swagger-ui .opblock.opblock-post {
    border-color: #27ae60;
    background-color: rgba(39, 174, 96, 0.1);
}

.swagger-ui .opblock.opblock-put {
    border-color: #f39c12;
    background-color: rgba(243, 156, 18, 0.1);
}

.swagger-ui .opblock.opblock-delete {
    border-color: #c0392b;
    background-color: rgba(192, 57, 43, 0.1);
}
```

2. Configure Swagger UI to use the custom CSS:
```csharp
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SuperCare API v1");
    c.InjectStylesheet("/css/swagger-custom.css");
});
```

## Code Generation

### Generating Client Code

You can use the Swagger/OpenAPI specification to generate client code for various languages:

1. **Using NSwag**:
   
   Install the NSwag CLI:
   ```
   dotnet tool install -g NSwag.ConsoleCore
   ```

   Generate TypeScript client:
   ```
   nswag openapi2tsclient /input:https://localhost:5001/swagger/v1/swagger.json /output:client.ts
   ```

   Generate C# client:
   ```
   nswag openapi2csclient /input:https://localhost:5001/swagger/v1/swagger.json /output:ApiClient.cs /namespace:SuperCare.Client
   ```

2. **Using OpenAPI Generator**:

   Install OpenAPI Generator:
   ```
   npm install @openapitools/openapi-generator-cli -g
   ```

   Generate clients:
   ```
   openapi-generator-cli generate -i https://localhost:5001/swagger/v1/swagger.json -g typescript-angular -o ./ts-client
   openapi-generator-cli generate -i https://localhost:5001/swagger/v1/swagger.json -g csharp-netcore -o ./csharp-client
   ```

## Integration with Other Tools

### Postman Integration

You can import the Swagger/OpenAPI specification into Postman:

1. Export the Swagger JSON:
   ```
   https://localhost:5001/swagger/v1/swagger.json
   ```

2. In Postman:
   - Click "Import"
   - Select "Link" tab
   - Paste the URL to the Swagger JSON
   - Click "Import"

### ReDoc Integration

ReDoc provides an alternative UI for OpenAPI documentation:

1. Install the NuGet package:
   ```
   dotnet add package Swashbuckle.AspNetCore.ReDoc
   ```

2. Configure ReDoc in `Program.cs`:
   ```csharp
   app.UseReDoc(c =>
   {
       c.RoutePrefix = "docs";
       c.SpecUrl = "/swagger/v1/swagger.json";
       c.DocumentTitle = "SuperCare API Documentation";
       c.EnableUntrustedSpec();
       c.ScrollYOffset(10);
       c.HideHostname();
       c.HideDownloadButton();
       c.ExpandResponses("200,201");
       c.RequiredPropsFirst();
       c.NoAutoAuth();
       c.PathInMiddlePanel();
       c.HideLoading();
       c.NativeScrollbars();
       c.DisableSearch();
       c.OnlyRequiredInSamples();
       c.SortPropsAlphabetically();
   });
   ```

3. Access ReDoc at:
   ```
   https://localhost:5001/docs
   ```

## Performance Optimization

### Optimizing Swagger for Large APIs

For large APIs, Swagger can become slow. Here are some optimization techniques:

1. **Lazy Loading**:
   ```csharp
   app.UseSwaggerUI(c =>
   {
       c.SwaggerEndpoint("/swagger/v1/swagger.json", "SuperCare API v1");
       c.ConfigObject.DocExpansion = DocExpansion.None;
       c.ConfigObject.DefaultModelsExpandDepth = 0; // Hide models section by default
       c.ConfigObject.DeepLinking = true;
       c.ConfigObject.DisplayOperationId = false;
       c.ConfigObject.DefaultModelRendering = ModelRendering.Example;
       c.ConfigObject.DisplayRequestDuration = true;
       c.ConfigObject.ShowExtensions = false;
   });
   ```

2. **Split into Multiple Documents**:
   - Divide your API into logical groups
   - Create separate Swagger documents for each group
   - Use the DocInclusionPredicate to filter endpoints

3. **Caching**:
   ```csharp
   app.UseSwagger(c =>
   {
       c.SerializeAsV2 = false;
       c.RouteTemplate = "swagger/{documentName}/swagger.json";
       c.PreSerializeFilters.Add((swaggerDoc, httpReq) =>
       {
           // Add cache headers
           httpReq.HttpContext.Response.Headers.Add("Cache-Control", "public, max-age=3600");
       });
   });
   ```

## Security Considerations

### Securing Swagger in Production

In production environments, you might want to restrict access to Swagger:

1. **Disable Swagger in Production**:
   ```csharp
   if (!app.Environment.IsProduction())
   {
       app.UseSwagger();
       app.UseSwaggerUI();
   }
   ```

2. **Require Authentication**:
   ```csharp
   app.UseSwagger();
   app.UseSwaggerUI(c =>
   {
       c.SwaggerEndpoint("/swagger/v1/swagger.json", "SuperCare API v1");
       c.RoutePrefix = "swagger";
       c.EnableFilter();
       c.DisplayRequestDuration();
   });

   app.MapGet("swagger", () => Results.Redirect("/swagger/index.html"))
      .RequireAuthorization("AdminPolicy");
   
   app.MapGet("swagger/{**rest}", (string rest) => Results.Redirect($"/swagger/index.html"))
      .RequireAuthorization("AdminPolicy");
   ```

3. **Basic Authentication**:
   ```csharp
   app.UseSwagger(c =>
   {
       c.PreSerializeFilters.Add((swaggerDoc, httpReq) =>
       {
           // Check basic auth
           if (!IsAuthorized(httpReq))
           {
               httpReq.HttpContext.Response.StatusCode = 401;
               httpReq.HttpContext.Response.Headers.Add("WWW-Authenticate", "Basic");
               httpReq.HttpContext.Response.WriteAsync("Unauthorized").Wait();
               httpReq.HttpContext.Response.Body.Close();
           }
       });
   });

   bool IsAuthorized(HttpRequest request)
   {
       // Get the Authorization header
       var authHeader = request.Headers["Authorization"].FirstOrDefault();
       if (authHeader == null || !authHeader.StartsWith("Basic "))
           return false;

       // Decode the credentials
       var encodedCredentials = authHeader.Substring("Basic ".Length).Trim();
       var credentials = Encoding.UTF8.GetString(Convert.FromBase64String(encodedCredentials));
       var parts = credentials.Split(':');
       
       // Check the credentials
       return parts.Length == 2 && 
              parts[0] == "swagger" && 
              parts[1] == "P@ssw0rd";
   }
   ```

## Conclusion

This advanced guide provides additional configuration options and techniques for extending the Swagger implementation in the SuperCare application. By leveraging these advanced features, you can create a more customized, secure, and performant API documentation experience.

For more information, refer to the official documentation:
- [Swashbuckle](https://github.com/domaindrivendev/Swashbuckle.AspNetCore)
- [OpenAPI Initiative](https://www.openapis.org/)
- [Swagger.io](https://swagger.io/)
