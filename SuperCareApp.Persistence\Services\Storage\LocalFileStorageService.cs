﻿using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;

namespace SuperCareApp.Persistence.Services.Storage;

/// <summary>
/// Implementation of IFileStorageService that stores files locally
/// </summary>
public class LocalFileStorageService : IFileStorageService
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<LocalFileStorageService> _logger;
    private readonly string _baseStoragePath;
    private readonly string _baseUrl;

    public LocalFileStorageService(
        IWebHostEnvironment environment,
        ILogger<LocalFileStorageService> logger,
        IHttpContextAccessor httpContextAccessor
    )
    {
        _environment = environment;
        _logger = logger;
        _baseStoragePath = Path.Combine(_environment.WebRootPath, "uploads");

        // Construct base URL for file access
        var request = httpContextAccessor.HttpContext?.Request;
        var baseUrl = $"{request?.Scheme}://{request?.Host}";
        if (baseUrl.Contains("http://careappapi.intellexio.com"))
        {
            //Set to https
            baseUrl = baseUrl.Replace("http://", "https://");
        }
        _baseUrl = baseUrl;
    }

    /// <inheritdoc />
    public async Task<Result<FileMetadata>> UploadFileAsync(
        IFormFile file,
        string containerName,
        string? fileName = null
    )
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Result.Failure<FileMetadata>(Error.BadRequest("File is empty or null"));
            }

            // Create container directory if it doesn't exist
            var containerPath = Path.Combine(_baseStoragePath, SanitizePath(containerName));
            Directory.CreateDirectory(containerPath);

            // Generate a unique file name if not provided
            var storedFileName = fileName ?? GenerateUniqueFileName(file.FileName);
            var filePath = Path.Combine(containerPath, storedFileName);

            // Save the file
            await using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Create relative path for storage and URL
            var relativePath = Path.Combine("uploads", containerName, storedFileName)
                .Replace("\\", "/");
            var fileUrl = $"{_baseUrl}/{relativePath}";

            var metadata = new FileMetadata
            {
                OriginalFileName = file.FileName,
                StoredFileName = storedFileName,
                FilePath = filePath,
                RelativePath = $"/{relativePath}",
                FileUrl = fileUrl,
                FileSize = file.Length,
                ContentType = file.ContentType,
                UploadedAt = DateTime.UtcNow,
            };

            _logger.LogInformation("File uploaded successfully: {FilePath}", filePath);
            return Result.Success(metadata);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName}", file.FileName);
            return Result.Failure<FileMetadata>(
                Error.Internal("An error occurred while uploading the file")
            );
        }
    }

    public async Task<Result<FileMetadata>> UploadFileToLocalStorageAsync(
        Stream fileStream,
        string containerName,
        string fileName,
        string contentType = "application/octet-stream"
    )
    {
        try
        {
            if (fileStream == null || fileStream.Length == 0)
                return Result.Failure<FileMetadata>(
                    Error.Validation("File stream is empty or null.")
                );

            // Ensure container directory exists
            var containerPath = Path.Combine(_baseStoragePath, containerName);
            Directory.CreateDirectory(containerPath);

            // Generate unique file name if needed
            var storedFileName = fileName;
            var filePath = Path.Combine(containerPath, storedFileName);
            var relativePath = Path.Combine(containerName, storedFileName).Replace("\\", "/");

            // If file already exists, append a unique identifier
            if (File.Exists(filePath))
            {
                var fileExtension = Path.GetExtension(fileName);
                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                storedFileName = $"{fileNameWithoutExtension}-{Guid.NewGuid()}{fileExtension}";
                filePath = Path.Combine(containerPath, storedFileName);
                relativePath = Path.Combine(containerName, storedFileName).Replace("\\", "/");
            }

            // Write file to local storage
            using (
                var fileStreamOutput = new FileStream(filePath, FileMode.Create, FileAccess.Write)
            )
            {
                await fileStream.CopyToAsync(fileStreamOutput);
            }

            // Generate file URL (assuming a simple local path-based URL for now)
            var fileUrl = $"{_baseUrl}/uploads/{relativePath}";

            var metadata = new FileMetadata
            {
                OriginalFileName = fileName,
                StoredFileName = storedFileName,
                FilePath = filePath,
                RelativePath = relativePath,
                FileUrl = fileUrl,
                FileSize = fileStream.Length,
                ContentType = contentType,
                UploadedAt = DateTime.UtcNow,
            };

            return Result.Success(metadata);
        }
        catch (Exception ex)
        {
            return Result.Failure<FileMetadata>(
                Error.Internal($"Failed to upload file to local storage: {ex.Message}")
            );
        }
    }

    /// <inheritdoc />
    public async Task<Result<bool>> DeleteFileAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                return Result.Failure<bool>(Error.BadRequest("File path is empty or null"));
            }

            if (!File.Exists(filePath))
            {
                return Result.Failure<bool>(Error.NotFound("File not found"));
            }

            await Task.Run(() => File.Delete(filePath)); // Offload delete to background thread

            _logger.LogInformation("File deleted successfully: {FilePath}", filePath);
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FilePath}", filePath);
            return Result.Failure<bool>(
                Error.Internal("An error occurred while deleting the file")
            );
        }
    }

    /// <inheritdoc />
    public string GetFileUrl(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return string.Empty;
        }

        // If it's already a URL, return it
        if (path.StartsWith("http://") || path.StartsWith("https://"))
        {
            return path;
        }

        // Check if it's a relative path (starts with /)
        if (path.StartsWith("/"))
        {
            return $"{_baseUrl}{path}";
        }

        // If it's an absolute file path, convert it to a relative URL
        if (Path.IsPathRooted(path))
        {
            var relativePath = path.Replace(_baseStoragePath, "uploads")
                .Replace("\\", "/")
                .TrimStart('/');

            return $"{_baseUrl}/{relativePath}";
        }

        // If it's already a relative path without leading slash, just add the base URL
        return $"{_baseUrl}/{path.TrimStart('/')}";
    }

    /// <inheritdoc />
    public Result<bool> ValidateFile(IFormFile file, int maxSizeInMb, string[] allowedExtensions)
    {
        if (file == null || file.Length == 0)
        {
            return Result.Failure<bool>(Error.BadRequest("File is empty or null"));
        }

        // Check file size
        var maxSizeInBytes = maxSizeInMb * 1024 * 1024;
        if (file.Length > maxSizeInBytes)
        {
            return Result.Failure<bool>(
                Error.BadRequest($"File size exceeds the maximum allowed size of {maxSizeInMb}MB")
            );
        }

        // Check file extension
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(fileExtension))
        {
            return Result.Failure<bool>(
                Error.BadRequest(
                    $"File type {fileExtension} is not allowed. Allowed types: {string.Join(", ", allowedExtensions)}"
                )
            );
        }

        return Result.Success(true);
    }

    /// <summary>
    /// Generates a unique file name to prevent collisions
    /// </summary>
    private string GenerateUniqueFileName(string originalFileName)
    {
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
        var fileExtension = Path.GetExtension(originalFileName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmssfff");
        var randomPart = Guid.NewGuid().ToString("N").Substring(0, 8);

        return $"{SanitizeFileName(fileNameWithoutExtension)}_{timestamp}_{randomPart}{fileExtension}";
    }

    /// <summary>
    /// Sanitizes a file name to remove invalid characters
    /// </summary>
    private string SanitizeFileName(string fileName)
    {
        // Remove invalid characters
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());

        // Replace spaces with underscores
        sanitized = sanitized.Replace(" ", "_");

        // Remove any other potentially problematic characters
        sanitized = Regex.Replace(sanitized, @"[^\w\-\.]", "");

        return sanitized;
    }

    /// <summary>
    /// Sanitizes a path to remove invalid characters
    /// </summary>
    private string SanitizePath(string path)
    {
        // Remove invalid characters
        var invalidChars = Path.GetInvalidPathChars();
        var sanitized = new string(path.Where(c => !invalidChars.Contains(c)).ToArray());

        // Replace spaces with underscores
        sanitized = sanitized.Replace(" ", "_");

        // Remove any other potentially problematic characters
        sanitized = Regex.Replace(sanitized, @"[^\w\-\/\\]", "");

        return sanitized;
    }
}
