﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to deactivate a care category
    /// </summary>
    public record DeactivateCareCategoryCommand(Guid CategoryId, Guid AdminId)
        : ICommand<Result<CareCategoryResponse>>;

    /// <summary>
    /// Handler for the DeactivateCareCategoryCommand
    /// </summary>
    public sealed class DeactivateCareCategoryCommandHandler
        : ICommandHandler<DeactivateCareCategoryCommand, Result<CareCategoryResponse>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<DeactivateCareCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public DeactivateCareCategoryCommandHandler(
            ICareCategoryService categoryService,
            ILogger<DeactivateCareCategoryCommandHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result<CareCategoryResponse>> Handle(
            DeactivateCareCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Create an update request that only sets IsActive to false
                var updateRequest = new UpdateCareCategoryRequest { IsActive = false };

                return await _categoryService.UpdateCategoryAsync(
                    request.CategoryId,
                    updateRequest,
                    request.AdminId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error deactivating care category with ID {CategoryId}",
                    request.CategoryId
                );
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }
    }
}
