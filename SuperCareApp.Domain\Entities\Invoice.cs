using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities;

public sealed class Invoice : BaseEntity
{
    public Guid BookingWindowId { get; set; } // Changed from BookingId
    public string InvoiceNumber { get; set; } = null!;
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public InvoiceStatus Status { get; set; }
    public decimal Amount { get; set; }
    public decimal Tax { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = null!;
    public string FileName { get; set; }
    public string FileUrl { get; set; }

    // Navigation properties
    public BookingWindow BookingWindow { get; set; } = null!;
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
}
