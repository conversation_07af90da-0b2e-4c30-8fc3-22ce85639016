﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Persistence.Configurations
{
    public class ApplicationRoleClaimConfiguration : IEntityTypeConfiguration<ApplicationRoleClaim>
    {
        public void Configure(EntityTypeBuilder<ApplicationRoleClaim> builder)
        {
            // Relationships
            builder
                .HasOne(rc => rc.Role)
                .WithMany()
                .HasForeignKey(rc => rc.RoleId)
                .HasPrincipalKey(r => r.Id)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
