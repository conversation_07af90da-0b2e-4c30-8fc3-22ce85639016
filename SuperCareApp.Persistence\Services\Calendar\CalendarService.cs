using System.Globalization;
using SuperCareApp.Application.Common.Interfaces.Calendar;
using SuperCareApp.Application.Common.Models.Calendar;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Calendar
{
    /// <summary>
    /// Service for calendar-related operations
    /// </summary>
    public class CalendarService : ICalendarService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CalendarService> _logger;

        public CalendarService(ApplicationDbContext context, ILogger<CalendarService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Result<MonthlyCalendarResponse>> GetMonthlyCalendarAsync(
            Guid userId,
            int year,
            int month
        )
        {
            try
            {
                // Check if user is a provider
                var providerProfile = await _context.CareProviderProfiles.FirstOrDefaultAsync(p =>
                    p.UserId == userId && !p.IsDeleted
                );

                if (providerProfile == null)
                {
                    return Result.Failure<MonthlyCalendarResponse>(
                        Error.NotFound("Provider profile not found")
                    );
                }

                return await GetCalendarSummaryAsync(providerProfile.Id, year, month);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly calendar for user {UserId}", userId);
                return Result.Failure<MonthlyCalendarResponse>(
                    Error.Internal("Error retrieving monthly calendar")
                );
            }
        }

        public async Task<Result<CalendarRangeResponse>> GetCalendarRangeAsync(
            Guid providerId,
            CalendarRangeParams parameters
        )
        {
            try
            {
                // Validate provider exists
                var providerExists = await _context.CareProviderProfiles.AnyAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );

                if (!providerExists)
                {
                    return Result.Failure<CalendarRangeResponse>(
                        Error.NotFound("Provider not found")
                    );
                }

                var days = new List<CalendarDayResponse>();
                var currentDate = parameters.StartDate.Date;

                while (currentDate <= parameters.EndDate.Date)
                {
                    var dayResponse = await GetDayAvailabilityInternalAsync(
                        providerId,
                        currentDate,
                        parameters.IncludeBookings
                    );
                    days.Add(dayResponse);
                    currentDate = currentDate.AddDays(1);
                }

                var response = new CalendarRangeResponse
                {
                    StartDate = parameters.StartDate,
                    EndDate = parameters.EndDate,
                    Days = days,
                };

                if (parameters.IncludeStatistics)
                {
                    response.Statistics = CalculateStatistics(days);
                }

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting calendar range for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<CalendarRangeResponse>(
                    Error.Internal("Error retrieving calendar range")
                );
            }
        }

        public async Task<Result<CalendarDayResponse>> GetDayAvailabilityAsync(
            Guid providerId,
            DateTime date
        )
        {
            try
            {
                // Validate provider exists
                var providerExists = await _context.CareProviderProfiles.AnyAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );

                if (!providerExists)
                {
                    return Result.Failure<CalendarDayResponse>(
                        Error.NotFound("Provider not found")
                    );
                }

                var dayResponse = await GetDayAvailabilityInternalAsync(
                    providerId,
                    date.Date,
                    true
                );
                return Result.Success(dayResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting day availability for provider {ProviderId} on {Date}",
                    providerId,
                    date
                );
                return Result.Failure<CalendarDayResponse>(
                    Error.Internal("Error retrieving day availability")
                );
            }
        }

        public async Task<Result<NextAvailableSlotsResponse>> GetNextAvailableSlotsAsync(
            Guid providerId,
            NextAvailableSlotsParams parameters
        )
        {
            try
            {
                // Validate provider exists
                var providerExists = await _context.CareProviderProfiles.AnyAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );

                if (!providerExists)
                {
                    return Result.Failure<NextAvailableSlotsResponse>(
                        Error.NotFound("Provider not found")
                    );
                }

                var startDate = parameters.StartDate?.Date ?? DateTime.Today;
                var endDate = startDate.AddDays(parameters.MaxDaysAhead);
                var availableSlots = new List<AvailableSlotResponse>();
                var currentDate = startDate;

                while (currentDate <= endDate && availableSlots.Count < parameters.MaxResults)
                {
                    var dayAvailability = await GetDayAvailabilityInternalAsync(
                        providerId,
                        currentDate,
                        true
                    );

                    if (dayAvailability.IsAvailable && !dayAvailability.IsOnLeave)
                    {
                        var daySlots = GenerateAvailableSlots(
                            dayAvailability,
                            parameters.DurationMinutes
                        );
                        availableSlots.AddRange(
                            daySlots.Take(parameters.MaxResults - availableSlots.Count)
                        );
                    }

                    currentDate = currentDate.AddDays(1);
                }

                var response = new NextAvailableSlotsResponse
                {
                    Slots = availableSlots,
                    SearchedUntil = currentDate.AddDays(-1),
                    HasMoreSlots = currentDate <= endDate,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting next available slots for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<NextAvailableSlotsResponse>(
                    Error.Internal("Error retrieving next available slots")
                );
            }
        }

        public async Task<Result<CheckAvailabilityResponse>> CheckAvailabilityAsync(
            Guid providerId,
            CheckAvailabilityParams parameters
        )
        {
            try
            {
                // Validate provider exists
                var providerExists = await _context.CareProviderProfiles.AnyAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );

                if (!providerExists)
                {
                    return Result.Failure<CheckAvailabilityResponse>(
                        Error.NotFound("Provider not found")
                    );
                }

                var dayAvailability = await GetDayAvailabilityInternalAsync(
                    providerId,
                    parameters.Date.Date,
                    true
                );
                var conflicts = new List<string>();
                var isAvailable = true;
                var reason = "Available";

                // Check if provider is on leave
                if (dayAvailability.IsOnLeave)
                {
                    isAvailable = false;
                    reason = "Provider is on leave";
                    conflicts.Add("Provider has a leave period on this date");
                }

                // Check if day is configured as available
                if (!dayAvailability.IsAvailable)
                {
                    isAvailable = false;
                    reason = "Provider is not available on this day";
                    conflicts.Add("Provider does not work on this day of the week");
                }

                // Check for time slot conflicts
                if (isAvailable)
                {
                    var requestedStart = TimeOnly.Parse(parameters.StartTime);
                    var requestedEnd = TimeOnly.Parse(parameters.EndTime);

                    var hasTimeSlotConflict = dayAvailability.Bookings.Any(b =>
                    {
                        var bookingStart = TimeOnly.Parse(b.StartTime);
                        var bookingEnd = TimeOnly.Parse(b.EndTime);
                        return requestedStart < bookingEnd && requestedEnd > bookingStart;
                    });

                    if (hasTimeSlotConflict)
                    {
                        isAvailable = false;
                        reason = "Time slot is already booked";
                        conflicts.Add("Requested time slot conflicts with existing booking");
                    }
                }

                // Get alternative slots if not available
                var alternativeSlots = new List<AvailableSlotResponse>();
                if (!isAvailable)
                {
                    var duration = parameters.DurationMinutes ?? 60;
                    alternativeSlots = GenerateAvailableSlots(dayAvailability, duration);
                }

                var response = new CheckAvailabilityResponse
                {
                    IsAvailable = isAvailable,
                    Reason = reason,
                    Conflicts = conflicts,
                    AlternativeSlots = alternativeSlots,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error checking availability for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<CheckAvailabilityResponse>(
                    Error.Internal("Error checking availability")
                );
            }
        }

        public async Task<Result<MonthlyCalendarResponse>> GetCalendarSummaryAsync(
            Guid providerId,
            int year,
            int month
        )
        {
            try
            {
                // Validate provider exists
                var providerExists = await _context.CareProviderProfiles.AnyAsync(p =>
                    p.Id == providerId && !p.IsDeleted
                );

                if (!providerExists)
                {
                    return Result.Failure<MonthlyCalendarResponse>(
                        Error.NotFound("Provider not found")
                    );
                }

                var startDate = new DateTime(year, month, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);
                var days = new List<CalendarDayResponse>();
                var currentDate = startDate;

                while (currentDate <= endDate)
                {
                    var dayResponse = await GetDayAvailabilityInternalAsync(
                        providerId,
                        currentDate,
                        true
                    );
                    days.Add(dayResponse);
                    currentDate = currentDate.AddDays(1);
                }

                var response = new MonthlyCalendarResponse
                {
                    Year = year,
                    Month = month,
                    MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month),
                    Days = days,
                    Statistics = CalculateStatistics(days),
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting calendar summary for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<MonthlyCalendarResponse>(
                    Error.Internal("Error retrieving calendar summary")
                );
            }
        }

        public async Task<Result<AvailableProvidersResponse>> GetAvailableProvidersAsync(
            AvailableProvidersParams parameters
        )
        {
            try
            {
                var query = _context
                    .CareProviderProfiles.Where(p => !p.IsDeleted)
                    .Include(p => p.User)
                    .ThenInclude(applicationUser => applicationUser.UserProfile)
                    .Include(p => p.CareProviderCategories)
                    .ThenInclude(careProviderCategory => careProviderCategory.CareCategory)
                    .Include(careProviderProfile => careProviderProfile.User)
                    .ThenInclude(applicationUser => applicationUser.UserAddresses)
                    .ThenInclude(userAddress => userAddress.Address)
                    .AsQueryable();

                // Filter by category if specified
                if (parameters.CategoryId.HasValue)
                {
                    query = query.Where(p =>
                        p.CareProviderCategories.Any(c => c.Id == parameters.CategoryId.Value)
                    );
                }

                // Filter by location if specified
                if (!string.IsNullOrEmpty(parameters.Location))
                {
                    query = query.Where(p =>
                        p.User.UserAddresses.Select(a => a.Address.City)
                            .Contains(parameters.Location)
                        || p.User.UserAddresses.Select(a => a.Address.State)
                            .Contains(parameters.Location)
                    );
                }

                var providers = await query.ToListAsync();
                var availableProviders = new List<AvailableProviderResponse>();

                foreach (var provider in providers)
                {
                    var dayAvailability = await GetDayAvailabilityInternalAsync(
                        provider.Id,
                        parameters.Date.Date,
                        true
                    );

                    if (dayAvailability.IsAvailable && !dayAvailability.IsOnLeave)
                    {
                        var availableSlots = new List<AvailableSlotResponse>();

                        if (
                            !string.IsNullOrEmpty(parameters.StartTime)
                            && !string.IsNullOrEmpty(parameters.EndTime)
                        )
                        {
                            // Check specific time slot
                            var checkParams = new CheckAvailabilityParams
                            {
                                Date = parameters.Date,
                                StartTime = parameters.StartTime,
                                EndTime = parameters.EndTime,
                            };

                            var availabilityCheck = await CheckAvailabilityAsync(
                                provider.Id,
                                checkParams
                            );
                            if (availabilityCheck.IsSuccess && availabilityCheck.Value.IsAvailable)
                            {
                                availableSlots.Add(
                                    new AvailableSlotResponse
                                    {
                                        Date = parameters.Date,
                                        DayOfWeek = parameters.Date.DayOfWeek.ToString(),
                                        StartTime = parameters.StartTime,
                                        EndTime = parameters.EndTime,
                                        DurationMinutes = parameters.DurationMinutes ?? 60,
                                    }
                                );
                            }
                        }
                        else
                        {
                            // Get all available slots for the day
                            var duration = parameters.DurationMinutes ?? 60;
                            availableSlots = GenerateAvailableSlots(dayAvailability, duration);
                        }

                        if (availableSlots.Any())
                        {
                            availableProviders.Add(
                                new AvailableProviderResponse
                                {
                                    Id = provider.Id,
                                    FirstName = provider.User.UserProfile.FirstName ?? "",
                                    LastName = provider.User.UserProfile.LastName ?? "",
                                    ProfilePicture = provider.User.UserProfile.ImagePath,
                                    Rating = provider.Rating ?? 0,
                                    ReviewCount = provider.RatingCount ?? 0,
                                    Location =
                                        $"{provider.User.UserAddresses.First().Address.City}, {provider.User.UserAddresses.First().Address.State}",
                                    Categories = provider
                                        .CareProviderCategories.Select(c => c.CareCategory.Name)
                                        .ToList(),
                                    AvailableSlots = availableSlots,
                                    HourlyRate = provider
                                        .CareProviderCategories.FirstOrDefault(cpc =>
                                            cpc.ProviderId == provider.Id
                                        )
                                        ?.HourlyRate,
                                }
                            );
                        }
                    }
                }

                // Filter by max price if specified
                if (parameters.MaxPrice.HasValue)
                {
                    availableProviders = availableProviders
                        .Where(p => p.HourlyRate <= parameters.MaxPrice.Value)
                        .ToList();
                }

                var response = new AvailableProvidersResponse
                {
                    Providers = availableProviders,
                    SearchDate = parameters.Date,
                    SearchTimeSlot =
                        !string.IsNullOrEmpty(parameters.StartTime)
                        && !string.IsNullOrEmpty(parameters.EndTime)
                            ? $"{parameters.StartTime} - {parameters.EndTime}"
                            : null,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available providers");
                return Result.Failure<AvailableProvidersResponse>(
                    Error.Internal("Error retrieving available providers")
                );
            }
        }

        public async Task<Result<MonthlyCalendarResponse>> GetFilteredCalendarAsync(
            Guid providerId,
            int year,
            int month,
            FilteredCalendarParams filters
        )
        {
            try
            {
                // Get base calendar
                var calendarResult = await GetCalendarSummaryAsync(providerId, year, month);
                if (calendarResult.IsFailure)
                {
                    return calendarResult;
                }

                var calendar = calendarResult.Value;

                // Apply filters
                if (filters.AvailableOnly == true)
                {
                    calendar.Days = calendar
                        .Days.Where(d => d.IsAvailable && !d.IsOnLeave)
                        .ToList();
                }

                if (!string.IsNullOrEmpty(filters.Status))
                {
                    calendar.Days.ForEach(day =>
                    {
                        day.Bookings = day
                            .Bookings.Where(b =>
                                b.Status.Equals(filters.Status, StringComparison.OrdinalIgnoreCase)
                            )
                            .ToList();
                    });
                }

                if (filters.CategoryId.HasValue)
                {
                    // Filter bookings by category (would need to join with booking data)
                    // This is a simplified version
                    calendar.Days.ForEach(day =>
                    {
                        // In a real implementation, you'd filter by category ID
                        // For now, we'll keep all bookings
                    });
                }

                if (filters.IncludeLeave == false)
                {
                    calendar.Days.ForEach(day => day.IsOnLeave = false);
                }

                if (filters.IncludeBookings == false)
                {
                    calendar.Days.ForEach(day => day.Bookings.Clear());
                }

                // Recalculate statistics after filtering
                calendar.Statistics = CalculateStatistics(calendar.Days);

                return Result.Success(calendar);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting filtered calendar for provider {ProviderId}",
                    providerId
                );
                return Result.Failure<MonthlyCalendarResponse>(
                    Error.Internal("Error retrieving filtered calendar")
                );
            }
        }

        #region Private Helper Methods

        private async Task<CalendarDayResponse> GetDayAvailabilityInternalAsync(
            Guid providerId,
            DateTime date,
            bool includeBookings
        )
        {
            var dateOnly = DateOnly.FromDateTime(date);
            var dayOfWeek = date.DayOfWeek.ToString();

            // Get availability configuration
            var availability = await _context
                .Set<Availability>()
                .Include(a => a.AvailabilitySlots)
                .FirstOrDefaultAsync(a =>
                    a.ProviderId == providerId && a.DayOfWeek == dayOfWeek && !a.IsDeleted
                );

            // Check for leave periods
            var isOnLeave = await _context.Leaves.AnyAsync(l =>
                l.ProviderId == providerId
                && !l.IsDeleted
                && l.StartDate <= date.Date
                && l.EndDate >= date.Date
            );

            var response = new CalendarDayResponse
            {
                Date = date,
                DayOfWeek = dayOfWeek,
                IsAvailable = availability?.IsAvailable == true && !isOnLeave,
                IsOnLeave = isOnLeave,
                TotalSlots = availability?.AvailabilitySlots.Count ?? 0,
                Slots = new List<CalendarSlotResponse>(),
                Bookings = new List<CalendarBookingResponse>(),
            };

            if (availability != null)
            {
                response.Slots = availability
                    .AvailabilitySlots.Select(s => new CalendarSlotResponse
                    {
                        StartTime = s.StartTime.ToString("HH:mm"),
                        EndTime = s.EndTime.ToString("HH:mm"),
                        IsAvailable = !isOnLeave,
                        IsBooked = false, // Would need to check against bookings
                    })
                    .ToList();
            }

            if (includeBookings)
            {
                var bookings = await _context
                    .Bookings.Include(b => b.Client)
                    .Include(b => b.Category)
                    .Include(b => b.Status)
                    .Include(b => b.BookingWindows)
                    .Where(b =>
                        b.ProviderId == providerId
                        && b.BookingWindows.Any(w => w.Date == dateOnly)
                        && !b.IsDeleted
                    )
                    .ToListAsync();

                response.Bookings = bookings
                    .SelectMany(b =>
                        b.BookingWindows.Where(w => w.Date == dateOnly)
                            .Select(w => new { Booking = b, Window = w })
                    )
                    .Select(bw => new CalendarBookingResponse
                    {
                        Id = bw.Booking.Id,
                        StartTime = bw.Window.StartTime.ToString("HH:mm"),
                        EndTime = bw.Window.EndTime.ToString("HH:mm"),
                        ClientName =
                            $"{bw.Booking.Client.UserProfile.FirstName} {bw.Booking.Client.UserProfile.LastName}",
                        CategoryName = bw.Booking.Category.Name,
                        Status = bw.Booking.Status?.Status.ToString() ?? "Unknown",
                        Amount = bw.Booking.TotalAmount,
                        SpecialInstructions = bw.Booking.SpecialInstructions,
                    })
                    .ToList();

                response.BookedSlots = response.Bookings.Count;
            }

            response.AvailableSlots = response.TotalSlots - response.BookedSlots;

            return response;
        }

        private List<AvailableSlotResponse> GenerateAvailableSlots(
            CalendarDayResponse dayAvailability,
            int durationMinutes
        )
        {
            var availableSlots = new List<AvailableSlotResponse>();

            if (!dayAvailability.IsAvailable || dayAvailability.IsOnLeave)
            {
                return availableSlots;
            }

            foreach (var slot in dayAvailability.Slots.Where(s => s.IsAvailable && !s.IsBooked))
            {
                var slotStart = TimeOnly.Parse(slot.StartTime);
                var slotEnd = TimeOnly.Parse(slot.EndTime);
                var currentTime = slotStart;

                while (currentTime.AddMinutes(durationMinutes) <= slotEnd)
                {
                    var endTime = currentTime.AddMinutes(durationMinutes);

                    // Check if this time slot conflicts with any booking
                    var hasConflict = dayAvailability.Bookings.Any(b =>
                    {
                        var bookingStart = TimeOnly.Parse(b.StartTime);
                        var bookingEnd = TimeOnly.Parse(b.EndTime);
                        return currentTime < bookingEnd && endTime > bookingStart;
                    });

                    if (!hasConflict)
                    {
                        availableSlots.Add(
                            new AvailableSlotResponse
                            {
                                Date = dayAvailability.Date,
                                DayOfWeek = dayAvailability.DayOfWeek,
                                StartTime = currentTime.ToString("HH:mm"),
                                EndTime = endTime.ToString("HH:mm"),
                                DurationMinutes = durationMinutes,
                            }
                        );
                    }

                    currentTime = currentTime.AddMinutes(30); // 30-minute intervals
                }
            }

            return availableSlots;
        }

        private CalendarStatistics CalculateStatistics(List<CalendarDayResponse> days)
        {
            var totalDays = days.Count;
            var availableDays = days.Count(d => d.IsAvailable && !d.IsOnLeave);
            var leaveDays = days.Count(d => d.IsOnLeave);
            var totalSlots = days.Sum(d => d.TotalSlots);
            var bookedSlots = days.Sum(d => d.BookedSlots);
            var availableSlots = totalSlots - bookedSlots;
            var totalRevenue = days.SelectMany(d => d.Bookings).Sum(b => b.Amount);
            var utilizationRate = totalSlots > 0 ? (double)bookedSlots / totalSlots * 100 : 0;

            return new CalendarStatistics
            {
                TotalDays = totalDays,
                AvailableDays = availableDays,
                LeaveDays = leaveDays,
                TotalSlots = totalSlots,
                BookedSlots = bookedSlots,
                AvailableSlots = availableSlots,
                TotalRevenue = totalRevenue,
                UtilizationRate = utilizationRate,
            };
        }

        #endregion
    }
}
