using System.Text.Json.Serialization;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Complete dashboard statistics response
    /// </summary>
    public class DashboardStatisticsResponse
    {
        /// <summary>
        /// System overview statistics
        /// </summary>
        public SystemOverviewStatistics SystemOverview { get; set; } = new();

        /// <summary>
        /// User analytics
        /// </summary>
        public UserAnalyticsStatistics UserAnalytics { get; set; } = new();

        /// <summary>
        /// Booking analytics
        /// </summary>
        public BookingAnalyticsStatistics BookingAnalytics { get; set; } = new();

        /// <summary>
        /// Financial analytics
        /// </summary>
        public FinancialAnalyticsStatistics FinancialAnalytics { get; set; } = new();

        /// <summary>
        /// Notification analytics
        /// </summary>
        public NotificationAnalyticsStatistics NotificationAnalytics { get; set; } = new();

        /// <summary>
        /// When the statistics were generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Date range for the statistics
        /// </summary>
        public DateRange DateRange { get; set; } = new();
    }

    /// <summary>
    /// System overview statistics
    /// </summary>
    public class SystemOverviewStatistics
    {
        /// <summary>
        /// Total number of users in the system
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// Number of active users (logged in within last 30 days)
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// Total number of care providers
        /// </summary>
        public int TotalProviders { get; set; }

        /// <summary>
        /// Number of verified care providers
        /// </summary>
        public int VerifiedProviders { get; set; }

        /// <summary>
        /// Total number of bookings
        /// </summary>
        public int TotalBookings { get; set; }

        /// <summary>
        /// Number of completed bookings
        /// </summary>
        public int CompletedBookings { get; set; }

        /// <summary>
        /// Total revenue generated
        /// </summary>
        public decimal TotalRevenue { get; set; }

        /// <summary>
        /// Platform fees collected
        /// </summary>
        public decimal PlatformFees { get; set; }

        /// <summary>
        /// Number of pending approval requests
        /// </summary>
        public int PendingApprovals { get; set; }

        /// <summary>
        /// Number of pending document verifications
        /// </summary>
        public int PendingDocuments { get; set; }

        /// <summary>
        /// System uptime percentage
        /// </summary>
        public double SystemUptime { get; set; }

        /// <summary>
        /// Average response time in milliseconds
        /// </summary>
        public double AverageResponseTime { get; set; }
    }

    /// <summary>
    /// User analytics statistics
    /// </summary>
    public class UserAnalyticsStatistics
    {
        /// <summary>
        /// New user registrations in the period
        /// </summary>
        public int NewRegistrations { get; set; }

        /// <summary>
        /// User growth rate percentage
        /// </summary>
        public double GrowthRate { get; set; }

        /// <summary>
        /// User retention rate percentage
        /// </summary>
        public double RetentionRate { get; set; }

        /// <summary>
        /// Average session duration in minutes
        /// </summary>
        public double AverageSessionDuration { get; set; }

        /// <summary>
        /// User registrations by day
        /// </summary>
        public List<DailyStatistic> RegistrationsByDay { get; set; } = new();

        /// <summary>
        /// User activity by day
        /// </summary>
        public List<DailyStatistic> ActivityByDay { get; set; } = new();

        /// <summary>
        /// User distribution by role
        /// </summary>
        public Dictionary<string, int> UsersByRole { get; set; } = new();

        /// <summary>
        /// User distribution by country
        /// </summary>
        public Dictionary<string, int> UsersByCountry { get; set; } = new();

        /// <summary>
        /// Top active users
        /// </summary>
        public List<TopUserStatistic> TopActiveUsers { get; set; } = new();
    }

    /// <summary>
    /// Booking analytics statistics
    /// </summary>
    public class BookingAnalyticsStatistics
    {
        /// <summary>
        /// New bookings in the period
        /// </summary>
        public int NewBookings { get; set; }

        /// <summary>
        /// Booking growth rate percentage
        /// </summary>
        public double GrowthRate { get; set; }

        /// <summary>
        /// Booking completion rate percentage
        /// </summary>
        public double CompletionRate { get; set; }

        /// <summary>
        /// Booking cancellation rate percentage
        /// </summary>
        public double CancellationRate { get; set; }

        /// <summary>
        /// Average booking value
        /// </summary>
        public decimal AverageBookingValue { get; set; }

        /// <summary>
        /// Bookings by day
        /// </summary>
        public List<DailyStatistic> BookingsByDay { get; set; } = new();

        /// <summary>
        /// Bookings by status
        /// </summary>
        public Dictionary<string, int> BookingsByStatus { get; set; } = new();

        /// <summary>
        /// Bookings by care category
        /// </summary>
        public Dictionary<string, int> BookingsByCategory { get; set; } = new();

        /// <summary>
        /// Top performing providers
        /// </summary>
        public List<TopProviderStatistic> TopProviders { get; set; } = new();

        /// <summary>
        /// Peak booking hours
        /// </summary>
        public Dictionary<int, int> BookingsByHour { get; set; } = new();
    }

    /// <summary>
    /// Financial analytics statistics
    /// </summary>
    public class FinancialAnalyticsStatistics
    {
        /// <summary>
        /// Total revenue in the period
        /// </summary>
        public decimal TotalRevenue { get; set; }

        /// <summary>
        /// Revenue growth rate percentage
        /// </summary>
        public double GrowthRate { get; set; }

        /// <summary>
        /// Platform fees collected
        /// </summary>
        public decimal PlatformFees { get; set; }

        /// <summary>
        /// Provider earnings
        /// </summary>
        public decimal ProviderEarnings { get; set; }

        /// <summary>
        /// Average transaction value
        /// </summary>
        public decimal AverageTransactionValue { get; set; }

        /// <summary>
        /// Revenue by day
        /// </summary>
        public List<DailyFinancialStatistic> RevenueByDay { get; set; } = new();

        /// <summary>
        /// Revenue by care category
        /// </summary>
        public Dictionary<string, decimal> RevenueByCategory { get; set; } = new();

        /// <summary>
        /// Payment method distribution
        /// </summary>
        public Dictionary<string, int> PaymentMethods { get; set; } = new();

        /// <summary>
        /// Top earning providers
        /// </summary>
        public List<TopEarnerStatistic> TopEarners { get; set; } = new();
    }

    /// <summary>
    /// Notification analytics statistics
    /// </summary>
    public class NotificationAnalyticsStatistics
    {
        /// <summary>
        /// Total notifications sent
        /// </summary>
        public int TotalNotifications { get; set; }

        /// <summary>
        /// Notification delivery rate percentage
        /// </summary>
        public double DeliveryRate { get; set; }

        /// <summary>
        /// Notification read rate percentage
        /// </summary>
        public double ReadRate { get; set; }

        /// <summary>
        /// Average delivery time in seconds
        /// </summary>
        public double AverageDeliveryTime { get; set; }

        /// <summary>
        /// Notifications by type
        /// </summary>
        public Dictionary<string, int> NotificationsByType { get; set; } = new();

        /// <summary>
        /// Notifications by channel
        /// </summary>
        public Dictionary<string, int> NotificationsByChannel { get; set; } = new();

        /// <summary>
        /// WebSocket connection statistics
        /// </summary>
        public WebSocketStatistics WebSocketStats { get; set; } = new();

        /// <summary>
        /// Failed notifications
        /// </summary>
        public int FailedNotifications { get; set; }
    }

    /// <summary>
    /// WebSocket connection statistics
    /// </summary>
    public class WebSocketStatistics
    {
        /// <summary>
        /// Current active connections
        /// </summary>
        public int ActiveConnections { get; set; }

        /// <summary>
        /// Total connections established
        /// </summary>
        public int TotalConnections { get; set; }

        /// <summary>
        /// Authenticated connections
        /// </summary>
        public int AuthenticatedConnections { get; set; }

        /// <summary>
        /// Unique users connected
        /// </summary>
        public int UniqueUsers { get; set; }

        /// <summary>
        /// Average connection duration in minutes
        /// </summary>
        public double AverageConnectionDuration { get; set; }

        /// <summary>
        /// Messages sent through WebSocket
        /// </summary>
        public int MessagesSent { get; set; }

        /// <summary>
        /// Connection errors
        /// </summary>
        public int ConnectionErrors { get; set; }
    }

    /// <summary>
    /// Daily statistic data point
    /// </summary>
    public class DailyStatistic
    {
        /// <summary>
        /// Date of the statistic
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Count for that day
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// Daily financial statistic data point
    /// </summary>
    public class DailyFinancialStatistic
    {
        /// <summary>
        /// Date of the statistic
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Revenue for that day
        /// </summary>
        public decimal Revenue { get; set; }

        /// <summary>
        /// Number of transactions
        /// </summary>
        public int TransactionCount { get; set; }
    }

    /// <summary>
    /// Top user statistic
    /// </summary>
    public class TopUserStatistic
    {
        /// <summary>
        /// User ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// User email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Activity score or metric
        /// </summary>
        public int ActivityScore { get; set; }

        /// <summary>
        /// Last login date
        /// </summary>
        public DateTime? LastLogin { get; set; }
    }

    /// <summary>
    /// Top provider statistic
    /// </summary>
    public class TopProviderStatistic
    {
        /// <summary>
        /// Provider ID
        /// </summary>
        public Guid ProviderId { get; set; }

        /// <summary>
        /// Provider name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Number of bookings
        /// </summary>
        public int BookingCount { get; set; }

        /// <summary>
        /// Average rating
        /// </summary>
        public decimal AverageRating { get; set; }

        /// <summary>
        /// Total earnings
        /// </summary>
        public decimal TotalEarnings { get; set; }
    }

    /// <summary>
    /// Top earner statistic
    /// </summary>
    public class TopEarnerStatistic
    {
        /// <summary>
        /// Provider ID
        /// </summary>
        public Guid ProviderId { get; set; }

        /// <summary>
        /// Provider name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Total earnings
        /// </summary>
        public decimal TotalEarnings { get; set; }

        /// <summary>
        /// Number of completed bookings
        /// </summary>
        public int CompletedBookings { get; set; }

        /// <summary>
        /// Average booking value
        /// </summary>
        public decimal AverageBookingValue { get; set; }
    }

    /// <summary>
    /// Date range for statistics
    /// </summary>
    public class DateRange
    {
        /// <summary>
        /// Start date
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Period type (e.g., "7days", "30days", "custom")
        /// </summary>
        public string Period { get; set; } = string.Empty;
    }

    /// <summary>
    /// Dashboard statistics request parameters
    /// </summary>
    public class DashboardStatisticsRequest
    {
        /// <summary>
        /// Start date for statistics (optional, defaults to 30 days ago)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date for statistics (optional, defaults to now)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Predefined period (7days, 30days, 90days, 1year)
        /// </summary>
        public string? Period { get; set; }

        /// <summary>
        /// Include detailed breakdowns
        /// </summary>
        public bool IncludeDetails { get; set; } = true;

        /// <summary>
        /// Include real-time WebSocket statistics
        /// </summary>
        public bool IncludeRealTimeStats { get; set; } = true;
    }
}
