﻿using SuperCareApp.Domain.Common.Exceptions;

namespace SuperCareApp.Domain.Common.Results
{
    /// <summary>
    /// Factory for creating Result objects from exceptions
    /// </summary>
    public static class ResultFactory
    {
        /// <summary>
        /// Converts an exception to a Result
        /// </summary>
        /// <param name="exception">The exception to convert</param>
        /// <returns>A Result representing the exception</returns>
        public static Result FromException(Exception exception)
        {
            return exception switch
            {
                EntityNotFoundException ex => Result.Failure(Error.NotFound(ex.Message)),
                ValidationException ex => Result.Failure(Error.Validation(ex.Message, ex.Errors)),
                UnauthorizedException ex => Result.Failure(Error.Unauthorized(ex.Message)),
                ForbiddenException ex => Result.Failure(Error.Forbidden(ex.Message)),
                ConflictException ex => Result.Failure(Error.Conflict(ex.Message)),
                BadRequestException ex => Result.Failure(Error.BadRequest(ex.Message)),
                ExternalServiceException ex => Result.Failure(Error.ExternalService(ex.Message)),
                SuperCareException ex => Result.Failure(Error.Custom(ex.Code, ex.Message)),
                _ => Result.Failure(Error.Internal(exception.Message)),
            };
        }

        /// <summary>
        /// Converts an exception to a Result with a value
        /// </summary>
        /// <typeparam name="T">The type of the value</typeparam>
        /// <param name="exception">The exception to convert</param>
        /// <returns>A Result representing the exception</returns>
        public static Result<T> FromException<T>(Exception exception)
        {
            return exception switch
            {
                EntityNotFoundException ex => Result.Failure<T>(Error.NotFound(ex.Message)),
                ValidationException ex => Result.Failure<T>(
                    Error.Validation(ex.Message, ex.Errors)
                ),
                UnauthorizedException ex => Result.Failure<T>(Error.Unauthorized(ex.Message)),
                ForbiddenException ex => Result.Failure<T>(Error.Forbidden(ex.Message)),
                ConflictException ex => Result.Failure<T>(Error.Conflict(ex.Message)),
                BadRequestException ex => Result.Failure<T>(Error.BadRequest(ex.Message)),
                ExternalServiceException ex => Result.Failure<T>(Error.ExternalService(ex.Message)),
                SuperCareException ex => Result.Failure<T>(Error.Custom(ex.Code, ex.Message)),
                _ => Result.Failure<T>(Error.Internal(exception.Message)),
            };
        }

        /// <summary>
        /// Executes an operation and wraps any exceptions in a Result
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <returns>A Result representing the outcome of the operation</returns>
        public static async Task<Result<T>> TryAsync<T>(Func<Task<T>> operation)
        {
            try
            {
                var result = await operation();
                return Result.Success(result);
            }
            catch (Exception ex)
            {
                return FromException<T>(ex);
            }
        }

        /// <summary>
        /// Executes an operation and wraps any exceptions in a Result
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <returns>A Result representing the outcome of the operation</returns>
        public static async Task<Result> TryAsync(Func<Task> operation)
        {
            try
            {
                await operation();
                return Result.Success();
            }
            catch (Exception ex)
            {
                return FromException(ex);
            }
        }

        /// <summary>
        /// Executes an operation that returns a Result and handles any exceptions
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <returns>A Result representing the outcome of the operation</returns>
        public static async Task<Result<T>> TryResultAsync<T>(Func<Task<Result<T>>> operation)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                return FromException<T>(ex);
            }
        }

        /// <summary>
        /// Executes an operation that returns a Result and handles any exceptions
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <returns>A Result representing the outcome of the operation</returns>
        public static async Task<Result> TryResultAsync(Func<Task<Result>> operation)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                return FromException(ex);
            }
        }
    }
}
