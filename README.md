# SuperCare App

<div align="center">
  <img src="https://via.placeholder.com/200x200?text=SuperCare" alt="SuperCare Logo" width="200" height="200">
  <h3>A modern healthcare management application built with .NET 8</h3>
</div>

<div align="center">
  
  [![.NET](https://img.shields.io/badge/.NET-8.0-512BD4?style=flat-square&logo=dotnet)](https://dotnet.microsoft.com/)
  [![EF Core](https://img.shields.io/badge/EF_Core-8.0-512BD4?style=flat-square&logo=dotnet)](https://docs.microsoft.com/en-us/ef/core/)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15-336791?style=flat-square&logo=********ql&logoColor=white)](https://www.********ql.org/)
  [![Docker](https://img.shields.io/badge/Docker-Support-2496ED?style=flat-square&logo=docker&logoColor=white)](https://www.docker.com/)
  [![Swagger](https://img.shields.io/badge/Swagger-Support-85EA2D?style=flat-square&logo=swagger&logoColor=black)](https://swagger.io/)
  
</div>

## 📋 Table of Contents

- [Overview](#-overview)
- [Features](#-features)
- [Architecture](#-architecture)
- [API Response Model](#-api-response-model)
- [Getting Started](#-getting-started)
- [API Documentation](#-api-documentation)
- [Docker Support](#-docker-support)
- [Exception Handling](#-exception-handling)
- [Comprehensive Codebase Index](#-comprehensive-codebase-index)
- [Contributing](#-contributing)
- [License](#-license)

## 🔍 Overview

SuperCare App is a comprehensive healthcare management system designed to streamline patient care, appointment scheduling, and medical record management. Built with modern .NET technologies, it provides a robust and scalable solution for healthcare providers.

## ✨ Features

- **User Management**: Role-based access control with support for patients, doctors, and administrators
- **Authentication & Authorization**: Secure JWT-based authentication
- **Standardized API Responses**: Consistent response format across all endpoints
- **Health Checks**: Monitoring system health and dependencies
- **Exception Handling**: Comprehensive error handling and reporting
- **Docker Support**: Easy deployment with containerization
- **Swagger Documentation**: Interactive API documentation

## 🏗️ Architecture

The application follows Clean Architecture principles with a clear separation of concerns:

<div align="center">
  <img src="https://via.placeholder.com/800x400?text=Clean+Architecture" alt="Clean Architecture Diagram" width="800">
</div>

### Project Structure

```
SuperCareApp/
├── SuperCareApp.Domain/           # Domain entities, enums, exceptions, interfaces, etc.
├── SuperCareApp.Application/      # Application business logic and interfaces
├── SuperCareApp.Persistence/      # Data access and persistence infrastructure
└── SuperCareApp/                  # API Controllers, Filters, Middleware, etc.
    ├── Controllers/               # API endpoints
    ├── Filters/                   # Action filters
    ├── Middleware/                # Custom middleware
    ├── Extensions/                # Extension methods
    └── Shared/                    # Shared utilities
        └── Utility/               # API response models and extensions
```

### Key Components

- **Domain Layer**: Contains enterprise logic and entities
- **Application Layer**: Contains business logic and interfaces
- **Persistence Layer**: Implements data access and storage
- **API Layer**: Handles HTTP requests and responses

## 📊 API Response Model

All API responses follow a standardized format using the `ApiResponseModel<T>` pattern:

```json
{
  "apiResponseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "success",
  "statusCode": 200,
  "message": "The operation completed successfully.",
  "payload": {
    // The actual data returned by the API
  },
  "timestamp": "2023-06-15T14:30:45.123Z"
}
```

### Interactive Example

<details>
<summary>Click to see a successful response example</summary>

```json
{
  "apiResponseId": "7b2dd82d-9e1f-4e6c-8651-8c11b9c76ea8",
  "status": "success",
  "statusCode": 200,
  "message": "User retrieved successfully",
  "payload": {
    "id": "12345678-1234-1234-1234-123456789012",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["Patient"]
  },
  "timestamp": "2023-06-15T14:30:45.123Z"
}
```

</details>

<details>
<summary>Click to see an error response example</summary>

```json
{
  "apiResponseId": "9c4f1d2e-8b3a-4c7d-9e6f-5a2b1c0d3e4f",
  "status": "notFound",
  "statusCode": 404,
  "message": "User with ID '12345678-1234-1234-1234-123456789012' was not found",
  "payload": null,
  "timestamp": "2023-06-15T14:32:10.456Z"
}
```

</details>

<details>
<summary>Click to see a validation error example</summary>

```json
{
  "apiResponseId": "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d",
  "status": "badRequest",
  "statusCode": 400,
  "message": "Validation failed",
  "payload": {
    "email": ["Email is required", "Email format is invalid"],
    "password": ["Password must be at least 8 characters"]
  },
  "timestamp": "2023-06-15T14:33:22.789Z"
}
```

</details>

For more details, see the [API Response Model Documentation](./README-ApiResponse.md).

## 🚀 Getting Started

### Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [PostgreSQL](https://www.********ql.org/download/) or [Docker](https://www.docker.com/products/docker-desktop)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/super-care-app.git
   cd super-care-app
   ```

2. Set up the database:

   ```bash
   # Using Docker
   docker-compose up -d db

   # Or configure your connection string in appsettings.json
   ```

3. Run the application:

   ```bash
   dotnet run --project super-care-app/SuperCareApp.csproj
   ```

4. Open your browser and navigate to:
   ```
   https://localhost:5001/swagger
   ```

### Configuration

The application can be configured through the `appsettings.json` file:

<details>
<summary>Click to see configuration options</summary>

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=supercare;Username=********;Password=********"
  },
  "JwtSettings": {
    "Secret": "your-secret-key-here",
    "Issuer": "SuperCareApp",
    "Audience": "SuperCareAppUsers",
    "ExpiryMinutes": 60
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

</details>

## 📝 API Documentation

The API is documented using Swagger/OpenAPI. When running the application, you can access the interactive documentation at:

```
https://localhost:5001/swagger
```

### Key Endpoints

<details>
<summary>Authentication</summary>

- `POST /api/auth/login` - Authenticate a user
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/refresh-token` - Refresh an authentication token
</details>

<details>
<summary>Users</summary>

- `GET /api/users` - Get a list of users
- `GET /api/users/{id}` - Get a user by ID
- `POST /api/users` - Create a new user
- `PUT /api/users/{id}` - Update a user
- `POST /api/users/{id}/deactivate` - Deactivate a user
- `POST /api/users/{id}/activate` - Activate a user
</details>

<details>
<summary>Health Checks</summary>

- `GET /health` - Basic health check
- `GET /api/health` - Detailed health check
</details>

## 🐳 Docker Support

The application includes Docker support for easy deployment:

```bash
# Start all services
docker-compose up -d

# Start only the database
docker-compose up -d db

# Stop all services
docker-compose down
```

For more details, see the [Docker Documentation](./docker-readme.md).

## ⚠️ Exception Handling

The application uses a comprehensive exception handling system:

- **Global Exception Handler**: Catches all unhandled exceptions
- **Custom Exceptions**: Domain-specific exception types
- **Standardized Error Responses**: Consistent error format

For more details, see the [Exception Handling Documentation](./README-ExceptionHandling.md).

## 📚 Comprehensive Codebase Index

This section provides a detailed overview of the entire SuperCare App codebase structure, automatically indexed for quick reference.

### 🏗️ Domain Layer (`SuperCareApp.Domain`)

#### Core Entities

<details>
<summary><strong>Identity & User Management</strong></summary>

- **`ApplicationUser`** - Main user entity extending IdentityUser<Guid>

  - **Properties**: AuthProvider, AuthProviderId, EmailVerified, IsActive, LastLogin
  - **Audit Properties**: CreatedAt, CreatedBy, UpdatedAt, UpdatedBy, IsDeleted, DeletedAt, DeletedBy
  - **Relationships**: UserProfile (1:1), UserNotifications (1:many), OtpCodes (1:many), UserAddresses (1:many)

- **`ApplicationRole`** - Custom role entity extending IdentityRole<Guid>

  - **Audit Properties**: CreatedAt, CreatedBy, UpdatedAt, UpdatedBy, IsDeleted, DeletedAt, DeletedBy

- **`ApplicationUserRole`** - User-role relationship entity

  - **Navigation**: User, Role

- **`UserProfile`** - Extended user profile information
  - **Properties**: ApplicationUserId, FirstName, LastName, PhoneNumber, DateOfBirth, Gender, ImageName, ImagePath, Country, Preferences (JSON)
  - **Relationships**: User (1:1), Documents (1:many)
  </details>

<details>
<summary><strong>Care Provider System</strong></summary>

- **`CareProviderProfile`** - Care provider specific information

  - **Properties**: UserId, Bio, YearsExperience, HourlyRate, ProvidesOvernight, ProvidesLiveIn, Qualifications (JSON), VerificationStatus, Rating, RatingCount
  - **Relationships**: User (1:1), CareProviderCategories (many:many), Availabilities (1:many), Leaves (1:many)

- **`CareCategory`** - Service categories for care providers

  - **Properties**: Name, Description, BufferDuration, IsActive, HourlyRate, PlatformFee
  - **Relationships**: CareProviderCategories (many:many)

- **`CareProviderCategory`** - Many-to-many relationship between providers and categories
  - **Properties**: CareProviderId, CareCategoryId
  - **Relationships**: CareProviderProfile, CareCategory
  </details>

<details>
<summary><strong>Booking & Scheduling System</strong></summary>

- **`Booking`** - Core booking entity

  - **Properties**: ClientId, ProviderId, CategoryId, BookingDate, StartTime (TimeOnly), EndTime (TimeOnly), SpecialInstructions, TotalAmount, PlatformFee, ProviderAmount
  - **Relationships**: Client (ApplicationUser), Provider (CareProviderProfile), Category (CareCategory), Status (BookingStatus), Payments (1:many), Reviews (1:many)

- **`BookingStatus`** - Booking status tracking entity

  - **Properties**: BookingId, Status (BookingStatusType), CreatedBy, Notes
  - **Relationships**: Booking (1:1), CreatedByUser (ApplicationUser)

- **`Availability`** - Provider availability by day of week

  - **Properties**: ProviderId, DayOfWeek, IsAvailable
  - **Relationships**: CareProviderProfile (many:1), AvailabilitySlots (1:many)

- **`AvailabilitySlot`** - Specific time slots for availability

  - **Properties**: AvailabilityId, StartTime, EndTime
  - **Relationships**: Availability (many:1)

- **`Leave`** - Provider leave periods
  - **Properties**: ProviderId, StartDate, EndDate, Reason
  - **Relationships**: CareProviderProfile (many:1)
  - **Business Rules**: EndDate >= StartDate constraint
  </details>

<details>
<summary><strong>Payment & Financial System</strong></summary>

- **`Payment`** - Payment transactions

  - **Properties**: BookingId, Amount (decimal), PaymentMethod, TransactionId, Status (PaymentStatus), PaymentDateTime, InvoiceNumber
  - **Relationships**: Booking (many:1)

- **`Subscription`** - Provider subscription management
  - **Properties**: ProviderId, SubscriptionType, StartDate, EndDate, Amount (decimal), Status (PaymentStatus), PaymentMethod
  - **Relationships**: Provider (ApplicationUser, many:1)
  </details>

<details>
<summary><strong>Communication System</strong></summary>

- **`Conversation`** - Chat conversations

  - **Properties**: Inherits from BaseEntity
  - **Relationships**: Messages (1:many)

- **`Message`** - Individual messages

  - **Properties**: ConversationId, SenderId, Content, IsRead, ReadAt
  - **Relationships**: Conversation (many:1), Sender (ApplicationUser), Attachments (1:many)

- **`Attachment`** - Message attachments

  - **Properties**: MessageId, FileName, FileUrl, FileSize, ContentType
  - **Relationships**: Message (many:1)

- **`Notification`** - System notifications

  - **Properties**: Title, Content, NotificationType, ActionUrl
  - **Relationships**: UserNotifications (1:many)

- **`UserNotification`** - User-specific notification tracking
  - **Properties**: ApplicationUserId, NotificationId, IsRead, ReadAt
  - **Relationships**: ApplicationUser (many:1), Notification (many:1)
  </details>

<details>
<summary><strong>Document & Verification System</strong></summary>

- **`Document`** - Document management

  - **Properties**: UserId, DocumentType, DocumentUrl, VerificationStatus, UploadedAt, VerifiedAt, VerifiedBy, RejectionReason, Country, CertificationType, OtherCertificationType, CertificationNumber, ExpiryDate
  - **Relationships**: UserProfile (many:1)
  - **Business Logic**: IsExpired computed property (ExpiryDate < DateTime.UtcNow)

- **`Approval`** - General approval workflow
  - **Properties**: UserId, ApprovalType, IsApproved, RejectionReason
  - **Relationships**: User (ApplicationUser, many:1)
  </details>

<details>
<summary><strong>Location & Address System</strong></summary>

- **`Address`** - Address information

  - **Properties**: Street, City, State, PostalCode, Country
  - **Relationships**: UserAddresses (1:many)

- **`UserAddress`** - User-address relationships
  - **Properties**: ApplicationUserId, AddressId, AddressType, IsDefault
  - **Relationships**: ApplicationUser (many:1), Address (many:1)
  </details>

<details>
<summary><strong>Review & Rating System</strong></summary>

- **`Review`** - User reviews and ratings
  - **Properties**: BookingId, ReviewerId, RevieweeId, Rating (decimal), Comment
  - **Relationships**: Booking (many:1), Reviewer (ApplicationUser), Reviewee (ApplicationUser)
  </details>

<details>
<summary><strong>Security & Authentication</strong></summary>

- **`OtpCode`** - OTP verification codes

  - **Properties**: ApplicationUserId, Code, ExpiresAt, IsUsed, Purpose
  - **Relationships**: ApplicationUser (many:1)

- **`TrackingSession`** - User session tracking

  - **Properties**: UserId, SessionId, IpAddress, UserAgent, LoginTime, LogoutTime
  - **Relationships**: User (ApplicationUser, many:1)

- **`AuditLog`** - System audit logging
  - **Properties**: EntityName, EntityId, Action, Changes, UserId, Timestamp
  - **Relationships**: User (ApplicationUser, many:1)
  </details>

#### Enums & Value Objects

<details>
<summary><strong>Core Enums</strong></summary>

- **`UserType`** - Admin, Client, CareProvider
- **`BookingStatusType`** - Requested, Accepted, Rejected, Cancelled, Completed, InProgress, Expired, Confirmed, Modified, NoShow
- **`VerificationStatus`** - Pending, Verified, Rejected, Suspended
- **`PaymentStatus`** - Pending, Completed, Failed, Refunded
- **`SubscriptionType`** - Free, Basic, Premium, Professional
- **`NotificationType`** - BookingRequest, BookingConfirmation, BookingCancellation, PaymentReceived, MessageReceived, DocumentVerified, DocumentRejected, SystemNotification
- **`ApprovalType`** - CareProviderVerification, DocumentVerification, ProfileUpdate, AccountActivation, Other
- **`Country`** - UnitedKingdom, UnitedStates, Australia, Canada, NewZealand, Ireland, Other (with helper methods)
- **`CertificationType`** - Professional certifications by country (58+ certification types)
- **`AvailabilityType`** - Regular, OneTime, Vacation
</details>

### 🎯 Application Layer (`SuperCareApp.Application`)

#### CQRS Pattern Implementation

<details>
<summary><strong>Command/Query Interfaces</strong></summary>

- **`ICommand`** - Base command interface (no return value)
- **`ICommand<TResponse>`** - Generic command interface with return value
- **`ICommandHandler<TCommand, TResponse>`** - Command handler interface
- **`IQuery<TResponse>`** - Query interface with return value
- **`IQueryHandler<TQuery, TResponse>`** - Query handler interface
- **`IMediator`** - Central mediator for CQRS pattern
</details>

#### Core Interfaces & Services

<details>
<summary><strong>Business Logic Interfaces</strong></summary>

- **`ICurrentUserService`** - Current user context and claims
- **`IRequestValidator`** - Request validation pipeline
- **`IUnitOfWork`** - Transaction management and repository coordination
- **`IRepository<T>`** - Generic repository pattern with Result<T> return types
- **`IMailSender`** - Email notification services
- **`ICareProviderService`** - Care provider business logic and profile management
- **`IApprovalService`** - Approval workflow management
- **`IAuthService`** - Authentication and authorization services
- **`IUserService`** - User management operations
- **`IBookingService`** - Booking lifecycle management
- **`IAvailabilityService`** - Provider availability management
- **`IDocumentService`** - Document upload and verification
</details>

#### Models & DTOs

<details>
<summary><strong>Request/Response Models</strong></summary>

- **Identity Models**: RegisterRequest, LoginRequest, AuthRequest, AuthResponse, UserResponse, OtpRequest, OtpResponse
- **Booking Models**: BookingRequest, BookingResponse, AvailabilityRequest, AvailabilityResponse
- **Document Models**: DocumentUploadRequest, DocumentResponse, DocumentVerificationRequest
- **Address Models**: AddressRequest, AddressResponse
- **Category Models**: CareCategoryRequest, CareCategoryResponse
- **Admin Models**: AdminDashboardResponse, UserManagementRequest, ApprovalRequest
- **Provider Models**: ProviderProfileRequest, ProviderProfileResponse, UpdateCareProviderRequest
</details>

### 🗄️ Persistence Layer (`SuperCareApp.Persistence`)

#### Database Context & Configuration

<details>
<summary><strong>Entity Framework Setup</strong></summary>

- **`ApplicationDbContext`** - Main EF Core context

  - **DbSets**: All 17 entity DbSets (UserProfiles, Documents, Bookings, etc.)
  - **Identity Integration**: ASP.NET Core Identity with custom ApplicationUser
  - **Audit Trail**: Automatic audit logging with interceptors
  - **Soft Delete**: Global query filters for soft-deleted entities

- **Entity Configurations**: Fluent API configurations for all entities
  - `CareProviderProfileConfiguration` - JSONB qualifications, decimal precision
  - `PaymentConfiguration` - Decimal precision, enum conversions
  - `LeaveConfiguration` - Date constraints (EndDate >= StartDate)
  - `NotificationConfiguration` - String length constraints
  - `SubscriptionConfiguration` - Enum conversions, decimal precision
  - And 12+ other entity configurations
  </details>

#### Repository Pattern

<details>
<summary><strong>Repository Implementation</strong></summary>

- **`Repository<TEntity, TContext>`** - Generic repository with Result<T> pattern

  - **Methods**: GetAllAsync, GetByIdAsync, AddAsync, UpdateAsync, DeleteAsync, ExistsAsync, GetPagedAsync
  - **Features**: Soft delete support, error handling, async operations
  - **Return Types**: All methods return Result<T> for consistent error handling

- **Specialized Repositories**: Entity-specific repositories with custom business logic
  - Custom query methods for complex business requirements
  - Optimized queries with Include statements for related data
  </details>

#### Services & Command/Query Handlers

<details>
<summary><strong>CQRS Implementation</strong></summary>

- **Identity Services**:

  - `RegisterUserCommand` - User registration with OTP generation and care provider profile creation
  - `LoginCommand` - JWT authentication with role-based claims
  - `ValidateOtpCommand` - OTP verification for account activation
  - `GenerateOtpCommand` - OTP generation for various purposes
  - `RefreshTokenCommand` - JWT token refresh

- **Booking Services**:

  - `CreateAvailabilityCommand` - Provider weekly availability setup
  - `GetAllAvailabilitiesQuery` - Retrieve provider availability with caching
  - `CreateBookingCommand` - Booking creation with validation
  - `UpdateBookingCommand` - Booking modification
  - `CancelBookingCommand` - Booking cancellation

- **Document Services**:

  - `UploadDocumentCommand` - Document upload with country-specific validation
  - `GetDocumentsByUserIdQuery` - Retrieve user documents with caching
  - `VerifyDocumentCommand` - Admin document verification

- **Address Services**:

  - `CreateAddressCommand` - Address creation with validation
  - `UpdateAddressCommand` - Address modification
  - `GetUserAddressesQuery` - Retrieve user addresses

- **Admin Services**:

  - `ApproveProviderCommand` - Care provider approval workflow
  - `GetAdminDashboardQuery` - Admin dashboard statistics
  - `GetUsersQuery` - User management with pagination

- **Provider Services**:
  - `CreateCareProviderProfileCommand` - Provider profile creation
  - `UpdateCareProviderProfileCommand` - Profile updates
  - `AssignCategoryCommand` - Category assignment to providers
  </details>

#### Unit of Work & Transaction Management

<details>
<summary><strong>Transaction Handling & Behaviors</strong></summary>

- **`UnitOfWork`** - Centralized transaction management with repository coordination
- **`ValidationBehaviour`** - FluentValidation pipeline behavior for request validation
- **`CachingBehaviour`** - Response caching behavior with configurable cache duration
- **Audit Interceptors** - Automatic audit trail creation for entity changes
- **Soft Delete Interceptors** - Automatic soft delete handling
</details>

### 🌐 API Layer (`super-care-app`)

#### Controllers & Endpoints

<details>
<summary><strong>API Controllers</strong></summary>

- **`BaseController`** - Abstract base controller with standardized response methods

  - **Features**: ApiResponseModel integration, Result<T> pattern support, error handling
  - **Attributes**: API versioning, content type specifications, response type documentation

- **`AuthController`** - Authentication and authorization endpoints

  - POST `/api/v1/auth/login` - User authentication with JWT
  - POST `/api/v1/auth/register` - User registration with OTP
  - POST `/api/v1/auth/validate-otp` - OTP verification
  - POST `/api/v1/auth/refresh-token` - JWT token refresh
  - POST `/api/v1/auth/generate-otp` - OTP generation
  - POST `/api/v1/auth/verify-otp` - OTP verification
  - POST `/api/v1/auth/change-password` - Password change

- **`AccountController`** - User account management

  - GET `/api/v1/account/profile` - Get current user profile
  - PUT `/api/v1/account/profile` - Update user profile
  - POST `/api/v1/account/addresses` - Create user address
  - GET `/api/v1/account/addresses` - Get user addresses
  - PUT `/api/v1/account/addresses/{id}` - Update address
  - DELETE `/api/v1/account/addresses/{id}` - Delete address

- **`BookingsController`** - Booking lifecycle management

  - POST `/api/v1/bookings` - Create new booking
  - GET `/api/v1/bookings` - Get user bookings with pagination
  - GET `/api/v1/bookings/{id}` - Get booking details
  - PUT `/api/v1/bookings/{id}` - Update booking
  - DELETE `/api/v1/bookings/{id}` - Cancel booking
  - POST `/api/v1/bookings/availability` - Create provider availability
  - GET `/api/v1/bookings/availabilities` - Get provider availabilities

- **`AdminController`** - Administrative functions (Admin role required)

  - GET `/api/v1/admin/users` - User management with pagination
  - POST `/api/v1/admin/approve-provider` - Approve care provider
  - GET `/api/v1/admin/dashboard` - Admin dashboard statistics
  - GET `/api/v1/admin/categories` - Manage care categories
  - POST `/api/v1/admin/categories` - Create care category

- **`DocumentsController`** - Document management and verification
  - POST `/api/v1/documents/upload` - Upload document with country-specific validation
  - GET `/api/v1/documents` - Get user documents
  - GET `/api/v1/documents/{id}` - Get document details
  - PUT `/api/v1/documents/{id}/verify` - Verify document (Admin only)
  - DELETE `/api/v1/documents/{id}` - Delete document
  </details>

#### Middleware & Cross-Cutting Concerns

<details>
<summary><strong>Middleware Pipeline</strong></summary>

- **`GlobalExceptionHandlerMiddleware`** - Centralized exception handling

  - **Features**: Converts all exceptions to standardized ApiResponseModel format
  - **Exception Types**: EntityNotFoundException, ValidationException, BadRequestException, UnauthorizedException
  - **Environment Handling**: Detailed error messages in development, generic messages in production
  - **Logging**: Comprehensive error logging with structured data

- **Action Filters**:

  - `ValidationFilter` - FluentValidation integration for request validation
  - `ResultActionFilter` - Result<T> pattern to HTTP response conversion
  - `ApiResponseFilter` - Automatic response wrapping in ApiResponseModel
  - `ValidationExceptionHandler` - Validation error formatting

- **Route Conventions**:
  - `LowercaseControllerTransformer` - Lowercase URL routing
  - API versioning support with route templates
  </details>

#### API Response Model

<details>
<summary><strong>Response Standardization</strong></summary>

- **`ApiResponseModel<T>`** - Consistent response format across all endpoints

  - **Properties**: ApiResponseId (Guid), Success (bool), StatusCode (int), Message (string), Payload (T), Timestamp (DateTime)
  - **Status Types**: Success, BadRequest, NotFound, Unauthorized, Forbidden, InternalServerError, Warning, Info
  - **Pagination Support**: `PaginatedApiResponseModel<T>` with metadata (currentPage, totalPages, totalCount, pageSize)

- **Response Extensions**:

  - `ControllerExtensions` - Helper methods for creating standardized responses
  - `ApiResponseExtension` - Minimal API response helpers
  - Automatic response wrapping through filters

- **Validation Error Handling**:
  - Structured validation error responses with field-specific error messages
  - FluentValidation integration with detailed error information
  </details>

### 🔧 Configuration & Infrastructure

#### Dependency Injection

<details>
<summary><strong>Service Registration</strong></summary>

- **`DependencyInjection.cs`** (Persistence Layer) - Core service container configuration

  - **Database**: ApplicationDbContext with PostgreSQL, connection string configuration
  - **Identity**: ASP.NET Core Identity with custom ApplicationUser and ApplicationRole
  - **Repositories**: Generic repository pattern with specialized repositories
  - **Services**: Business logic services (IUserService, IBookingService, etc.)
  - **Mediator**: Custom mediator implementation for CQRS pattern
  - **Validation**: FluentValidation with pipeline behaviors
  - **Caching**: Response caching with configurable duration

- **`ServiceExtensions.cs`** (API Layer) - API-specific service configuration
  - **Controllers**: API controllers with filters and JSON options
  - **Authentication**: JWT bearer token configuration
  - **Authorization**: Role-based authorization policies
  - **Swagger**: API documentation with examples and security definitions
  - **CORS**: Cross-origin resource sharing configuration
  - **Health Checks**: Database and service health monitoring
  </details>

#### Authentication & Authorization

<details>
<summary><strong>Security Implementation</strong></summary>

- **JWT Authentication** - Token-based authentication with refresh token support

  - **Configuration**: Secret key, issuer, audience, expiry settings
  - **Claims**: User ID, email, roles, and custom claims
  - **Refresh Tokens**: Secure token refresh mechanism

- **Role-based Authorization** - Three-tier role system

  - **Admin**: Full system access, user management, provider approval
  - **Client**: Booking creation, profile management, document upload
  - **CareProvider**: Availability management, booking acceptance, profile updates

- **Identity Framework Integration** - ASP.NET Core Identity with custom entities

  - **Custom User**: ApplicationUser with additional properties
  - **Custom Roles**: ApplicationRole with audit properties
  - **Password Policies**: Configurable password requirements

- **OTP Verification** - Multi-purpose OTP system
  - **Delivery Methods**: Email and SMS support
  - **Use Cases**: Account verification, password reset, two-factor authentication
  - **Expiry Management**: Configurable OTP expiration times
  </details>

#### Health Checks & Monitoring

<details>
<summary><strong>System Monitoring</strong></summary>

- **Health Check Endpoints**:

  - `/health` - Basic application health check
  - `/api/health` - Detailed health check with dependencies

- **Monitored Components**:

  - **Database Connectivity**: PostgreSQL connection and query execution
  - **Service Dependencies**: External service availability
  - **Memory Usage**: Application memory consumption
  - **Disk Space**: Available storage space

- **Logging & Diagnostics**:
  - **Structured Logging**: JSON-formatted logs with correlation IDs
  - **Error Tracking**: Comprehensive exception logging
  - **Performance Metrics**: Request duration and throughput monitoring
  </details>

### 🐳 DevOps & Deployment

#### Docker Support

<details>
<summary><strong>Containerization</strong></summary>

- **`Dockerfile`** - Multi-stage .NET application containerization

  - **Build Stage**: .NET 8 SDK for compilation and publishing
  - **Runtime Stage**: ASP.NET Core runtime for optimized production image
  - **Security**: Non-root user execution, minimal attack surface

- **`docker-compose.yml`** - Production multi-service orchestration

  - **Application Service**: SuperCare API with health checks
  - **Database Service**: PostgreSQL 15 with persistent volumes
  - **Network Configuration**: Internal service communication
  - **Environment Variables**: Production configuration management

- **`docker-compose.override.yml`** - Development environment overrides

  - **Port Mapping**: Local development port exposure
  - **Volume Mounts**: Hot reload for development
  - **Debug Configuration**: Development-specific settings

- **Container Features**:
  - **Health Checks**: Application and database health monitoring
  - **Restart Policies**: Automatic container recovery
  - **Resource Limits**: Memory and CPU constraints
  - **Logging**: Structured container logging
  </details>

#### Database Migrations

<details>
<summary><strong>Schema Management</strong></summary>

- **Entity Framework Migrations** - Code-first database schema management

  - **Initial Migration**: `20250603203310_InitialModel` - Complete domain model setup
  - **Migration Commands**: Add-Migration, Update-Database, Script-Migration
  - **Environment Support**: Development, staging, and production migrations

- **Database Schema**:

  - **17 Core Tables**: All domain entities with proper relationships
  - **Identity Tables**: ASP.NET Core Identity integration
  - **Indexes**: Performance-optimized database indexes
  - **Constraints**: Foreign keys, check constraints, unique constraints

- **Data Seeding**:
  - **Initial Roles**: Admin, Client, CareProvider roles
  - **Care Categories**: Default service categories
  - **Configuration Data**: System configuration and lookup data
  - **Test Data**: Development environment sample data
  </details>

### 📝 Documentation & Examples

#### API Documentation

<details>
<summary><strong>Swagger Integration</strong></summary>

- **Interactive API Documentation** - Swagger UI with comprehensive endpoint documentation

  - **Endpoint Documentation**: All API endpoints with detailed descriptions
  - **Request/Response Examples**: Real-world examples for every endpoint
  - **Model Schemas**: Complete data model documentation with validation rules
  - **Authentication Integration**: JWT bearer token authentication in Swagger UI

- **Advanced Swagger Features**:

  - **`SwaggerResponseExamplesFilter`** - Automatic response example generation
  - **Custom Examples**: Controller-specific response examples
  - **Error Response Examples**: Standardized error response documentation
  - **Security Definitions**: JWT authentication configuration

- **Documentation Files**:
  - `README-Swagger.md` - Basic Swagger setup and usage
  - `README-Swagger-Advanced.md` - Advanced Swagger configuration
  - `README-Swagger-Examples.md` - Custom example implementation
  </details>

#### Code Examples

<details>
<summary><strong>Implementation Examples</strong></summary>

- **API Response Examples**:

  - `ApiResponseExamples` - Standardized API response examples
  - `CareCategoryExamples` - Care category API examples
  - `DocumentExamples` - Document upload and verification examples
  - `AuthExamples` - Authentication flow examples

- **Business Logic Examples**:

  - **Booking Workflow**: Complete booking lifecycle examples
  - **Authentication Flow**: Registration, login, OTP verification examples
  - **Document Verification**: Country-specific document validation examples
  - **Provider Management**: Care provider profile and availability examples

- **Error Handling Examples**:
  - **Validation Errors**: Field-specific validation error responses
  - **Business Logic Errors**: Domain-specific error scenarios
  - **System Errors**: Infrastructure and system error handling
  </details>

### 🧪 Testing & Quality

#### Validation & Business Rules

<details>
<summary><strong>Data Validation</strong></summary>

- **FluentValidation Integration** - Comprehensive request validation pipeline

  - **Request Validators**: All API requests validated with FluentValidation
  - **Domain Validators**: Entity-level validation in `SuperCareApp.Domain.Validation`
  - **Custom Rules**: Business-specific validation rules (e.g., phone number formatting)
  - **Conditional Validation**: Context-aware validation based on user type and country

- **Database Constraints** - Entity-level data integrity

  - **Foreign Key Constraints**: Referential integrity across all relationships
  - **Check Constraints**: Business rule enforcement at database level
  - **Unique Constraints**: Data uniqueness validation
  - **Date Constraints**: Leave EndDate >= StartDate validation

- **Business Rule Validation**:
  - **Booking Conflicts**: Prevent overlapping bookings and leave periods
  - **Provider Availability**: Validate booking times against provider availability
  - **Document Requirements**: Country-specific document validation rules
  - **Role-based Access**: Operation validation based on user roles
  </details>

#### Error Handling

<details>
<summary><strong>Exception Management</strong></summary>

- **Result Pattern** - Functional error handling throughout the application

  - **Result<T>**: Success/failure pattern with typed error information
  - **Error Types**: Validation, NotFound, Unauthorized, Internal, Business errors
  - **Error Propagation**: Consistent error handling from domain to API layer

- **Custom Exception Types**:

  - `EntityNotFoundException` - Resource not found errors
  - `ValidationException` - Data validation failures
  - `BadRequestException` - Invalid request errors
  - `UnauthorizedException` - Authentication/authorization failures

- **Global Exception Handling**:
  - **Middleware**: `GlobalExceptionHandlerMiddleware` for centralized error processing
  - **Logging**: Structured error logging with correlation IDs
  - **Response Formatting**: Consistent error response format across all endpoints
  - **Environment Handling**: Detailed errors in development, sanitized in production
  </details>

### 🔍 Key Business Rules & Constraints

<details>
<summary><strong>Critical Business Logic</strong></summary>

- **Leave Management**:

  - **No Overlapping Periods**: Users cannot create overlapping leave periods
  - **Future Dates Only**: Leave periods must be in the future
  - **Booking Conflicts**: Existing bookings prevent leave creation

- **Booking Validation**:

  - **Provider Availability**: Bookings must align with provider availability slots
  - **Leave Period Conflicts**: Bookings cannot overlap with provider leave periods
  - **Time Validation**: Start time must be before end time, future dates only
  - **Category Matching**: Provider must be assigned to the requested care category

- **Document Verification**:

  - **Country-Specific Requirements**: Different document types per country
  - **Certification Validation**: Professional certifications must match country standards
  - **Expiry Tracking**: Automatic expiry detection for time-sensitive documents
  - **Multi-step Approval**: Admin verification required for care provider documents

- **Payment Processing**:

  - **Platform Fee Calculation**: Automatic platform fee calculation based on booking amount
  - **Provider Amount**: Net amount calculation after platform fees
  - **Payment Status Tracking**: Complete payment lifecycle management
  - **Invoice Generation**: Automatic invoice number generation

- **Rating & Review System**:

  - **Bidirectional Reviews**: Both clients and providers can review each other
  - **Booking-based Reviews**: Reviews tied to completed bookings only
  - **Rating Aggregation**: Automatic provider rating calculation
  - **Review Moderation**: Admin oversight of review content

- **Subscription Management**:

  - **Tier-based Access**: Different features based on subscription level
  - **Automatic Billing**: Recurring subscription payment processing
  - **Grace Periods**: Temporary access during payment processing
  - **Upgrade/Downgrade**: Seamless subscription tier changes

- **Security & Privacy**:
  - **Data Encryption**: Sensitive data encryption at rest and in transit
  - **Audit Logging**: Complete audit trail for all data modifications
  - **Soft Delete**: Data retention with soft delete for compliance
  - **Role-based Access Control**: Granular permissions based on user roles
  </details>

---

_This comprehensive codebase index reflects the current state of the SuperCare App as of December 2024. The application demonstrates modern .NET development practices with Clean Architecture, CQRS pattern, comprehensive validation, and robust error handling._
