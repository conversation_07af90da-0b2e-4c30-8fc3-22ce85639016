using System;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Attribute to specify a response example for a controller action
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class SwaggerResponseExampleAttribute : Attribute
    {
        /// <summary>
        /// The HTTP status code
        /// </summary>
        public int StatusCode { get; }

        /// <summary>
        /// The type that contains the example methods
        /// </summary>
        public Type ExamplesType { get; }

        /// <summary>
        /// Creates a new instance of the SwaggerResponseExampleAttribute
        /// </summary>
        /// <param name="statusCode">The HTTP status code</param>
        /// <param name="examplesType">The type that contains the example methods</param>
        public SwaggerResponseExampleAttribute(int statusCode, Type examplesType)
        {
            StatusCode = statusCode;
            ExamplesType = examplesType;
        }
    }
}
