using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace SuperCareApp.Persistence.Extensions
{
    /// <summary>
    /// Database seeding utilities for staging environment
    /// </summary>
    public static class DatabaseSeeder
    {
        /// <summary>
        /// Example seeding method - customize this based on your domain entities
        /// </summary>
        /// <param name="context">The database context</param>
        /// <param name="services">Service provider for dependency injection</param>
        /// <returns>Task</returns>
        public static async Task SeedStagingDataAsync<TContext>(
            TContext context,
            IServiceProvider services
        )
            where TContext : DbContext
        {
            var logger = services.GetRequiredService<ILogger<TContext>>();

            try
            {
                logger.LogInformation("Starting data seeding for staging environment");

                // Example: Seed users (customize based on your entities)
                // if (!context.Set<User>().Any())
                // {
                //     var users = new[]
                //     {
                //         new User { Name = "Admin User", Email = "<EMAIL>" },
                //         new User { Name = "Test User", Email = "<EMAIL>" }
                //     };
                //
                //     context.Set<User>().AddRange(users);
                //     await context.SaveChangesAsync();
                //     logger.LogInformation("Seeded {Count} users", users.Length);
                // }

                // Example: Seed other entities
                // if (!context.Set<Category>().Any())
                // {
                //     var categories = new[]
                //     {
                //         new Category { Name = "Default Category" }
                //     };
                //
                //     context.Set<Category>().AddRange(categories);
                //     await context.SaveChangesAsync();
                //     logger.LogInformation("Seeded {Count} categories", categories.Length);
                // }

                await Task.Yield();

                logger.LogInformation("Data seeding completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred during data seeding");
                throw;
            }
        }

        /// <summary>
        /// Checks if the database has any data (useful to determine if seeding is needed)
        /// </summary>
        /// <param name="context">The database context</param>
        /// <returns>True if database appears to be empty</returns>
        public static async Task<bool> IsDatabaseEmptyAsync<TContext>(TContext context)
            where TContext : DbContext
        {
            try
            {
                // Check if any of your main entities exist
                // Customize this based on your domain entities

                // Example checks (uncomment and modify based on your entities):
                // var hasUsers = await context.Set<User>().AnyAsync();
                // var hasCategories = await context.Set<Category>().AnyAsync();
                //
                // return !hasUsers && !hasCategories;

                // For now, return true to always seed (remove this when you implement the checks above)
                await Task.Yield();
                return true;
            }
            catch
            {
                // If we can't check, assume it's empty
                return true;
            }
        }
    }
}
