﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class Review : BaseEntity
    {
        public Guid BookingId { get; set; }
        public Guid ReviewerId { get; set; }
        public Guid RevieweeId { get; set; }
        public decimal Rating { get; set; }
        public string? Comment { get; set; }

        // Navigation properties
        public Booking Booking { get; set; } = null!;
        public ApplicationUser Reviewer { get; set; } = null!;
        public ApplicationUser Reviewee { get; set; } = null!;
    }
}
