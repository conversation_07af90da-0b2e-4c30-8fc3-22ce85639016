namespace SuperCareApp.Domain.Entities.ValueObjects;

public class TimeRange
{
    public TimeOnly Start { get; }
    public TimeOnly End { get; }

    public TimeRange(TimeOnly start, TimeOnly end)
    {
        if (end <= start)
            throw new ArgumentException("Invalid time range.");
        Start = start;
        End = end;
    }

    public static List<TimeRange> SubtractMultiple(
        List<TimeRange> baseRanges,
        List<TimeRange> subtracting
    )
    {
        var result = new List<TimeRange>();
        foreach (var baseRange in baseRanges)
        {
            var reduced = subtracting.Aggregate(
                new List<TimeRange> { baseRange },
                (acc, s) => acc.SelectMany(b => b.Subtract(s)).ToList()
            );
            result.AddRange(reduced);
        }
        return result;
    }

    public IEnumerable<TimeRange> Subtract(TimeRange other)
    {
        if (other.End <= Start || other.Start >= End)
            yield return this;
        else
        {
            if (other.Start > Start)
                yield return new TimeRange(Start, Min(End, other.Start));
            if (other.End < End)
                yield return new TimeRange(Max(Start, other.End), End);
        }
    }

    public IEnumerable<TimeRange> SplitIntoSlots(int minutes)
    {
        var slots = new List<TimeRange>();
        var current = Start;
        while (current.AddMinutes(minutes) <= End)
        {
            var next = current.AddMinutes(minutes);
            slots.Add(new TimeRange(current, next));
            current = next;
        }
        return slots;
    }

    private static TimeOnly Min(TimeOnly a, TimeOnly b) => a < b ? a : b;

    private static TimeOnly Max(TimeOnly a, TimeOnly b) => a > b ? a : b;
}
