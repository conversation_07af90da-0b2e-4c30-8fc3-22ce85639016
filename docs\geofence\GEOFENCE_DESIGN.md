# Design Document

## Overview

The geo-fencing enhancement for the SuperCare tracking session system will add intelligent location-based validation and monitoring capabilities to the existing time tracking infrastructure. The design follows the established Clean Architecture pattern with CQRS implementation, integrating seamlessly with the current domain model while adding new geo-spatial capabilities.

The solution will extend the existing `TrackingSession` entity and services to include geo-fence validation, location monitoring, and compliance reporting. It will leverage the existing address infrastructure and add new geo-spatial services for location calculations and validation.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
│  TrackingSessionsController + GeoFencingController          │
├─────────────────────────────────────────────────────────────┤
│              Application Layer (DTOs/Interfaces)            │
│  IGeoFencingService, ILocationValidationService            │
├─────────────────────────────────────────────────────────────┤
│              Persistence Layer (Services)                   │
│  GeoFencingService, LocationValidationService              │
├─────────────────────────────────────────────────────────────┤
│                   Domain Layer (Entities)                   │
│  GeoFence, LocationValidation, Enhanced TrackingSession    │
└─────────────────────────────────────────────────────────────┘
```

### Integration Points

1. **Existing TrackingSession System**: Extends current functionality without breaking changes
2. **Address System**: Leverages existing Address entity with lat/lng coordinates
3. **Booking System**: Integrates with BookingWindow to determine service locations
4. **Notification System**: Uses existing infrastructure for alerts and compliance notifications
5. **Admin Dashboard**: Extends current admin capabilities with geo-fencing metrics

## Components and Interfaces

### Domain Layer Enhancements

#### New Value Objects

**GeoFence Value Object**
```csharp
public record GeoFence
{
    public decimal CenterLatitude { get; init; }
    public decimal CenterLongitude { get; init; }
    public int RadiusMeters { get; init; }
    public GeoFenceType Type { get; init; } = GeoFenceType.Primary;
    public string? Description { get; init; }

    public GeoFence(decimal centerLatitude, decimal centerLongitude, int radiusMeters, GeoFenceType type = GeoFenceType.Primary, string? description = null)
    {
        if (radiusMeters < 50 || radiusMeters > 1000)
            throw new ArgumentException("Radius must be between 50 and 1000 meters", nameof(radiusMeters));
        
        if (Math.Abs(centerLatitude) > 90)
            throw new ArgumentException("Latitude must be between -90 and 90 degrees", nameof(centerLatitude));
        
        if (Math.Abs(centerLongitude) > 180)
            throw new ArgumentException("Longitude must be between -180 and 180 degrees", nameof(centerLongitude));

        CenterLatitude = centerLatitude;
        CenterLongitude = centerLongitude;
        RadiusMeters = radiusMeters;
        Type = type;
        Description = description;
    }

    public bool IsWithinBounds(GeoLocation location)
    {
        var distance = CalculateDistance(CenterLatitude, CenterLongitude, location.Latitude, location.Longitude);
        return distance <= RadiusMeters;
    }

    public double DistanceFrom(GeoLocation location)
    {
        return CalculateDistance(CenterLatitude, CenterLongitude, location.Latitude, location.Longitude);
    }

    private static double CalculateDistance(decimal lat1, decimal lon1, decimal lat2, decimal lon2)
    {
        const double earthRadius = 6371000; // Earth radius in meters
        var dLat = ToRadians((double)(lat2 - lat1));
        var dLon = ToRadians((double)(lon2 - lon1));
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians((double)lat1)) * Math.Cos(ToRadians((double)lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return earthRadius * c;
    }

    private static double ToRadians(double degrees) => degrees * Math.PI / 180;
}
```

**GeoLocation Value Object**
```csharp
public record GeoLocation
{
    public decimal Latitude { get; init; }
    public decimal Longitude { get; init; }
    public decimal AccuracyMeters { get; init; }
    public DateTime Timestamp { get; init; }
    public string? Source { get; init; }

    public GeoLocation(decimal latitude, decimal longitude, decimal accuracyMeters, DateTime? timestamp = null, string? source = null)
    {
        if (Math.Abs(latitude) > 90)
            throw new ArgumentException("Latitude must be between -90 and 90 degrees", nameof(latitude));
        
        if (Math.Abs(longitude) > 180)
            throw new ArgumentException("Longitude must be between -180 and 180 degrees", nameof(longitude));
        
        if (accuracyMeters < 0)
            throw new ArgumentException("Accuracy cannot be negative", nameof(accuracyMeters));

        Latitude = latitude;
        Longitude = longitude;
        AccuracyMeters = accuracyMeters;
        Timestamp = timestamp ?? DateTime.UtcNow;
        Source = source;
    }

    public bool HasAcceptableAccuracy(decimal maxAccuracyMeters = 50m) => AccuracyMeters <= maxAccuracyMeters;
    
    public double DistanceTo(GeoLocation other)
    {
        const double earthRadius = 6371000; // Earth radius in meters
        var dLat = ToRadians((double)(other.Latitude - Latitude));
        var dLon = ToRadians((double)(other.Longitude - Longitude));
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians((double)Latitude)) * Math.Cos(ToRadians((double)other.Latitude)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return earthRadius * c;
    }

    private static double ToRadians(double degrees) => degrees * Math.PI / 180;
}
```

**LocationValidationResult Value Object**
```csharp
public record LocationValidationResult
{
    public LocationValidationStatus Status { get; init; }
    public LocationValidationType Type { get; init; }
    public GeoLocation ValidatedLocation { get; init; }
    public GeoFence? MatchedGeoFence { get; init; }
    public double? DistanceFromCenter { get; init; }
    public DateTime ValidationTime { get; init; }
    public string? ValidationMessage { get; init; }
    public string? OverrideReason { get; init; }
    public Guid? OverrideBy { get; init; }
    public IReadOnlyList<string> Warnings { get; init; }

    public LocationValidationResult(
        LocationValidationStatus status,
        LocationValidationType type,
        GeoLocation validatedLocation,
        GeoFence? matchedGeoFence = null,
        double? distanceFromCenter = null,
        string? validationMessage = null,
        string? overrideReason = null,
        Guid? overrideBy = null,
        IEnumerable<string>? warnings = null)
    {
        Status = status;
        Type = type;
        ValidatedLocation = validatedLocation;
        MatchedGeoFence = matchedGeoFence;
        DistanceFromCenter = distanceFromCenter;
        ValidationTime = DateTime.UtcNow;
        ValidationMessage = validationMessage;
        OverrideReason = overrideReason;
        OverrideBy = overrideBy;
        Warnings = warnings?.ToList() ?? new List<string>();
    }

    public bool IsValid => Status == LocationValidationStatus.Valid;
    public bool RequiresOverride => Status == LocationValidationStatus.Invalid && string.IsNullOrEmpty(OverrideReason);
    public bool HasWarnings => Warnings.Any();
}
```

#### Enhanced Entities

**TrackingSession Enhancements**
```csharp
public class TrackingSession : BaseEntity
{
    // Existing properties...
    
    // New geo-fencing properties using value objects
    public bool LocationValidationRequired { get; set; } = true;
    public LocationValidationStatus LocationValidationStatus { get; set; } = LocationValidationStatus.Pending;
    public int GeoFenceViolationCount { get; set; } = 0;
    public TimeSpan TotalTimeOutsideGeoFence { get; set; } = TimeSpan.Zero;
    public GeoLocation? LastKnownLocation { get; set; }
    
    // Serialized JSON columns for complex value objects
    public string? GeoFenceConfigurationJson { get; set; } // Serialized GeoFence value object
    public string? LocationValidationHistoryJson { get; set; } // Serialized List<LocationValidationResult>
    
    // Computed properties
    public bool IsLocationCompliant => LocationValidationStatus == LocationValidationStatus.Valid;
    public bool HasLocationViolations => GeoFenceViolationCount > 0;
    public decimal LocationCompliancePercentage => CalculateLocationCompliancePercentage();
    
    // Helper methods for working with value objects
    public GeoFence? GetGeoFenceConfiguration()
    {
        if (string.IsNullOrEmpty(GeoFenceConfigurationJson))
            return null;
        
        return JsonSerializer.Deserialize<GeoFence>(GeoFenceConfigurationJson);
    }
    
    public void SetGeoFenceConfiguration(GeoFence? geoFence)
    {
        GeoFenceConfigurationJson = geoFence != null 
            ? JsonSerializer.Serialize(geoFence) 
            : null;
    }
    
    public IReadOnlyList<LocationValidationResult> GetLocationValidationHistory()
    {
        if (string.IsNullOrEmpty(LocationValidationHistoryJson))
            return new List<LocationValidationResult>();
        
        return JsonSerializer.Deserialize<List<LocationValidationResult>>(LocationValidationHistoryJson) 
               ?? new List<LocationValidationResult>();
    }
    
    public void AddLocationValidation(LocationValidationResult validation)
    {
        var history = GetLocationValidationHistory().ToList();
        history.Add(validation);
        
        // Keep only last 100 validations to prevent JSON from growing too large
        if (history.Count > 100)
        {
            history = history.OrderByDescending(v => v.ValidationTime).Take(100).ToList();
        }
        
        LocationValidationHistoryJson = JsonSerializer.Serialize(history);
    }
    
    private decimal CalculateLocationCompliancePercentage()
    {
        var validations = GetLocationValidationHistory();
        if (!validations.Any()) return 0;
        
        var validCount = validations.Count(v => v.IsValid);
        return (decimal)validCount / validations.Count * 100;
    }
}
```

**Booking Enhancements**
```csharp
public class Booking : BaseEntity
{
    // Existing properties...
    
    // New geo-fencing properties
    public string? ServiceLocationGeoFenceJson { get; set; } // Serialized GeoFence value object
    
    // Helper methods
    public GeoFence? GetServiceLocationGeoFence()
    {
        if (string.IsNullOrEmpty(ServiceLocationGeoFenceJson))
            return null;
        
        return JsonSerializer.Deserialize<GeoFence>(ServiceLocationGeoFenceJson);
    }
    
    public void SetServiceLocationGeoFence(GeoFence? geoFence)
    {
        ServiceLocationGeoFenceJson = geoFence != null 
            ? JsonSerializer.Serialize(geoFence) 
            : null;
    }
    
    public GeoFence CreateDefaultGeoFence(Address serviceAddress, int radiusMeters = 100)
    {
        if (serviceAddress.Latitude == null || serviceAddress.Longitude == null)
            throw new InvalidOperationException("Service address must have coordinates to create geo-fence");
        
        return new GeoFence(
            serviceAddress.Latitude.Value,
            serviceAddress.Longitude.Value,
            radiusMeters,
            GeoFenceType.Primary,
            $"Service location for booking {Id}"
        );
    }
}
```

#### New Enums

```csharp
public enum GeoFenceType
{
    Primary,    // Main service location
    Secondary,  // Additional approved locations
    Transit     // Temporary locations for travel
}

public enum LocationValidationStatus
{
    Pending,        // Not yet validated
    Valid,          // Within geo-fence
    Invalid,        // Outside geo-fence
    Override,       // Manual override applied
    Unavailable,    // Location services unavailable
    Degraded        // Poor GPS signal/accuracy
}

public enum LocationValidationType
{
    SessionStart,   // Validation at session start
    Periodic,       // Regular interval checks
    SessionEnd,     // Validation at session end
    Manual,         // Manual validation request
    Violation,      // Geo-fence violation detected
    Recovery        // Return to geo-fence after violation
}

public enum GeoFenceViolationType
{
    InitialViolation,   // Started outside geo-fence
    ExitViolation,      // Left geo-fence during session
    ExtendedViolation,  // Remained outside for extended period
    RecurringViolation  // Multiple violations in single session
}
```

### Application Layer

#### Service Interfaces

**IGeoFencingService**
```csharp
public interface IGeoFencingService
{
    Task<Result<GeoFence>> CreateGeoFenceAsync(CreateGeoFenceRequest request, CancellationToken ct = default);
    Task<Result<IEnumerable<GeoFence>>> GetGeoFencesForBookingAsync(Guid bookingId, CancellationToken ct = default);
    Task<Result<GeoFence>> UpdateGeoFenceAsync(Guid geoFenceId, UpdateGeoFenceRequest request, CancellationToken ct = default);
    Task<Result> DeleteGeoFenceAsync(Guid geoFenceId, CancellationToken ct = default);
    Task<Result<GeoFenceValidationResult>> ValidateLocationAsync(ValidateLocationRequest request, CancellationToken ct = default);
    Task<Result<IEnumerable<GeoFence>>> GetNearbyGeoFencesAsync(decimal latitude, decimal longitude, int radiusMeters, CancellationToken ct = default);
}
```

**ILocationValidationService**
```csharp
public interface ILocationValidationService
{
    Task<Result<LocationValidationResult>> ValidateSessionStartLocationAsync(Guid trackingSessionId, LocationData location, CancellationToken ct = default);
    Task<Result<LocationValidationResult>> ValidatePeriodicLocationAsync(Guid trackingSessionId, LocationData location, CancellationToken ct = default);
    Task<Result<LocationValidationResult>> ValidateSessionEndLocationAsync(Guid trackingSessionId, LocationData location, CancellationToken ct = default);
    Task<Result> ProcessLocationViolationAsync(Guid trackingSessionId, LocationViolationData violation, CancellationToken ct = default);
    Task<Result> ApplyLocationOverrideAsync(Guid trackingSessionId, LocationOverrideRequest request, CancellationToken ct = default);
    Task<Result<LocationComplianceReport>> GenerateComplianceReportAsync(Guid trackingSessionId, CancellationToken ct = default);
}
```

**IGeospatialCalculationService**
```csharp
public interface IGeospatialCalculationService
{
    double CalculateDistance(decimal lat1, decimal lon1, decimal lat2, decimal lon2);
    bool IsWithinRadius(decimal centerLat, decimal centerLon, decimal pointLat, decimal pointLon, int radiusMeters);
    Task<Result<GeocodingResult>> GeocodeAddressAsync(string address, CancellationToken ct = default);
    Task<Result<ReverseGeocodingResult>> ReverseGeocodeAsync(decimal latitude, decimal longitude, CancellationToken ct = default);
    Result<LocationAccuracyAssessment> AssessLocationAccuracy(decimal accuracy, LocationValidationType validationType);
}
```

#### DTOs and Request Models

**Location Data Models**
```csharp
public record LocationData(
    decimal Latitude,
    decimal Longitude,
    decimal Accuracy,
    DateTime Timestamp,
    string? Address = null,
    string? Source = null
);

public record GeoFenceValidationResult(
    bool IsValid,
    decimal DistanceFromCenter,
    GeoFence? MatchedGeoFence,
    LocationValidationStatus Status,
    string? ValidationMessage,
    IEnumerable<string> Warnings
);

public record LocationValidationResult(
    Guid ValidationId,
    LocationValidationStatus Status,
    bool RequiresOverride,
    string? Message,
    decimal? DistanceFromNearestGeoFence,
    IEnumerable<GeoFence> NearbyGeoFences
);

public record LocationComplianceReport(
    Guid TrackingSessionId,
    decimal CompliancePercentage,
    int TotalValidations,
    int ValidValidations,
    int ViolationCount,
    TimeSpan TotalTimeOutside,
    IEnumerable<LocationViolationSummary> Violations
);
```

**Request Models**
```csharp
public record CreateGeoFenceRequest(
    Guid BookingId,
    Guid AddressId,
    int RadiusMeters,
    GeoFenceType Type,
    string? Description
);

public record ValidateLocationRequest(
    Guid TrackingSessionId,
    LocationData Location,
    LocationValidationType ValidationType
);

public record LocationOverrideRequest(
    Guid TrackingSessionId,
    Guid UserId,
    string Reason,
    LocationData Location
);
```

### Persistence Layer

#### Service Implementations

**GeoFencingService**
- Manages geo-fence CRUD operations
- Handles geo-fence validation logic
- Integrates with geospatial calculation service
- Manages geo-fence configuration and defaults

**LocationValidationService**
- Processes location validation requests
- Manages violation detection and escalation
- Handles override approvals and logging
- Generates compliance reports

**GeospatialCalculationService**
- Implements Haversine formula for distance calculations
- Provides location accuracy assessment
- Integrates with external geocoding services
- Handles coordinate system conversions

#### Enhanced Command/Query Handlers

**Enhanced Start Tracking Session**
```csharp
public class StartTrackingSessionWithLocationCommand : ICommand<Result<TrackingSessionResponse>>
{
    public Guid BookingWindowId { get; init; }
    public Guid ProviderId { get; init; }
    public LocationData InitialLocation { get; init; }
    public string? Notes { get; init; }
}

internal class StartTrackingSessionWithLocationHandler : ICommandHandler<StartTrackingSessionWithLocationCommand, Result<TrackingSessionResponse>>
{
    // Implementation includes:
    // 1. Existing session start validation
    // 2. Geo-fence lookup for booking
    // 3. Location validation against geo-fence
    // 4. Session creation with location validation status
    // 5. Initial location validation record creation
}
```

**New Location Validation Commands**
```csharp
public class ValidateLocationCommand : ICommand<Result<LocationValidationResult>>
public class ProcessLocationViolationCommand : ICommand<Result>
public class ApplyLocationOverrideCommand : ICommand<Result>
```

#### Repository Enhancements

**IGeoFenceRepository**
```csharp
public interface IGeoFenceRepository : IRepository<GeoFence>
{
    Task<Result<IEnumerable<GeoFence>>> GetByBookingIdAsync(Guid bookingId, CancellationToken ct = default);
    Task<Result<IEnumerable<GeoFence>>> GetNearbyGeoFencesAsync(decimal latitude, decimal longitude, int searchRadiusMeters, CancellationToken ct = default);
    Task<Result<GeoFence?>> GetPrimaryGeoFenceForBookingAsync(Guid bookingId, CancellationToken ct = default);
}
```

**ILocationValidationRepository**
```csharp
public interface ILocationValidationRepository : IRepository<LocationValidation>
{
    Task<Result<IEnumerable<LocationValidation>>> GetByTrackingSessionIdAsync(Guid trackingSessionId, CancellationToken ct = default);
    Task<Result<IEnumerable<LocationValidation>>> GetViolationsByProviderAsync(Guid providerId, DateTime fromDate, DateTime toDate, CancellationToken ct = default);
    Task<Result<LocationValidation?>> GetLatestValidationAsync(Guid trackingSessionId, CancellationToken ct = default);
}
```

## Data Models

### Database Schema Changes

#### Enhanced Tables (Value Object Approach)

**Bookings Table Additions**
```sql
-- Add geo-fence configuration as JSON column
ALTER TABLE "Bookings" ADD COLUMN "ServiceLocationGeoFenceJson" JSONB;

-- Create index for JSON queries if needed
CREATE INDEX "IX_Bookings_GeoFence_Type" ON "Bookings" USING GIN ((ServiceLocationGeoFenceJson->'Type'));
```

**TrackingSessions Table Additions**
```sql
-- Location validation properties
ALTER TABLE "TrackingSessions" ADD COLUMN "LocationValidationRequired" BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE "TrackingSessions" ADD COLUMN "LocationValidationStatus" VARCHAR(20) NOT NULL DEFAULT 'Pending';
ALTER TABLE "TrackingSessions" ADD COLUMN "GeoFenceViolationCount" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "TrackingSessions" ADD COLUMN "TotalTimeOutsideGeoFence" INTERVAL NOT NULL DEFAULT INTERVAL '0 seconds';

-- Last known location as individual columns for indexing and querying
ALTER TABLE "TrackingSessions" ADD COLUMN "LastKnownLatitude" DECIMAL(10,8);
ALTER TABLE "TrackingSessions" ADD COLUMN "LastKnownLongitude" DECIMAL(11,8);
ALTER TABLE "TrackingSessions" ADD COLUMN "LastKnownAccuracy" DECIMAL(8,2);
ALTER TABLE "TrackingSessions" ADD COLUMN "LastLocationUpdate" TIMESTAMP WITH TIME ZONE;

-- JSON columns for complex value objects
ALTER TABLE "TrackingSessions" ADD COLUMN "GeoFenceConfigurationJson" JSONB;
ALTER TABLE "TrackingSessions" ADD COLUMN "LocationValidationHistoryJson" JSONB;

-- Indexes for performance
CREATE INDEX "IX_TrackingSessions_LocationValidationStatus" ON "TrackingSessions"("LocationValidationStatus");
CREATE INDEX "IX_TrackingSessions_LastLocationUpdate" ON "TrackingSessions"("LastLocationUpdate");
CREATE INDEX "IX_TrackingSessions_LastKnownLocation" ON "TrackingSessions"("LastKnownLatitude", "LastKnownLongitude");

-- JSON indexes for querying validation history
CREATE INDEX "IX_TrackingSessions_ValidationHistory_Status" ON "TrackingSessions" USING GIN ((LocationValidationHistoryJson->'Status'));
CREATE INDEX "IX_TrackingSessions_GeoFence_Type" ON "TrackingSessions" USING GIN ((GeoFenceConfigurationJson->'Type'));
```

#### Entity Framework Configuration

**TrackingSession Configuration**
```csharp
public class TrackingSessionConfiguration : IEntityTypeConfiguration<TrackingSession>
{
    public void Configure(EntityTypeBuilder<TrackingSession> builder)
    {
        // Existing configuration...
        
        // Configure value object properties
        builder.Property(e => e.LocationValidationRequired)
            .HasDefaultValue(true);
            
        builder.Property(e => e.LocationValidationStatus)
            .HasConversion<string>()
            .HasDefaultValue(LocationValidationStatus.Pending);
            
        builder.Property(e => e.GeoFenceViolationCount)
            .HasDefaultValue(0);
            
        builder.Property(e => e.TotalTimeOutsideGeoFence)
            .HasDefaultValue(TimeSpan.Zero);

        // Configure JSON columns
        builder.Property(e => e.GeoFenceConfigurationJson)
            .HasColumnType("jsonb");
            
        builder.Property(e => e.LocationValidationHistoryJson)
            .HasColumnType("jsonb");

        // Configure GeoLocation value object as owned entity
        builder.OwnsOne(e => e.LastKnownLocation, location =>
        {
            location.Property(l => l.Latitude)
                .HasColumnName("LastKnownLatitude")
                .HasPrecision(10, 8);
                
            location.Property(l => l.Longitude)
                .HasColumnName("LastKnownLongitude")
                .HasPrecision(11, 8);
                
            location.Property(l => l.AccuracyMeters)
                .HasColumnName("LastKnownAccuracy")
                .HasPrecision(8, 2);
                
            location.Property(l => l.Timestamp)
                .HasColumnName("LastLocationUpdate");
                
            location.Property(l => l.Source)
                .HasColumnName("LastLocationSource")
                .HasMaxLength(50);
        });
    }
}
```

**Booking Configuration**
```csharp
public class BookingConfiguration : IEntityTypeConfiguration<Booking>
{
    public void Configure(EntityTypeBuilder<Booking> builder)
    {
        // Existing configuration...
        
        // Configure JSON column for geo-fence
        builder.Property(e => e.ServiceLocationGeoFenceJson)
            .HasColumnType("jsonb");
    }
}
```

### Configuration Settings

**GeoFencing Configuration**
```csharp
public class GeoFencingSettings
{
    public int DefaultRadiusMeters { get; set; } = 100;
    public int MinRadiusMeters { get; set; } = 50;
    public int MaxRadiusMeters { get; set; } = 1000;
    public int LocationValidationIntervalMinutes { get; set; } = 5;
    public int ViolationEscalationMinutes { get; set; } = 15;
    public decimal MinAccuracyMeters { get; set; } = 20.0m;
    public bool RequireLocationForSessionStart { get; set; } = true;
    public bool AllowManualOverrides { get; set; } = true;
    public Dictionary<string, int> CategorySpecificRadius { get; set; } = new();
}
```

## Error Handling

### New Error Types

```csharp
public static class GeoFencingErrors
{
    public static Error LocationOutsideGeoFence(decimal distance) => 
        Error.BadRequest($"Location is {distance:F0}m outside the allowed service area");
    
    public static Error InsufficientLocationAccuracy(decimal accuracy) => 
        Error.BadRequest($"Location accuracy of {accuracy:F0}m is insufficient for validation");
    
    public static Error GeoFenceNotFound(Guid bookingId) => 
        Error.NotFound($"No geo-fence configured for booking {bookingId}");
    
    public static Error LocationServicesUnavailable() => 
        Error.BadRequest("Location services are currently unavailable");
    
    public static Error OverrideNotAllowed() => 
        Error.Forbidden("Location override is not allowed for this session");
    
    public static Error InvalidGeoFenceRadius(int radius) => 
        Error.Validation($"Geo-fence radius of {radius}m is outside allowed range");
}
```

### Error Handling Strategy

1. **Graceful Degradation**: Allow sessions to continue with warnings when location services fail
2. **User-Friendly Messages**: Provide clear guidance on resolving location issues
3. **Escalation Paths**: Automatic escalation for repeated violations or system failures
4. **Audit Trail**: Complete logging of all location-related errors and resolutions

## Testing Strategy

### Unit Tests

1. **Geospatial Calculations**
   - Distance calculation accuracy
   - Geo-fence boundary detection
   - Coordinate validation

2. **Business Logic**
   - Location validation rules
   - Violation detection algorithms
   - Override approval workflows

3. **Service Integration**
   - Command/query handler behavior
   - Repository operations
   - External service integration

### Integration Tests

1. **API Endpoints**
   - Location validation workflows
   - Error handling scenarios
   - Authorization enforcement

2. **Database Operations**
   - Geospatial queries
   - Performance with large datasets
   - Constraint validation

3. **External Services**
   - Geocoding service integration
   - Location service fallbacks
   - Network failure handling

### Performance Tests

1. **Geospatial Queries**
   - Large dataset performance
   - Index effectiveness
   - Query optimization

2. **Real-time Processing**
   - Location validation latency
   - Concurrent session handling
   - Memory usage patterns

### Test Data Strategy

1. **Synthetic Locations**: Generate test coordinates for various scenarios
2. **Boundary Testing**: Test edge cases around geo-fence boundaries
3. **Accuracy Simulation**: Test various GPS accuracy scenarios
4. **Violation Patterns**: Test different violation types and durations

## Security Considerations

### Data Protection

1. **Location Data Encryption**: Encrypt sensitive GPS coordinates at rest
2. **Access Control**: Strict role-based access to location data
3. **Data Retention**: Automatic purging of old location data per privacy policies
4. **Anonymization**: Remove identifying information from analytics data

### Privacy Compliance

1. **Consent Management**: Clear consent for location tracking
2. **Data Minimization**: Collect only necessary location data
3. **User Rights**: Support for data deletion and access requests
4. **Cross-border**: Compliance with international data transfer regulations

### Audit and Monitoring

1. **Access Logging**: Log all location data access
2. **Change Tracking**: Audit trail for geo-fence modifications
3. **Violation Monitoring**: Real-time monitoring of compliance violations
4. **Security Alerts**: Automated alerts for suspicious location patterns

## Performance Optimization

### Database Optimization

1. **Spatial Indexes**: Optimize geospatial queries with proper indexing
2. **Query Optimization**: Efficient distance calculations and geo-fence lookups
3. **Data Archiving**: Archive old location validation data
4. **Connection Pooling**: Optimize database connections for location services

### Caching Strategy

1. **Geo-fence Caching**: Cache active geo-fences in memory
2. **Location Validation**: Cache recent validation results
3. **Geocoding Cache**: Cache address-to-coordinate conversions
4. **Configuration Cache**: Cache geo-fencing settings

### Real-time Processing

1. **Asynchronous Processing**: Handle location updates asynchronously
2. **Batch Processing**: Batch non-critical location validations
3. **Queue Management**: Use message queues for violation processing
4. **Load Balancing**: Distribute location processing across instances

This design provides a comprehensive foundation for implementing geo-fencing capabilities while maintaining the existing system's architecture and performance characteristics.