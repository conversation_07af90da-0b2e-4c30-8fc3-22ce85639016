﻿using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Parameters for filtering and pagination of user profiles for admin
    /// </summary>
    public class UserProfileListParams
    {
        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        /// <summary>
        /// Search term to filter by name, email, or phone number
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Filter by verification status (for care providers)
        /// </summary>
        public VerificationStatus? VerificationStatus { get; set; }

        /// <summary>
        /// Include only care providers
        /// </summary>
        public bool? IsCareProvider { get; set; }

        /// <summary>
        /// Sort by field name
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort in descending order
        /// </summary>
        public bool SortDescending { get; set; } = false;
    }
}
