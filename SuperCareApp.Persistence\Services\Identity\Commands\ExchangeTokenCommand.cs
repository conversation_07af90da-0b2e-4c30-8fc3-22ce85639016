using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record ExchangeTokenCommand(string RefreshToken) : ICommand<Result<AuthResponse>>;

internal sealed class ExchangeTokenCommandHandler
    : ICommandHandler<ExchangeTokenCommand, Result<AuthResponse>>
{
    private readonly ITokenService _tokensService;

    public ExchangeTokenCommandHandler(ITokenService tokensService)
    {
        _tokensService = tokensService;
    }

    public async Task<Result<AuthResponse>> Handle(
        ExchangeTokenCommand request,
        CancellationToken cancellationToken
    )
    {
        var result = await _tokensService.ExchangeTokenAsync(request.RefreshToken);
        if (result.IsFailure)
        {
            return Result.Failure<AuthResponse>(result.Error);
        }
        return Result.Success(result.Value);
    }
}
