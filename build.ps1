# SuperCare App CI/CD Build Script (PowerShell)
# This script handles building, testing, and deployment of the SuperCare application on Windows

param(
    [Parameter(Position=0)]
    [ValidateSet("build", "test", "docker", "publish", "clean", "migrate", "full")]
    [string]$Command = "build",
    
    [Parameter()]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [Parameter()]
    [ValidateSet("linux-x64", "win-x64", "osx-x64")]
    [string]$Runtime = "",
    
    [Parameter()]
    [string]$Tag = "latest",
    
    [Parameter()]
    [switch]$SkipTests,
    
    [Parameter()]
    [switch]$SkipDocker,
    
    [Parameter()]
    [switch]$Clean,
    
    [Parameter()]
    [switch]$Verbose,
    
    [Parameter()]
    [switch]$Help
)

# Configuration
$ProjectName = "SuperCareApp"
$MainProject = "super-care-app"
$SolutionFile = "super-care-app.sln"
$TestProject = "SuperCareApp.Persistence.Test"
$DockerImageName = "supercare-api"
$BuildDir = "bin/release"
$ArtifactsDir = "artifacts"

# Function to write colored output
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to show usage
function Show-Help {
    @"
SuperCare App Build Script (PowerShell)

Usage: .\build.ps1 [COMMAND] [OPTIONS]

COMMANDS:
    build           Build the application (default)
    test            Run tests only
    docker          Build Docker image only
    publish         Build and publish application
    clean           Clean build artifacts
    migrate         Run database migrations
    full            Full CI/CD pipeline (build, test, docker)

OPTIONS:
    -Configuration CONFIG   Build configuration (Debug|Release) [default: Release]
    -Runtime RUNTIME        Target runtime (linux-x64|win-x64|osx-x64)
    -Tag TAG               Docker image tag [default: latest]
    -SkipTests             Skip running tests
    -SkipDocker            Skip Docker build
    -Clean                 Clean before build
    -Verbose               Enable verbose output
    -Help                  Show this help message

EXAMPLES:
    .\build.ps1                           # Build with default settings
    .\build.ps1 build -Clean              # Clean build
    .\build.ps1 test                      # Run tests only
    .\build.ps1 docker -Tag "v1.0.0"      # Build Docker image with tag v1.0.0
    .\build.ps1 full -SkipDocker          # Full pipeline without Docker
    .\build.ps1 publish -Runtime "win-x64" # Publish for Windows

"@
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if dotnet is installed
    try {
        $dotnetVersion = dotnet --version
        Write-Info "Using .NET SDK version: $dotnetVersion"
    }
    catch {
        Write-Error ".NET SDK is not installed or not in PATH"
        exit 1
    }
    
    # Check if Docker is available (only if not skipping Docker)
    if (-not $SkipDocker) {
        try {
            docker --version | Out-Null
        }
        catch {
            Write-Warning "Docker is not installed or not in PATH. Skipping Docker build."
            $script:SkipDocker = $true
        }
    }
    
    # Check if solution file exists
    if (-not (Test-Path $SolutionFile)) {
        Write-Error "Solution file '$SolutionFile' not found"
        exit 1
    }
    
    Write-Success "Prerequisites check completed"
}

# Function to clean build artifacts
function Invoke-Clean {
    Write-Info "Cleaning build artifacts..."
    
    if (Test-Path $BuildDir) {
        Remove-Item -Path $BuildDir -Recurse -Force
        Write-Info "Removed $BuildDir"
    }
    
    if (Test-Path $ArtifactsDir) {
        Remove-Item -Path $ArtifactsDir -Recurse -Force
        Write-Info "Removed $ArtifactsDir"
    }
    
    # Clean dotnet artifacts
    $cleanArgs = @("clean", $SolutionFile, "--configuration", $Configuration)
    if ($Verbose) {
        $cleanArgs += "--verbosity", "detailed"
    }
    
    & dotnet @cleanArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Clean completed"
    }
    else {
        Write-Error "Clean failed"
        exit 1
    }
}

# Function to restore NuGet packages
function Invoke-Restore {
    Write-Info "Restoring NuGet packages..."
    
    $restoreArgs = @("restore", $SolutionFile)
    if ($Verbose) {
        $restoreArgs += "--verbosity", "detailed"
    }
    
    & dotnet @restoreArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Package restore completed"
    }
    else {
        Write-Error "Package restore failed"
        exit 1
    }
}

# Function to build the application
function Invoke-Build {
    Write-Info "Building application in $Configuration mode..."
    
    $buildArgs = @("build", $SolutionFile, "--configuration", $Configuration, "--no-restore")
    
    if ($Runtime) {
        $buildArgs += "--runtime", $Runtime
    }
    
    if ($Verbose) {
        $buildArgs += "--verbosity", "detailed"
    }
    
    & dotnet @buildArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Build completed successfully"
    }
    else {
        Write-Error "Build failed"
        exit 1
    }
}

# Function to run tests
function Invoke-Tests {
    if ($SkipTests) {
        Write-Warning "Skipping tests as requested"
        return
    }
    
    Write-Info "Running tests..."
    
    # Create artifacts directory for test results
    if (-not (Test-Path $ArtifactsDir)) {
        New-Item -Path $ArtifactsDir -ItemType Directory -Force | Out-Null
    }
    
    $testResultsDir = Join-Path $ArtifactsDir "test-results"
    if (-not (Test-Path $testResultsDir)) {
        New-Item -Path $testResultsDir -ItemType Directory -Force | Out-Null
    }
    
    $testArgs = @(
        "test", $SolutionFile,
        "--configuration", $Configuration,
        "--no-build", "--no-restore",
        "--logger", "trx",
        "--results-directory", $testResultsDir,
        "--collect:XPlat Code Coverage"
    )
    
    if ($Verbose) {
        $testArgs += "--verbosity", "detailed"
    }
    
    & dotnet @testArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "All tests passed"
        
        # Display test summary if available
        $trxFiles = Get-ChildItem -Path $testResultsDir -Filter "*.trx" -ErrorAction SilentlyContinue
        if ($trxFiles) {
            Write-Info "Test results saved to $testResultsDir"
        }
    }
    else {
        Write-Error "Tests failed"
        exit 1
    }
}

# Function to publish application
function Invoke-Publish {
    Write-Info "Publishing application..."
    
    $outputDir = $BuildDir
    if ($Runtime) {
        $outputDir = Join-Path $BuildDir $Runtime
    }
    
    $publishArgs = @(
        "publish", "$MainProject/$ProjectName.csproj",
        "--configuration", $Configuration,
        "--no-build", "--no-restore",
        "--output", $outputDir
    )
    
    if ($Runtime) {
        $publishArgs += "--runtime", $Runtime, "--self-contained", "false"
    }
    
    if ($Verbose) {
        $publishArgs += "--verbosity", "detailed"
    }
    
    & dotnet @publishArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Application published to $outputDir"
        
        # Create deployment package
        if ($Runtime) {
            New-DeploymentPackage -OutputDir $outputDir -Runtime $Runtime
        }
    }
    else {
        Write-Error "Publish failed"
        exit 1
    }
}

# Function to create deployment package
function New-DeploymentPackage {
    param(
        [string]$OutputDir,
        [string]$Runtime
    )
    
    Write-Info "Creating deployment package for $Runtime..."
    
    if (-not (Test-Path $ArtifactsDir)) {
        New-Item -Path $ArtifactsDir -ItemType Directory -Force | Out-Null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $packageName = "$ProjectName-$Runtime-$timestamp.zip"
    $packagePath = Join-Path $ArtifactsDir $packageName
    
    try {
        Compress-Archive -Path "$OutputDir\*" -DestinationPath $packagePath -Force
        Write-Success "Deployment package created: $packagePath"
    }
    catch {
        Write-Warning "Failed to create deployment package: $_"
    }
}

# Function to build Docker image
function Invoke-DockerBuild {
    if ($SkipDocker) {
        Write-Warning "Skipping Docker build as requested"
        return
    }
    
    Write-Info "Building Docker image..."
    
    $imageTag = "$DockerImageName`:$Tag"
    
    & docker build -t $imageTag .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker image built successfully: $imageTag"
        
        # Save image info
        if (-not (Test-Path $ArtifactsDir)) {
            New-Item -Path $ArtifactsDir -ItemType Directory -Force | Out-Null
        }
        
        $imageTag | Out-File -FilePath (Join-Path $ArtifactsDir "docker-image.txt") -Encoding UTF8
        
        & docker images $DockerImageName`:$Tag --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}" | 
            Out-File -FilePath (Join-Path $ArtifactsDir "docker-info.txt") -Encoding UTF8
    }
    else {
        Write-Error "Docker build failed"
        exit 1
    }
}

# Function to run database migrations
function Invoke-Migrations {
    Write-Info "Running database migrations..."
    
    # Check if Entity Framework tools are available
    try {
        & dotnet ef --version | Out-Null
    }
    catch {
        Write-Info "Installing Entity Framework tools..."
        & dotnet tool install --global dotnet-ef
    }
    
    # Run migrations
    $migrateArgs = @(
        "ef", "database", "update",
        "--project", "SuperCareApp.Persistence",
        "--startup-project", $MainProject,
        "--context", "SuperCareApp.Persistence.Context.ApplicationDbContext"
    )
    
    if ($Verbose) {
        $migrateArgs += "--verbose"
    }
    
    & dotnet @migrateArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database migrations completed"
    }
    else {
        Write-Error "Database migrations failed"
        exit 1
    }
}

# Function to run full CI/CD pipeline
function Invoke-FullPipeline {
    Write-Info "Starting full CI/CD pipeline..."
    
    Test-Prerequisites
    
    if ($Clean) {
        Invoke-Clean
    }
    
    Invoke-Restore
    Invoke-Build
    Invoke-Tests
    Invoke-Publish
    Invoke-DockerBuild
    
    Write-Success "Full CI/CD pipeline completed successfully!"
    
    # Print summary
    Write-Info "Build Summary:"
    Write-Host "  - Configuration: $Configuration"
    Write-Host "  - Target Runtime: $(if ($Runtime) { $Runtime } else { 'Framework Dependent' })"
    Write-Host "  - Docker Tag: $Tag"
    Write-Host "  - Artifacts: $ArtifactsDir"
}

# Function to display build info
function Show-BuildInfo {
    Write-Info "Build Information:"
    Write-Host "  - Project: $ProjectName"
    Write-Host "  - Solution: $SolutionFile"
    Write-Host "  - Configuration: $Configuration"
    Write-Host "  - Target Runtime: $(if ($Runtime) { $Runtime } else { 'Framework Dependent' })"
    Write-Host "  - Skip Tests: $SkipTests"
    Write-Host "  - Skip Docker: $SkipDocker"
    Write-Host "  - Clean Build: $Clean"
    Write-Host "  - Docker Tag: $Tag"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

# Show build information
Show-BuildInfo

# Execute the requested command
switch ($Command) {
    "build" {
        Test-Prerequisites
        if ($Clean) { Invoke-Clean }
        Invoke-Restore
        Invoke-Build
    }
    "test" {
        Test-Prerequisites
        Invoke-Restore
        Invoke-Build
        Invoke-Tests
    }
    "docker" {
        Test-Prerequisites
        Invoke-DockerBuild
    }
    "publish" {
        Test-Prerequisites
        if ($Clean) { Invoke-Clean }
        Invoke-Restore
        Invoke-Build
        Invoke-Tests
        Invoke-Publish
    }
    "clean" {
        Invoke-Clean
    }
    "migrate" {
        Invoke-Migrations
    }
    "full" {
        Invoke-FullPipeline
    }
    default {
        Write-Error "Unknown command: $Command"
        Show-Help
        exit 1
    }
}

Write-Success "Build script completed successfully!"