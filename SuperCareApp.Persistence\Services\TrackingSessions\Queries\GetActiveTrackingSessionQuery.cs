﻿using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.TrackingSession;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.TrackingSessions.Queries;

public sealed record GetActiveTrackingSessionQuery(Guid ProviderId)
    : IQuery<Result<TrackingSessionResponse>>;

internal sealed class GetActiveTrackingSessionHandler(ApplicationDbContext db)
    : IQueryHandler<GetActiveTrackingSessionQuery, Result<TrackingSessionResponse>>
{
    private readonly ApplicationDbContext _db = db;

    public async Task<Result<TrackingSessionResponse>> Handle(
        GetActiveTrackingSessionQuery req,
        CancellationToken ct
    )
    {
        return await _db
            .TrackingSessions.AsNoTracking()
            .Where(s => s.ProviderId == req.ProviderId && s.Status != TrackingSessionStatus.Stopped)
            .Select(s => new TrackingSessionResponse(
                s.Id,
                s.BookingWindowId,
                s.Status.ToString(),
                s.StartTime,
                s.EndTime,
                (int)s.TotalPausedDuration.TotalMinutes,
                s.TotalHours
            ))
            .SingleOrDefaultAsync(ct);
    }
}
