﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Services.Admin.Commands
{
    /// <summary>
    /// Command to suspend a care provider
    /// </summary>
    /// <param name="ProviderId">The ID of the care provider to suspend</param>
    /// <param name="AdminId">The ID of the admin performing the suspension</param>
    /// <param name="SuspensionReason">Optional reason for suspension</param>
    /// <param name="Notes">Optional additional notes</param>
    public record SuspendProviderCommand(
        Guid ProviderId,
        Guid AdminId,
        string? SuspensionReason,
        string? Notes
    ) : ICommand<Result<CareProviderProfile>>;

    /// <summary>
    /// Handler for the SuspendProviderCommand
    /// </summary>
    public class SuspendProviderCommandHandler
        : ICommandHandler<SuspendProviderCommand, Result<CareProviderProfile>>
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SuspendProviderCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public SuspendProviderCommandHandler(
            ApplicationDbContext context,
            ILogger<SuspendProviderCommandHandler> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command to suspend a care provider
        /// </summary>
        public async Task<Result<CareProviderProfile>> Handle(
            SuspendProviderCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Find the care provider profile
                var providerProfile = await _context
                    .CareProviderProfiles.Include(cp => cp.User)
                    .FirstOrDefaultAsync(
                        cp => cp.Id == request.ProviderId && !cp.IsDeleted,
                        cancellationToken
                    );

                if (providerProfile == null)
                {
                    return Result.Failure<CareProviderProfile>(
                        Error.NotFound("Care provider profile not found")
                    );
                }

                // Check if the provider is already suspended
                if (providerProfile.VerificationStatus == VerificationStatus.Suspended)
                {
                    return Result.Failure<CareProviderProfile>(
                        Error.Conflict("Care provider is already suspended")
                    );
                }

                // Update the verification status to Suspended
                providerProfile.VerificationStatus = VerificationStatus.Suspended;

                // Update audit fields
                providerProfile.UpdatedAt = DateTime.UtcNow;
                providerProfile.UpdatedBy = request.AdminId;

                var approvalData = new
                {
                    suspensionReason = request.SuspensionReason ?? "No reason provided",
                };

                // Create a record of the suspension
                var suspensionRecord = new Approval
                {
                    Id = Guid.NewGuid(),
                    UserId = providerProfile.UserId,
                    ApprovalType = ApprovalType.CareProviderVerification,
                    ApprovalData = JsonSerializer.Serialize(approvalData),
                    RelatedEntityId = providerProfile.Id,
                    IsApproved = false,
                    ProcessedBy = request.AdminId,
                    ProcessedAt = DateTime.UtcNow,
                    Notes = request.Notes,
                    RejectionReason = request.SuspensionReason,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = request.AdminId,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = request.AdminId,
                };

                await _context.Approvals.AddAsync(suspensionRecord, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                return Result.Success(providerProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error suspending care provider {ProviderId}",
                    request.ProviderId
                );
                return Result.Failure<CareProviderProfile>(
                    Error.Internal($"Error suspending care provider: {ex.Message}")
                );
            }
        }
    }
}
