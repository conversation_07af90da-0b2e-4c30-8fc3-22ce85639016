﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

// Command
public record DeleteAvailabilityCommand(Guid UserId, Guid AvailabilityId) : ICommand<Result>;

// Handler
public class DeleteAvailabilityCommandHandler : ICommandHandler<DeleteAvailabilityCommand, Result>
{
    private readonly IAvailabilityService _availabilityService;

    public DeleteAvailabilityCommandHandler(IAvailabilityService availabilityService)
    {
        _availabilityService = availabilityService;
    }

    public async Task<Result> Handle(DeleteAvailabilityCommand request, CancellationToken ct)
    {
        return await _availabilityService.DeleteAvailabilityAsync(
            request.UserId,
            request.AvailabilityId
        );
    }
}
