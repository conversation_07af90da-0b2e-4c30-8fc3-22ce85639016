﻿namespace SuperCareApp.Domain.Enums
{
    public enum ApprovalType
    {
        CareProviderVerification,
        DocumentVerification,
        ProfileUpdate,
        AccountActivation,
        Other,
    }

    public static class ApprovalTypeExtensions
    {
        public static string GetDescription(this ApprovalType type)
        {
            return type switch
            {
                ApprovalType.CareProviderVerification => "Care Provider Verification",
                ApprovalType.DocumentVerification => "Document Verification",
                ApprovalType.ProfileUpdate => "Profile Update",
                ApprovalType.AccountActivation => "Account Activation",
                ApprovalType.Other => "Other",
                _ => "Unknown",
            };
        }
    }
}
