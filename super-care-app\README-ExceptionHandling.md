# Exception Handling in SuperCareApp

This document explains how exception handling works in the SuperCareApp and how to use it effectively.

## Overview

The application uses a combination of custom exceptions, a global exception handler middleware, and a Result pattern to handle errors consistently across the application.

## Components

### 1. Custom Exceptions

All custom exceptions inherit from `SuperCareException` and include:

- `EntityNotFoundException`: Thrown when an entity is not found
- `ValidationException`: Thrown when validation fails
- `UnauthorizedException`: Thrown when a user is not authorized
- `ForbiddenException`: Thrown when a user is authenticated but not allowed to access a resource
- `ConflictException`: Thrown when there is a conflict with the current state of a resource
- `BadRequestException`: Thrown when a request is invalid
- `ExternalServiceException`: Thrown when an external service fails

### 2. Global Exception Handler Middleware

The `GlobalExceptionHandlerMiddleware` catches all unhandled exceptions in the application and converts them to standardized API responses with appropriate HTTP status codes.

### 3. Result Pattern

The `Result` and `Result<T>` classes provide a way to return success or failure from methods without throwing exceptions. The `ResultActionFilter` automatically converts these results to appropriate HTTP responses.

## How to Use

### Throwing Exceptions

```csharp
// When an entity is not found
throw new EntityNotFoundException(nameof(User), userId);

// When validation fails
throw new ValidationException("email", "Email is required");

// When a user is not authorized
throw new UnauthorizedException();

// When a user is forbidden from accessing a resource
throw new ForbiddenException("You do not have permission to access this resource");

// When there is a conflict
throw new ConflictException("The resource already exists");

// When a request is invalid
throw new BadRequestException("The request is invalid");

// When an external service fails
throw new ExternalServiceException("PaymentGateway", "Failed to process payment");
```

### Using the Result Pattern

```csharp
// Return a success result with a value
return Result.Success(user);

// Return a failure result with an error
return Result.Failure<User>(Error.NotFound("User not found"));

// In a controller action
[HttpGet("{id}")]
public async Task<IActionResult> GetUser(Guid id)
{
    var result = await _userService.GetUserByIdAsync(id);
    // Don't return the Result directly, use FromResult helper method
    return FromResult(result);
}
```

### Using Resilience Patterns

```csharp
// Create a resilience manager
var resilienceManager = ResilienceManager.Create(config =>
{
    config.UseRetry = true;
    config.MaxRetries = 3;
    config.UseCircuitBreaker = true;
    config.UseTimeout = true;
    config.TimeoutMilliseconds = 5000;
});

// Execute an operation with resilience
var result = await resilienceManager.ExecuteAsync(async (cancellationToken) =>
{
    // Your operation here
    var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
    return Result.Success(user);
});
```

## Error Response Format

All error responses follow this format:

```json
{
  "code": "NotFound",
  "message": "User with ID '12345678-1234-1234-1234-123456789012' was not found.",
  "errors": null,
  "requestId": "0HM6Q1KQPN1QK:00000001",
  "timestamp": "2023-06-01T12:34:56.789Z"
}
```

For validation errors, the `errors` field contains detailed validation errors:

```json
{
  "code": "ValidationError",
  "message": "One or more validation failures have occurred.",
  "errors": {
    "email": ["Email is required", "Email format is invalid"],
    "password": ["Password must be at least 8 characters"]
  },
  "requestId": "0HM6Q1KQPN1QK:00000002",
  "timestamp": "2023-06-01T12:34:56.789Z"
}
```

## HTTP Status Codes

The middleware maps exceptions to these HTTP status codes:

- `EntityNotFoundException` → 404 Not Found
- `ValidationException` → 400 Bad Request
- `BadRequestException` → 400 Bad Request
- `UnauthorizedException` → 401 Unauthorized
- `ForbiddenException` → 403 Forbidden
- `ConflictException` → 409 Conflict
- `ExternalServiceException` → 502 Bad Gateway
- Other exceptions → 500 Internal Server Error
