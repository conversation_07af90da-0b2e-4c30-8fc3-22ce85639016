﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.TrackingSession;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.TrackingSessions.Commands;

public sealed record StartTrackingSessionCommand(Guid BookingWindowId, Guid UserId)
    : ICommand<Result<TrackingSessionResponse>>;

internal sealed class StartTrackingSessionHandler(ApplicationDbContext db, TimeProvider time)
    : ICommandHandler<StartTrackingSessionCommand, Result<TrackingSessionResponse>>
{
    private readonly ApplicationDbContext _db = db;
    private readonly TimeProvider _time = time;

    public async Task<Result<TrackingSessionResponse>> Handle(
        StartTrackingSessionCommand req,
        CancellationToken ct
    )
    {
        try
        {
            var window = await _db
                .BookingWindows.AsNoTracking()
                .Include(w => w.Booking)
                .SingleOrDefaultAsync(w => w.Id == req.BookingWindowId, ct);

            if (window is null)
                return Result.Failure<TrackingSessionResponse>(
                    Error.NotFound("Booking window not found.")
                );

            var provider = await _db
                .CareProviderProfiles.AsNoTracking()
                .Where(p => p.UserId == req.UserId)
                .SingleOrDefaultAsync(ct);

            if (provider is null)
                return Result.Failure<TrackingSessionResponse>(
                    Error.NotFound("Care provider profile not found for user.")
                );

            // Use the provider.Id for comparison with booking
            if (window.Booking.ProviderId != provider.Id)
                return Result.Failure<TrackingSessionResponse>(
                    Error.Unauthorized("Window does not belong to provider.")
                );

            var now = _time.GetUtcNow();

            DateTime windowEnd = window.Date.ToDateTime(window.EndTime);

            if (now > windowEnd)
                return Result.Failure<TrackingSessionResponse>(
                    Error.BadRequest("Cannot start after window end.")
                );

            // Check for active sessions using the correct provider identifier
            // Use provider.Id if ProviderId FK references CareProviderProfiles table
            // Use req.userId if ProviderId FK references Users table
            var active = await _db.TrackingSessions.AnyAsync(
                s => s.ProviderId == req.UserId && s.Status != TrackingSessionStatus.Stopped,
                ct
            );

            if (active)
                return Result.Failure<TrackingSessionResponse>(
                    Error.BadRequest("Another session is already active.")
                );

            var session = new TrackingSession
            {
                Id = Guid.NewGuid(),
                BookingWindowId = req.BookingWindowId,
                ProviderId = req.UserId, // Use userId if FK references Users table
                Status = TrackingSessionStatus.Running,
                StartTime = now.UtcDateTime, // Fixed: Use UtcDateTime to avoid Kind=Unspecified issue
                TotalPausedDuration = TimeSpan.Zero,
            };

            _db.TrackingSessions.Add(session);
            window.Status = BookingWindowStatus.Pending;
            window.UpdatedAt = now.UtcDateTime;
            window.UpdatedBy = req.UserId;
            _db.BookingWindows.Update(window);
            await _db.SaveChangesAsync(ct);

            return new TrackingSessionResponse(
                session.Id,
                session.BookingWindowId,
                session.Status.ToString(),
                session.StartTime,
                null,
                0,
                null
            );
        }
        catch (Exception ex)
        {
            return Result.Failure<TrackingSessionResponse>(
                Error.BadRequest(ex.Message + "" + ex.StackTrace)
            );
        }
    }
}
