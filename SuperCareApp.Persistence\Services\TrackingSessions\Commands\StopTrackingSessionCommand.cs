﻿using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.TrackingSession;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Bookings.Commands;

namespace SuperCareApp.Persistence.Services.TrackingSessions.Commands;

public sealed record StopTrackingSessionCommand(Guid SessionId, Guid ProviderId)
    : ICommand<Result<TrackingSessionResponse>>;

internal sealed class StopTrackingSessionHandler(
    ApplicationDbContext db,
    TimeProvider time,
    IMediator mediator
) : ICommandHandler<StopTrackingSessionCommand, Result<TrackingSessionResponse>>
{
    private readonly ApplicationDbContext _db = db;
    private readonly TimeProvider _time = time;

    public async Task<Result<TrackingSessionResponse>> Handle(
        StopTrackingSessionCommand req,
        CancellationToken ct
    )
    {
        try
        {
            var session = await _db
                .TrackingSessions.Include(bw => bw.BookingWindow)
                .SingleOrDefaultAsync(
                    s => s.Id == req.SessionId && s.ProviderId == req.ProviderId,
                    ct
                );

            var window = session.BookingWindow;

            if (session is null)
                return Result.Failure<TrackingSessionResponse>(
                    Error.NotFound("Session not found.")
                );

            if (session.Status == TrackingSessionStatus.Stopped)
                return Result.Failure<TrackingSessionResponse>(
                    Error.BadRequest("Session already stopped.")
                );

            if (session.Status == TrackingSessionStatus.Paused && session.PausedAt is not null)
            {
                var pausedSpan = _time.GetUtcNow() - session.PausedAt.Value;
                session.TotalPausedDuration += pausedSpan;
            }

            session.Status = TrackingSessionStatus.Stopped;
            session.EndTime = _time.GetUtcNow().UtcDateTime;

            var totalElapsed = session.EndTime - session.StartTime - session.TotalPausedDuration;

            if (totalElapsed < TimeSpan.Zero)
            {
                return Result.Failure<TrackingSessionResponse>(
                    Error.BadRequest("Session duration is negative.")
                );
            }
            session.TotalHours = (decimal)totalElapsed.Value.TotalHours;
            window.Status = BookingWindowStatus.Completed;
            await _db.SaveChangesAsync(ct);

            var generateInvoiceCommand = new GenerateInvoicesForBookingCommand(window.Id);
            var invoiceResult = await mediator.Send(generateInvoiceCommand, ct);
            if (invoiceResult.IsFailure)
            {
                return Result.Failure<TrackingSessionResponse>(invoiceResult.Error);
            }

            return new TrackingSessionResponse(
                session.Id,
                session.BookingWindowId,
                session.Status.ToString(),
                session.StartTime,
                session.EndTime,
                (int)session.TotalPausedDuration.TotalMinutes,
                session.TotalHours
            );
        }
        catch (Exception ex)
        {
            return Result.Failure<TrackingSessionResponse>(
                Error.BadRequest(ex.Message + "" + ex.StackTrace)
            );
        }
    }
}
