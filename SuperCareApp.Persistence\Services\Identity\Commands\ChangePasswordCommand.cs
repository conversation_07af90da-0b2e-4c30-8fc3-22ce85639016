using FluentValidation;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record ChangePasswordCommand(
    Guid UserId,
    string CurrentPassword,
    string NewPassword,
    string ConfirmNewPassword
) : ICommand<Result<GenericObjectResponse>>;

internal class ChangePasswordCommandHandler
    : ICommandHandler<ChangePasswordCommand, Result<GenericObjectResponse>>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ILogger<ChangePasswordCommandHandler> _logger;

    public ChangePasswordCommandHandler(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ILogger<ChangePasswordCommandHandler> logger
    )
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _logger = logger;
    }

    public async Task<Result<GenericObjectResponse>> Handle(
        ChangePasswordCommand request,
        CancellationToken cancellationToken
    )
    {
        var user = await _userManager.FindByIdAsync(request.UserId.ToString());
        if (user == null)
        {
            return Result.Failure<GenericObjectResponse>(Error.NotFound("User not found"));
        }

        var result = await _userManager.ChangePasswordAsync(
            user,
            request.CurrentPassword,
            request.NewPassword
        );
        if (!result.Succeeded)
        {
            return Result.Failure<GenericObjectResponse>(
                Error.Internal(
                    $"Password change failed with error: {result.Errors.FirstOrDefault()?.Description}"
                )
            );
        }
        var response = new GenericObjectResponse("Password changed successfully", Guid.NewGuid());
        return Result.Success(response);
    }
}
