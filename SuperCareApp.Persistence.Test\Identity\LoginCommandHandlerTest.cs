using Moq;
using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Persistence.Services.Identity.Commands;

namespace SuperCareApp.Persistence.Test.Identity;

public class LoginCommandHandlerTest
{
    private readonly Mock<IAuthService> _authServiceMock;

    public LoginCommandHandlerTest()
    {
        _authServiceMock = new Mock<IAuthService>();
    }

    [Fact]
    public async Task Handle_ValidCredentials_ReturnsAuthServiceSuccess()
    {
        // Arrange
        var email = "<EMAIL>";
        var password = "CorrectPass123!";
        var command = new LoginCommand(email, password);

        var expectedAuthResponse = new AuthResponse(
            accessToken: "mock_access_token",
            refreshToken: "mock_refresh_token",
            expiresIn: DateTime.UtcNow.AddDays(1),
            isVerifiedByAdmin: true,
            user: new AuthUserResponse(
                userId: "mock_user_id",
                email: "mock_email",
                phoneNumber: "mock_phone_number",
                providerId: "mock_provider_id"
            )
        );
        var authServiceResult = Result.Success(expectedAuthResponse);

        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(email, password))
            .ReturnsAsync(authServiceResult);

        var handler = new LoginCommandHandler(_authServiceMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(expectedAuthResponse.accessToken, result.Value.accessToken);
        Assert.Equal(expectedAuthResponse.refreshToken, result.Value.refreshToken);
        // Add more assertions for other properties of AuthResponse and UserDetailsDto as needed

        _authServiceMock.Verify(auth => auth.AuthenticateAsync(email, password), Times.Once);
    }

    [Fact]
    public async Task Handle_InvalidCredentials_ReturnsAuthServiceFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var password = "WrongPass!";
        var command = new LoginCommand(email, password);

        var expectedError = Error.Validation("Invalid email or password.");
        var authServiceResult = Result.Failure<AuthResponse>(expectedError);

        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(email, password))
            .ReturnsAsync(authServiceResult);

        var handler = new LoginCommandHandler(_authServiceMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal(expectedError.Code, result.Error.Code);
        Assert.Equal(expectedError.Message, result.Error.Message);
        // Add assertions for other Error properties if applicable (e.g., Details)

        _authServiceMock.Verify(auth => auth.AuthenticateAsync(email, password), Times.Once);
    }

    [Fact]
    public async Task Handle_AuthServiceReturnsUnexpectedFailure_ReturnsInternalFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var password = "AnyPass123!";
        var command = new LoginCommand(email, password);

        // Simulate an unexpected error from the service layer, like internal server error
        var expectedError = Error.Internal("Database connection failed during authentication.");
        var authServiceResult = Result.Failure<AuthResponse>(expectedError);

        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(email, password))
            .ReturnsAsync(authServiceResult);

        var handler = new LoginCommandHandler(_authServiceMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal(expectedError.Code, result.Error.Code);
        Assert.Equal(expectedError.Message, result.Error.Message);

        _authServiceMock.Verify(auth => auth.AuthenticateAsync(email, password), Times.Once);
    }

    [Fact]
    public async Task Handle_AuthServiceThrowsException_ReturnsInternalFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var password = "AnyPass123!";
        var command = new LoginCommand(email, password);

        var exceptionMessage = "Simulated service unavailable";

        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(email, password))
            .ThrowsAsync(new InvalidOperationException(exceptionMessage));

        var handler = new LoginCommandHandler(_authServiceMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        //Check for failure result
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Internal", result.Error.Code);
        Assert.Equal(exceptionMessage, result.Error.Message);

        _authServiceMock.Verify(auth => auth.AuthenticateAsync(email, password), Times.Once);
    }
}
