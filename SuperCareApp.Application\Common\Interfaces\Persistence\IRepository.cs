﻿using System.Linq.Expressions;
using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Persistence
{
    /// <summary>
    /// Generic repository interface for CRUD operations
    /// </summary>
    /// <typeparam name="TEntity">Entity type</typeparam>
    public interface IRepository<TEntity>
        where TEntity : BaseEntity
    {
        /// <summary>
        /// Gets all entities
        /// </summary>
        Task<Result<IEnumerable<TEntity>>> GetAllAsync(
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets entities by filter
        /// </summary>
        Task<Result<IEnumerable<TEntity>>> GetByFilterAsync(
            Expression<Func<TEntity, bool>> filter,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets entity by id
        /// </summary>
        Task<Result<TEntity>> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Adds a new entity
        /// </summary>
        Task<Result<TEntity>> AddAsync(
            TEntity entity,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Updates an existing entity
        /// </summary>
        Task<Result<TEntity>> UpdateAsync(
            TEntity entity,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Deletes an entity by id
        /// </summary>
        Task<Result> DeleteAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Soft deletes an entity by id
        /// </summary>
        Task<Result> SoftDeleteAsync(
            Guid id,
            Guid deletedBy,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Checks if an entity exists
        /// </summary>
        Task<Result<bool>> ExistsAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a queryable for advanced operations
        /// </summary>
        IQueryable<TEntity> AsQueryable();
    }
}
