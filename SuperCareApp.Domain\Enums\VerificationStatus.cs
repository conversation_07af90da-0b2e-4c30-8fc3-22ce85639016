﻿namespace SuperCareApp.Domain.Enums
{
    public enum VerificationStatus
    {
        Pending,
        Verified,
        Rejected,
        Suspended,
    }

    public static class VerificationStatusExtensions
    {
        public static string GetDescription(this VerificationStatus status)
        {
            return status switch
            {
                VerificationStatus.Pending => "Pending",
                VerificationStatus.Verified => "Verified",
                VerificationStatus.Rejected => "Rejected",
                VerificationStatus.Suspended => "Suspended",
                _ => "Unknown",
            };
        }
    }
}
