namespace SuperCareApp.Application.Common.Models.Provider
{
    /// <summary>
    /// DTO for availability template operations
    /// </summary>
    public class AvailabilityTemplateDto
    {
        /// <summary>
        /// Care provider's user ID
        /// </summary>
        public Guid ProviderId { get; set; }

        /// <summary>
        /// Availability for each day of the week
        /// </summary>
        public List<DayAvailabilityDto> DayAvailabilities { get; set; } = new();

        /// <summary>
        /// Indicates if the template is complete (has all 7 days)
        /// </summary>
        public bool IsComplete => DayAvailabilities.Count == 7;

        /// <summary>
        /// Indicates if the provider has any available days
        /// </summary>
        public bool HasAnyAvailability => DayAvailabilities.Any(d => d.IsAvailable);

        /// <summary>
        /// Count of available days
        /// </summary>
        public int AvailableDaysCount => DayAvailabilities.Count(d => d.IsAvailable);

        /// <summary>
        /// List of available day names
        /// </summary>
        public List<string> AvailableDays =>
            DayAvailabilities.Where(d => d.IsAvailable).Select(d => d.DayOfWeek).ToList();

        /// <summary>
        /// List of unavailable day names
        /// </summary>
        public List<string> UnavailableDays =>
            DayAvailabilities.Where(d => !d.IsAvailable).Select(d => d.DayOfWeek).ToList();
    }

    /// <summary>
    /// DTO for individual day availability
    /// </summary>
    public class DayAvailabilityDto
    {
        /// <summary>
        /// Availability record ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Day of the week (Monday, Tuesday, etc.)
        /// </summary>
        public string DayOfWeek { get; set; } = string.Empty;

        /// <summary>
        /// Whether the provider is available on this day
        /// </summary>
        public bool IsAvailable { get; set; }

        /// <summary>
        /// Number of time slots configured for this day
        /// </summary>
        public int SlotCount { get; set; }

        /// <summary>
        /// Indicates if this day has time slots configured
        /// </summary>
        public bool HasSlots => SlotCount > 0;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Request DTO for updating availability template
    /// </summary>
    public class UpdateAvailabilityTemplateRequest
    {
        /// <summary>
        /// Day availability updates
        /// </summary>
        public List<UpdateDayAvailabilityRequest> DayUpdates { get; set; } = new();
    }

    /// <summary>
    /// Request DTO for updating individual day availability
    /// </summary>
    public class UpdateDayAvailabilityRequest
    {
        /// <summary>
        /// Day of the week to update
        /// </summary>
        public string DayOfWeek { get; set; } = string.Empty;

        /// <summary>
        /// New availability status
        /// </summary>
        public bool IsAvailable { get; set; }
    }

    /// <summary>
    /// Request DTO for bulk day updates
    /// </summary>
    public class BulkDayUpdateRequest
    {
        /// <summary>
        /// Dictionary of day names and their new availability status
        /// </summary>
        public Dictionary<string, bool> DayUpdates { get; set; } = new();
    }

    /// <summary>
    /// Request DTO for setting weekdays/weekend availability
    /// </summary>
    public class SetDaysAvailabilityRequest
    {
        /// <summary>
        /// Availability status to set
        /// </summary>
        public bool IsAvailable { get; set; }

        /// <summary>
        /// Optional reason for the change
        /// </summary>
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Response DTO for availability template operations
    /// </summary>
    public class AvailabilityTemplateResponse
    {
        /// <summary>
        /// Operation success status
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Operation message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Updated availability template
        /// </summary>
        public AvailabilityTemplateDto? Template { get; set; }

        /// <summary>
        /// Number of days affected by the operation
        /// </summary>
        public int AffectedDaysCount { get; set; }
    }
}
