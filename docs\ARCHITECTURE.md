# SuperCareApp Architecture

This document provides a comprehensive overview of the `SuperCareApp` solution's architecture. The project separates concerns into distinct layers to promote maintainability, scalability, and testability.

## High-Level Architecture

The solution follows a linear dependency architecture, where each layer depends on the one "below" it. This structure promotes a clear separation of concerns.

The solution is divided into the following primary layers:

- **Domain:** The core of the application, containing business entities and logic. It has no dependencies on other layers.
- **Application:** Orchestrates the domain logic and handles application-specific use cases. It depends on the Domain layer.
- **Persistence (Infrastructure):** Responsible for data access and implementation of interfaces defined in the Application layer. It depends on the Application layer.
- **Presentation:** The user-facing layer, implemented as an ASP.NET Core API. It depends on the Persistence layer.
- **Tests:** Contains tests for the persistence layer.

### Architecture Diagram

The following diagram illustrates the dependencies between these layers, which flow in one direction:

```mermaid
graph TD
    subgraph Presentation
        A[super-care-app]
    end
    subgraph Infrastructure
        D[SuperCareApp.Persistence]
    end
    subgraph Application
        B[SuperCareApp.Application]
    end
    subgraph Domain
        C[SuperCareApp.Domain]
    end
    subgraph Tests
        E[SuperCareApp.Persistence.Test]
    end

    A --> D
    D --> B
    B --> C
    E --> D
```

## Detailed Layer Breakdown

This section provides a more detailed look at the structure and responsibilities of each layer.

### 1. SuperCareApp.Domain

The `SuperCareApp.Domain` layer is the heart of the application. It is framework-agnostic and contains the core business logic, entities, and value objects.

- **`Entities/`**: This directory contains the core business objects of the application. These classes represent the fundamental concepts of the domain.
  - **`ValueObjects/`**: Contains value objects, which are immutable types that are distinguishable only by their values.
- **`Enums/`**: Contains various enumerations used throughout the domain, providing strong typing for states and categories.
- **`Identity/`**: Defines the entities for authentication and authorization, built upon ASP.NET Core Identity.
- **`Common/`**: Holds base classes and common components that are shared across the domain.
  - **`Exceptions/`**: Contains custom exception types for handling specific error scenarios.
  - **`Resilience/`**: Provides resilience patterns such as `CircuitBreaker` and `RetryPolicy`.
  - **`Results/`**: A lightweight result pattern implementation for returning operation outcomes.
- **`Validation/`**: Contains domain-specific validation logic.
- **`Constants/`**: Defines constants used within the domain layer.

### 2. SuperCareApp.Application

The `SuperCareApp.Application` layer contains the application-specific business logic. It orchestrates the domain models to perform use cases and defines the interfaces that are implemented by the `Persistence` layer.

- **`Common/Interfaces/`**: This is a critical part of the application layer, defining the contracts (interfaces) for services that are implemented in the infrastructure layer. This follows the Dependency Inversion Principle. The interfaces are organized by feature, such as `Bookings`, `Identity`, and `Categories`.
  - **`Messages/`**: Defines the `ICommand` and `IQuery` interfaces, which are part of the CQRS pattern implementation.
  - **`Mediator/`**: Contains interfaces for the Mediator pattern, which decouples objects by making them communicate through a mediator object.
- **`Common/Models/`**: Contains the Data Transfer Objects (DTOs) used to pass data between layers. These models are tailored for specific use cases and are organized by feature.
- **`Common/Settings/`**: Contains settings objects that are bound from configuration files.
- **`Shared/`**: Holds shared logic, dispatchers, and utilities that are specific to the application layer.

### 3. SuperCareApp.Persistence

The `SuperCareApp.Persistence` layer is the infrastructure layer of the application. It handles all external concerns, such as database access, file storage, and sending emails. It implements the interfaces defined in the `Application` layer.

- **`Services/`**: Implements the various service interfaces defined in the application layer. The services are organized by feature, and each feature folder contains its own `Commands` and `Queries` subdirectories, following the CQRS pattern.
- **`Repositories/`**: Contains the concrete implementations of the repository interfaces. It uses Entity Framework Core for data access and includes a generic repository pattern. The `UnitOfWork.cs` class is used to manage transactions.
- **`Context/`**: Contains the `ApplicationDbContext` class, which represents the session with the database.
- **`Configurations/`**: Contains the Entity Framework Core configurations for the domain entities.
- **`Migrations/`**: Holds the database migration files.
- **`Interceptors/`**: Contains Entity Framework Core interceptors for handling cross-cutting concerns like auditing and processing outbox messages.
- **`Behaviours/`**: Implements pipeline behaviors for the Mediator pattern, such as logging, caching, and performance monitoring.
- **`Jobs/`**: Contains background jobs that are executed on a schedule.
- **`DependencyInjection.cs`**: This file is responsible for registering the services, repositories, and other components from the persistence layer with the dependency injection container.

### 4. super-care-app (Presentation)

The `super-care-app` project is the presentation layer of the application, implemented as an ASP.NET Core Web API. It is responsible for handling incoming HTTP requests and returning appropriate responses.

- **`Controllers/`**: Contains the API controllers that define the application's endpoints. Each controller is responsible for a specific resource or feature.
- **`Middleware/`**: Holds custom middleware components, such as the `GlobalExceptionHandlerMiddleware`, which is used for centralized exception handling.
- **`Extensions/`**: Contains extension methods for configuring services in the `Program.cs` file.
- **`Filters/`**: Includes custom action filters, such as the `ValidationFilter` for validating incoming requests.
- **`Swagger/`**: Contains configurations for Swagger/OpenAPI, which is used to generate interactive API documentation.
- **`Models/Doc/`**: Contains example requests and responses for the Swagger documentation.
- **`Shared/`**: Holds shared components, such as API response models, configuration for API versioning, and constants.
- **`appsettings.json`**: The main configuration file for the application.
- **`Program.cs`**: The entry point of the application, where services are configured and the HTTP request pipeline is built.
