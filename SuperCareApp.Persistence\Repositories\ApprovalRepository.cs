﻿using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Repositories
{
    public class ApprovalRepository : GenericRepository<Approval>, IApprovalRepository
    {
        public ApprovalRepository(ApplicationDbContext dbContext)
            : base(dbContext) { }

        public async Task<Result<IEnumerable<Approval>>> GetPendingApprovalsAsync(
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var approvals = await _dbSet
                    .Where(a =>
                        (a.IsApproved == false || a.IsApproved == null)
                        && a.ProcessedAt == null
                        && !a.IsDeleted
                    )
                    .Include(a => a.User)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync(cancellationToken);

                return Result.Success<IEnumerable<Approval>>(approvals);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<Approval>>(Error.Internal(ex.Message));
            }
        }

        public async Task<Result<IEnumerable<Approval>>> GetApprovalsByUserIdAsync(
            Guid userId,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var approvals = await _dbSet
                    .Where(a => a.UserId == userId && !a.IsDeleted)
                    .Include(a => a.User)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync(cancellationToken);

                return Result.Success<IEnumerable<Approval>>(approvals);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<Approval>>(Error.Internal(ex.Message));
            }
        }

        public async Task<Result<IEnumerable<Approval>>> GetApprovalsByTypeAsync(
            ApprovalType type,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var approvals = await _dbSet
                    .Where(a => a.ApprovalType == type && !a.IsDeleted)
                    .Include(a => a.User)
                    .OrderByDescending(a => a.CreatedAt)
                    .ToListAsync(cancellationToken);

                return Result.Success<IEnumerable<Approval>>(approvals);
            }
            catch (Exception ex)
            {
                return Result.Failure<IEnumerable<Approval>>(Error.Internal(ex.Message));
            }
        }

        public async Task<Result<Approval>> ApproveAsync(
            Guid approvalId,
            Guid adminId,
            string? notes = null,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var approval = await _dbSet
                    .Where(a => a.Id == approvalId && !a.IsDeleted)
                    .FirstOrDefaultAsync(cancellationToken);
                if (approval == null)
                {
                    return Result.Failure<Approval>(Error.NotFound("Approval not found"));
                }

                approval.IsApproved = true;
                approval.ProcessedBy = adminId;
                approval.ProcessedAt = DateTime.UtcNow;
                approval.Notes = notes;

                // Note: SaveChanges is handled by the UnitOfWork
                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                return Result.Failure<Approval>(Error.Internal(ex.Message));
            }
        }

        public async Task<Result<Approval>> RejectAsync(
            Guid approvalId,
            Guid adminId,
            string rejectionReason,
            string? notes = null,
            CancellationToken cancellationToken = default
        )
        {
            try
            {
                var approval = await _dbSet
                    .Where(a => a.Id == approvalId && !a.IsDeleted)
                    .FirstOrDefaultAsync(cancellationToken);
                if (approval == null)
                {
                    return Result.Failure<Approval>(Error.NotFound("Approval not found"));
                }

                approval.IsApproved = false;
                approval.RejectionReason = rejectionReason;
                approval.ProcessedBy = adminId;
                approval.ProcessedAt = DateTime.UtcNow;
                approval.Notes = notes;

                // Note: SaveChanges is handled by the UnitOfWork
                return Result.Success(approval);
            }
            catch (Exception ex)
            {
                return Result.Failure<Approval>(Error.Internal(ex.Message));
            }
        }
    }
}
