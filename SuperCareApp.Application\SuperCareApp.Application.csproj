﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="MimeKit" Version="4.11.0" />
    <PackageReference Include="NetTopologySuite" Version="2.6.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SuperCareApp.Domain\SuperCareApp.Domain.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Common\Interfaces\Provider\" />
    <Folder Include="Shared\Handler\" />
  </ItemGroup>
</Project>
