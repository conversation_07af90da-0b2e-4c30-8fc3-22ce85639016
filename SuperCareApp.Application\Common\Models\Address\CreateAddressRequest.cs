﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Address
{
    public class CreateAddressRequest
    {
        public string StreetAddress { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool IsPrimary { get; set; } = true;
        public string? Label { get; set; } = "Home";
    }

    public class CreateAddressRequestValidator : AbstractValidator<CreateAddressRequest>
    {
        public CreateAddressRequestValidator()
        {
            RuleFor(x => x.StreetAddress)
                .NotEmpty()
                .WithMessage("Street address is required.")
                .MaximumLength(200)
                .WithMessage("Street address cannot exceed 200 characters.");

            RuleFor(x => x.City)
                .NotEmpty()
                .WithMessage("City is required.")
                .MaximumLength(100)
                .WithMessage("City cannot exceed 100 characters.");

            RuleFor(x => x.State)
                .NotEmpty()
                .WithMessage("State is required.")
                .MaximumLength(100)
                .WithMessage("State cannot exceed 100 characters.");

            RuleFor(x => x.PostalCode)
                .NotEmpty()
                .WithMessage("Postal code is required.")
                .Matches(@"^\d{5}(-\d{4})?$")
                .WithMessage("Postal code must be a valid format (e.g., 12345 or 12345-6789).")
                .MaximumLength(10);

            RuleFor(x => x.Latitude)
                .InclusiveBetween(-90, 90)
                .WithMessage("Latitude must be between -90 and 90 degrees.")
                .When(x => x.Latitude.HasValue);

            RuleFor(x => x.Longitude)
                .InclusiveBetween(-180, 180)
                .WithMessage("Longitude must be between -180 and 180 degrees.")
                .When(x => x.Longitude.HasValue);

            RuleFor(x => x.Label)
                .MaximumLength(50)
                .WithMessage("Label cannot exceed 50 characters.")
                .When(x => !string.IsNullOrEmpty(x.Label));
        }
    }
}
