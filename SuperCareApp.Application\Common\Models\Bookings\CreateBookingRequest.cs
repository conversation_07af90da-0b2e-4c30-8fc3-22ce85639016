﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class CreateBookingRequest
    {
        public Guid ProviderId { get; set; }
        public Guid CategoryId { get; set; }
        public string? SpecialInstructions { get; set; }

        // New property for per-day windows
        public List<BookingWindowRequest>? BookingWindows { get; set; }
    }

    public class CreateBookingRequestValidator : AbstractValidator<CreateBookingRequest>
    {
        public CreateBookingRequestValidator()
        {
            // --- ID Validations ---
            RuleFor(x => x.ProviderId).NotEmpty().WithMessage("Provider ID is required.");
            RuleFor(x => x.CategoryId).NotEmpty().WithMessage("Category ID is required.");
            RuleForEach(x => x.BookingWindows).SetValidator(new BookingWindowRequestValidator());

            // --- Booking Windows Validation ---
            RuleFor(x => x.BookingWindows)
                .NotEmpty()
                .WithMessage("At least one booking window is required.")
                .Must(HaveNoOverlappingWindows)
                .WithMessage(
                    "Booking windows within the same request must not overlap on the same date."
                );

            // --- Optional Fields Validation ---
            RuleFor(x => x.SpecialInstructions)
                // Set a reasonable length limit to prevent excessively long inputs.
                .MaximumLength(500)
                .WithMessage("Special instructions cannot exceed 500 characters.");
        }

        private bool HaveNoOverlappingWindows(List<BookingWindowRequest>? windows)
        {
            if (windows == null || windows.Count <= 1)
                return true;

            var windowsByDate = windows.GroupBy(w => w.Date.Date);

            foreach (var dayGroup in windowsByDate)
            {
                var sortedWindows = dayGroup.OrderBy(w => w.StartTime).ToList();
                for (int i = 1; i < sortedWindows.Count; i++)
                {
                    if (sortedWindows[i].StartTime < sortedWindows[i - 1].EndTime)
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Checks if the provided time is in the future.
        /// </summary>
        private bool BeInTheFuture(TimeOnly? time)
        {
            return time > TimeOnly.FromDateTime(DateTime.Now);
        }
    }
}
