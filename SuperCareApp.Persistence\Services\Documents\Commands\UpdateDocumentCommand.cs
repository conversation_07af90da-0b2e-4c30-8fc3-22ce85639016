﻿using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Documents;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Documents.Commands;

/// <summary>
/// Command to update a document
/// </summary>
/// <param name="File">The new file to upload</param>
/// <param name="DocumentId">ID of the document to update</param>
/// <param name="UserId">ID of the user updating the document</param>
public record UpdateDocumentCommand(
    IFormFile? File,
    Guid DocumentId,
    Guid UserId,
    string? DocumentType = null,
    string? Issuer = null,
    string? Country = null,
    string? CertificationType = null,
    string? OtherCertificationType = null,
    string? CertificationNumber = null,
    DateTime? ExpiryDate = null
) : ICommand<Result<DocumentResponse>>;

/// <summary>
/// Handler for the UpdateDocumentCommand
/// </summary>
internal sealed class UpdateDocumentCommandHandler
    : ICommandHandler<UpdateDocumentCommand, Result<DocumentResponse>>
{
    private readonly IDocumentService _documentService;
    private readonly ILogger<UpdateDocumentCommandHandler> _logger;

    public UpdateDocumentCommandHandler(
        IDocumentService documentService,
        ILogger<UpdateDocumentCommandHandler> logger
    )
    {
        _documentService = documentService;
        _logger = logger;
    }

    public async Task<Result<DocumentResponse>> Handle(
        UpdateDocumentCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Check if the document belongs to the user
            var document = await _documentService.GetDocumentByIdAsync(request.DocumentId);
            if (document.IsFailure)
            {
                return Result.Failure<DocumentResponse>(document.Error);
            }

            if (document.Value.UserId != request.UserId)
            {
                return Result.Failure<DocumentResponse>(
                    Error.Forbidden("You do not have permission to update this document")
                );
            }

            var result = await _documentService.UpdateDocumentAsync(
                request.File,
                request.DocumentId,
                request.DocumentType,
                request.Issuer,
                request.Country,
                request.CertificationType,
                request.OtherCertificationType,
                request.CertificationNumber,
                request.ExpiryDate
            );
            if (result.IsFailure)
            {
                return Result.Failure<DocumentResponse>(result.Error);
            }

            // Get the updated document
            var updatedDocument = await _documentService.GetDocumentByIdAsync(request.DocumentId);
            if (updatedDocument.IsFailure)
            {
                return Result.Failure<DocumentResponse>(updatedDocument.Error);
            }

            return Result.Success(updatedDocument.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document {DocumentId}", request.DocumentId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while updating the document")
            );
        }
    }
}
