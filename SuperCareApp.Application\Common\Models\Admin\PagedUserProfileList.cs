﻿using SuperCareApp.Application.Shared.Utility;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Paginated list of user profiles for admin
    /// </summary>
    public class PagedUserProfileList
    {
        /// <summary>
        /// List of user profiles
        /// </summary>
        public List<AdminUserProfileResponse> Profiles { get; set; } =
            new List<AdminUserProfileResponse>();

        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; } = 0;

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; } = 0;

        /// <summary>
        /// Converts this paged list to a PaginationMetadata object
        /// </summary>
        public PaginationMetadata ToMetadata()
        {
            return new PaginationMetadata(PageNumber, TotalPages, TotalCount, PageSize);
        }
    }
}
