﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities
{
    public class Notification : BaseEntity
    {
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public NotificationType NotificationType { get; set; }
        public string? ActionUrl { get; set; }

        // Navigation property
        public ICollection<UserNotification> UserNotifications { get; set; } =
            new List<UserNotification>();
    }
}
