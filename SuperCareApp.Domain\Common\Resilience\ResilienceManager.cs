﻿using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Domain.Common.Resilience
{
    /// <summary>
    /// Configuration for resilience patterns
    /// </summary>
    public class ResilienceConfig
    {
        public bool UseRetry { get; set; } = true;
        public int MaxRetries { get; set; } = 3;
        public IEnumerable<Type>? RetryableExceptions { get; set; }

        public bool UseCircuitBreaker { get; set; } = true;
        public int FailureThreshold { get; set; } = 5;
        public TimeSpan? ResetTimeout { get; set; }

        public bool UseTimeout { get; set; } = true;
        public int TimeoutMilliseconds { get; set; } = 30000;

        public bool UseBulkhead { get; set; } = true;
        public int MaxConcurrentOperations { get; set; } = 10;
        public string? PartitionKey { get; set; }
        public int MaxConcurrentOperationsPerPartition { get; set; } = 5;

        public static ResilienceConfig Default => new ResilienceConfig();
    }

    /// <summary>
    /// Manages resilience patterns for operations
    /// </summary>
    public class ResilienceManager
    {
        private readonly CircuitBreaker _circuitBreaker;
        private readonly Bulkhead _bulkhead;
        private readonly ResilienceConfig _config;

        public ResilienceManager(ResilienceConfig? config = null)
        {
            _config = config ?? ResilienceConfig.Default;
            _circuitBreaker = new CircuitBreaker(_config.FailureThreshold, _config.ResetTimeout);
            _bulkhead = new Bulkhead(_config.MaxConcurrentOperations);
        }

        /// <summary>
        /// Executes an operation with configured resilience patterns
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result<T>> ExecuteAsync<T>(
            Func<CancellationToken, Task<Result<T>>> operation
        )
        {
            Func<Task<Result<T>>> wrappedOperation = async () =>
            {
                if (_config.UseTimeout)
                {
                    return await Timeout.ExecuteWithTimeoutAsync(
                        operation,
                        _config.TimeoutMilliseconds
                    );
                }
                else
                {
                    return await operation(CancellationToken.None);
                }
            };

            if (_config.UseCircuitBreaker)
            {
                wrappedOperation = () => _circuitBreaker.ExecuteAsync(wrappedOperation);
            }

            if (_config.UseBulkhead)
            {
                if (!string.IsNullOrEmpty(_config.PartitionKey))
                {
                    wrappedOperation = () =>
                        _bulkhead.ExecutePartitionedAsync(
                            _config.PartitionKey!,
                            wrappedOperation,
                            _config.MaxConcurrentOperationsPerPartition
                        );
                }
                else
                {
                    wrappedOperation = () => _bulkhead.ExecuteAsync(wrappedOperation);
                }
            }

            if (_config.UseRetry)
            {
                return await RetryPolicy.ExecuteWithRetryAsync(
                    wrappedOperation,
                    _config.MaxRetries,
                    _config.RetryableExceptions
                );
            }
            else
            {
                return await wrappedOperation();
            }
        }

        /// <summary>
        /// Executes an operation with configured resilience patterns
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result> ExecuteAsync(Func<CancellationToken, Task<Result>> operation)
        {
            Func<Task<Result>> wrappedOperation = async () =>
            {
                if (_config.UseTimeout)
                {
                    return await Timeout.ExecuteWithTimeoutAsync(
                        operation,
                        _config.TimeoutMilliseconds
                    );
                }
                else
                {
                    return await operation(CancellationToken.None);
                }
            };

            if (_config.UseCircuitBreaker)
            {
                wrappedOperation = () => _circuitBreaker.ExecuteAsync(wrappedOperation);
            }

            if (_config.UseBulkhead)
            {
                if (!string.IsNullOrEmpty(_config.PartitionKey))
                {
                    wrappedOperation = () =>
                        _bulkhead.ExecutePartitionedAsync(
                            _config.PartitionKey!,
                            wrappedOperation,
                            _config.MaxConcurrentOperationsPerPartition
                        );
                }
                else
                {
                    wrappedOperation = () => _bulkhead.ExecuteAsync(wrappedOperation);
                }
            }

            if (_config.UseRetry)
            {
                return await RetryPolicy.ExecuteWithRetryAsync(
                    wrappedOperation,
                    _config.MaxRetries,
                    _config.RetryableExceptions
                );
            }
            else
            {
                return await wrappedOperation();
            }
        }

        /// <summary>
        /// Creates a new resilience manager with a specific configuration
        /// </summary>
        /// <param name="configAction">Action to configure the resilience patterns</param>
        /// <returns>A new resilience manager</returns>
        public static ResilienceManager Create(Action<ResilienceConfig> configAction)
        {
            var config = new ResilienceConfig();
            configAction(config);
            return new ResilienceManager(config);
        }
    }
}
