# 📅 Calendar API Integration Guide

## Overview

This document provides comprehensive integration guidelines for the SuperCare Calendar API endpoints. The Calendar API offers 8 powerful endpoints for managing provider schedules, checking availability, and finding available providers.

## 🔗 Base URL

```
Production: https://api.supercare.com
Staging: https://staging-api.supercare.com
Development: https://localhost:7001
```

## 🔐 Authentication

All Calendar API endpoints require authentication. Include the JWT token in the Authorization header:

```http
Authorization: Bearer {your-jwt-token}
```

## 📚 API Endpoints Overview

| Endpoint                                             | Method | Purpose               | Use Case            |
| ---------------------------------------------------- | ------ | --------------------- | ------------------- |
| `/api/calendar/monthly/{year}/{month}`               | GET    | Monthly calendar view | Provider dashboard  |
| `/api/calendar/range/{providerId}`                   | GET    | Date range calendar   | Custom date views   |
| `/api/calendar/day/{providerId}/{date}`              | GET    | Single day details    | Day view modal      |
| `/api/calendar/next-available/{providerId}`          | GET    | Next available slots  | Quick booking       |
| `/api/calendar/check-availability/{providerId}`      | GET    | Check specific slot   | Booking validation  |
| `/api/calendar/summary/{providerId}/{year}/{month}`  | GET    | Calendar with stats   | Analytics dashboard |
| `/api/calendar/available-providers`                  | GET    | Find providers        | Provider search     |
| `/api/calendar/filtered/{providerId}/{year}/{month}` | GET    | Filtered calendar     | Custom filtering    |

## 🎯 Frontend Integration Guide

### 1. Monthly Calendar View

**Use Case:** Provider dashboard showing monthly schedule

**Endpoint:** `GET /api/calendar/monthly/{year}/{month}`

**Frontend Implementation:**

```javascript
// Calendar service
class CalendarService {
  async getMonthlyCalendar(year, month) {
    try {
      const response = await fetch(`/api/calendar/monthly/${year}/${month}`, {
        headers: {
          Authorization: `Bearer ${this.getToken()}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("Error fetching monthly calendar:", error);
      throw error;
    }
  }
}

// React component example
const MonthlyCalendar = () => {
  const [calendarData, setCalendarData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());

  useEffect(() => {
    loadCalendarData();
  }, [currentDate]);

  const loadCalendarData = async () => {
    try {
      setLoading(true);
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;

      const data = await calendarService.getMonthlyCalendar(year, month);
      setCalendarData(data);
    } catch (error) {
      showErrorMessage("Failed to load calendar data");
    } finally {
      setLoading(false);
    }
  };

  const renderCalendarDay = (day) => (
    <div
      key={day.date}
      className={`calendar-day ${
        day.isAvailable ? "available" : "unavailable"
      } ${day.isOnLeave ? "on-leave" : ""}`}
    >
      <div className="day-number">{new Date(day.date).getDate()}</div>
      <div className="day-stats">
        <span className="available-slots">{day.availableSlots}</span>
        <span className="booked-slots">{day.bookedSlots}</span>
      </div>
      {day.isOnLeave && <div className="leave-indicator">🏖️</div>}
    </div>
  );

  return (
    <div className="monthly-calendar">
      <div className="calendar-header">
        <h2>
          {calendarData?.monthName} {calendarData?.year}
        </h2>
        <div className="calendar-stats">
          <div>Available Days: {calendarData?.statistics.availableDays}</div>
          <div>
            Utilization: {calendarData?.statistics.utilizationRate.toFixed(1)}%
          </div>
          <div>Revenue: ${calendarData?.statistics.totalRevenue}</div>
        </div>
      </div>

      <div className="calendar-grid">
        {calendarData?.days.map(renderCalendarDay)}
      </div>
    </div>
  );
};
```

**Response Structure:**

```json
{
  "success": true,
  "data": {
    "year": 2024,
    "month": 12,
    "monthName": "December",
    "days": [
      {
        "date": "2024-12-01T00:00:00Z",
        "dayOfWeek": "Sunday",
        "isAvailable": true,
        "isOnLeave": false,
        "totalSlots": 8,
        "availableSlots": 6,
        "bookedSlots": 2,
        "slots": [
          {
            "startTime": "09:00",
            "endTime": "10:00",
            "isAvailable": true,
            "isBooked": false
          }
        ],
        "bookings": [
          {
            "id": "booking-id",
            "startTime": "10:00",
            "endTime": "11:00",
            "clientName": "John Doe",
            "categoryName": "Home Care",
            "status": "Confirmed",
            "amount": 75.0
          }
        ]
      }
    ],
    "statistics": {
      "totalDays": 31,
      "availableDays": 25,
      "leaveDays": 3,
      "totalSlots": 248,
      "bookedSlots": 156,
      "availableSlots": 92,
      "totalRevenue": 11700.0,
      "utilizationRate": 62.9
    }
  }
}
```

### 2. Provider Search & Booking

**Use Case:** Client searching for available providers

**Endpoint:** `GET /api/calendar/available-providers`

**Frontend Implementation:**

```javascript
// Provider search component
const ProviderSearch = () => {
  const [searchParams, setSearchParams] = useState({
    date: "",
    startTime: "",
    endTime: "",
    categoryId: "",
    location: "",
    maxPrice: "",
  });
  const [providers, setProviders] = useState([]);
  const [loading, setLoading] = useState(false);

  const searchProviders = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      Object.entries(searchParams).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await fetch(
        `/api/calendar/available-providers?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();
      if (result.success) {
        setProviders(result.data.providers);
      }
    } catch (error) {
      showErrorMessage("Failed to search providers");
    } finally {
      setLoading(false);
    }
  };

  const renderProvider = (provider) => (
    <div key={provider.id} className="provider-card">
      <div className="provider-info">
        <img src={provider.profilePicture} alt={provider.fullName} />
        <div>
          <h3>{provider.fullName}</h3>
          <div className="rating">
            ⭐ {provider.rating} ({provider.reviewCount} reviews)
          </div>
          <div className="location">📍 {provider.location}</div>
          <div className="categories">
            {provider.categories.map((cat) => (
              <span key={cat} className="category-tag">
                {cat}
              </span>
            ))}
          </div>
        </div>
      </div>

      <div className="available-slots">
        <h4>Available Times:</h4>
        {provider.availableSlots.map((slot) => (
          <button
            key={`${slot.startTime}-${slot.endTime}`}
            className="time-slot-btn"
            onClick={() => selectTimeSlot(provider.id, slot)}
          >
            {slot.startTime} - {slot.endTime}
          </button>
        ))}
      </div>

      <div className="provider-actions">
        <div className="price">${provider.hourlyRate}/hour</div>
        <button
          className="book-btn"
          onClick={() => initiateBooking(provider.id)}
        >
          Book Now
        </button>
      </div>
    </div>
  );

  return (
    <div className="provider-search">
      <div className="search-form">
        <input
          type="date"
          value={searchParams.date}
          onChange={(e) =>
            setSearchParams({ ...searchParams, date: e.target.value })
          }
          min={new Date().toISOString().split("T")[0]}
        />
        <input
          type="time"
          value={searchParams.startTime}
          onChange={(e) =>
            setSearchParams({ ...searchParams, startTime: e.target.value })
          }
          placeholder="Start Time"
        />
        <input
          type="time"
          value={searchParams.endTime}
          onChange={(e) =>
            setSearchParams({ ...searchParams, endTime: e.target.value })
          }
          placeholder="End Time"
        />
        <select
          value={searchParams.categoryId}
          onChange={(e) =>
            setSearchParams({ ...searchParams, categoryId: e.target.value })
          }
        >
          <option value="">All Categories</option>
          <option value="cat-1">Home Care</option>
          <option value="cat-2">Medical Care</option>
        </select>
        <input
          type="text"
          value={searchParams.location}
          onChange={(e) =>
            setSearchParams({ ...searchParams, location: e.target.value })
          }
          placeholder="Location"
        />
        <input
          type="number"
          value={searchParams.maxPrice}
          onChange={(e) =>
            setSearchParams({ ...searchParams, maxPrice: e.target.value })
          }
          placeholder="Max Price"
        />
        <button onClick={searchProviders} disabled={loading}>
          {loading ? "Searching..." : "Search Providers"}
        </button>
      </div>

      <div className="search-results">
        {providers.length === 0 && !loading && (
          <div className="no-results">
            No providers available for selected criteria
          </div>
        )}
        {providers.map(renderProvider)}
      </div>
    </div>
  );
};
```

### 3. Availability Checking

**Use Case:** Validate time slot before booking

**Endpoint:** `GET /api/calendar/check-availability/{providerId}`

**Frontend Implementation:**

```javascript
// Availability checker utility
class AvailabilityChecker {
  async checkAvailability(providerId, date, startTime, endTime) {
    const params = new URLSearchParams({
      date: date,
      startTime: startTime,
      endTime: endTime,
    });

    try {
      const response = await fetch(
        `/api/calendar/check-availability/${providerId}?${params}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("Error checking availability:", error);
      throw error;
    }
  }
}

// Booking form component
const BookingForm = ({ providerId }) => {
  const [bookingData, setBookingData] = useState({
    date: "",
    startTime: "",
    endTime: "",
  });
  const [availabilityCheck, setAvailabilityCheck] = useState(null);
  const [checking, setChecking] = useState(false);

  const checkTimeSlot = async () => {
    if (!bookingData.date || !bookingData.startTime || !bookingData.endTime) {
      return;
    }

    try {
      setChecking(true);
      const checker = new AvailabilityChecker();
      const result = await checker.checkAvailability(
        providerId,
        bookingData.date,
        bookingData.startTime,
        bookingData.endTime
      );
      setAvailabilityCheck(result);
    } catch (error) {
      showErrorMessage("Failed to check availability");
    } finally {
      setChecking(false);
    }
  };

  const renderAvailabilityResult = () => {
    if (!availabilityCheck) return null;

    if (availabilityCheck.isAvailable) {
      return (
        <div className="availability-success">
          ✅ Time slot is available!
          <button className="confirm-booking-btn" onClick={confirmBooking}>
            Confirm Booking
          </button>
        </div>
      );
    } else {
      return (
        <div className="availability-error">
          ❌ {availabilityCheck.reason}
          {availabilityCheck.conflicts.length > 0 && (
            <div className="conflicts">
              <h4>Conflicts:</h4>
              <ul>
                {availabilityCheck.conflicts.map((conflict, index) => (
                  <li key={index}>{conflict}</li>
                ))}
              </ul>
            </div>
          )}
          {availabilityCheck.alternativeSlots.length > 0 && (
            <div className="alternatives">
              <h4>Alternative Times:</h4>
              <div className="alternative-slots">
                {availabilityCheck.alternativeSlots.map((slot) => (
                  <button
                    key={`${slot.startTime}-${slot.endTime}`}
                    className="alternative-slot-btn"
                    onClick={() => selectAlternative(slot)}
                  >
                    {slot.startTime} - {slot.endTime}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }
  };

  return (
    <div className="booking-form">
      <div className="form-fields">
        <input
          type="date"
          value={bookingData.date}
          onChange={(e) =>
            setBookingData({ ...bookingData, date: e.target.value })
          }
          min={new Date().toISOString().split("T")[0]}
        />
        <input
          type="time"
          value={bookingData.startTime}
          onChange={(e) =>
            setBookingData({ ...bookingData, startTime: e.target.value })
          }
        />
        <input
          type="time"
          value={bookingData.endTime}
          onChange={(e) =>
            setBookingData({ ...bookingData, endTime: e.target.value })
          }
        />
        <button onClick={checkTimeSlot} disabled={checking}>
          {checking ? "Checking..." : "Check Availability"}
        </button>
      </div>

      {renderAvailabilityResult()}
    </div>
  );
};
```

### 4. Quick Booking - Next Available Slots

**Use Case:** Show next available time slots for quick booking

**Endpoint:** `GET /api/calendar/next-available/{providerId}`

**Frontend Implementation:**

```javascript
// Quick booking component
const QuickBooking = ({ providerId }) => {
  const [availableSlots, setAvailableSlots] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({
    startDate: new Date().toISOString().split("T")[0],
    durationMinutes: 60,
    maxResults: 10,
    maxDaysAhead: 14,
  });

  const loadNextAvailableSlots = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams(searchParams);

      const response = await fetch(
        `/api/calendar/next-available/${providerId}?${params}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();
      if (result.success) {
        setAvailableSlots(result.data.slots);
      }
    } catch (error) {
      showErrorMessage("Failed to load available slots");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNextAvailableSlots();
  }, [providerId, searchParams]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
    });
  };

  const bookSlot = async (slot) => {
    try {
      // Implement booking logic here
      const bookingData = {
        providerId: providerId,
        date: slot.date,
        startTime: slot.startTime,
        endTime: slot.endTime,
        durationMinutes: slot.durationMinutes,
      };

      // Call booking API
      await createBooking(bookingData);
      showSuccessMessage("Booking created successfully!");
    } catch (error) {
      showErrorMessage("Failed to create booking");
    }
  };

  return (
    <div className="quick-booking">
      <div className="search-controls">
        <h3>Next Available Slots</h3>
        <div className="controls">
          <select
            value={searchParams.durationMinutes}
            onChange={(e) =>
              setSearchParams({
                ...searchParams,
                durationMinutes: parseInt(e.target.value),
              })
            }
          >
            <option value={30}>30 minutes</option>
            <option value={60}>1 hour</option>
            <option value={90}>1.5 hours</option>
            <option value={120}>2 hours</option>
          </select>

          <select
            value={searchParams.maxDaysAhead}
            onChange={(e) =>
              setSearchParams({
                ...searchParams,
                maxDaysAhead: parseInt(e.target.value),
              })
            }
          >
            <option value={7}>Next 7 days</option>
            <option value={14}>Next 2 weeks</option>
            <option value={30}>Next month</option>
          </select>
        </div>
      </div>

      <div className="available-slots-list">
        {loading ? (
          <div className="loading">Loading available slots...</div>
        ) : availableSlots.length === 0 ? (
          <div className="no-slots">No available slots found</div>
        ) : (
          availableSlots.map((slot, index) => (
            <div key={index} className="slot-card">
              <div className="slot-info">
                <div className="slot-date">{formatDate(slot.date)}</div>
                <div className="slot-day">{slot.dayOfWeek}</div>
                <div className="slot-time">
                  {slot.startTime} - {slot.endTime}
                </div>
                <div className="slot-duration">
                  {slot.durationMinutes} minutes
                </div>
              </div>
              <button className="book-slot-btn" onClick={() => bookSlot(slot)}>
                Book Now
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
```

### 5. Day View Modal

**Use Case:** Detailed view of a specific day

**Endpoint:** `GET /api/calendar/day/{providerId}/{date}`

**Frontend Implementation:**

```javascript
// Day view modal component
const DayViewModal = ({ providerId, date, isOpen, onClose }) => {
  const [dayData, setDayData] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadDayData = async () => {
    if (!isOpen || !date) return;

    try {
      setLoading(true);
      const formattedDate = date.toISOString().split("T")[0];

      const response = await fetch(
        `/api/calendar/day/${providerId}/${formattedDate}`,
        {
          headers: {
            Authorization: `Bearer ${getToken()}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();
      if (result.success) {
        setDayData(result.data);
      }
    } catch (error) {
      showErrorMessage("Failed to load day data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDayData();
  }, [isOpen, date, providerId]);

  const renderTimeSlot = (slot) => (
    <div
      key={`${slot.startTime}-${slot.endTime}`}
      className={`time-slot ${slot.isBooked ? "booked" : "available"}`}
    >
      <div className="slot-time">
        {slot.startTime} - {slot.endTime}
      </div>
      <div className="slot-status">
        {slot.isBooked ? "🔒 Booked" : "✅ Available"}
      </div>
      {slot.bookingId && (
        <div className="booking-info">Booking ID: {slot.bookingId}</div>
      )}
    </div>
  );

  const renderBooking = (booking) => (
    <div key={booking.id} className="booking-card">
      <div className="booking-header">
        <div className="booking-time">
          {booking.startTime} - {booking.endTime}
        </div>
        <div
          className={`booking-status status-${booking.status.toLowerCase()}`}
        >
          {booking.status}
        </div>
      </div>
      <div className="booking-details">
        <div className="client-name">👤 {booking.clientName}</div>
        <div className="category">🏷️ {booking.categoryName}</div>
        <div className="amount">💰 ${booking.amount}</div>
        {booking.specialInstructions && (
          <div className="instructions">📝 {booking.specialInstructions}</div>
        )}
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="day-view-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            {date?.toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </h2>
          <button className="close-btn" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-content">
          {loading ? (
            <div className="loading">Loading day details...</div>
          ) : dayData ? (
            <>
              <div className="day-summary">
                <div className="availability-status">
                  <span
                    className={`status-indicator ${
                      dayData.isAvailable ? "available" : "unavailable"
                    }`}
                  >
                    {dayData.isOnLeave
                      ? "🏖️ On Leave"
                      : dayData.isAvailable
                      ? "✅ Available"
                      : "❌ Unavailable"}
                  </span>
                </div>
                <div className="day-stats">
                  <div className="stat">
                    <span className="label">Total Slots:</span>
                    <span className="value">{dayData.totalSlots}</span>
                  </div>
                  <div className="stat">
                    <span className="label">Available:</span>
                    <span className="value">{dayData.availableSlots}</span>
                  </div>
                  <div className="stat">
                    <span className="label">Booked:</span>
                    <span className="value">{dayData.bookedSlots}</span>
                  </div>
                </div>
              </div>

              <div className="day-sections">
                <div className="time-slots-section">
                  <h3>Time Slots</h3>
                  <div className="time-slots-grid">
                    {dayData.slots.map(renderTimeSlot)}
                  </div>
                </div>

                {dayData.bookings.length > 0 && (
                  <div className="bookings-section">
                    <h3>Bookings ({dayData.bookings.length})</h3>
                    <div className="bookings-list">
                      {dayData.bookings.map(renderBooking)}
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="error">Failed to load day data</div>
          )}
        </div>
      </div>
    </div>
  );
};
```

## 🔧 Error Handling Best Practices

### 1. HTTP Status Codes

| Status Code | Meaning      | Action                     |
| ----------- | ------------ | -------------------------- |
| `200`       | Success      | Process response data      |
| `400`       | Bad Request  | Show validation errors     |
| `401`       | Unauthorized | Redirect to login          |
| `403`       | Forbidden    | Show permission error      |
| `404`       | Not Found    | Show "not found" message   |
| `500`       | Server Error | Show generic error message |

### 2. Error Response Structure

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Validation failed",
    "details": [
      {
        "field": "date",
        "message": "Date must be in the future"
      }
    ]
  }
}
```

### 3. Frontend Error Handling

```javascript
// Centralized error handler
class ApiErrorHandler {
  static handle(error, response) {
    if (response?.status === 401) {
      // Redirect to login
      window.location.href = "/login";
      return;
    }

    if (response?.status === 403) {
      showErrorMessage("You do not have permission to perform this action");
      return;
    }

    if (response?.status === 404) {
      showErrorMessage("The requested resource was not found");
      return;
    }

    if (response?.status >= 500) {
      showErrorMessage("Server error. Please try again later.");
      return;
    }

    // Handle validation errors
    if (error?.error?.details) {
      const validationErrors = error.error.details
        .map((detail) => detail.message)
        .join(", ");
      showErrorMessage(`Validation Error: ${validationErrors}`);
      return;
    }

    // Generic error
    showErrorMessage(error?.error?.message || "An unexpected error occurred");
  }
}

// Enhanced fetch wrapper
async function apiCall(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        Authorization: `Bearer ${getToken()}`,
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      ApiErrorHandler.handle(data, response);
      throw new Error(data.error?.message || "API call failed");
    }

    return data;
  } catch (error) {
    if (error.name === "TypeError" && error.message.includes("fetch")) {
      showErrorMessage("Network error. Please check your connection.");
    }
    throw error;
  }
}
```

## 🧪 Testing Guide

### 1. Unit Testing Examples

```javascript
// Jest test examples
describe("CalendarService", () => {
  let calendarService;

  beforeEach(() => {
    calendarService = new CalendarService();
    // Mock fetch
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  test("should fetch monthly calendar successfully", async () => {
    const mockResponse = {
      success: true,
      data: {
        year: 2024,
        month: 12,
        monthName: "December",
        days: [],
        statistics: {},
      },
    };

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const result = await calendarService.getMonthlyCalendar(2024, 12);

    expect(fetch).toHaveBeenCalledWith(
      "/api/calendar/monthly/2024/12",
      expect.objectContaining({
        headers: expect.objectContaining({
          Authorization: expect.stringContaining("Bearer"),
          "Content-Type": "application/json",
        }),
      })
    );

    expect(result).toEqual(mockResponse.data);
  });

  test("should handle API errors gracefully", async () => {
    const mockErrorResponse = {
      success: false,
      error: {
        code: "NOT_FOUND",
        message: "Provider not found",
      },
    };

    fetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
      json: async () => mockErrorResponse,
    });

    await expect(calendarService.getMonthlyCalendar(2024, 12)).rejects.toThrow(
      "Provider not found"
    );
  });

  test("should validate date parameters", async () => {
    await expect(
      calendarService.getMonthlyCalendar(2019, 12) // Invalid year
    ).rejects.toThrow("Year must be between 2020 and 2030");

    await expect(
      calendarService.getMonthlyCalendar(2024, 13) // Invalid month
    ).rejects.toThrow("Month must be between 1 and 12");
  });
});

// React component testing
describe("MonthlyCalendar Component", () => {
  test("should render calendar data correctly", async () => {
    const mockCalendarData = {
      year: 2024,
      month: 12,
      monthName: "December",
      days: [
        {
          date: "2024-12-01T00:00:00Z",
          dayOfWeek: "Sunday",
          isAvailable: true,
          isOnLeave: false,
          totalSlots: 8,
          availableSlots: 6,
          bookedSlots: 2,
        },
      ],
      statistics: {
        totalDays: 31,
        availableDays: 25,
        utilizationRate: 62.9,
      },
    };

    // Mock the calendar service
    jest
      .spyOn(calendarService, "getMonthlyCalendar")
      .mockResolvedValue(mockCalendarData);

    render(<MonthlyCalendar />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText("December 2024")).toBeInTheDocument();
      expect(screen.getByText("Available Days: 25")).toBeInTheDocument();
      expect(screen.getByText("Utilization: 62.9%")).toBeInTheDocument();
    });
  });

  test("should handle loading state", () => {
    render(<MonthlyCalendar />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  test("should handle error state", async () => {
    jest
      .spyOn(calendarService, "getMonthlyCalendar")
      .mockRejectedValue(new Error("API Error"));

    render(<MonthlyCalendar />);

    await waitFor(() => {
      expect(
        screen.getByText(/Failed to load calendar data/)
      ).toBeInTheDocument();
    });
  });
});
```

### 2. Integration Testing

```javascript
// Cypress integration tests
describe("Calendar Integration Tests", () => {
  beforeEach(() => {
    // Login and set up authentication
    cy.login("<EMAIL>", "password");
    cy.visit("/dashboard/calendar");
  });

  it("should display monthly calendar", () => {
    cy.get('[data-testid="monthly-calendar"]').should("be.visible");
    cy.get('[data-testid="calendar-month-year"]').should("contain", "2024");
    cy.get('[data-testid="calendar-stats"]').should("be.visible");
  });

  it("should navigate between months", () => {
    cy.get('[data-testid="prev-month-btn"]').click();
    cy.get('[data-testid="calendar-month-year"]').should("contain", "November");

    cy.get('[data-testid="next-month-btn"]').click();
    cy.get('[data-testid="calendar-month-year"]').should("contain", "December");
  });

  it("should open day view modal", () => {
    cy.get('[data-testid="calendar-day"]').first().click();
    cy.get('[data-testid="day-view-modal"]').should("be.visible");
    cy.get('[data-testid="day-bookings"]').should("exist");
  });

  it("should search for available providers", () => {
    cy.visit("/search/providers");

    cy.get('[data-testid="search-date"]').type("2024-12-15");
    cy.get('[data-testid="search-start-time"]').type("10:00");
    cy.get('[data-testid="search-end-time"]').type("11:00");
    cy.get('[data-testid="search-btn"]').click();

    cy.get('[data-testid="provider-results"]').should("be.visible");
    cy.get('[data-testid="provider-card"]').should(
      "have.length.greaterThan",
      0
    );
  });

  it("should check availability before booking", () => {
    cy.visit("/booking/new");

    cy.get('[data-testid="provider-select"]').select("Provider Name");
    cy.get('[data-testid="booking-date"]').type("2024-12-15");
    cy.get('[data-testid="booking-start-time"]').type("10:00");
    cy.get('[data-testid="booking-end-time"]').type("11:00");

    cy.get('[data-testid="check-availability-btn"]').click();

    cy.get('[data-testid="availability-result"]').should("be.visible");
    cy.get('[data-testid="availability-status"]').should(
      "contain",
      "Available"
    );
  });
});
```

### 3. API Testing with Postman/Newman

```json
{
  "info": {
    "name": "Calendar API Tests",
    "description": "Test suite for Calendar API endpoints"
  },
  "item": [
    {
      "name": "Get Monthly Calendar",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200', function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test('Response has required fields', function () {",
              "    const jsonData = pm.response.json();",
              "    pm.expect(jsonData).to.have.property('success', true);",
              "    pm.expect(jsonData.data).to.have.property('year');",
              "    pm.expect(jsonData.data).to.have.property('month');",
              "    pm.expect(jsonData.data).to.have.property('days');",
              "    pm.expect(jsonData.data).to.have.property('statistics');",
              "});",
              "",
              "pm.test('Days array contains valid data', function () {",
              "    const jsonData = pm.response.json();",
              "    const days = jsonData.data.days;",
              "    pm.expect(days).to.be.an('array');",
              "    ",
              "    if (days.length > 0) {",
              "        const firstDay = days[0];",
              "        pm.expect(firstDay).to.have.property('date');",
              "        pm.expect(firstDay).to.have.property('dayOfWeek');",
              "        pm.expect(firstDay).to.have.property('isAvailable');",
              "        pm.expect(firstDay).to.have.property('totalSlots');",
              "    }",
              "});"
            ]
          }
        }
      ],
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}"
          }
        ],
        "url": {
          "raw": "{{baseUrl}}/api/calendar/monthly/2024/12",
          "host": ["{{baseUrl}}"],
          "path": ["api", "calendar", "monthly", "2024", "12"]
        }
      }
    },
    {
      "name": "Check Availability",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200', function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test('Availability check response is valid', function () {",
              "    const jsonData = pm.response.json();",
              "    pm.expect(jsonData.data).to.have.property('isAvailable');",
              "    pm.expect(jsonData.data).to.have.property('reason');",
              "    pm.expect(jsonData.data).to.have.property('conflicts');",
              "    pm.expect(jsonData.data).to.have.property('alternativeSlots');",
              "});"
            ]
          }
        }
      ],
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}"
          }
        ],
        "url": {
          "raw": "{{baseUrl}}/api/calendar/check-availability/{{providerId}}?date=2024-12-15&startTime=10:00&endTime=11:00",
          "host": ["{{baseUrl}}"],
          "path": ["api", "calendar", "check-availability", "{{providerId}}"],
          "query": [
            { "key": "date", "value": "2024-12-15" },
            { "key": "startTime", "value": "10:00" },
            { "key": "endTime", "value": "11:00" }
          ]
        }
      }
    }
  ]
}
```

### 4. Load Testing

```javascript
// Artillery.js load test configuration
module.exports = {
  config: {
    target: "https://api.supercare.com",
    phases: [
      { duration: 60, arrivalRate: 10 }, // Warm up
      { duration: 120, arrivalRate: 50 }, // Ramp up
      { duration: 300, arrivalRate: 100 }, // Sustained load
    ],
    defaults: {
      headers: {
        Authorization: "Bearer {{authToken}}",
        "Content-Type": "application/json",
      },
    },
  },
  scenarios: [
    {
      name: "Calendar API Load Test",
      weight: 100,
      flow: [
        {
          get: {
            url: "/api/calendar/monthly/2024/12",
            capture: {
              json: "$.data.year",
              as: "year",
            },
          },
        },
        {
          get: {
            url: "/api/calendar/available-providers?date=2024-12-15&durationMinutes=60",
          },
        },
        {
          get: {
            url: "/api/calendar/check-availability/{{providerId}}?date=2024-12-15&startTime=10:00&endTime=11:00",
          },
        },
      ],
    },
  ],
};
```

## ⚡ Performance Optimization

### 1. Caching Strategies

```javascript
// Service Worker caching for calendar data
const CACHE_NAME = "calendar-cache-v1";
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class CalendarCache {
  static async get(key) {
    const cache = await caches.open(CACHE_NAME);
    const response = await cache.match(key);

    if (response) {
      const data = await response.json();
      const timestamp = response.headers.get("cached-at");

      if (Date.now() - parseInt(timestamp) < CACHE_DURATION) {
        return data;
      }
    }

    return null;
  }

  static async set(key, data) {
    const cache = await caches.open(CACHE_NAME);
    const response = new Response(JSON.stringify(data), {
      headers: {
        "cached-at": Date.now().toString(),
        "content-type": "application/json",
      },
    });

    await cache.put(key, response);
  }
}

// Enhanced calendar service with caching
class CachedCalendarService extends CalendarService {
  async getMonthlyCalendar(year, month) {
    const cacheKey = `monthly-calendar-${year}-${month}`;

    // Try cache first
    const cached = await CalendarCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // Fetch from API
    const data = await super.getMonthlyCalendar(year, month);

    // Cache the result
    await CalendarCache.set(cacheKey, data);

    return data;
  }
}
```

### 2. Request Optimization

```javascript
// Debounced search for providers
import { debounce } from 'lodash';

const ProviderSearchOptimized = () => {
  const [searchParams, setSearchParams] = useState({});
  const [providers, setProviders] = useState([]);
  const [loading, setLoading] = useState(false);

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce(async (params) => {
      if (!params.date) return;

      try {
        setLoading(true);
        const result = await calendarService.getAvailableProviders(params);
        setProviders(result.providers);
      } catch (error) {
        showErrorMessage('Search failed');
      } finally {
        setLoading(false);
      }
    }, 500),
    []
  );

  useEffect(() => {
    debouncedSearch(searchParams);
  }, [searchParams, debouncedSearch]);

  return (
    // Component JSX
  );
};

// Request batching for multiple availability checks
class BatchedAvailabilityChecker {
  constructor() {
    this.pendingChecks = [];
    this.batchTimeout = null;
  }

  async checkAvailability(providerId, date, startTime, endTime) {
    return new Promise((resolve, reject) => {
      this.pendingChecks.push({
        providerId,
        date,
        startTime,
        endTime,
        resolve,
        reject
      });

      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout);
      }

      this.batchTimeout = setTimeout(() => {
        this.processBatch();
      }, 100); // Batch requests within 100ms
    });
  }

  async processBatch() {
    const checks = [...this.pendingChecks];
    this.pendingChecks = [];

    // Group by provider for efficient processing
    const groupedChecks = checks.reduce((groups, check) => {
      if (!groups[check.providerId]) {
        groups[check.providerId] = [];
      }
      groups[check.providerId].push(check);
      return groups;
    }, {});

    // Process each provider's checks
    for (const [providerId, providerChecks] of Object.entries(groupedChecks)) {
      try {
        // In a real implementation, you'd have a batch endpoint
        const results = await Promise.all(
          providerChecks.map(check =>
            this.singleAvailabilityCheck(check.providerId, check.date, check.startTime, check.endTime)
          )
        );

        providerChecks.forEach((check, index) => {
          check.resolve(results[index]);
        });
      } catch (error) {
        providerChecks.forEach(check => {
          check.reject(error);
        });
      }
    }
  }
}
```

### 3. Memory Management

```javascript
// Efficient calendar component with virtualization
import { FixedSizeList as List } from 'react-window';

const VirtualizedCalendarList = ({ days }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <CalendarDay day={days[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={days.length}
      itemSize={100}
      width="100%"
    >
      {Row}
    </List>
  );
};

// Cleanup and memory management
const CalendarComponent = () => {
  const [data, setData] = useState(null);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const loadData = async () => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch('/api/calendar/monthly/2024/12', {
        signal: abortControllerRef.current.signal,
        headers: {
          'Authorization': `Bearer ${getToken()}`
        }
      });

      const result = await response.json();
      setData(result.data);
    } catch (error) {
      if (error.name !== 'AbortError') {
        showErrorMessage('Failed to load data');
      }
    }
  };

  return (
    // Component JSX
  );
};
```

## 📋 Quick Reference

### API Endpoints Summary

```
GET /api/calendar/monthly/{year}/{month}
GET /api/calendar/range/{providerId}?startDate=2024-12-01&endDate=2024-12-31
GET /api/calendar/day/{providerId}/{date}
GET /api/calendar/next-available/{providerId}?durationMinutes=60&maxResults=10
GET /api/calendar/check-availability/{providerId}?date=2024-12-15&startTime=10:00&endTime=11:00
GET /api/calendar/summary/{providerId}/{year}/{month}
GET /api/calendar/available-providers?date=2024-12-15&categoryId=123
GET /api/calendar/filtered/{providerId}/{year}/{month}?status=Confirmed
```

### Common Query Parameters

| Parameter         | Type   | Description               | Example        |
| ----------------- | ------ | ------------------------- | -------------- |
| `date`            | string | Date in YYYY-MM-DD format | `2024-12-15`   |
| `startTime`       | string | Time in HH:mm format      | `10:00`        |
| `endTime`         | string | Time in HH:mm format      | `11:00`        |
| `durationMinutes` | number | Duration in minutes       | `60`           |
| `maxResults`      | number | Maximum results to return | `10`           |
| `categoryId`      | guid   | Care category filter      | `123e4567-...` |
| `location`        | string | Location filter           | `New York`     |
| `maxPrice`        | number | Maximum price filter      | `100.00`       |

### Response Status Codes

| Code | Meaning      | Action              |
| ---- | ------------ | ------------------- |
| 200  | Success      | Process data        |
| 400  | Bad Request  | Check parameters    |
| 401  | Unauthorized | Re-authenticate     |
| 403  | Forbidden    | Check permissions   |
| 404  | Not Found    | Handle gracefully   |
| 500  | Server Error | Retry or show error |

### Frontend Checklist

- [ ] Implement authentication headers
- [ ] Add loading states
- [ ] Handle error responses
- [ ] Validate user inputs
- [ ] Cache frequently accessed data
- [ ] Implement proper cleanup
- [ ] Add accessibility features
- [ ] Test on different devices
- [ ] Optimize for performance
- [ ] Add proper error boundaries

### Testing Checklist

- [ ] Unit tests for services
- [ ] Component integration tests
- [ ] API endpoint tests
- [ ] Error handling tests
- [ ] Performance tests
- [ ] Accessibility tests
- [ ] Cross-browser tests
- [ ] Mobile responsiveness tests
- [ ] Load testing
- [ ] Security testing
