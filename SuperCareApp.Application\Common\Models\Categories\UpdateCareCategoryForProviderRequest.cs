using System.Text.Json.Serialization;
using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Categories;

public class UpdateCareCategoryForProviderRequest
{
    [JsonPropertyName("description")]
    public string? ProviderSpecificDescription { get; init; }
    public decimal? HourlyRate { get; init; }
    public int? ExperienceLevel { get; init; }
}

public class UpdateCareCategoryForProviderRequestValidation
    : AbstractValidator<UpdateCareCategoryForProviderRequest>
{
    public UpdateCareCategoryForProviderRequestValidation()
    {
        RuleFor(x => x.ProviderSpecificDescription)
            .MaximumLength(500)
            .WithMessage("Description cannot exceed 500 characters.")
            .When(x => x.ProviderSpecificDescription != null);

        RuleFor(x => x.HourlyRate)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Hourly rate must be a non-negative value.");

        RuleFor(x => x.ExperienceLevel)
            .InclusiveBetween(1, 5)
            .WithMessage("Experience level must be between 1 and 5.");
    }
}
