﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class UserAddress : BaseEntity
    {
        public Guid UserId { get; set; }
        public Guid AddressId { get; set; }
        public bool IsPrimary { get; set; }
        public string? Label { get; set; } // e.g., "Home", "Work", etc.

        // Navigation properties
        public ApplicationUser User { get; set; } = null!;
        public Address Address { get; set; } = null!;
    }
}
