﻿namespace super_care_app.Models.Doc;

/// <summary>
/// Examples response models for Account API documentation
/// </summary>
public class AccountExamples
{
    /// <summary>
    /// Returns a JSON example for the GetAllAccounts endpoint response
    /// </summary>
    /// <returns>A JSON string representing a sample response</returns>
    public static string GetAllAccountsExample()
    {
        return @"{
  ""apiResponseId"": ""bd563e0a-b9c2-40f7-b5b1-e080dec11540"",
  ""success"": true,
  ""statusCode"": 200,
  ""message"": ""Profiles retrieved successfully"",
  ""payload"": [
    {
      ""userId"": ""c1d2e3f4-a5b6-7788-9900-************"",
      ""providerId"": ""d2e3f4a5-b6c7-8899-0011-************"",
      ""firstName"": ""<PERSON>"",
      ""lastName"": ""<PERSON>"",
      ""email"": ""<EMAIL>"",
      ""phoneNumber"": ""+**********"",
      ""gender"": ""Male"",
      ""yearsExperience"": 15,
      ""providesRecurringBooking"": false,
      ""workingHours"": null,
      ""categories"": [
        {
          ""id"": ""b4c5d6e7-f8a9-0011-2233-ccddeeff2233"",
          ""name"": ""Elderly Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        },
        {
          ""id"": ""a3b4c5d6-e7f8-9900-1122-bbccddeeff22"",
          ""name"": ""Child Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        }
      ],
      ""dateOfBirth"": ""1979-12-03T00:00:00Z"",
      ""profilePictureUrl"": null,
      ""primaryAddress"": null,
      ""documents"": null
    },
    {
      ""userId"": ""c3d4e5f6-a7b8-9900-1122-ccddeeff0012"",
      ""providerId"": ""d4e5f6a7-b8c9-0011-2233-ddeeff001123"",
      ""firstName"": ""Mark"",
      ""lastName"": ""Walker"",
      ""email"": ""<EMAIL>"",
      ""phoneNumber"": ""+**********"",
      ""gender"": ""Male"",
      ""yearsExperience"": 13,
      ""providesRecurringBooking"": false,
      ""workingHours"": null,
      ""categories"": [
        {
          ""id"": ""c5d6e7f8-a9b0-1122-3344-ddeeff223344"",
          ""name"": ""Disability Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        },
        {
          ""id"": ""b4c5d6e7-f8a9-0011-2233-ccddeeff2233"",
          ""name"": ""Elderly Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        },
        {
          ""id"": ""a3b4c5d6-e7f8-9900-1122-bbccddeeff22"",
          ""name"": ""Child Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        }
      ],
      ""dateOfBirth"": ""1977-05-09T00:00:00Z"",
      ""profilePictureUrl"": null,
      ""primaryAddress"": null,
      ""documents"": null
    },
    {
      ""userId"": ""e7f8a9b0-c1d2-3344-5566-001122334455"",
      ""providerId"": ""f8a9b0c1-d2e3-4455-6677-112233445566"",
      ""firstName"": ""James"",
      ""lastName"": ""Martinez"",
      ""email"": ""<EMAIL>"",
      ""phoneNumber"": ""+**********"",
      ""gender"": ""Male"",
      ""yearsExperience"": 12,
      ""providesRecurringBooking"": false,
      ""workingHours"": null,
      ""categories"": [
        {
          ""id"": ""b4c5d6e7-f8a9-0011-2233-ccddeeff2233"",
          ""name"": ""Elderly Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        },
        {
          ""id"": ""c5d6e7f8-a9b0-1122-3344-ddeeff223344"",
          ""name"": ""Disability Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        },
        {
          ""id"": ""a3b4c5d6-e7f8-9900-1122-bbccddeeff22"",
          ""name"": ""Child Care"",
          ""hourlyRate"": 0,
          ""experiennceLevel"": 0
        }
      ],
      ""dateOfBirth"": ""1983-02-28T00:00:00Z"",
      ""profilePictureUrl"": null,
      ""primaryAddress"": null,
      ""documents"": null
    }
  ],
  ""meta"": {
    ""currentPage"": 1,
    ""totalPages"": 1,
    ""totalCount"": 3,
    ""pageSize"": 10,
    ""hasPreviousPage"": false,
    ""hasNextPage"": false
  },
  ""timestamp"": ""2025-07-02T11:57:54.6275482Z""
}";
    }

    /// <summary>
    /// Returns a JSON example for the GetSingleAccount endpoint response
    /// </summary>
    /// <returns>A JSON string representing a sample single account response</returns>
    public static string GetSingleAccountResponse()
    {
        return @"{
  ""apiResponseId"": ""********-c116-4818-938c-dfe2631b8c51"",
  ""success"": true,
  ""statusCode"": 200,
  ""message"": ""Profile retrieved successfully"",
  ""payload"": {
    ""userId"": ""a1b2c3d4-e5f6-7788-9900-aabbccddeef0"",
    ""providerId"": ""b2c3d4e5-f6a7-8899-0011-bbccddeeff01"",
    ""firstName"": ""Karen"",
    ""lastName"": ""Robinson"",
    ""email"": ""<EMAIL>"",
    ""phoneNumber"": ""+**********"",
    ""gender"": ""Female"",
    ""yearsExperience"": 5,
    ""providesRecurringBooking"": null,
    ""workingHours"": null,
    ""categories"": [
      {
        ""id"": ""a3b4c5d6-e7f8-9900-1122-bbccddeeff22"",
        ""name"": ""Child Care"",
        ""hourlyRate"": 0,
        ""experienceLevel"": 0
      },
      {
        ""id"": ""b4c5d6e7-f8a9-0011-2233-ccddeeff2233"",
        ""name"": ""Elderly Care"",
        ""hourlyRate"": 0,
        ""experienceLevel"": 0
      },
      {
        ""id"": ""c5d6e7f8-a9b0-1122-3344-ddeeff223344"",
        ""name"": ""Disability Care"",
        ""hourlyRate"": 0,
        ""experienceLevel"": 0
      }
    ],
    ""hourlyRate"": 27.5,
    ""dateOfBirth"": ""1992-11-14T00:00:00Z"",
    ""profilePictureUrl"": null,
    ""primaryAddress"": null,
    ""documents"": [],
    ""bio"": ""Dedicated caregiver with experience in medication management and health monitoring. Provides reliable and professional care."",
    ""providesOvernight"": true,
    ""providesLiveIn"": false,
    ""qualifications"": ""{\""education\"": \""Certificate in Healthcare\"", \""certifications\"": [\""Medication Administration\"", \""CPR\"", \""CNA\""]}"",
    ""ratingCount"": 0,
    ""travelExperience"": {
      ""willingToTravel"": true,
      ""maxTravelDistance"": null,
      ""preferredTransportation"": null,
      ""travelLocations"": null
    }
  },
  ""timestamp"": ""2025-07-03T07:04:44.7245192Z""
}";
    }

    /// <summary>
    /// Returns a JSON example for the GetAddresses endpoint response
    /// </summary>
    /// <returns>A JSON string representing a sample addresses response</returns>
    public static string GetAddresses()
    {
        return @"{
            ""apiResponseId"": ""46507dc4-d346-4ab8-b058-bb3cc8bd3d06"",
            ""success"": true,
            ""statusCode"": 200,
            ""message"": ""Addresses retrieved successfully"",
            ""payload"": [
                {
                    ""id"": ""1c163f00-d15f-4ffc-9082-eb53f3bf1ec3"",
                    ""streetAddress"": ""753 Willow Way"",
                    ""city"": ""San Diego"",
                    ""state"": ""CA"",
                    ""postalCode"": ""92101"",
                    ""latitude"": 32.7157,
                    ""longitude"": -117.1611,
                    ""isPrimary"": true,
                    ""label"": ""Home""
                }
            ],
            ""timestamp"": ""2025-05-27T09:30:00Z""
        }";
    }

    /// <summary>
    /// Returns a JSON example for the GetSingleAddress endpoint response
    /// </summary>
    /// <returns>A JSON string representing a sample single address response</returns>
    public static string GetSingleAddress()
    {
        return @"{
            ""apiResponseId"": ""26d6211b-c0ab-4ee4-b267-452d6ffa1936"",
            ""success"": true,
            ""statusCode"": 200,
            ""message"": ""Address retrieved successfully"",
            ""payload"": {
                ""id"": ""1c163f00-d15f-4ffc-9082-eb53f3bf1ec3"",
                ""streetAddress"": ""753 Willow Way"",
                ""city"": ""San Diego"",
                ""state"": ""CA"",
                ""postalCode"": ""92101"",
                ""latitude"": 32.7157,
                ""longitude"": -117.1611,
                ""isPrimary"": true,
                ""label"": ""Home""
            },
            ""timestamp"": ""2025-05-27T09:35:00Z""
        }";
    }

    /// <summary>
    /// Returns a JSON example for the GetDocumentsByUserId endpoint response
    /// </summary>
    /// <returns>A JSON string representing a sample documents response</returns>
    public static string GetDocumentsByUserIdResponse()
    {
        return @"{
            ""apiResponseId"": ""9a8b7c6d-5e4f-4c3b-b2a1-0f9e8d7c6b5a"",
            ""success"": true,
            ""statusCode"": 200,
            ""message"": ""Documents retrieved successfully"",
            ""payload"": [
                {
                    ""documentId"": ""d1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a"",
                    ""userId"": ""fc10c209-ede2-414e-8989-70a9c7b29cfd"",
                    ""fileName"": ""cna_certificate.pdf"",
                    ""mimeType"": ""application/pdf"",
                    ""documentUrl"": ""https://storage.supercareapp.com/documents/cna_certificate.pdf"",
                    ""documentType"": ""Certificate"",
                    ""verificationStatus"": ""Verified"",
                    ""uploadedAt"": ""2025-01-10T08:00:00Z"",
                    ""verifiedAt"": ""2025-01-12T10:30:00Z"",
                    ""verifiedBy"": ""a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d"",
                    ""rejectionReason"": null,
                    ""country"": ""USA"",
                    ""certificationType"": ""CNA"",
                    ""otherCertificationType"": null,
                    ""certificationNumber"": ""CNA-123456"",
                    ""expiryDate"": ""2027-01-10T00:00:00Z"",
                    ""isExpired"": false
                },
                {
                    ""documentId"": ""e2f3a4b5-c6d7-4e8f-9a0b-1c2d3e4f5a6b"",
                    ""userId"": ""fc10c209-ede2-414e-8989-70a9c7b29cfd"",
                    ""fileName"": ""first_aid_certificate.pdf"",
                    ""mimeType"": ""application/pdf"",
                    ""documentUrl"": ""https://storage.supercareapp.com/documents/first_aid_certificate.pdf"",
                    ""documentType"": ""Certificate"",
                    ""verificationStatus"": ""Rejected"",
                    ""uploadedAt"": ""2025-02-15T09:15:00Z"",
                    ""verifiedAt"": null,
                    ""verifiedBy"": null,
                    ""rejectionReason"": ""Document is blurry and illegible"",
                    ""country"": ""USA"",
                    ""certificationType"": ""Other"",
                    ""otherCertificationType"": ""First Aid"",
                    ""certificationNumber"": ""FA-789012"",
                    ""expiryDate"": null,
                    ""isExpired"": false
                }
            ],
            ""timestamp"": ""2025-05-27T09:46:00Z""
        }";
    }

    /// <summary>
    /// Returns a JSON example for the GetDocumentByUserId endpoint response
    /// </summary>
    /// <returns>A JSON string representing a sample single document response</returns>
    public static string GetDocumentByUserIdResponse()
    {
        return @"{
            ""apiResponseId"": ""3c4d5e6f-7a8b-4c9d-0e1f-2a3b4c5d6e7f"",
            ""success"": true,
            ""statusCode"": 200,
            ""message"": ""Document retrieved successfully"",
            ""payload"": {
                ""documentId"": ""d1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a"",
                ""userId"": ""fc10c209-ede2-414e-8989-70a9c7b29cfd"",
                ""fileName"": ""cna_certificate.pdf"",
                ""mimeType"": ""application/pdf"",
                ""documentUrl"": ""https://storage.supercareapp.com/documents/cna_certificate.pdf"",
                ""documentType"": ""Certificate"",
                ""verificationStatus"": ""Verified"",
                ""uploadedAt"": ""2025-01-10T08:00:00Z"",
                ""verifiedAt"": ""2025-01-12T10:30:00Z"",
                ""verifiedBy"": ""a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d"",
                ""rejectionReason"": null,
                ""country"": ""USA"",
                ""certificationType"": ""CNA"",
                ""otherCertificationType"": null,
                ""certificationNumber"": ""CNA-123456"",
                ""expiryDate"": ""2027-01-10T00:00:00Z"",
                ""isExpired"": false
            },
            ""timestamp"": ""2025-05-27T09:49:00Z""
        }";
    }
}
