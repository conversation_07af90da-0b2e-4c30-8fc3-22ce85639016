using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Otp;
using SuperCareApp.Application.Shared.Dispatcher;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

/// <summary>
/// Command to verify an OTP code
/// </summary>
/// <param name="Email">User's email address (optional if phone is provided)</param>
/// <param name="PhoneNumber">User's phone number (optional if email is provided)</param>
/// <param name="EmailOtpCode">The OTP code to verify for email (required if email is provided)</param>
/// <param name="PhoneOtpCode">The OTP code to verify for phone (required if phone is provided)</param>
/// <param name="VerificationType">Type of verification (e.g., "Registration", "PasswordReset")</param>
public record VerifyOtpCommand(
    string? Email,
    string? PhoneNumber,
    string? EmailOtpCode,
    string? PhoneOtpCode,
    string VerificationType
) : ICommand<Result<VerifyOtpResponse>>;

/// <summary>
/// Handler for the VerifyOtpCommand
/// </summary>
internal sealed class VerifyOtpCommandHandler
    : ICommandHandler<VerifyOtpCommand, Result<VerifyOtpResponse>>
{
    private readonly IOtpDispatcher _otpDispatcher;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<VerifyOtpCommandHandler> _logger;

    public VerifyOtpCommandHandler(
        IOtpDispatcher otpDispatcher,
        UserManager<ApplicationUser> userManager,
        ILogger<VerifyOtpCommandHandler> logger
    )
    {
        _otpDispatcher = otpDispatcher;
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<Result<VerifyOtpResponse>> Handle(
        VerifyOtpCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Validate that at least one contact method is provided
            if (string.IsNullOrEmpty(request.Email) && string.IsNullOrEmpty(request.PhoneNumber))
            {
                return Result.Failure<VerifyOtpResponse>(
                    Error.BadRequest("Either email or phone number must be provided")
                );
            }

            // Validate that OTP codes are provided for the respective contact methods
            if (!string.IsNullOrEmpty(request.Email) && string.IsNullOrEmpty(request.EmailOtpCode))
            {
                return Result.Failure<VerifyOtpResponse>(
                    Error.BadRequest("Email OTP code must be provided when email is specified")
                );
            }

            if (
                !string.IsNullOrEmpty(request.PhoneNumber)
                && string.IsNullOrEmpty(request.PhoneOtpCode)
            )
            {
                return Result.Failure<VerifyOtpResponse>(
                    Error.BadRequest(
                        "Phone OTP code must be provided when phone number is specified"
                    )
                );
            }

            // Find the user based on the provided contact information
            ApplicationUser? user = null;

            // If both email and phone are provided, try to find the user by both
            if (!string.IsNullOrEmpty(request.Email) && !string.IsNullOrEmpty(request.PhoneNumber))
            {
                user = await _userManager.Users.FirstOrDefaultAsync(
                    u => u.Email == request.Email && u.PhoneNumber == request.PhoneNumber,
                    cancellationToken: cancellationToken
                );

                // If user not found with both, try email
                if (user == null)
                {
                    user = await _userManager.FindByEmailAsync(request.Email);
                }

                // If still not found, try phone
                if (user == null)
                {
                    user = await _userManager.Users.FirstOrDefaultAsync(
                        u => u.PhoneNumber == request.PhoneNumber,
                        cancellationToken: cancellationToken
                    );
                }
            }
            // Try to find by email only
            else if (!string.IsNullOrEmpty(request.Email))
            {
                user = await _userManager.FindByEmailAsync(request.Email);
            }
            // Try to find by phone only
            else if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                user = await _userManager.Users.FirstOrDefaultAsync(
                    u => u.PhoneNumber == request.PhoneNumber,
                    cancellationToken: cancellationToken
                );
            }

            if (user == null)
            {
                return Result.Failure<VerifyOtpResponse>(Error.NotFound("User not found"));
            }

            // Track verification results
            bool emailVerified = false;
            bool phoneVerified = false;

            // Verify email OTP if provided
            if (!string.IsNullOrEmpty(request.Email) && !string.IsNullOrEmpty(request.EmailOtpCode))
            {
                var emailVerifyResult = await _otpDispatcher.VerifyOtpAsync(
                    request.EmailOtpCode,
                    user.Id
                );
                emailVerified = emailVerifyResult.IsSuccess;

                if (emailVerifyResult.IsFailure)
                {
                    _logger.LogWarning(
                        "Email OTP verification failed: {Error}",
                        emailVerifyResult.Error.Message
                    );

                    return Result.Failure<VerifyOtpResponse>(
                        Error.BadRequest("Invalid or expired OTP")
                    );
                }
            }

            // Verify phone OTP if provided
            if (
                !string.IsNullOrEmpty(request.PhoneNumber)
                && !string.IsNullOrEmpty(request.PhoneOtpCode)
            )
            {
                var phoneVerifyResult = await _otpDispatcher.VerifyOtpAsync(
                    request.PhoneOtpCode,
                    user.Id
                );
                phoneVerified = phoneVerifyResult.IsSuccess;

                if (phoneVerifyResult.IsFailure)
                {
                    _logger.LogWarning(
                        "Phone OTP verification failed: {Error}",
                        phoneVerifyResult.Error.Message
                    );
                    return Result.Failure<VerifyOtpResponse>(
                        Error.BadRequest("Invalid or expired OTP")
                    );
                }
            }

            // If no verification succeeded, return failure
            if (!emailVerified && !phoneVerified)
            {
                return Result.Failure<VerifyOtpResponse>(
                    Error.BadRequest("OTP verification failed for all provided contact methods")
                );
            }

            if (
                request.VerificationType == null
                || (
                    request.VerificationType.Trim() != "Registration"
                    && request.VerificationType.Trim() != "PasswordReset"
                )
            )
            {
                return Result.Failure<VerifyOtpResponse>(
                    Error.BadRequest(
                        "Verification type does not match any of the supported types (Registration, PasswordReset)"
                    )
                );
            }

            // Handle specific verification types
            if (request.VerificationType.Equals("Registration", StringComparison.OrdinalIgnoreCase))
            {
                // Mark email as verified if email OTP was verified
                if (emailVerified && !user.EmailVerified)
                {
                    user.EmailConfirmed = true;
                    user.EmailVerified = true;
                    await _userManager.UpdateAsync(user);
                }

                // Mark phone as verified if phone OTP was verified
                if (phoneVerified && !user.PhoneNumberConfirmed)
                {
                    user.PhoneNumberConfirmed = true;
                    await _userManager.UpdateAsync(user);
                }
            }
            else if (
                request.VerificationType.Equals("PasswordReset", StringComparison.OrdinalIgnoreCase)
            )
            {
                // Generate a password reset token
                var resetToken = await _userManager.GeneratePasswordResetTokenAsync(user);
                return Result.Success(new VerifyOtpResponse(resetToken, user.Id));
            }

            // Default response for other verification types
            return Result.Success(new VerifyOtpResponse(null, user.Id));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error verifying OTP {ex.Message}");
            return Result.Failure<VerifyOtpResponse>(
                Error.Internal($"An error occurred while verifying OTP {ex.Message}")
            );
        }
    }
}
