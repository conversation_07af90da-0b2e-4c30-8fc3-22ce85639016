# Design Document

## Overview

This design document outlines the implementation of development mode capabilities for running the SuperCare application after publishing. The solution extends the existing build scripts (build.sh and build.ps1) with new commands and options that enable developers to run published applications in development mode with enhanced debugging, monitoring, and hot reload capabilities.

## Architecture

### Command Structure

The development mode functionality will be implemented as new commands and options in the existing build scripts:

```bash
# New commands
./build.sh dev-run [options]
./build.sh dev-watch [options]
./build.sh dev-status

# PowerShell equivalent
.\build.ps1 dev-run [options]
.\build.ps1 dev-watch [options]
.\build.ps1 dev-status
```

### Core Components

1. **Development Runner**: Manages the lifecycle of the published application in development mode
2. **Environment Manager**: Handles environment-specific configurations and settings
3. **File Watcher**: Monitors source files for changes and triggers rebuilds
4. **Port Manager**: Handles port allocation and conflict resolution
5. **Migration Runner**: Manages database migrations before application startup
6. **Metrics Monitor**: Collects and displays real-time application metrics

## Components and Interfaces

### Development Runner Interface

```bash
# Core functions for development runner
run_dev_application() {
    local environment=$1
    local port=$2
    local auto_migrate=$3
    local watch_mode=$4
}

stop_dev_application() {
    local pid=$1
}

restart_dev_application() {
    local environment=$1
    local port=$2
}
```

### Environment Manager

The Environment Manager will handle:

- Loading environment-specific configuration files
- Setting environment variables
- Validating environment configurations
- Providing environment discovery

```bash
# Environment management functions
setup_environment() {
    local env_name=$1
    export ASPNETCORE_ENVIRONMENT=$env_name
    validate_environment_config $env_name
}

list_available_environments() {
    # Scan for appsettings.{Environment}.json files
}
```

### File Watcher Implementation

For Linux/macOS (bash):

- Use `inotifywait` or `fswatch` for file system monitoring
- Monitor specific file patterns (_.cs, _.json, \*.csproj)
- Implement debouncing to avoid excessive rebuilds

For Windows (PowerShell):

- Use `FileSystemWatcher` .NET class
- Monitor file changes with appropriate filters
- Handle file locking scenarios common on Windows

### Port Management

```bash
# Port management functions
find_available_port() {
    local start_port=$1
    local max_attempts=10
}

check_port_availability() {
    local port=$1
    # Use netstat or ss to check port availability
}

suggest_alternative_ports() {
    local base_port=$1
}
```

## Data Models

### Development Configuration

```json
{
  "developmentMode": {
    "defaultEnvironment": "Development",
    "defaultPorts": {
      "http": 5000,
      "https": 5001
    },
    "watchPatterns": ["**/*.cs", "**/*.json", "**/*.csproj"],
    "excludePatterns": ["**/bin/**", "**/obj/**", "**/node_modules/**"],
    "autoMigrate": true,
    "hotReload": true,
    "metricsEnabled": true
  }
}
```

### Application State

```bash
# Application state tracking
DEV_APP_PID=""
DEV_APP_PORT=""
DEV_APP_ENVIRONMENT=""
DEV_APP_START_TIME=""
DEV_WATCH_MODE=false
```

## Error Handling

### Port Conflicts

- Detect port conflicts before starting the application
- Automatically suggest alternative ports
- Allow manual port specification
- Gracefully handle port binding failures

### Environment Configuration Errors

- Validate environment-specific configuration files exist
- Check for required configuration keys
- Provide clear error messages for missing configurations
- Suggest available environments when invalid ones are specified

### File System Monitoring Errors

- Handle file system watcher initialization failures
- Gracefully degrade to manual restart mode if watching fails
- Provide clear feedback when file monitoring is unavailable

### Database Migration Errors

- Capture and display migration errors clearly
- Prevent application startup if critical migrations fail
- Provide rollback options for failed migrations
- Allow bypassing migrations in development scenarios

## Testing Strategy

### Unit Testing

- Test port availability checking functions
- Test environment configuration loading
- Test file pattern matching for watch mode
- Test migration status checking

### Integration Testing

- Test full dev-run workflow from publish to startup
- Test environment switching scenarios
- Test hot reload functionality with actual file changes
- Test graceful shutdown and cleanup

### Manual Testing Scenarios

- Start application in different environments
- Test port conflict resolution
- Verify hot reload with various file types
- Test migration scenarios (pending, failed, successful)
- Test metrics display and accuracy

## Implementation Details

### Command Line Interface

#### New Commands

1. **dev-run**: Publish and run in development mode

   ```bash
   ./build.sh dev-run --env Development --port 5000 --auto-migrate --watch
   ```

2. **dev-watch**: Start file watching mode for continuous development

   ```bash
   ./build.sh dev-watch --env Development
   ```

3. **dev-status**: Show status of running development instance

   ```bash
   ./build.sh dev-status
   ```

4. **dev-stop**: Stop running development instance
   ```bash
   ./build.sh dev-stop
   ```

#### New Options

- `--env, --environment`: Specify environment (Development, Staging, Testing)
- `--port`: Specify custom port
- `--urls`: Specify custom URLs
- `--auto-migrate`: Enable automatic database migrations
- `--watch`: Enable file watching and hot reload
- `--no-build`: Skip build and use existing published artifacts
- `--metrics`: Enable real-time metrics display

### File Structure

```
project-root/
├── build.sh (enhanced)
├── build.ps1 (enhanced)
├── dev-config.json (new)
├── scripts/
│   ├── dev-runner.sh (new)
│   ├── dev-runner.ps1 (new)
│   ├── file-watcher.sh (new)
│   ├── file-watcher.ps1 (new)
│   └── port-manager.sh (new)
└── artifacts/
    ├── dev-session.log
    └── dev-metrics.json
```

### Process Management

#### Linux/macOS Implementation

- Use background processes with proper PID tracking
- Implement signal handling for graceful shutdown
- Use process groups for managing child processes
- Store process information in temporary files

#### Windows Implementation

- Use PowerShell jobs for background execution
- Implement proper job cleanup on script termination
- Use Windows-specific process management APIs
- Handle Windows service integration if needed

### Hot Reload Integration

#### For .NET Applications

- Leverage `dotnet watch` capabilities where possible
- Implement custom file monitoring for non-standard scenarios
- Handle compilation errors gracefully
- Provide clear feedback on reload status

#### File Change Detection

- Monitor source files, configuration files, and project files
- Implement debouncing to prevent excessive rebuilds
- Exclude binary and temporary files from monitoring
- Provide configurable watch patterns

### Metrics and Monitoring

#### Real-time Metrics Display

- Memory usage tracking
- CPU usage monitoring
- Request count and response times
- Active connections count
- Health check status

#### Logging Integration

- Structured logging output
- Log level filtering
- Real-time log streaming
- Error highlighting and stack trace formatting

### Configuration Management

#### Environment-Specific Settings

- Automatic detection of appsettings files
- Environment variable override support
- Configuration validation
- Secrets management for development

#### Development Defaults

- Sensible default ports and URLs
- Development-friendly logging levels
- Disabled authentication for local testing
- Enhanced error pages and debugging information
