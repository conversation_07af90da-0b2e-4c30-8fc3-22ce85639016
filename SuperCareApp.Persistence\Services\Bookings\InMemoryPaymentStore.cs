﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings;

public class InMemoryPaymentStore : IPaymentStore
{
    private readonly List<InMemoryPayment> _payments = new();
    private readonly SemaphoreSlim _lock = new(1, 1); // Ensures thread safety
    private static readonly ConcurrentDictionary<Guid, List<UserPaymentMethod>> _paymentMethods =
        new();

    public async Task<InMemoryPayment> CreateAsync(InMemoryPayment payment)
    {
        await _lock.WaitAsync();
        try
        {
            _payments.Add(payment);
            return payment;
        }
        finally
        {
            _lock.Release();
        }
    }

    public async Task<List<InMemoryPayment>> GetByProviderAsync(Guid providerId)
    {
        await _lock.WaitAsync();
        try
        {
            return _payments
                .Where(p => p.ProviderId == providerId)
                .OrderByDescending(p => p.CreatedAt)
                .ToList();
        }
        finally
        {
            _lock.Release();
        }
    }

    public async Task<InMemoryPayment?> GetByIdAsync(Guid paymentId)
    {
        await _lock.WaitAsync();
        try
        {
            return _payments.FirstOrDefault(p => p.Id == paymentId);
        }
        finally
        {
            _lock.Release();
        }
    }

    public async Task<List<InMemoryPayment>> GetAllAsync()
    {
        await _lock.WaitAsync();
        try
        {
            return _payments.OrderByDescending(p => p.CreatedAt).ToList();
        }
        finally
        {
            _lock.Release();
        }
    }

    public Task<IEnumerable<PaymentMethodResponse>> GetPaymentMethodsForUserAsync(Guid userId)
    {
        if (_paymentMethods.TryGetValue(userId, out var methods))
        {
            var seededData = UserPaymentMethod.GetSeedData(userId);
            methods.AddRange(seededData);
            var response = methods
                .Select(pm => new PaymentMethodResponse
                {
                    Id = pm.Id,
                    UserId = pm.UserId,
                    PaymentMethod = pm.PaymentMethodId,
                    PmType = pm.Type,
                    PmLastFour = pm.LastFour,
                    ExpMonth = pm.ExpMonth,
                    ExpYear = pm.ExpYear,
                    IsDefault = pm.IsDefault,
                })
                .ToList();

            return Task.FromResult<IEnumerable<PaymentMethodResponse>>(response);
        }

        var seedData = UserPaymentMethod.GetSeedData(userId);
        var emptyMethod = new List<UserPaymentMethod>();
        emptyMethod.AddRange(seedData);
        var defaultResponse = emptyMethod
            .Select(pm => new PaymentMethodResponse
            {
                Id = pm.Id,
                UserId = pm.UserId,
                PaymentMethod = pm.PaymentMethodId,
                PmType = pm.Type,
                PmLastFour = pm.LastFour,
                ExpMonth = pm.ExpMonth,
                ExpYear = pm.ExpYear,
                IsDefault = pm.IsDefault,
            })
            .ToList();
        return Task.FromResult<IEnumerable<PaymentMethodResponse>>(defaultResponse);
    }

    public Task<PaymentMethodResponse> AddPaymentMethodAsync(
        Guid userId,
        PaymentMethodRequest request
    )
    {
        var newMethod = new UserPaymentMethod
        {
            // Simulate the database generating a new ID.
            Id = Guid.NewGuid(),
            UserId = userId,
            PaymentMethodId = request.PaymentMethod,
            Type = request.Type,
            LastFour = request.LastFour,
            ExpMonth = request.ExpMonth,
            ExpYear = request.ExpYear,
            IsDefault = request.IsDefault,
        };

        // This lambda is executed atomically for a given key.
        _paymentMethods.AddOrUpdate(
            userId,
            // Factory function if the user is new.
            addValueFactory: (key) =>
            {
                // If this is the first card, it's the default.
                newMethod.IsDefault = true;
                return new List<UserPaymentMethod> { newMethod };
            },
            // Update function if the user already has cards.
            updateValueFactory: (key, existingMethods) =>
            {
                // If the new card is marked as default, unset the old default.
                if (newMethod.IsDefault)
                {
                    existingMethods.ForEach(m => m.IsDefault = false);
                }
                // If there are no other cards, make this one the default.
                else if (!existingMethods.Any())
                {
                    newMethod.IsDefault = true;
                }

                existingMethods.Add(newMethod);
                return existingMethods;
            }
        );

        // Map the newly created entity to the response DTO.
        var response = new PaymentMethodResponse
        {
            Id = newMethod.Id,
            UserId = newMethod.UserId,
            PaymentMethod = newMethod.PaymentMethodId,
            PmType = newMethod.Type,
            PmLastFour = newMethod.LastFour,
            ExpMonth = newMethod.ExpMonth,
            ExpYear = newMethod.ExpYear,
            IsDefault = newMethod.IsDefault,
        };

        return Task.FromResult(response);
    }
}
