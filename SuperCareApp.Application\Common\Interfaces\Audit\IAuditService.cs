using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces;

/// <summary>
/// Service interface for audit log operations
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// Gets audit logs for a specific entity
    /// </summary>
    /// <param name="entityType">The entity type name</param>
    /// <param name="entityId">The entity ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of audit logs for the entity</returns>
    Task<List<AuditLog>> GetEntityAuditLogsAsync(
        string entityType,
        Guid entityId,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Gets audit logs for a specific user's actions
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit logs for the user</returns>
    Task<(List<AuditLog> AuditLogs, int TotalCount)> GetUserAuditLogsAsync(
        Guid userId,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Gets audit logs within a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="entityType">Optional entity type filter</param>
    /// <param name="action">Optional action filter (INSERT, UPDATE, DELETE)</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit logs</returns>
    Task<(List<AuditLog> AuditLogs, int TotalCount)> GetAuditLogsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        string? entityType = null,
        string? action = null,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Gets audit statistics for reporting
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit statistics</returns>
    Task<AuditStatistics> GetAuditStatisticsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default
    );
}

/// <summary>
/// Audit statistics model
/// </summary>
public class AuditStatistics
{
    public int TotalOperations { get; set; }
    public int InsertOperations { get; set; }
    public int UpdateOperations { get; set; }
    public int DeleteOperations { get; set; }
    public Dictionary<string, int> OperationsByEntityType { get; set; } = new();
    public Dictionary<Guid, int> OperationsByUser { get; set; } = new();
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}
