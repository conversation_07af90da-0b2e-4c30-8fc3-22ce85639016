using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class UserNotification : BaseEntity
    {
        public Guid ApplicationUserId { get; set; }
        public Guid NotificationId { get; set; }
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public ApplicationUser ApplicationUser { get; set; } = null!;
        public Notification Notification { get; set; } = null!;
    }
}
