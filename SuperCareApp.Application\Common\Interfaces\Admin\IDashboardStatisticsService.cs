using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Admin
{
    /// <summary>
    /// Service for generating admin dashboard statistics
    /// </summary>
    public interface IDashboardStatisticsService
    {
        /// <summary>
        /// Gets comprehensive dashboard statistics
        /// </summary>
        /// <param name="request">Statistics request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Complete dashboard statistics</returns>
        Task<Result<DashboardStatisticsResponse>> GetDashboardStatisticsAsync(
            DashboardStatisticsRequest request,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets system overview statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>System overview statistics</returns>
        Task<Result<SystemOverviewStatistics>> GetSystemOverviewAsync(
            DateTime startDate,
            DateTime endDate,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets user analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="includeDetails">Include detailed breakdowns</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>User analytics statistics</returns>
        Task<Result<UserAnalyticsStatistics>> GetUserAnalyticsAsync(
            DateTime startDate,
            DateTime endDate,
            bool includeDetails = true,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets booking analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="includeDetails">Include detailed breakdowns</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Booking analytics statistics</returns>
        Task<Result<BookingAnalyticsStatistics>> GetBookingAnalyticsAsync(
            DateTime startDate,
            DateTime endDate,
            bool includeDetails = true,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets financial analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="includeDetails">Include detailed breakdowns</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Financial analytics statistics</returns>
        Task<Result<FinancialAnalyticsStatistics>> GetFinancialAnalyticsAsync(
            DateTime startDate,
            DateTime endDate,
            bool includeDetails = true,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets notification analytics statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="includeRealTimeStats">Include real-time WebSocket statistics</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Notification analytics statistics</returns>
        Task<Result<NotificationAnalyticsStatistics>> GetNotificationAnalyticsAsync(
            DateTime startDate,
            DateTime endDate,
            bool includeRealTimeStats = true,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets real-time WebSocket statistics
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>WebSocket statistics</returns>
        Task<Result<WebSocketStatistics>> GetWebSocketStatisticsAsync(
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets system performance metrics
        /// </summary>
        /// <param name="startDate">Start date for metrics</param>
        /// <param name="endDate">End date for metrics</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>System performance metrics</returns>
        Task<Result<SystemPerformanceMetrics>> GetSystemPerformanceAsync(
            DateTime startDate,
            DateTime endDate,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets trending data for dashboard charts
        /// </summary>
        /// <param name="metric">Metric type (users, bookings, revenue, etc.)</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="granularity">Data granularity (daily, weekly, monthly)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Trending data points</returns>
        Task<Result<List<TrendingDataPoint>>> GetTrendingDataAsync(
            string metric,
            DateTime startDate,
            DateTime endDate,
            string granularity = "daily",
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets comparative statistics between two periods
        /// </summary>
        /// <param name="currentStartDate">Current period start date</param>
        /// <param name="currentEndDate">Current period end date</param>
        /// <param name="previousStartDate">Previous period start date</param>
        /// <param name="previousEndDate">Previous period end date</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comparative statistics</returns>
        Task<Result<ComparativeStatistics>> GetComparativeStatisticsAsync(
            DateTime currentStartDate,
            DateTime currentEndDate,
            DateTime previousStartDate,
            DateTime previousEndDate,
            CancellationToken cancellationToken = default
        );
    }

    /// <summary>
    /// System performance metrics
    /// </summary>
    public class SystemPerformanceMetrics
    {
        /// <summary>
        /// Average response time in milliseconds
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 95th percentile response time
        /// </summary>
        public double P95ResponseTime { get; set; }

        /// <summary>
        /// Error rate percentage
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// System uptime percentage
        /// </summary>
        public double Uptime { get; set; }

        /// <summary>
        /// Database query performance
        /// </summary>
        public double AverageQueryTime { get; set; }

        /// <summary>
        /// Memory usage percentage
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// CPU usage percentage
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// Active database connections
        /// </summary>
        public int ActiveConnections { get; set; }
    }

    /// <summary>
    /// Trending data point for charts
    /// </summary>
    public class TrendingDataPoint
    {
        /// <summary>
        /// Date/time of the data point
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Value at this point
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// Label for the data point
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// Additional metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Comparative statistics between two periods
    /// </summary>
    public class ComparativeStatistics
    {
        /// <summary>
        /// Current period statistics
        /// </summary>
        public PeriodStatistics CurrentPeriod { get; set; } = new();

        /// <summary>
        /// Previous period statistics
        /// </summary>
        public PeriodStatistics PreviousPeriod { get; set; } = new();

        /// <summary>
        /// Growth rates and changes
        /// </summary>
        public Dictionary<string, double> GrowthRates { get; set; } = new();

        /// <summary>
        /// Absolute changes
        /// </summary>
        public Dictionary<string, decimal> AbsoluteChanges { get; set; } = new();
    }

    /// <summary>
    /// Statistics for a specific period
    /// </summary>
    public class PeriodStatistics
    {
        /// <summary>
        /// Period start date
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Period end date
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Key metrics for the period
        /// </summary>
        public Dictionary<string, decimal> Metrics { get; set; } = new();
    }
}
