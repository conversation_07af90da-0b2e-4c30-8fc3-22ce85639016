# Care Professional Recommendation System - Implementation Guide

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [Architecture Overview](#architecture-overview)
3. [Geospatial Implementation](#geospatial-implementation)
4. [Database Schema Extensions](#database-schema-extensions)
5. [Scoring Algorithm](#scoring-algorithm)
6. [Caching Strategy](#caching-strategy)
7. [API Implementation](#api-implementation)
8. [Performance Optimization](#performance-optimization)
9. [Monitoring & Analytics](#monitoring--analytics)
10. [Implementation Timeline](#implementation-timeline)

---

## Executive Summary

This document outlines a scalable care professional recommendation system that leverages PostGIS for efficient geospatial queries, Redis for caching, and a multi-factor scoring algorithm. The system is designed to handle millions of providers and booking requests while maintaining sub-100ms response times.

**Key Features:**

- PostGIS-based geospatial indexing (no Haversine calculations)
- Multi-dimensional scoring algorithm
- Redis-powered caching layer
- Real-time availability filtering
- Machine learning integration ready
- Horizontal scaling support

---

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │    │   Load Balancer │    │   API Gateway   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                                 │                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ Recommendation  │    │   Scoring       │    │   Availability  ││
│  │   Service       │    │   Engine        │    │   Service       ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                 │                                 │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                                 │                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ Redis Cluster   │    │ PostgreSQL +    │    │ Elasticsearch   ││
│  │ (Caching)       │    │ PostGIS         │    │ (Search Index)  ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────────┘
```

---

## Geospatial Implementation

### PostGIS Setup

**Database Extensions:**

```sql
-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- Create spatial indexes
CREATE INDEX CONCURRENTLY idx_addresses_location
ON addresses USING GIST (ST_Point(longitude, latitude));

-- Create geohash index for faster proximity queries
ALTER TABLE addresses ADD COLUMN geohash VARCHAR(12);
CREATE INDEX CONCURRENTLY idx_addresses_geohash ON addresses (geohash);
```

**Geohash Implementation:**

```csharp
public static class GeohashService
{
    private const string Base32 = "0123456789bcdefghjkmnpqrstuvwxyz";

    public static string Encode(double latitude, double longitude, int precision = 8)
    {
        var latRange = new double[] { -90.0, 90.0 };
        var lonRange = new double[] { -180.0, 180.0 };
        var geohash = new StringBuilder();
        var bits = 0;
        var bit = 0;
        var ch = 0;
        var even = true;

        while (geohash.Length < precision)
        {
            double mid;
            if (even)
            {
                mid = (lonRange[0] + lonRange[1]) / 2;
                if (longitude >= mid)
                {
                    ch |= (1 << (4 - bit));
                    lonRange[0] = mid;
                }
                else
                {
                    lonRange[1] = mid;
                }
            }
            else
            {
                mid = (latRange[0] + latRange[1]) / 2;
                if (latitude >= mid)
                {
                    ch |= (1 << (4 - bit));
                    latRange[0] = mid;
                }
                else
                {
                    latRange[1] = mid;
                }
            }

            even = !even;
            if (bit < 4)
            {
                bit++;
            }
            else
            {
                geohash.Append(Base32[ch]);
                bit = 0;
                ch = 0;
            }
        }

        return geohash.ToString();
    }

    public static List<string> GetNeighbors(string geohash, int radius = 1)
    {
        var neighbors = new List<string> { geohash };

        // Get all neighboring geohashes within radius
        for (int i = 0; i < radius; i++)
        {
            var currentLevel = new List<string>(neighbors);
            foreach (var hash in currentLevel)
            {
                neighbors.AddRange(GetAdjacentHashes(hash));
            }
        }

        return neighbors.Distinct().ToList();
    }
}
```

### Spatial Query Optimization

**Tiered Location Search:**

```sql
-- Level 1: Geohash prefix matching (fastest)
WITH geohash_candidates AS (
    SELECT p.*, a.latitude, a.longitude, a.geohash
    FROM care_provider_profiles p
    JOIN user_addresses ua ON p.user_id = ua.user_id AND ua.is_primary = true
    JOIN addresses a ON ua.address_id = a.id
    WHERE a.geohash LIKE @geohash_prefix || '%'
),
-- Level 2: PostGIS distance filtering for precision
distance_filtered AS (
    SELECT *,
           ST_Distance(
               ST_Point(longitude, latitude)::geography,
               ST_Point(@client_longitude, @client_latitude)::geography
           ) as distance_meters
    FROM geohash_candidates
    WHERE ST_DWithin(
        ST_Point(longitude, latitude)::geography,
        ST_Point(@client_longitude, @client_latitude)::geography,
        @radius_meters
    )
)
SELECT * FROM distance_filtered ORDER BY distance_meters;
```

---

## Database Schema Extensions

### New Tables for Recommendation System

```sql
-- Provider scoring cache
CREATE TABLE provider_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_id UUID NOT NULL REFERENCES care_provider_profiles(user_id),
    location_score DECIMAL(5,4) NOT NULL,
    experience_score DECIMAL(5,4) NOT NULL,
    rating_score DECIMAL(5,4) NOT NULL,
    availability_score DECIMAL(5,4) NOT NULL,
    category_match_score DECIMAL(5,4) NOT NULL,
    composite_score DECIMAL(5,4) NOT NULL,
    calculated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    geohash VARCHAR(12) NOT NULL,
    category_id UUID NOT NULL,

    INDEX idx_provider_scores_geohash_category (geohash, category_id),
    INDEX idx_provider_scores_composite (composite_score DESC),
    INDEX idx_provider_scores_expires (expires_at)
);

-- Search analytics for ML training
CREATE TABLE search_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL,
    search_parameters JSONB NOT NULL,
    results_returned INTEGER NOT NULL,
    results_clicked INTEGER NOT NULL,
    booking_made BOOLEAN DEFAULT FALSE,
    provider_selected UUID,
    search_timestamp TIMESTAMP NOT NULL DEFAULT NOW(),

    INDEX idx_search_analytics_client (client_id),
    INDEX idx_search_analytics_timestamp (search_timestamp),
    INDEX idx_search_analytics_parameters USING GIN (search_parameters)
);

-- Real-time provider availability cache
CREATE TABLE provider_availability_cache (
    provider_id UUID PRIMARY KEY,
    available_slots JSONB NOT NULL,
    last_updated TIMESTAMP NOT NULL DEFAULT NOW(),
    next_available_slot TIMESTAMP,

    INDEX idx_availability_cache_next_slot (next_available_slot),
    INDEX idx_availability_cache_updated (last_updated)
);
```

### Extended Existing Schema

```sql
-- Add spatial and search optimization columns
ALTER TABLE addresses
ADD COLUMN geohash VARCHAR(12),
ADD COLUMN search_vector tsvector;

-- Add recommendation tracking to bookings
ALTER TABLE bookings
ADD COLUMN recommendation_score DECIMAL(5,4),
ADD COLUMN recommendation_factors JSONB;

-- Add performance metrics to providers
ALTER TABLE care_provider_profiles
ADD COLUMN response_rate DECIMAL(5,4) DEFAULT 1.0,
ADD COLUMN average_response_time INTEGER DEFAULT 0, -- minutes
ADD COLUMN cancellation_rate DECIMAL(5,4) DEFAULT 0.0,
ADD COLUMN last_active TIMESTAMP DEFAULT NOW();
```

---

## Scoring Algorithm

### Multi-Factor Scoring System

```csharp
public class RecommendationScorer
{
    private readonly IConfiguration _config;
    private readonly ILogger<RecommendationScorer> _logger;

    public RecommendationScorer(IConfiguration config, ILogger<RecommendationScorer> logger)
    {
        _config = config;
        _logger = logger;
    }

    public async Task<ProviderScore> CalculateScoreAsync(
        ProviderScoringInput input,
        RecommendationContext context)
    {
        var weights = await GetScoringWeightsAsync(context.ClientId);

        var scores = new ProviderScore
        {
            ProviderId = input.ProviderId,
            LocationScore = CalculateLocationScore(input, context),
            ExperienceScore = CalculateExperienceScore(input),
            RatingScore = CalculateRatingScore(input),
            AvailabilityScore = await CalculateAvailabilityScoreAsync(input, context),
            CategoryMatchScore = CalculateCategoryMatchScore(input, context),
            ResponseScore = CalculateResponseScore(input),
            ReliabilityScore = CalculateReliabilityScore(input)
        };

        scores.CompositeScore = CalculateCompositeScore(scores, weights);

        return scores;
    }

    private decimal CalculateLocationScore(ProviderScoringInput input, RecommendationContext context)
    {
        // Use geohash-based scoring for performance
        var clientGeohash = GeohashService.Encode(context.Latitude, context.Longitude, 8);
        var providerGeohash = input.Geohash;

        // Calculate geohash similarity (common prefix length)
        var commonPrefixLength = GetCommonPrefixLength(clientGeohash, providerGeohash);
        var maxDistance = _config.GetValue<double>("Recommendation:MaxDistanceKm");

        // Convert geohash precision to approximate distance score
        var distanceScore = Math.Min(1.0m, (decimal)commonPrefixLength / 8.0m);

        // Apply distance decay function
        return ApplyDistanceDecay(distanceScore, maxDistance);
    }

    private decimal CalculateExperienceScore(ProviderScoringInput input)
    {
        var maxYears = _config.GetValue<int>("Recommendation:MaxExperienceYears", 20);
        var experienceScore = Math.Min(1.0m, (decimal)input.YearsExperience / maxYears);

        // Apply logarithmic scaling to prevent over-weighting very experienced providers
        return (decimal)Math.Log10(1 + 9 * (double)experienceScore);
    }

    private decimal CalculateRatingScore(ProviderScoringInput input)
    {
        if (!input.Rating.HasValue || input.RatingCount < 5)
            return 0.5m; // Neutral score for new providers

        var ratingScore = input.Rating.Value / 5.0m;
        var confidenceAdjustment = CalculateRatingConfidence(input.RatingCount.Value);

        return ratingScore * confidenceAdjustment;
    }

    private decimal CalculateRatingConfidence(int ratingCount)
    {
        // Wilson score interval for rating confidence
        const double z = 1.96; // 95% confidence
        var n = (double)ratingCount;
        return (decimal)(1 - (z * Math.Sqrt(1 / n) / (1 + z * z / n)));
    }

    private async Task<decimal> CalculateAvailabilityScoreAsync(
        ProviderScoringInput input,
        RecommendationContext context)
    {
        var availabilityKey = $"availability:{input.ProviderId}";
        var cachedAvailability = await _redis.GetAsync<ProviderAvailabilityCache>(availabilityKey);

        if (cachedAvailability == null)
        {
            cachedAvailability = await RefreshProviderAvailabilityAsync(input.ProviderId);
        }

        // Score based on how soon provider is available
        var hoursUntilAvailable = CalculateHoursUntilAvailable(cachedAvailability, context.RequestedDateTime);

        if (hoursUntilAvailable <= 2) return 1.0m;
        if (hoursUntilAvailable <= 24) return 0.8m;
        if (hoursUntilAvailable <= 72) return 0.6m;
        if (hoursUntilAvailable <= 168) return 0.4m;

        return 0.2m;
    }

    private decimal CalculateCompositeScore(ProviderScore scores, ScoringWeights weights)
    {
        return (scores.LocationScore * weights.LocationWeight) +
               (scores.ExperienceScore * weights.ExperienceWeight) +
               (scores.RatingScore * weights.RatingWeight) +
               (scores.AvailabilityScore * weights.AvailabilityWeight) +
               (scores.CategoryMatchScore * weights.CategoryMatchWeight) +
               (scores.ResponseScore * weights.ResponseWeight) +
               (scores.ReliabilityScore * weights.ReliabilityWeight);
    }
}

public class ScoringWeights
{
    public decimal LocationWeight { get; set; } = 0.25m;
    public decimal ExperienceWeight { get; set; } = 0.15m;
    public decimal RatingWeight { get; set; } = 0.20m;
    public decimal AvailabilityWeight { get; set; } = 0.20m;
    public decimal CategoryMatchWeight { get; set; } = 0.10m;
    public decimal ResponseWeight { get; set; } = 0.05m;
    public decimal ReliabilityWeight { get; set; } = 0.05m;
}
```

---

## Caching Strategy

### Redis Cache Architecture

```csharp
public class RecommendationCacheService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly ILogger<RecommendationCacheService> _logger;

    // Cache key patterns
    private const string PROVIDER_SCORE_KEY = "rec:provider:{0}:{1}:{2}"; // providerId:geohash:categoryId
    private const string LOCATION_PROVIDERS_KEY = "rec:location:{0}:{1}"; // geohash:categoryId
    private const string AVAILABILITY_KEY = "avail:{0}"; // providerId
    private const string CLIENT_PREFERENCES_KEY = "pref:{0}"; // clientId

    public async Task<List<ProviderRecommendation>> GetCachedRecommendationsAsync(
        string geohash,
        Guid categoryId,
        int limit = 20)
    {
        var cacheKey = string.Format(LOCATION_PROVIDERS_KEY, geohash, categoryId);
        var cachedResults = await _database.ListRangeAsync(cacheKey, 0, limit - 1);

        if (cachedResults.Length == 0)
            return null;

        var recommendations = new List<ProviderRecommendation>();

        foreach (var result in cachedResults)
        {
            var recommendation = JsonSerializer.Deserialize<ProviderRecommendation>(result);
            recommendations.Add(recommendation);
        }

        return recommendations;
    }

    public async Task CacheRecommendationsAsync(
        string geohash,
        Guid categoryId,
        List<ProviderRecommendation> recommendations,
        TimeSpan expiry)
    {
        var cacheKey = string.Format(LOCATION_PROVIDERS_KEY, geohash, categoryId);
        var pipeline = _database.CreateBatch();

        // Clear existing cache
        pipeline.KeyDeleteAsync(cacheKey);

        // Add new recommendations (sorted by composite score)
        var sortedRecommendations = recommendations
            .OrderByDescending(r => r.CompositeScore)
            .Take(50); // Cache top 50 providers per location/category

        foreach (var recommendation in sortedRecommendations)
        {
            var serialized = JsonSerializer.Serialize(recommendation);
            pipeline.ListRightPushAsync(cacheKey, serialized);
        }

        // Set expiry
        pipeline.KeyExpireAsync(cacheKey, expiry);

        await pipeline.ExecuteAsync();
    }

    // Availability caching with real-time updates
    public async Task UpdateProviderAvailabilityAsync(Guid providerId, ProviderAvailabilityCache availability)
    {
        var cacheKey = string.Format(AVAILABILITY_KEY, providerId);
        var serialized = JsonSerializer.Serialize(availability);

        await _database.StringSetAsync(cacheKey, serialized, TimeSpan.FromMinutes(15));

        // Publish availability update for real-time notifications
        await _database.PublishAsync($"availability_update:{providerId}", serialized);
    }
}
```

### Cache Invalidation Strategy

```csharp
public class CacheInvalidationService
{
    private readonly IRecommendationCacheService _cacheService;
    private readonly ILogger<CacheInvalidationService> _logger;

    public async Task InvalidateProviderCacheAsync(Guid providerId, string reason)
    {
        _logger.LogInformation("Invalidating cache for provider {ProviderId}: {Reason}", providerId, reason);

        // Get provider's geohash and categories
        var provider = await _providerRepository.GetProviderWithLocationAsync(providerId);

        if (provider != null)
        {
            // Invalidate location-based caches
            var neighboringHashes = GeohashService.GetNeighbors(provider.Geohash, 2);

            foreach (var geohash in neighboringHashes)
            {
                foreach (var categoryId in provider.CategoryIds)
                {
                    await _cacheService.InvalidateLocationCacheAsync(geohash, categoryId);
                }
            }

            // Invalidate provider-specific caches
            await _cacheService.InvalidateProviderCacheAsync(providerId);
        }
    }

    // Event-driven cache invalidation
    public async Task HandleProviderUpdatedAsync(ProviderUpdatedEvent eventData)
    {
        await InvalidateProviderCacheAsync(eventData.ProviderId, "Provider profile updated");
    }

    public async Task HandleBookingStatusChangedAsync(BookingStatusChangedEvent eventData)
    {
        if (eventData.NewStatus == BookingStatusType.Completed)
        {
            // Provider availability changed
            await InvalidateProviderCacheAsync(eventData.ProviderId, "Booking completed");
        }
    }
}
```

---

## API Implementation

### Recommendation Controller

```csharp
[ApiController]
[Route("api/[controller]")]
public class RecommendationController : ControllerBase
{
    private readonly IRecommendationService _recommendationService;
    private readonly ILogger<RecommendationController> _logger;

    [HttpPost("providers")]
    [ProducesResponseType(typeof(RecommendationResponse), 200)]
    public async Task<IActionResult> GetRecommendations(
        [FromBody] RecommendationRequest request)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var recommendations = await _recommendationService.GetRecommendationsAsync(request);

            stopwatch.Stop();
            _logger.LogInformation(
                "Recommendations generated in {ElapsedMs}ms for client {ClientId}",
                stopwatch.ElapsedMilliseconds,
                request.ClientId);

            return Ok(new RecommendationResponse
            {
                Providers = recommendations,
                RequestId = Guid.NewGuid(),
                GeneratedAt = DateTime.UtcNow,
                ResponseTimeMs = stopwatch.ElapsedMilliseconds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating recommendations for client {ClientId}", request.ClientId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("feedback")]
    public async Task<IActionResult> SubmitFeedback([FromBody] RecommendationFeedback feedback)
    {
        await _recommendationService.ProcessFeedbackAsync(feedback);
        return Ok();
    }
}

public class RecommendationRequest
{
    public Guid ClientId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public Guid CategoryId { get; set; }
    public DateTime? PreferredDateTime { get; set; }
    public int MaxDistance { get; set; } = 50; // km
    public int Limit { get; set; } = 20;
    public RecommendationFilters Filters { get; set; } = new();
}

public class RecommendationFilters
{
    public decimal? MinRating { get; set; }
    public int? MinExperience { get; set; }
    public decimal? MaxHourlyRate { get; set; }
    public bool? ProvidesOvernight { get; set; }
    public bool? ProvidesLiveIn { get; set; }
    public List<string> RequiredQualifications { get; set; } = new();
}
```

### Core Recommendation Service

```csharp
public class RecommendationService : IRecommendationService
{
    private readonly IProviderRepository _providerRepository;
    private readonly IRecommendationScorer _scorer;
    private readonly IRecommendationCacheService _cacheService;
    private readonly ILogger<RecommendationService> _logger;

    public async Task<List<ProviderRecommendation>> GetRecommendationsAsync(
        RecommendationRequest request)
    {
        var geohash = GeohashService.Encode(request.Latitude, request.Longitude, 6);

        // Try cache first
        var cachedRecommendations = await _cacheService.GetCachedRecommendationsAsync(
            geohash, request.CategoryId, request.Limit);

        if (cachedRecommendations != null && cachedRecommendations.Count >= request.Limit)
        {
            return await ApplyFiltersAndPersonalization(cachedRecommendations, request);
        }

        // Generate fresh recommendations
        var recommendations = await GenerateFreshRecommendationsAsync(request);

        // Cache for future requests
        await _cacheService.CacheRecommendationsAsync(
            geohash,
            request.CategoryId,
            recommendations,
            TimeSpan.FromMinutes(30));

        return recommendations.Take(request.Limit).ToList();
    }

    private async Task<List<ProviderRecommendation>> GenerateFreshRecommendationsAsync(
        RecommendationRequest request)
    {
        var context = new RecommendationContext
        {
            ClientId = request.ClientId,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            CategoryId = request.CategoryId,
            RequestedDateTime = request.PreferredDateTime,
            MaxDistanceKm = request.MaxDistance
        };

        // Get candidate providers using geospatial query
        var candidates = await _providerRepository.GetCandidateProvidersAsync(context);

        // Score all candidates
        var scoringTasks = candidates.Select(async candidate =>
        {
            var score = await _scorer.CalculateScoreAsync(candidate, context);
            return new ProviderRecommendation
            {
                ProviderId = candidate.ProviderId,
                ProviderName = candidate.ProviderName,
                ProfileImageUrl = candidate.ProfileImageUrl,
                HourlyRate = candidate.HourlyRate,
                Rating = candidate.Rating,
                RatingCount = candidate.RatingCount,
                YearsExperience = candidate.YearsExperience,
                Bio = candidate.Bio,
                DistanceKm = candidate.DistanceKm,
                CompositeScore = score.CompositeScore,
                ScoreBreakdown = score,
                NextAvailableSlot = candidate.NextAvailableSlot
            };
        });

        var recommendations = await Task.WhenAll(scoringTasks);

        return recommendations
            .Where(r => r.CompositeScore > 0.3m) // Minimum quality threshold
            .OrderByDescending(r => r.CompositeScore)
            .ToList();
    }

    private async Task<List<ProviderRecommendation>> ApplyFiltersAndPersonalization(
        List<ProviderRecommendation> recommendations,
        RecommendationRequest request)
    {
        var filtered = recommendations.AsQueryable();

        // Apply filters
        if (request.Filters.MinRating.HasValue)
            filtered = filtered.Where(r => r.Rating >= request.Filters.MinRating);

        if (request.Filters.MinExperience.HasValue)
            filtered = filtered.Where(r => r.YearsExperience >= request.Filters.MinExperience);

        if (request.Filters.MaxHourlyRate.HasValue)
            filtered = filtered.Where(r => r.HourlyRate <= request.Filters.MaxHourlyRate);

        // Apply personalization based on client history
        var personalizedRecommendations = await ApplyPersonalizationAsync(
            filtered.ToList(), request.ClientId);

        return personalizedRecommendations;
    }

    private async Task<List<ProviderRecommendation>> ApplyPersonalizationAsync(
        List<ProviderRecommendation> recommendations,
        Guid clientId)
    {
        // Get client preferences and booking history
        var clientPreferences = await _cacheService.GetClientPreferencesAsync(clientId);
        var bookingHistory = await _providerRepository.GetClientBookingHistoryAsync(clientId);

        // Boost providers client has booked before (if they had good experience)
        foreach (var recommendation in recommendations)
        {
            var previousBookings = bookingHistory
                .Where(b => b.ProviderId == recommendation.ProviderId)
                .ToList();

            if (previousBookings.Any())
            {
                var avgRating = previousBookings.Average(b => b.ClientRating ?? 0);
                if (avgRating >= 4.0)
                {
                    recommendation.CompositeScore *= 1.2m; // 20% boost for previously liked providers
                }
            }
        }

        // Apply preference-based adjustments
        if (clientPreferences != null)
        {
            ApplyPreferenceAdjustments(recommendations, clientPreferences);
        }

        return recommendations.OrderByDescending(r => r.CompositeScore).ToList();
    }
}
```

---

## Performance Optimization

### Database Optimization

```sql
-- Materialized view for provider statistics
CREATE MATERIALIZED VIEW provider_stats AS
SELECT
    p.user_id as provider_id,
    p.rating,
    p.rating_count,
    p.years_experience,
    p.hourly_rate,
    p.response_rate,
    p.cancellation_rate,
    a.latitude,
    a.longitude,
    a.geohash,
    array_agg(DISTINCT pc.category_id) as category_ids,
    COUNT(DISTINCT b.id) as total_bookings,
    AVG(CASE WHEN b.created_at >= NOW() - INTERVAL '30 days' THEN 1 ELSE 0 END) as recent_activity_score
FROM care_provider_profiles p
JOIN user_addresses ua ON p.user_id = ua.user_id AND ua.is_primary = true
JOIN addresses a ON ua.address_id = a.id
LEFT JOIN care_provider_categories pc ON p.user_id = pc.provider_id
LEFT JOIN bookings b ON p.user_id = b.provider_id
WHERE p.verification_status = 'Verified'
GROUP BY p.user_id, p.rating, p.rating_count, p.years_experience,
         p.hourly_rate, p.response_rate, p.cancellation_rate,
         a.latitude, a.longitude, a.geohash;

-- Refresh materialized view every 15 minutes
CREATE OR REPLACE FUNCTION refresh_provider_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY provider_stats;
END;
$$ LANGUAGE plpgsql;

-- Scheduled refresh
SELECT cron.schedule('refresh-provider-stats', '*/15 * * * *', 'SELECT refresh_provider_stats();');
```

### Connection Pooling and Configuration

```csharp
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=SuperCare;Username=app_user;Password=***;Maximum Pool Size=200;Minimum Pool Size=20;Connection Idle Lifetime=300;Connection Pruning Interval=10;Pooling=true;",
    "ReadOnlyConnection": "Host=readonly-replica;Database=SuperCare;Username=readonly_user;Password=***;Maximum Pool Size=100;Minimum Pool Size=10;"
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "Database": 0,
    "KeyPrefix": "supercare:",
    "DefaultExpiry": "00:30:00"
  },
  "Recommendation": {
    "MaxDistanceKm": 50,
    "MaxExperienceYears": 20,
    "CacheExpiryMinutes": 30,
    "MaxCandidates": 100,
    "MinQualityScore": 0.3,
    "BatchSize": 50
  }
}
```

### Batch Processing and Background Jobs

```csharp
// Background service for precomputing recommendations
public class RecommendationPrecomputeService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RecommendationPrecomputeService> _logger;
    private readonly IConfiguration _config;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await PrecomputePopularLocations();
            await Task.Delay(TimeSpan.FromMinutes(15), stoppingToken);
        }
    }

    private async Task PrecomputePopularLocations()
    {
        using var scope = _serviceProvider.CreateScope();
        var recommendationService = scope.ServiceProvider.GetRequiredService<IRecommendationService>();
        var analyticsService = scope.ServiceProvider.GetRequiredService<IAnalyticsService>();

        // Get popular search locations from analytics
        var popularLocations = await analyticsService.GetPopularSearchLocationsAsync();

        var tasks = popularLocations.Select(async location =>
        {
            try
            {
                var request = new RecommendationRequest
                {
                    ClientId = Guid.Empty, // System precompute
                    Latitude = location.Latitude,
                    Longitude = location.Longitude,
                    CategoryId = location.CategoryId,
                    Limit = 50
                };

                await recommendationService.PrecomputeRecommendationsAsync(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error precomputing recommendations for location {Location}", location);
            }
        });

        await Task.WhenAll(tasks);
    }
}

// Hangfire job for periodic cache warming
public class CacheWarmupJob
{
    private readonly IRecommendationService _recommendationService;
    private readonly IProviderRepository _providerRepository;

    [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 30, 60, 120 })]
    public async Task WarmupProviderCaches()
    {
        var activeProviders = await _providerRepository.GetActiveProvidersAsync();

        await Parallel.ForEachAsync(activeProviders,
            new ParallelOptions { MaxDegreeOfParallelism = 10 },
            async (provider, ct) =>
            {
                await _recommendationService.WarmupProviderScoreAsync(provider.Id);
            });
    }
}
```

### Elasticsearch Integration for Advanced Search

```csharp
public class ElasticsearchService
{
    private readonly IElasticClient _client;
    private readonly ILogger<ElasticsearchService> _logger;

    public async Task<List<ProviderSearchResult>> SearchProvidersAsync(
        ProviderSearchRequest request)
    {
        var searchDescriptor = new SearchDescriptor<ProviderDocument>()
            .Index("providers")
            .Size(request.Limit)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .GeoDistance(gd => gd
                            .Field(f => f.Location)
                            .Distance($"{request.MaxDistanceKm}km")
                            .Location(request.Latitude, request.Longitude)
                        )
                    )
                    .Must(m => m
                        .Terms(t => t
                            .Field(f => f.CategoryIds)
                            .Terms(request.CategoryId)
                        )
                    )
                    .Filter(filters => ApplyFilters(filters, request.Filters))
                )
            )
            .Sort(s => s
                .GeoDistance(gd => gd
                    .Field(f => f.Location)
                    .Points(GeoLocation.Create(request.Latitude, request.Longitude))
                    .Order(SortOrder.Ascending)
                )
                .Descending(f => f.CompositeScore)
            );

        var response = await _client.SearchAsync<ProviderDocument>(searchDescriptor);

        return response.Documents.Select(doc => new ProviderSearchResult
        {
            ProviderId = doc.ProviderId,
            Name = doc.Name,
            Rating = doc.Rating,
            Distance = CalculateDistance(doc.Location, request.Latitude, request.Longitude),
            CompositeScore = doc.CompositeScore
        }).ToList();
    }

    public async Task IndexProviderAsync(ProviderDocument provider)
    {
        await _client.IndexDocumentAsync(provider);
    }

    public async Task BulkIndexProvidersAsync(List<ProviderDocument> providers)
    {
        var bulkDescriptor = new BulkDescriptor();

        foreach (var provider in providers)
        {
            bulkDescriptor.Index<ProviderDocument>(i => i
                .Document(provider)
                .Id(provider.ProviderId));
        }

        await _client.BulkAsync(bulkDescriptor);
    }
}

[ElasticsearchType(IdProperty = nameof(ProviderId))]
public class ProviderDocument
{
    public Guid ProviderId { get; set; }
    public string Name { get; set; }
    public string Bio { get; set; }
    public decimal Rating { get; set; }
    public int RatingCount { get; set; }
    public int YearsExperience { get; set; }
    public decimal HourlyRate { get; set; }
    public List<Guid> CategoryIds { get; set; }
    public GeoLocation Location { get; set; }
    public decimal CompositeScore { get; set; }
    public DateTime LastUpdated { get; set; }
    public bool IsAvailable { get; set; }
    public List<string> Qualifications { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

---

## Monitoring & Analytics

### Performance Monitoring

```csharp
public class RecommendationMetrics
{
    private readonly IMetricsCollector _metrics;
    private readonly ILogger<RecommendationMetrics> _logger;

    public void RecordRecommendationRequest(RecommendationRequest request)
    {
        _metrics.Counter("recommendation_requests_total")
            .WithTag("category", request.CategoryId.ToString())
            .WithTag("max_distance", request.MaxDistance.ToString())
            .Increment();
    }

    public void RecordRecommendationLatency(TimeSpan latency, bool fromCache)
    {
        _metrics.Histogram("recommendation_latency_ms")
            .WithTag("source", fromCache ? "cache" : "compute")
            .Record(latency.TotalMilliseconds);
    }

    public void RecordCacheHitRate(string cacheType, bool hit)
    {
        _metrics.Counter("cache_operations_total")
            .WithTag("type", cacheType)
            .WithTag("result", hit ? "hit" : "miss")
            .Increment();
    }

    public void RecordProviderSelection(Guid providerId, decimal score, int position)
    {
        _metrics.Counter("provider_selections_total")
            .WithTag("provider_id", providerId.ToString())
            .Increment();

        _metrics.Histogram("selection_position")
            .Record(position);

        _metrics.Histogram("selection_score")
            .Record((double)score);
    }
}

// Application Insights integration
public class RecommendationTelemetry
{
    private readonly TelemetryClient _telemetryClient;

    public void TrackRecommendationEvent(RecommendationRequest request, List<ProviderRecommendation> results)
    {
        var properties = new Dictionary<string, string>
        {
            ["ClientId"] = request.ClientId.ToString(),
            ["CategoryId"] = request.CategoryId.ToString(),
            ["Location"] = $"{request.Latitude},{request.Longitude}",
            ["ResultCount"] = results.Count.ToString(),
            ["MaxDistance"] = request.MaxDistance.ToString()
        };

        var metrics = new Dictionary<string, double>
        {
            ["AverageScore"] = results.Any() ? (double)results.Average(r => r.CompositeScore) : 0,
            ["TopScore"] = results.Any() ? (double)results.Max(r => r.CompositeScore) : 0,
            ["ResponseTimeMs"] = DateTime.UtcNow.Subtract(request.RequestTime).TotalMilliseconds
        };

        _telemetryClient.TrackEvent("RecommendationGenerated", properties, metrics);
    }

    public void TrackProviderBooking(Guid providerId, decimal recommendationScore, int position)
    {
        var properties = new Dictionary<string, string>
        {
            ["ProviderId"] = providerId.ToString(),
            ["Position"] = position.ToString()
        };

        var metrics = new Dictionary<string, double>
        {
            ["RecommendationScore"] = (double)recommendationScore
        };

        _telemetryClient.TrackEvent("ProviderBooked", properties, metrics);
    }
}
```

### A/B Testing Framework

```csharp
public class RecommendationExperimentService
{
    private readonly IFeatureManager _featureManager;
    private readonly ILogger<RecommendationExperimentService> _logger;

    public async Task<ScoringWeights> GetScoringWeightsAsync(Guid clientId)
    {
        var experimentGroup = await DetermineExperimentGroupAsync(clientId);

        return experimentGroup switch
        {
            "control" => GetControlWeights(),
            "location_focused" => GetLocationFocusedWeights(),
            "rating_focused" => GetRatingFocusedWeights(),
            "availability_focused" => GetAvailabilityFocusedWeights(),
            _ => GetControlWeights()
        };
    }

    private async Task<string> DetermineExperimentGroupAsync(Guid clientId)
    {
        // Consistent assignment based on client ID
        var hash = clientId.GetHashCode();
        var groupIndex = Math.Abs(hash) % 100;

        // A/B test configuration
        if (groupIndex < 25) return "control";
        if (groupIndex < 50) return "location_focused";
        if (groupIndex < 75) return "rating_focused";
        return "availability_focused";
    }

    private ScoringWeights GetLocationFocusedWeights()
    {
        return new ScoringWeights
        {
            LocationWeight = 0.35m,    // +10% from control
            RatingWeight = 0.15m,      // -5% from control
            AvailabilityWeight = 0.15m, // -5% from control
            ExperienceWeight = 0.15m,
            CategoryMatchWeight = 0.10m,
            ResponseWeight = 0.05m,
            ReliabilityWeight = 0.05m
        };
    }
}
```

### Analytics Dashboard Data

```csharp
public class RecommendationAnalyticsService
{
    private readonly IAnalyticsRepository _analyticsRepository;
    private readonly ILogger<RecommendationAnalyticsService> _logger;

    public async Task<RecommendationDashboard> GetDashboardDataAsync(DateRange dateRange)
    {
        var tasks = new[]
        {
            GetRecommendationMetricsAsync(dateRange),
            GetConversionRatesAsync(dateRange),
            GetPopularLocationsAsync(dateRange),
            GetProviderPerformanceAsync(dateRange),
            GetCachePerformanceAsync(dateRange)
        };

        var results = await Task.WhenAll(tasks);

        return new RecommendationDashboard
        {
            Metrics = results[0] as RecommendationMetrics,
            ConversionRates = results[1] as ConversionRates,
            PopularLocations = results[2] as List<PopularLocation>,
            ProviderPerformance = results[3] as ProviderPerformanceStats,
            CachePerformance = results[4] as CachePerformanceStats
        };
    }

    private async Task<RecommendationMetrics> GetRecommendationMetricsAsync(DateRange dateRange)
    {
        var query = @"
            SELECT
                COUNT(*) as total_requests,
                AVG(response_time_ms) as avg_response_time,
                PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY response_time_ms) as p95_response_time,
                AVG(results_returned) as avg_results_returned,
                SUM(CASE WHEN results_clicked > 0 THEN 1 ELSE 0 END) as requests_with_clicks,
                SUM(CASE WHEN booking_made THEN 1 ELSE 0 END) as requests_with_bookings
            FROM search_analytics
            WHERE search_timestamp BETWEEN @startDate AND @endDate";

        return await _analyticsRepository.QuerySingleAsync<RecommendationMetrics>(query, new
        {
            startDate = dateRange.StartDate,
            endDate = dateRange.EndDate
        });
    }

    public async Task<List<RecommendationInsight>> GetInsightsAsync()
    {
        var insights = new List<RecommendationInsight>();

        // Identify underperforming providers
        var underperformingProviders = await IdentifyUnderperformingProvidersAsync();
        if (underperformingProviders.Any())
        {
            insights.Add(new RecommendationInsight
            {
                Type = InsightType.UnderperformingProviders,
                Title = "Providers with Low Recommendation Scores",
                Description = $"{underperformingProviders.Count} providers have consistently low recommendation scores",
                ActionItems = new[]
                {
                    "Review profile completeness",
                    "Check availability patterns",
                    "Verify location accuracy"
                }
            });
        }

        // Identify cache optimization opportunities
        var cacheOptimizations = await IdentifyCacheOptimizationsAsync();
        insights.AddRange(cacheOptimizations);

        return insights;
    }
}
```

---

## Machine Learning Integration

### Recommendation Model Training

```csharp
public class MLRecommendationService
{
    private readonly MLContext _mlContext;
    private readonly ILogger<MLRecommendationService> _logger;
    private ITransformer _model;

    public async Task TrainModelAsync()
    {
        var trainingData = await LoadTrainingDataAsync();

        var pipeline = _mlContext.Transforms.Categorical
            .OneHotEncoding("CategoryIdEncoded", "CategoryId")
            .Append(_mlContext.Transforms.Concatenate("Features",
                "LocationScore", "ExperienceScore", "RatingScore",
                "AvailabilityScore", "CategoryIdEncoded"))
            .Append(_mlContext.Regression.Trainers.FastTree());

        _model = pipeline.Fit(trainingData);

        // Evaluate model
        var predictions = _model.Transform(trainingData);
        var metrics = _mlContext.Regression.Evaluate(predictions);

        _logger.LogInformation("Model trained with R² = {RSquared:F3}", metrics.RSquared);

        // Save model
        await SaveModelAsync(_model);
    }

    public async Task<decimal> PredictBookingProbabilityAsync(ProviderRecommendation recommendation)
    {
        if (_model == null)
            await LoadModelAsync();

        var predictionEngine = _mlContext.Model.CreatePredictionEngine<RecommendationInput, BookingPrediction>(_model);

        var input = new RecommendationInput
        {
            LocationScore = (float)recommendation.ScoreBreakdown.LocationScore,
            ExperienceScore = (float)recommendation.ScoreBreakdown.ExperienceScore,
            RatingScore = (float)recommendation.ScoreBreakdown.RatingScore,
            AvailabilityScore = (float)recommendation.ScoreBreakdown.AvailabilityScore,
            CategoryId = recommendation.CategoryId.ToString()
        };

        var prediction = predictionEngine.Predict(input);
        return (decimal)prediction.BookingProbability;
    }

    private async Task<IDataView> LoadTrainingDataAsync()
    {
        var query = @"
            SELECT
                sa.search_parameters->>'locationScore' as LocationScore,
                sa.search_parameters->>'experienceScore' as ExperienceScore,
                sa.search_parameters->>'ratingScore' as RatingScore,
                sa.search_parameters->>'availabilityScore' as AvailabilityScore,
                sa.search_parameters->>'categoryId' as CategoryId,
                CASE WHEN sa.booking_made THEN 1.0 ELSE 0.0 END as BookingMade
            FROM search_analytics sa
            WHERE sa.search_timestamp >= NOW() - INTERVAL '90 days'
            AND sa.search_parameters IS NOT NULL";

        var data = await _analyticsRepository.QueryAsync<MLTrainingData>(query);
        return _mlContext.Data.LoadFromEnumerable(data);
    }
}

public class RecommendationInput
{
    public float LocationScore { get; set; }
    public float ExperienceScore { get; set; }
    public float RatingScore { get; set; }
    public float AvailabilityScore { get; set; }
    public string CategoryId { get; set; }
}

public class BookingPrediction
{
    [ColumnName("Score")]
    public float BookingProbability { get; set; }
}
```

### Collaborative Filtering

```csharp
public class CollaborativeFilteringService
{
    private readonly IUserRepository _userRepository;
    private readonly IBookingRepository _bookingRepository;

    public async Task<List<ProviderRecommendation>> GetCollaborativeRecommendationsAsync(
        Guid clientId,
        List<ProviderRecommendation> baseRecommendations)
    {
        // Find similar clients based on booking history
        var similarClients = await FindSimilarClientsAsync(clientId);

        if (!similarClients.Any())
            return baseRecommendations;

        // Get providers that similar clients have booked and rated highly
        var collaborativeProviders = await GetCollaborativeProvidersAsync(similarClients);

        // Boost scores for collaborative providers
        foreach (var recommendation in baseRecommendations)
        {
            if (collaborativeProviders.TryGetValue(recommendation.ProviderId, out var boost))
            {
                recommendation.CompositeScore *= (1 + boost);
                recommendation.ScoreBreakdown.CollaborativeBoost = boost;
            }
        }

        return baseRecommendations.OrderByDescending(r => r.CompositeScore).ToList();
    }

    private async Task<List<Guid>> FindSimilarClientsAsync(Guid clientId)
    {
        // Use Jaccard similarity based on booked providers
        var clientBookings = await _bookingRepository.GetClientBookedProvidersAsync(clientId);

        if (!clientBookings.Any())
            return new List<Guid>();

        var query = @"
            WITH client_providers AS (
                SELECT DISTINCT provider_id
                FROM bookings
                WHERE client_id = @clientId
            ),
            other_clients AS (
                SELECT
                    b.client_id,
                    array_agg(DISTINCT b.provider_id) as provider_ids,
                    COUNT(DISTINCT b.provider_id) as provider_count
                FROM bookings b
                WHERE b.client_id != @clientId
                GROUP BY b.client_id
                HAVING COUNT(DISTINCT b.provider_id) >= 2
            ),
            similarity_scores AS (
                SELECT
                    oc.client_id,
                    (
                        SELECT COUNT(*)
                        FROM unnest(oc.provider_ids) p
                        WHERE p IN (SELECT provider_id FROM client_providers)
                    ) as intersection_count,
                    (
                        oc.provider_count + @clientProviderCount -
                        (SELECT COUNT(*) FROM unnest(oc.provider_ids) p WHERE p IN (SELECT provider_id FROM client_providers))
                    ) as union_count
                FROM other_clients oc
            )
            SELECT
                client_id,
                CASE
                    WHEN union_count > 0 THEN intersection_count::float / union_count::float
                    ELSE 0
                END as similarity_score
            FROM similarity_scores
            WHERE intersection_count > 0
            ORDER BY similarity_score DESC
            LIMIT 20";

        var similarClients = await _userRepository.QueryAsync<ClientSimilarity>(query, new
        {
            clientId,
            clientProviderCount = clientBookings.Count
        });

        return similarClients
            .Where(c => c.SimilarityScore > 0.1) // Minimum similarity threshold
            .Select(c => c.ClientId)
            .ToList();
    }
}
```

---

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-4)

**Week 1-2: Database Setup**

- [ ] Install and configure PostGIS extension
- [ ] Create spatial indexes and geohash columns
- [ ] Set up materialized views for provider statistics
- [ ] Implement database migration scripts

**Week 3-4: Basic Geospatial Service**

- [ ] Implement GeohashService class
- [ ] Create basic spatial queries
- [ ] Set up Redis caching infrastructure
- [ ] Implement basic provider repository

### Phase 2: Core Recommendation Engine (Weeks 5-8)

**Week 5-6: Scoring Algorithm**

- [ ] Implement RecommendationScorer class
- [ ] Create scoring weight configuration
- [ ] Build composite scoring logic
- [ ] Add availability scoring integration

**Week 7-8: Caching Layer**

- [ ] Implement RecommendationCacheService
- [ ] Create cache invalidation strategies
- [ ] Add background cache warming jobs
- [ ] Implement cache performance monitoring

### Phase 3: API and Integration (Weeks 9-12)

**Week 9-10: REST API**

- [ ] Create RecommendationController
- [ ] Implement request/response models
- [ ] Add input validation and error handling
- [ ] Create API documentation

**Week 11-12: Performance Optimization**

- [ ] Add connection pooling configuration
- [ ] Implement batch processing
- [ ] Set up Elasticsearch integration
- [ ] Add performance monitoring

### Phase 4: Advanced Features (Weeks 13-16)

**Week 13-14: Analytics and Monitoring**

- [ ] Implement metrics collection
- [ ] Create analytics dashboard
- [ ] Add A/B testing framework
- [ ] Set up alerting system

**Week 15-16: Machine Learning**

- [ ] Implement ML model training
- [ ] Add collaborative filtering
- [ ] Create model evaluation pipeline
- [ ] Deploy ML prediction service

### Phase 5: Production Deployment (Weeks 17-20)

**Week 17-18: Load Testing**

- [ ] Performance testing with simulated load
- [ ] Database query optimization
- [ ] Cache hit rate optimization
- [ ] Latency improvements

**Week 19-20: Production Deployment**

- [ ] Blue-green deployment setup
- [ ] Production monitoring configuration
- [ ] Rollback procedures
- [ ] Documentation and training

---

## Success Metrics

### Key Performance Indicators

**Performance Metrics:**

- API response time: < 100ms (95th percentile)
- Cache hit rate: > 80%
- Database query time: < 50ms average
- Throughput: > 1000 requests/second

**Business Metrics:**

- Recommendation click-through rate: > 15%
- Booking conversion rate: > 5%
- Provider coverage: > 90% of searches return results
- Client satisfaction: > 4.5/5 rating

**Technical Metrics:**

- System uptime: > 99.9%
- Error rate: < 0.1%
- Cache invalidation accuracy: > 99%
- ML model accuracy: > 80%

### Monitoring and Alerting

```csharp
public class RecommendationHealthCheck : IHealthCheck
{
    private readonly IRecommendationService _recommendationService;
    private readonly IConnectionMultiplexer _redis;
    private readonly IDbConnection _database;

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Test database connectivity
            await _database.QuerySingleAsync<int>("SELECT 1");

            // Test Redis connectivity
            var redisDatabase = _redis.GetDatabase();
            await redisDatabase.PingAsync();

            // Test recommendation service
            var testRequest = new RecommendationRequest
            {
                ClientId = Guid.NewGuid(),
                Latitude = 40.7128,
                Longitude = -74.0060,
                CategoryId = Guid.NewGuid(),
                Limit = 1
            };

            var stopwatch = Stopwatch.StartNew();
            await _recommendationService.GetRecommendationsAsync(testRequest);
            stopwatch.Stop();

            if (stopwatch.ElapsedMilliseconds > 500)
            {
                return HealthCheckResult.Degraded($"Slow response time: {stopwatch.ElapsedMilliseconds}ms");
            }

            return HealthCheckResult.Healthy();
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Recommendation service failed", ex);
        }
    }
}
```

This comprehensive implementation guide provides a scalable, high-performance recommendation system that avoids Haversine calculations while maintaining accuracy and speed. The system is designed to handle large datasets efficiently using PostGIS, Redis caching, and modern .NET practices.
