using Microsoft.EntityFrameworkCore.Diagnostics;

namespace SuperCareApp.Persistence.Interceptors;

public class DbPersistanceInterceptor : SaveChangesInterceptor
{
    private readonly ILogger<DbPersistanceInterceptor> _logger;
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTimeProvider _dateTimeProvider;

    /// <summary>
    /// Initializes a new instance of the <see cref="DbPersistanceInterceptor"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    /// <param name="currentUserService">The current user service.</param>
    public DbPersistanceInterceptor(
        ILogger<DbPersistanceInterceptor> logger,
        ICurrentUserService currentUserService,
        IDateTimeProvider dateTimeProvider
    )
    {
        _logger = logger;
        _currentUserService = currentUserService;
        _dateTimeProvider = dateTimeProvider;
    }

    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result
    )
    {
        Stamp(eventData.Context, _currentUserService.UserId, _dateTimeProvider.UtcNow);
        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default
    )
    {
        Stamp(eventData.Context, _currentUserService.UserId, _dateTimeProvider.UtcNow);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void Stamp(DbContext? context, Guid? userId, DateTime? timestamp)
    {
        if (context == null)
            return;

        _logger.LogInformation("Saving changes to database for tracking changes");

        foreach (var entry in context.ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.CreatedBy = userId ?? Guid.Empty;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.UpdatedBy = userId;
                    // Prevent overwriting the original Created* values
                    entry.Property(nameof(BaseEntity.CreatedAt)).IsModified = false;
                    entry.Property(nameof(BaseEntity.CreatedBy)).IsModified = false;
                    break;

                case EntityState.Deleted:
                    // Implement soft delete
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.DeletedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.DeletedBy = userId;
                    break;
            }
        }

        // Handle ApplicationUser audit properties
        foreach (var entry in context.ChangeTracker.Entries<ApplicationUser>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.CreatedBy = userId ?? Guid.Empty;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.UpdatedBy = userId;
                    break;

                case EntityState.Deleted:
                    // Convert hard delete to soft delete
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.DeletedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.DeletedBy = userId;
                    break;
            }
        }

        // Handle ApplicationRole audit properties
        foreach (var entry in context.ChangeTracker.Entries<ApplicationRole>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.CreatedBy = userId ?? Guid.Empty;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.UpdatedBy = userId;
                    break;

                case EntityState.Deleted:
                    // Convert hard delete to soft delete
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.DeletedAt = timestamp ?? DateTime.UtcNow;
                    entry.Entity.DeletedBy = userId;
                    break;
            }
        }
    }
}
