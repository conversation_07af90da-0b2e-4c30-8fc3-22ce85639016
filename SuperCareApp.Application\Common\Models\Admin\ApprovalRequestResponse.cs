﻿using System.Text.Json.Serialization;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Response model for approval requests
    /// </summary>
    public class ApprovalRequestResponse
    {
        /// <summary>
        /// Approval request ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// User ID who needs approval
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// User's email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Type of approval (string representation of the enum)
        /// </summary>
        [JsonPropertyName("approvalType")]
        public string ApprovalTypeString { get; set; } = string.Empty;

        /// <summary>
        /// Type of approval (human-readable)
        /// </summary>
        public string ApprovalTypeDescription { get; set; } = string.Empty;

        /// <summary>
        /// Type of approval (enum value, not serialized)
        /// </summary>
        [JsonIgnore]
        public ApprovalType ApprovalTypeEnum { get; set; }

        /// <summary>
        /// Status of the approval
        /// </summary>
        public bool? IsApproved { get; set; }

        /// <summary>
        /// Status of the approval (human-readable)
        /// </summary>
        public string Status
        {
            get
            {
                if (ProcessedAt == null)
                {
                    return "Pending";
                }

                return IsApproved.HasValue && IsApproved.Value ? "Approved" : "Rejected";
            }
        }

        /// <summary>
        /// Reason for rejection if applicable
        /// </summary>
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Additional data related to the approval
        /// </summary>
        public string? ApprovalData { get; set; }

        /// <summary>
        /// Reference to related entity (if applicable)
        /// </summary>
        public Guid? RelatedEntityId { get; set; }

        /// <summary>
        /// Admin who processed the approval
        /// </summary>
        public Guid? ProcessedBy { get; set; }

        /// <summary>
        /// Admin's name who processed the approval
        /// </summary>
        public string? ProcessedByName { get; set; }

        /// <summary>
        /// When the approval was processed
        /// </summary>
        public DateTime? ProcessedAt { get; set; }

        /// <summary>
        /// Notes or comments about the approval
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// When the approval request was created
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
