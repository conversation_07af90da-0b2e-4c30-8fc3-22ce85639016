﻿using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Categories
{
    /// <summary>
    /// Repository interface for care categories
    /// </summary>
    public interface ICareCategoryRepository : IRepository<CareCategory>
    {
        /// <summary>
        /// Gets a care category by name
        /// </summary>
        /// <param name="name">The name of the category</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The care category if found</returns>
        Task<Result<CareCategory>> GetByNameAsync(
            string name,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets all active care categories
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of active care categories</returns>
        Task<Result<IEnumerable<CareCategory>>> GetActiveAsync(
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets care categories for a specific provider
        /// </summary>
        /// <param name="providerId">The provider ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of care categories for the provider</returns>
        Task<Result<IEnumerable<CareCategory>>> GetByProviderIdAsync(
            Guid providerId,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets all care categories with pagination
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="includeInactive">Whether to include inactive categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paginated list of care categories</returns>
        Task<Result<(IEnumerable<CareCategory> Categories, int TotalCount)>> GetPaginatedAsync(
            int pageNumber,
            int pageSize,
            bool includeInactive = false,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Checks if a category with the given name already exists
        /// </summary>
        /// <param name="name">The category name to check</param>
        /// <param name="excludeId">Optional ID to exclude from the check (for updates)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if a category with the name exists, false otherwise</returns>
        Task<Result<bool>> ExistsByNameAsync(
            string name,
            Guid? excludeId = null,
            CancellationToken cancellationToken = default
        );
    }
}
