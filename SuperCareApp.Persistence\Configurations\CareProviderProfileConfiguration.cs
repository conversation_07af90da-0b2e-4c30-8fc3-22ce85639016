﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class CareProviderProfileConfiguration : IEntityTypeConfiguration<CareProviderProfile>
    {
        public void Configure(EntityTypeBuilder<CareProviderProfile> builder)
        {
            builder.HasKey(p => p.Id);

            builder.Property(p => p.Bio).HasMaxLength(1000);

            builder.Property(p => p.YearsExperience).IsRequired().HasColumnType("int");

            builder.Property(p => p.HourlyRate).HasColumnType("decimal(18,2)");

            builder.Property(p => p.CareDescription).HasMaxLength(500);

            builder.Property(p => p.ProvidesOvernight).HasDefaultValue(false);

            builder.Property(p => p.ProvidesLiveIn).HasDefaultValue(false);

            builder.Property(p => p.WorkingHours);

            builder.Property(p => p.Qualifications).HasColumnType("jsonb");

            builder.Property(p => p.VerificationStatus).IsRequired().HasConversion<string>();

            builder.Property(p => p.Rating).HasColumnType("decimal(3,2)");

            builder.Property(p => p.RatingCount).HasDefaultValue(0);

            builder.Property(c => c.BufferDuration).IsRequired().HasDefaultValue(0);

            // Relationships
            builder
                .HasOne(p => p.User)
                .WithOne()
                .HasForeignKey<CareProviderProfile>(p => p.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasMany(p => p.CareProviderCategories)
                .WithOne(c => c.CareProviderProfile)
                .HasForeignKey(c => c.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasMany(p => p.Availabilities)
                .WithOne(a => a.CareProviderProfile)
                .HasForeignKey(a => a.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
