﻿namespace SuperCareApp.Application.Common.Models.Bookings;

public record BookingRequest(
    Guid BookingId,
    BookingAction Action,
    Guid ProviderId,
    Guid ClientId,
    Guid CategoryId,
    DateOnly Date,
    TimeOnly StartTime,
    TimeOnly EndTime,
    string? SpecialInstructions,
    decimal TotalAmount,
    decimal PlatformFee,
    Guid CreatedBy
);

public enum BookingAction
{
    Create,
    Confirm,
    Cancel,
    Complete,
}
