﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to create a care category
    /// </summary>
    public record CreateCareCategoryCommand(CreateCareCategoryRequest Request, Guid UserId)
        : ICommand<Result<CareCategoryResponse>>;

    /// <summary>
    /// Handler for the CreateCareCategoryCommand
    /// </summary>
    public sealed class CreateCareCategoryCommandHandler
        : ICommandHandler<CreateCareCategoryCommand, Result<CareCategoryResponse>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<CreateCareCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CreateCareCategoryCommandHandler(
            ICareCategoryService categoryService,
            ILogger<CreateCareCategoryCommandHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result<CareCategoryResponse>> Handle(
            CreateCareCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _categoryService.CreateCategoryAsync(
                    request.Request,
                    request.UserId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating care category");
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }
    }
}
