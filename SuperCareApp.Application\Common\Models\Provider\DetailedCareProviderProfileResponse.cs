using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Provider
{
    public record DetailedCareProviderProfileResponse
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string Gender { get; set; }
        public int YearsExperience { get; set; }
        public decimal HourlyRate { get; set; }
        public bool ProvidesOvernight { get; set; }
        public bool ProvidesLiveIn { get; set; }
        public string? Bio { get; set; }
        public string? Qualifications { get; set; } // JSON string of qualifications
        public string VerificationStatus { get; set; } = string.Empty;
        public decimal Rating { get; set; }
        public int RatingCount { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<string> Categories { get; set; } = new List<string>();
    }
}
