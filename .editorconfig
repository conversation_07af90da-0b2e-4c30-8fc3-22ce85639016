﻿[*.cs]

# CS8618: Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
dotnet_diagnostic.CS8618.severity = silent
dotnet_diagnostic.CS8619.severity = silent
# CS8602: Dereference of a possibly null reference.
dotnet_diagnostic.CS8602.severity = silent
# CS8603: Possible null reference return.
dotnet_diagnostic.CS8603.severity = silent
# CS8604: Possible null reference argument for parameter.
dotnet_diagnostic.CS8604.severity = silent
# CS8605: Unboxing a possibly null value.
dotnet_diagnostic.CS8605.severity = silent
# CS8609: Possible null
dotnet_diagnostic.CS8609.severity = silent
# CS8610: Possible null reference return.
dotnet_diagnostic.CS8610.severity = silent
# CS8612: Nullability of reference types in value of type is not known.
dotnet_diagnostic.CS8612.severity = silent
#CS8600: Converting null literal or possible null value to non-nullable type.
dotnet_diagnostic.CS8600.severity = silent

# IDE0290: Use primary constructor
dotnet_diagnostic.IDE0290.severity = silent

# Default severity for analyzer diagnostics with category 'Style'
dotnet_analyzer_diagnostic.category-Style.severity = silent

# CS8629: Nullable value type may be null.
dotnet_diagnostic.CS8629.severity = none
dotnet_diagnostic.CS8601.severity = none

# CS8625: Cannot convert null literal to non-nullable reference type.
dotnet_diagnostic.CS8625.severity = none

# Default severity for all analyzer diagnostics
dotnet_analyzer_diagnostic.severity = none
