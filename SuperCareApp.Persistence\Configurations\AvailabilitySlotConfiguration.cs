﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class AvailabilitySlotConfiguration : IEntityTypeConfiguration<AvailabilitySlot>
    {
        public void Configure(EntityTypeBuilder<AvailabilitySlot> builder)
        {
            builder.HasKey(s => s.Id);

            builder.Property(s => s.AvailabilityId).IsRequired();

            builder.Property(s => s.StartTime).HasColumnType("time without time zone").IsRequired();

            builder.Property(s => s.EndTime).HasColumnType("time without time zone").IsRequired();

            // Foreign key relationship
            builder
                .HasOne(s => s.Availability)
                .WithMany(a => a.AvailabilitySlots)
                .HasForeignKey(s => s.AvailabilityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Constraint: End time must be after start time
            builder.ToTable(t =>
                t.HasCheckConstraint(
                    "CK_AvailabilitySlot_EndAfterStart",
                    "\"end_time\" > \"start_time\""
                )
            );

            // Indexes
            builder.HasIndex(s => new { s.AvailabilityId });
        }
    }
}
