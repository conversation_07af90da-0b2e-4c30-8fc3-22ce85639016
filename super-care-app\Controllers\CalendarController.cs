using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using super_care_app.Shared.Utility;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Calendar;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Persistence.Services.Calendar.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace super_care_app.Controllers
{
    /// <summary>
    /// Calendar management endpoints
    /// </summary>
    public class CalendarController : BaseController
    {
        private readonly IMediator _mediator;
        private readonly IProviderScheduleService _providerScheduleService;
        private readonly ICurrentUserService _currentUserService;
        private readonly RequestValidator _requestValidator;
        private readonly ILogger<CalendarController> _logger;

        public CalendarController(
            IMediator mediator,
            IProviderScheduleService providerScheduleService,
            ICurrentUserService currentUserService,
            RequestValidator requestValidator,
            ILogger<CalendarController> logger
        )
        {
            _mediator = mediator;
            _providerScheduleService = providerScheduleService;
            _currentUserService = currentUserService;
            _requestValidator = requestValidator;
            _logger = logger;
        }

        /// <summary>
        /// Gets monthly calendar view for the current user
        /// </summary>
        /// <param name="providerId">Care provider Id</param>
        /// <param name="monthsAhead">Number of months ahead to get calendar for</param>
        /// <param name="startDate">Effective start date</param>
        /// <param name="endDate">Effective end date</param>
        /// <returns>Monthly calendar data</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetMonthlyCalendar)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<MonthlyCalendarResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets monthly calendar view",
            Description = "Retrieves monthly calendar view for the current user with availability, bookings, and statistics",
            OperationId = "Calendar_GetMonthlyCalendar",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetMonthlyCalendar(
            [FromRoute] Guid providerId,
            [FromRoute] int monthsAhead,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate
        )
        {
            if (monthsAhead < 0)
            {
                _logger.LogWarning(
                    "Invalid monthsAhead value provided: {monthsAhead}",
                    monthsAhead
                );
                return BadRequestResponse<MonthlyCalendarResponse>(
                    "Invalid monthsAhead value. It must be zero or greater."
                );
            }

            if (providerId == Guid.Empty)
            {
                return UnauthorizedResponse<MonthlyCalendarResponse>("User is not authenticated.");
            }

            var response = await _providerScheduleService.GetUnavailableDays(
                providerId,
                monthsAhead,
                startDate,
                endDate
            );
            if (response.IsFailure)
            {
                _logger.LogError("Error retrieving monthly calendar: {Error}", response.Error);
                return ErrorResponseFromError<MonthlyCalendarResponse>(response.Error);
            }
            return SuccessResponse(response.Value, "Monthly calendar retrieved successfully");
        }

        /// <summary>
        /// Gets calendar data for a specific date range and provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="parameters">Range parameters</param>
        /// <returns>Calendar range data</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetCalendarRange)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CalendarRangeResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets calendar range",
            Description = "Retrieves calendar data for a specific date range and provider",
            OperationId = "Calendar_GetCalendarRange",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetCalendarRange(
            [FromRoute] Guid providerId,
            [FromQuery] CalendarRangeParams parameters
        )
        {
            var validator = await _requestValidator.ValidateAsync(
                parameters,
                new CalendarRangeParamsValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<object>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var query = new GetCalendarRangeQuery(providerId, parameters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error getting calendar range: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Calendar range retrieved successfully");
        }

        /// <summary>
        /// Gets detailed availability for a specific day and provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="date">Date to check (YYYY-MM-DD)</param>
        /// <returns>Day availability details</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetDayAvailability)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CalendarDayResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets day availability",
            Description = "Retrieves detailed availability for a specific day and provider including slots and bookings",
            OperationId = "Calendar_GetDayAvailability",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetDayAvailability(
            [FromRoute] Guid providerId,
            [FromRoute] DateTime date
        )
        {
            var query = new GetDayAvailabilityQuery(providerId, date);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error getting day availability: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Day availability retrieved successfully");
        }

        /// <summary>
        /// Gets next available time slots for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="parameters">Search parameters</param>
        /// <returns>Next available slots</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetNextAvailableSlots)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<NextAvailableSlotsResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets next available slots",
            Description = "Retrieves the next available time slots for a provider within specified parameters",
            OperationId = "Calendar_GetNextAvailableSlots",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetNextAvailableSlots(
            [FromRoute] Guid providerId,
            [FromQuery] NextAvailableSlotsParams parameters
        )
        {
            var validator = await _requestValidator.ValidateAsync(
                parameters,
                new NextAvailableSlotsParamsValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<object>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var query = new GetNextAvailableSlotsQuery(providerId, parameters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error getting next available slots: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Next available slots retrieved successfully");
        }

        /// <summary>
        /// Checks if a specific time slot is available for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="parameters">Availability check parameters</param>
        /// <returns>Availability check result</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.CheckAvailability)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CheckAvailabilityResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Checks availability",
            Description = "Checks if a specific time slot is available for a provider and provides alternatives if not",
            OperationId = "Calendar_CheckAvailability",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> CheckAvailability(
            [FromRoute] Guid providerId,
            [FromQuery] CheckAvailabilityParams parameters
        )
        {
            var validator = await _requestValidator.ValidateAsync(
                parameters,
                new CheckAvailabilityParamsValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<object>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var query = new CheckAvailabilityQuery(providerId, parameters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error checking availability: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Availability check completed successfully");
        }

        /// <summary>
        /// Gets calendar summary with statistics for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="year">Year (e.g., 2024)</param>
        /// <param name="month">Month (1-12)</param>
        /// <returns>Calendar summary with statistics</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetCalendarSummary)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<MonthlyCalendarResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets calendar summary",
            Description = "Retrieves calendar summary with detailed statistics for a provider",
            OperationId = "Calendar_GetCalendarSummary",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetCalendarSummary(
            [FromRoute] Guid providerId,
            [FromRoute] int year,
            [FromRoute] int month
        )
        {
            // Validate year and month
            if (year < 2020 || year > 2030)
            {
                return BadRequest("Year must be between 2020 and 2030.");
            }

            if (month < 1 || month > 12)
            {
                return BadRequest("Month must be between 1 and 12.");
            }

            var query = new GetCalendarSummaryQuery(providerId, year, month);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error getting calendar summary: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Calendar summary retrieved successfully");
        }

        /// <summary>
        /// Gets available providers for a specific date and time
        /// </summary>
        /// <param name="parameters">Search parameters</param>
        /// <returns>Available providers</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetAvailableProviders)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<AvailableProvidersResponse>)
        )]
        [SwaggerOperation(
            Summary = "Gets available providers",
            Description = "Retrieves available providers for a specific date and time with optional filters",
            OperationId = "Calendar_GetAvailableProviders",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetAvailableProviders(
            [FromQuery] AvailableProvidersParams parameters
        )
        {
            var validator = await _requestValidator.ValidateAsync(
                parameters,
                new AvailableProvidersParamsValidator()
            );
            if (!validator.IsSuccess)
            {
                return ErrorResponseFromError<object>(
                    Error.Validation("Validation failed", validator.Error.ValidationErrors)
                );
            }

            var query = new GetAvailableProvidersQuery(parameters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error getting available providers: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Available providers retrieved successfully");
        }

        /// <summary>
        /// Gets filtered calendar view for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="year">Year (e.g., 2024)</param>
        /// <param name="month">Month (1-12)</param>
        /// <param name="filters">Filter parameters</param>
        /// <returns>Filtered calendar data</returns>
        [Authorize]
        [HttpGet(ApiRoutes.Calendar.GetFilteredCalendar)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<MonthlyCalendarResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets filtered calendar",
            Description = "Retrieves filtered calendar view for a provider with customizable filters",
            OperationId = "Calendar_GetFilteredCalendar",
            Tags = new[] { "Calendar" }
        )]
        public async Task<IActionResult> GetFilteredCalendar(
            [FromRoute] Guid providerId,
            [FromRoute] int year,
            [FromRoute] int month,
            [FromQuery] FilteredCalendarParams filters
        )
        {
            // Validate year and month
            if (year < 2020 || year > 2030)
            {
                return BadRequest("Year must be between 2020 and 2030.");
            }

            if (month < 1 || month > 12)
            {
                return BadRequest("Month must be between 1 and 12.");
            }

            var query = new GetFilteredCalendarQuery(providerId, year, month, filters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Error getting filtered calendar: {Error}", result.Error);
                return ErrorResponseFromError<object>(result.Error);
            }

            return SuccessResponse(result.Value, "Filtered calendar retrieved successfully");
        }
    }
}
