﻿using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin.Queries;

public record GetCareProviderStatisticsQuery : IQuery<Result<CareProviderStatisticsResponse>>;

internal sealed class GetCareProviderStatisticsQueryHandler
    : I<PERSON>ueryHandler<GetCareProviderStatisticsQuery, Result<CareProviderStatisticsResponse>>
{
    private readonly ApplicationDbContext _context;

    public GetCareProviderStatisticsQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<CareProviderStatisticsResponse>> Handle(
        GetCareProviderStatisticsQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Fetch all required stats in as few queries as possible
            var activeProviders = _context.CareProviderProfiles.Where(cp => !cp.IsDeleted);

            // Query 1: Total & Verified Providers
            var totalAndVerified = await activeProviders
                .GroupBy(cp => cp.VerificationStatus == VerificationStatus.Verified)
                .Select(g => new { IsVerified = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.IsVerified, x => x.Count, cancellationToken);

            var totalCareProviders = await activeProviders.CountAsync(cancellationToken);
            var approvedCareProviders = totalAndVerified.GetValueOrDefault(true, 0);

            // Query 2: Email Confirmed
            var emailConfirmation = await activeProviders
                .Where(cp => cp.User.EmailVerified)
                .CountAsync(cancellationToken);

            // Query 3: Phone Confirmed
            var phoneConfirmed = await activeProviders
                .Join(
                    _context.UserProfiles,
                    cp => cp.UserId,
                    up => up.ApplicationUserId,
                    (cp, up) => new { cp, up }
                )
                .Where(x => x.up.PhoneNumber != null)
                .CountAsync(cancellationToken);

            // Query 4: Grouped Category Counts
            var categoryCounts = await _context
                .CareProviderCategories.Join(
                    _context.CareCategories,
                    cpc => cpc.CategoryId,
                    cc => cc.Id,
                    (cpc, cc) => new { cpc, cc }
                )
                .Join(
                    activeProviders,
                    x => x.cpc.ProviderId,
                    cp => cp.Id,
                    (x, cp) => x.cc.Name.Trim().ToLower()
                )
                .GroupBy(name => name)
                .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);

            var elderlyCare = categoryCounts.GetValueOrDefault("elderly care", 0);
            var childCare = categoryCounts.GetValueOrDefault("child care", 0);
            var disabilityCare = categoryCounts.GetValueOrDefault("disability care", 0);

            // Build response
            var response = new CareProviderStatisticsResponse
            {
                TotalCareProviders = totalCareProviders,
                ProviderElderlyCare = elderlyCare,
                ProviderChildCare = childCare,
                ProviderDisabilityCare = disabilityCare,
                EmailConfirmation = emailConfirmation,
                PhoneConfirmation = phoneConfirmed,
                ApprovedCareProviders = approvedCareProviders,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            return Result.Failure<CareProviderStatisticsResponse>(
                Error.Internal($"Error retrieving care provider statistics: {ex.Message}")
            );
        }
    }
}
