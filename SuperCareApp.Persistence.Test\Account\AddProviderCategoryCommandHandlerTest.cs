using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Moq;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Categories.Commands;
using Xunit;

namespace SuperCare.Domain.Tests.Account.AddProviderCategoryCommandHandlerTest;

public class AddProviderCategoryCommandHandlerTest : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly Mock<ILogger<AddProviderCategoryCommandHandler>> _mockLogger;

    public AddProviderCategoryCommandHandlerTest()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;
        _context = new ApplicationDbContext(options);
        _mockLogger = new Mock<ILogger<AddProviderCategoryCommandHandler>>();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task Handle_ValidCommand_ShouldAddCategoryToProvider()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        _context.Set<CareCategory>().Add(category);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        var providerCategory = await _context.Set<CareProviderCategory>().FirstOrDefaultAsync();
        Assert.NotNull(providerCategory);
        Assert.Equal(provider.Id, providerCategory.ProviderId);
        Assert.Equal(categoryId, providerCategory.CategoryId);
        Assert.Equal(userId, providerCategory.CreatedBy);
        Assert.False(providerCategory.IsDeleted);
    }

    [Fact]
    public async Task Handle_ProviderNotFound_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareCategory>().Add(category);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not found", result.Error.Message, StringComparison.OrdinalIgnoreCase);
        Assert.Equal("NotFound", result.Error.Code);
    }

    [Fact]
    public async Task Handle_CategoryNotFound_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not found", result.Error.Message, StringComparison.OrdinalIgnoreCase);
        Assert.Equal("NotFound", result.Error.Code);
    }

    [Fact]
    public async Task Handle_CategoryNotActive_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = false, // Not active
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        _context.Set<CareCategory>().Add(category);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not active", result.Error.Message, StringComparison.OrdinalIgnoreCase);
        Assert.Equal("Validation", result.Error.Code);
    }

    [Fact]
    public async Task Handle_CategoryAlreadyAssigned_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var existingProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = provider.Id,
            CategoryId = categoryId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        _context.Set<CareCategory>().Add(category);
        _context.Set<CareProviderCategory>().Add(existingProviderCategory);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains(
            "already has the category",
            result.Error.Message,
            StringComparison.OrdinalIgnoreCase
        );
        Assert.Equal("Conflict", result.Error.Code);
    }

    [Fact]
    public async Task Handle_DeletedProvider_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = true, // Deleted provider
            CreatedAt = DateTime.UtcNow,
        };

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        _context.Set<CareCategory>().Add(category);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not found", result.Error.Message, StringComparison.OrdinalIgnoreCase);
        Assert.Equal("NotFound", result.Error.Code);
    }

    [Fact]
    public async Task Handle_DeletedCategory_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = true,
            IsDeleted = true, // Deleted category
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        _context.Set<CareCategory>().Add(category);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not found", result.Error.Message, StringComparison.OrdinalIgnoreCase);
        Assert.Equal("NotFound", result.Error.Code);
    }

    [Fact]
    public async Task Handle_ExceptionThrown_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        // Don't add any entities to simulate database error
        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("NotFound", result.Error.Code);
    }

    [Fact]
    public async Task Handle_ValidCommand_ShouldLogSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();

        var provider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = providerId,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        var category = new CareCategory
        {
            Id = categoryId,
            Name = "Test Category",
            IsActive = true,
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
        };

        _context.Set<CareProviderProfile>().Add(provider);
        _context.Set<CareCategory>().Add(category);
        await _context.SaveChangesAsync();

        var request = new AddProviderCategoryCommand(
            providerId,
            new ProviderCategoryRequest { CategoryId = categoryId },
            userId
        );

        var handler = new AddProviderCategoryCommandHandler(_context, _mockLogger.Object);

        // Act
        await handler.Handle(request, CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x =>
                x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Added category")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
    }
}
