﻿using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.User
{
    /// <summary>
    /// Parameters for filtering and pagination of user lists
    /// </summary>
    public class UserListParams
    {
        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        public int PageNumber { get; set; } = 1;

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        public string? SearchTerm { get; set; }

        public UserRoleType? Role { get; set; }

        public bool? IsActive { get; set; }

        public bool? EmailVerified { get; set; }

        public string? SortBy { get; set; }

        public bool SortDescending { get; set; } = false;
    }
}
