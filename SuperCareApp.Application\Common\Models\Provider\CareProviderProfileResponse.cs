using System.Text.Json.Serialization;

namespace SuperCareApp.Application.Common.Models.Provider
{
    public record CareProviderProfileResponse
    {
        [JsonIgnore]
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string Gender { get; set; }
        public int YearsExperience { get; set; }
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Verification status as a string (for care providers)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? VerificationStatus { get; set; }
    }
}
