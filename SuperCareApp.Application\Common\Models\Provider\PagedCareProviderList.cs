using System.Collections.Generic;

namespace SuperCareApp.Application.Common.Models.Provider
{
    /// <summary>
    /// Paged list of care providers
    /// </summary>
    public class PagedCareProviderList
    {
        public List<CareProviderProfileResponse> Providers { get; set; } =
            new List<CareProviderProfileResponse>();
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
