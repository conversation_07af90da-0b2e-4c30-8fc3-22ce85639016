using FluentValidation;
using Microsoft.AspNetCore.Http;
using PhoneNumbers;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Request model for updating admin profile
    /// </summary>
    public class UpdateAdminProfileRequest
    {
        /// <summary>
        /// Admin's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Admin's last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Admin's phone number
        /// </summary>
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// Admin's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Admin's gender (Male, Female, Other)
        /// </summary>
        public string Gender { get; set; } = string.Empty;

        /// <summary>
        /// Optional profile picture file
        /// </summary>
        public IFormFile? ProfilePicture { get; set; }
    }

    /// <summary>
    /// Response model for admin profile update
    /// </summary>
    public class UpdateAdminProfileResponse
    {
        /// <summary>
        /// Admin user ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Admin's full name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Admin's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Admin's phone number
        /// </summary>
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// Admin's gender
        /// </summary>
        public string Gender { get; set; } = string.Empty;

        /// <summary>
        /// Profile picture URL (if uploaded)
        /// </summary>
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// When the profile was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Validator for UpdateAdminProfileRequest
    /// </summary>
    public class UpdateAdminProfileRequestValidator : AbstractValidator<UpdateAdminProfileRequest>
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateAdminProfileRequestValidator()
        {
            RuleFor(x => x.FirstName)
                .NotEmpty()
                .WithMessage("First name is required")
                .Length(2, 50)
                .WithMessage("First name must be between 2 and 50 characters")
                .Matches(@"^[a-zA-Z\s'-]+$")
                .WithMessage(
                    "First name can only contain letters, spaces, hyphens, and apostrophes"
                );

            RuleFor(x => x.LastName)
                .NotEmpty()
                .WithMessage("Last name is required")
                .Length(2, 50)
                .WithMessage("Last name must be between 2 and 50 characters")
                .Matches(@"^[a-zA-Z\s'-]+$")
                .WithMessage(
                    "Last name can only contain letters, spaces, hyphens, and apostrophes"
                );

            RuleFor(x => x.Email)
                .NotEmpty()
                .WithMessage("Email is required")
                .EmailAddress()
                .WithMessage("A valid email address is required")
                .MaximumLength(254)
                .WithMessage("Email cannot exceed 254 characters");

            RuleFor(x => x.PhoneNumber)
                .NotEmpty()
                .WithMessage("Phone number is required")
                .Must(BeAValidPhoneNumber)
                .WithMessage(
                    "Invalid phone number format. Must include country code (e.g., +12025550123, +919876543210)."
                )
                .Matches(@"^\+[1-9]\d{1,14}$")
                .WithMessage(
                    "Phone number must start with a '+' followed by country code and number (max 15 digits)."
                )
                .Must(phone => phone.Length >= 10 && phone.Length <= 15)
                .WithMessage(
                    "Phone number must be between 10 and 15 digits, including country code."
                );

            RuleFor(x => x.Gender)
                .NotEmpty()
                .WithMessage("Gender is required")
                .Must(gender => gender == "Male" || gender == "Female" || gender == "Other")
                .WithMessage("Gender must be Male, Female, or Other");

            RuleFor(x => x.ProfilePicture)
                .Must(BeValidImageFile)
                .WithMessage(
                    "Profile picture must be a valid image file (JPEG, PNG, GIF) and less than 5MB"
                )
                .When(x => x.ProfilePicture != null);
        }

        /// <summary>
        /// Validates if the phone number is in a valid format using libphonenumber
        /// </summary>
        private static bool BeAValidPhoneNumber(string? number)
        {
            if (string.IsNullOrWhiteSpace(number))
                return true; // optional field

            var phoneUtil = PhoneNumberUtil.GetInstance();

            try
            {
                var parsed = phoneUtil.Parse(number, null);
                return phoneUtil.IsValidNumber(parsed);
            }
            catch (NumberParseException)
            {
                return false;
            }
        }

        /// <summary>
        /// Validates if the uploaded file is a valid image
        /// </summary>
        private static bool BeValidImageFile(IFormFile? file)
        {
            if (file == null)
                return true; // Optional field

            // Check file size (5MB limit)
            if (file.Length > 5 * 1024 * 1024)
                return false;

            // Check file extension
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(extension))
                return false;

            // Check content type
            var allowedContentTypes = new[] { "image/jpeg", "image/png", "image/gif" };
            return allowedContentTypes.Contains(file.ContentType.ToLowerInvariant());
        }
    }
}
