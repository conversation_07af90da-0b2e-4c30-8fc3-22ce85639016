#!/bin/bash

# SuperCare App CI/CD Build Script
# This script handles building, testing, and deployment of the SuperCare application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="SuperCareApp"
MAIN_PROJECT="super-care-app"
SOLUTION_FILE="super-care-app.sln"
TEST_PROJECT="SuperCareApp.Persistence.Test"
DOCKER_IMAGE_NAME="supercare-api"
BUILD_DIR="bin/release"
ARTIFACTS_DIR="artifacts"
DEV_CONFIG_FILE="dev-config.json"

# Default values
BUILD_CONFIGURATION="Release"
SKIP_TESTS=false
SKIP_DOCKER=false
CLEAN_BUILD=false
VERBOSE=false
TARGET_RUNTIME=""
DOCKER_TAG="latest"

# Development mode variables
DEV_ENVIRONMENT="Development"
DEV_PORT=""
DEV_URLS=""
DEV_AUTO_MIGRATE=false
DEV_WATCH=false
DEV_NO_BUILD=false
DEV_METRICS=false
DEV_APP_PID=""
DEV_SESSION_FILE="/tmp/supercare-dev-session"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_help() {
    cat << EOF
SuperCare App Build Script

Usage: $0 [OPTIONS] [COMMAND]

COMMANDS:
    build           Build the application (default)
    test            Run tests only
    docker          Build Docker image only
    publish         Build and publish application
    clean           Clean build artifacts
    migrate         Run database migrations
    full            Full CI/CD pipeline (build, test, docker)

OPTIONS:
    -c, --config CONFIG     Build configuration (Debug|Release) [default: Release]
    -r, --runtime RUNTIME   Target runtime (linux-x64|win-x64|osx-x64)
    -t, --tag TAG          Docker image tag [default: latest]
    --skip-tests           Skip running tests
    --skip-docker          Skip Docker build
    --clean                Clean before build
    --verbose              Enable verbose output
    -h, --help             Show this help message

EXAMPLES:
    $0                      # Build with default settings
    $0 build --clean        # Clean build
    $0 test                 # Run tests only
    $0 docker -t v1.0.0     # Build Docker image with tag v1.0.0
    $0 full --skip-docker   # Full pipeline without Docker
    $0 publish -r linux-x64 # Publish for Linux

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if dotnet is installed
    if ! command -v dotnet &> /dev/null; then
        print_error ".NET SDK is not installed or not in PATH"
        exit 1
    fi
    
    # Check dotnet version
    DOTNET_VERSION=$(dotnet --version)
    print_info "Using .NET SDK version: $DOTNET_VERSION"
    
    # Check if Docker is available (only if not skipping Docker)
    if [ "$SKIP_DOCKER" = false ] && ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed or not in PATH. Skipping Docker build."
        SKIP_DOCKER=true
    fi
    
    # Check if solution file exists
    if [ ! -f "$SOLUTION_FILE" ]; then
        print_error "Solution file '$SOLUTION_FILE' not found"
        exit 1
    fi
    
    print_success "Prerequisites check completed"
}

# Function to clean build artifacts
clean_build() {
    print_info "Cleaning build artifacts..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        print_info "Removed $BUILD_DIR"
    fi
    
    if [ -d "$ARTIFACTS_DIR" ]; then
        rm -rf "$ARTIFACTS_DIR"
        print_info "Removed $ARTIFACTS_DIR"
    fi
    
    # Clean dotnet artifacts
    dotnet clean "$SOLUTION_FILE" --configuration "$BUILD_CONFIGURATION" ${VERBOSE:+--verbosity detailed}
    
    print_success "Clean completed"
}

# Function to restore NuGet packages
restore_packages() {
    print_info "Restoring NuGet packages..."
    
    dotnet restore "$SOLUTION_FILE" ${VERBOSE:+--verbosity detailed}
    
    if [ $? -eq 0 ]; then
        print_success "Package restore completed"
    else
        print_error "Package restore failed"
        exit 1
    fi
}

# Function to build the application
build_application() {
    print_info "Building application in $BUILD_CONFIGURATION mode..."
    
    local build_args="--configuration $BUILD_CONFIGURATION --no-restore"
    
    if [ -n "$TARGET_RUNTIME" ]; then
        build_args="$build_args --runtime $TARGET_RUNTIME"
    fi
    
    if [ "$VERBOSE" = true ]; then
        build_args="$build_args --verbosity detailed"
    fi
    
    dotnet build "$SOLUTION_FILE" $build_args
    
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to run tests
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        print_warning "Skipping tests as requested"
        return 0
    fi
    
    print_info "Running tests..."
    
    # Create artifacts directory for test results
    mkdir -p "$ARTIFACTS_DIR/test-results"
    
    local test_args="--configuration $BUILD_CONFIGURATION --no-build --no-restore"
    test_args="$test_args --logger trx --results-directory $ARTIFACTS_DIR/test-results"
    test_args="$test_args --collect:\"XPlat Code Coverage\""
    
    if [ "$VERBOSE" = true ]; then
        test_args="$test_args --verbosity detailed"
    fi
    
    dotnet test "$SOLUTION_FILE" $test_args
    
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
        
        # Display test summary if available
        if [ -f "$ARTIFACTS_DIR/test-results"/*.trx ]; then
            print_info "Test results saved to $ARTIFACTS_DIR/test-results/"
        fi
    else
        print_error "Tests failed"
        exit 1
    fi
}

# Function to publish application
publish_application() {
    print_info "Publishing application..."
    
    local output_dir="$BUILD_DIR"
    if [ -n "$TARGET_RUNTIME" ]; then
        output_dir="$BUILD_DIR/$TARGET_RUNTIME"
    fi
    
    local publish_args="--configuration $BUILD_CONFIGURATION --no-build --no-restore"
    publish_args="$publish_args --output $output_dir"
    
    if [ -n "$TARGET_RUNTIME" ]; then
        publish_args="$publish_args --runtime $TARGET_RUNTIME --self-contained false"
    fi
    
    if [ "$VERBOSE" = true ]; then
        publish_args="$publish_args --verbosity detailed"
    fi
    
    dotnet publish "$MAIN_PROJECT/$PROJECT_NAME.csproj" $publish_args
    
    if [ $? -eq 0 ]; then
        print_success "Application published to $output_dir"
        
        # Create deployment package
        if [ -n "$TARGET_RUNTIME" ]; then
            create_deployment_package "$output_dir" "$TARGET_RUNTIME"
        fi
    else
        print_error "Publish failed"
        exit 1
    fi
}

# Function to create deployment package
create_deployment_package() {
    local output_dir=$1
    local runtime=$2
    
    print_info "Creating deployment package for $runtime..."
    
    mkdir -p "$ARTIFACTS_DIR"
    local package_name="$PROJECT_NAME-$runtime-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    tar -czf "$ARTIFACTS_DIR/$package_name" -C "$output_dir" .
    
    if [ $? -eq 0 ]; then
        print_success "Deployment package created: $ARTIFACTS_DIR/$package_name"
    else
        print_warning "Failed to create deployment package"
    fi
}

# Function to build Docker image
build_docker() {
    if [ "$SKIP_DOCKER" = true ]; then
        print_warning "Skipping Docker build as requested"
        return 0
    fi
    
    print_info "Building Docker image..."
    
    local image_tag="$DOCKER_IMAGE_NAME:$DOCKER_TAG"
    
    docker build -t "$image_tag" .
    
    if [ $? -eq 0 ]; then
        print_success "Docker image built successfully: $image_tag"
        
        # Save image info
        mkdir -p "$ARTIFACTS_DIR"
        echo "$image_tag" > "$ARTIFACTS_DIR/docker-image.txt"
        docker images "$DOCKER_IMAGE_NAME:$DOCKER_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}" > "$ARTIFACTS_DIR/docker-info.txt"
    else
        print_error "Docker build failed"
        exit 1
    fi
}

# Function to run database migrations
run_migrations() {
    print_info "Running database migrations..."
    
    # Check if Entity Framework tools are available
    if ! dotnet ef --version &> /dev/null; then
        print_info "Installing Entity Framework tools..."
        dotnet tool install --global dotnet-ef
    fi
    
    # Run migrations
    dotnet ef database update \
        --project SuperCareApp.Persistence \
        --startup-project "$MAIN_PROJECT" \
        --context SuperCareApp.Persistence.Context.ApplicationDbContext \
        ${VERBOSE:+--verbose}
    
    if [ $? -eq 0 ]; then
        print_success "Database migrations completed"
    else
        print_error "Database migrations failed"
        exit 1
    fi
}



# Function to run full CI/CD pipeline
run_full_pipeline() {
    print_info "Starting full CI/CD pipeline..."
    
    check_prerequisites
    
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build
    fi
    
    restore_packages
    build_application
    run_tests
    publish_application
    build_docker
    
    print_success "Full CI/CD pipeline completed successfully!"
    
    # Print summary
    print_info "Build Summary:"
    echo "  - Configuration: $BUILD_CONFIGURATION"
    echo "  - Target Runtime: ${TARGET_RUNTIME:-"Framework Dependent"}"
    echo "  - Docker Tag: $DOCKER_TAG"
    echo "  - Artifacts: $ARTIFACTS_DIR"
}

# Function to display build info
show_build_info() {
    print_info "Build Information:"
    echo "  - Project: $PROJECT_NAME"
    echo "  - Solution: $SOLUTION_FILE"
    echo "  - Configuration: $BUILD_CONFIGURATION"
    echo "  - Target Runtime: ${TARGET_RUNTIME:-"Framework Dependent"}"
    echo "  - Skip Tests: $SKIP_TESTS"
    echo "  - Skip Docker: $SKIP_DOCKER"
    echo "  - Clean Build: $CLEAN_BUILD"
    echo "  - Docker Tag: $DOCKER_TAG"
    echo ""
}

# Parse command line arguments
COMMAND="build"
while [[ $# -gt 0 ]]; do
    case $1 in
        build|test|docker|publish|clean|migrate|full|dev-run|dev-watch|dev-status|dev-stop)
            COMMAND=$1
            shift
            ;;
        -c|--config)
            BUILD_CONFIGURATION="$2"
            shift 2
            ;;
        -r|--runtime)
            TARGET_RUNTIME="$2"
            shift 2
            ;;
        -t|--tag)
            DOCKER_TAG="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        # Development mode options
        --env|--environment)
            DEV_ENVIRONMENT="$2"
            shift 2
            ;;
        --port)
            DEV_PORT="$2"
            shift 2
            ;;
        --urls)
            DEV_URLS="$2"
            shift 2
            ;;
        --auto-migrate)
            DEV_AUTO_MIGRATE=true
            shift
            ;;
        --watch)
            DEV_WATCH=true
            shift
            ;;
        --no-build)
            DEV_NO_BUILD=true
            shift
            ;;
        --metrics)
            DEV_METRICS=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate build configuration
if [[ "$BUILD_CONFIGURATION" != "Debug" && "$BUILD_CONFIGURATION" != "Release" ]]; then
    print_error "Invalid build configuration: $BUILD_CONFIGURATION. Must be Debug or Release."
    exit 1
fi

# Show build information
show_build_info

# Execute the requested command
case $COMMAND in
    build)
        check_prerequisites
        if [ "$CLEAN_BUILD" = true ]; then clean_build; fi
        restore_packages
        build_application
        ;;
    test)
        check_prerequisites
        restore_packages
        build_application
        run_tests
        ;;
    docker)
        check_prerequisites
        build_docker
        ;;
    publish)
        check_prerequisites
        if [ "$CLEAN_BUILD" = true ]; then clean_build; fi
        restore_packages
        build_application
        run_tests
        publish_application
        ;;
    clean)
        clean_build
        ;;
    migrate)
        run_migrations
        ;;
    full)
        run_full_pipeline
        ;;
    dev-run)
        run_dev_application
        ;;
    dev-status)
        show_dev_status
        ;;
    dev-stop)
        stop_dev_application
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac

# =============================================================================
# DEVELOPMENT MODE UTILITY FUNCTIONS
# =============================================================================

# Function to load development configuration
load_dev_config() {
    if [ ! -f "$DEV_CONFIG_FILE" ]; then
        print_warning "Development config file not found: $DEV_CONFIG_FILE"
        return 1
    fi
    
    # Check if jq is available for JSON parsing
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed. Using basic JSON parsing."
        return 1
    fi
    
    print_info "Loading development configuration from $DEV_CONFIG_FILE"
    return 0
}

# Function to check if a port is available
check_port_availability() {
    local port=$1
    
    if [ -z "$port" ]; then
        return 1
    fi
    
    # Validate port number
    if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
        print_error "Invalid port number: $port"
        return 1
    fi
    
    # Check if port is in use using multiple methods
    local port_in_use=false
    
    # Method 1: Use ss (modern replacement for netstat)
    if command -v ss &> /dev/null; then
        if ss -tuln 2>/dev/null | grep -q ":$port "; then
            port_in_use=true
        fi
    # Method 2: Use netstat as fallback
    elif command -v netstat &> /dev/null; then
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            port_in_use=true
        fi
    # Method 3: Try to connect to the port
    else
        if timeout 1 bash -c "echo >/dev/tcp/localhost/$port" 2>/dev/null; then
            port_in_use=true
        fi
    fi
    
    if [ "$port_in_use" = true ]; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to find an available port
find_available_port() {
    local start_port=$1
    local max_attempts=${2:-50}
    local current_port=$start_port
    
    if [ -z "$start_port" ]; then
        start_port=5000
        current_port=$start_port
    fi
    
    print_info "Searching for available port starting from $start_port..."
    
    for ((i=0; i<max_attempts; i++)); do
        if check_port_availability $current_port; then
            print_info "Found available port: $current_port"
            echo $current_port
            return 0
        fi
        ((current_port++))
    done
    
    print_error "Could not find available port after checking $max_attempts ports starting from $start_port"
    return 1
}

# Function to suggest alternative ports
suggest_alternative_ports() {
    local base_port=$1
    local count=${2:-5}
    
    print_info "Port $base_port is in use. Checking alternatives..."
    
    local suggestions=()
    local current_port=$((base_port + 1))
    
    for ((i=0; i<count && ${#suggestions[@]} < 3; i++)); do
        if check_port_availability $current_port; then
            suggestions+=($current_port)
        fi
        ((current_port++))
    done
    
    if [ ${#suggestions[@]} -gt 0 ]; then
        print_info "Available alternative ports:"
        for port in "${suggestions[@]}"; do
            echo "  - $port"
        done
        echo ${suggestions[0]}  # Return first available port
        return 0
    else
        print_warning "No alternative ports found near $base_port"
        return 1
    fi
}

# Function to get port range for environment
get_port_range_for_env() {
    local env_name=$1
    local range_type=${2:-"start"}
    
    if load_dev_config && command -v jq &> /dev/null; then
        local env_lower=$(echo "$env_name" | tr '[:upper:]' '[:lower:]')
        local port=$(jq -r ".portRanges.$env_lower.$range_type // empty" "$DEV_CONFIG_FILE" 2>/dev/null)
        if [ -n "$port" ] && [ "$port" != "null" ]; then
            echo $port
            return 0
        fi
    fi
    
    # Fallback defaults based on environment
    case $env_name in
        "Development"|"development")
            [ "$range_type" = "end" ] && echo 5099 || echo 5000
            ;;
        "Testing"|"testing")
            [ "$range_type" = "end" ] && echo 6099 || echo 6000
            ;;
        "Staging"|"staging")
            [ "$range_type" = "end" ] && echo 7099 || echo 7000
            ;;
        *)
            [ "$range_type" = "end" ] && echo 5099 || echo 5000
            ;;
    esac
}

# Function to find port in environment range
find_port_in_env_range() {
    local env_name=$1
    local start_port=$(get_port_range_for_env "$env_name" "start")
    local end_port=$(get_port_range_for_env "$env_name" "end")
    local max_attempts=$((end_port - start_port + 1))
    
    print_info "Looking for available port in $env_name range: $start_port-$end_port"
    
    local available_port=$(find_available_port "$start_port" "$max_attempts")
    if [ $? -eq 0 ] && [ "$available_port" -le "$end_port" ]; then
        echo $available_port
        return 0
    else
        print_warning "No available ports in $env_name range ($start_port-$end_port)"
        return 1
    fi
}

# Function to validate environment
validate_environment() {
    local env_name=$1
    
    if [ -z "$env_name" ]; then
        print_error "Environment name cannot be empty"
        return 1
    fi
    
    # Check if appsettings file exists for the environment
    local settings_file="$MAIN_PROJECT/appsettings.$env_name.json"
    if [ ! -f "$settings_file" ]; then
        print_warning "Settings file not found: $settings_file"
        print_info "Available environments:"
        list_available_environments
        return 1
    fi
    
    print_info "Environment '$env_name' is valid"
    return 0
}

# Function to list available environments
list_available_environments() {
    print_info "Scanning for available environments..."
    
    local found_envs=()
    
    # Look for appsettings.{Environment}.json files
    for file in "$MAIN_PROJECT"/appsettings.*.json; do
        if [ -f "$file" ]; then
            # Extract environment name from filename
            local basename=$(basename "$file")
            local env_name=${basename#appsettings.}
            env_name=${env_name%.json}
            
            if [ "$env_name" != "appsettings" ]; then
                found_envs+=("$env_name")
            fi
        fi
    done
    
    if [ ${#found_envs[@]} -eq 0 ]; then
        print_warning "No environment-specific settings files found"
        echo "  Create files like: appsettings.Development.json, appsettings.Staging.json"
    else
        echo "  Available environments:"
        for env in "${found_envs[@]}"; do
            echo "    - $env"
        done
    fi
}

# Function to setup environment variables
setup_dev_environment() {
    local env_name=$1
    
    export ASPNETCORE_ENVIRONMENT="$env_name"
    export DOTNET_ENVIRONMENT="$env_name"
    
    # Set development-specific variables
    if [ "$env_name" = "Development" ]; then
        export ASPNETCORE_DETAILEDERRORS="true"
        export ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT="Information"
    fi
    
    print_info "Environment variables set for: $env_name"
}

# Function to get process info
get_dev_process_info() {
    if [ -f "$DEV_SESSION_FILE" ]; then
        source "$DEV_SESSION_FILE"
        if [ -n "$DEV_APP_PID" ] && kill -0 "$DEV_APP_PID" 2>/dev/null; then
            return 0  # Process is running
        fi
    fi
    return 1  # Process not running or session file not found
}

# Function to save development session info
save_dev_session() {
    local pid=$1
    local port=$2
    local env=$3
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$DEV_SESSION_FILE" << EOF
DEV_APP_PID=$pid
DEV_APP_PORT=$port
DEV_APP_ENVIRONMENT=$env
DEV_APP_START_TIME="$start_time"
EOF
    
    print_info "Development session saved (PID: $pid, Port: $port, Env: $env)"
}

# Function to cleanup development session
cleanup_dev_session() {
    if [ -f "$DEV_SESSION_FILE" ]; then
        source "$DEV_SESSION_FILE"
        
        if [ -n "$DEV_APP_PID" ] && kill -0 "$DEV_APP_PID" 2>/dev/null; then
            print_info "Stopping development application (PID: $DEV_APP_PID)..."
            kill -TERM "$DEV_APP_PID" 2>/dev/null || true
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$DEV_APP_PID" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done
            
            # Force kill if still running
            if kill -0 "$DEV_APP_PID" 2>/dev/null; then
                print_warning "Force killing application..."
                kill -KILL "$DEV_APP_PID" 2>/dev/null || true
            fi
        fi
        
        rm -f "$DEV_SESSION_FILE"
        print_success "Development session cleaned up"
    fi
}

# Function to get default port from config
get_default_port() {
    local port_type=${1:-"http"}
    
    if load_dev_config && command -v jq &> /dev/null; then
        local port=$(jq -r ".developmentMode.defaultPorts.$port_type // empty" "$DEV_CONFIG_FILE" 2>/dev/null)
        if [ -n "$port" ] && [ "$port" != "null" ]; then
            echo $port
            return 0
        fi
    fi
    
    # Fallback defaults
    case $port_type in
        "https") echo 5001 ;;
        *) echo 5000 ;;
    esac
}

# =============================================================================
# DEVELOPMENT MODE COMMANDS
# =============================================================================

# Function to run application in development mode
run_dev_application() {
    print_info "Starting development mode..."
    
    # Validate environment
    if ! validate_environment "$DEV_ENVIRONMENT"; then
        exit 1
    fi
    
    # Setup environment variables
    setup_dev_environment "$DEV_ENVIRONMENT"
    
    # Determine port to use
    local app_port="$DEV_PORT"
    if [ -z "$app_port" ]; then
        app_port=$(get_default_port "http")
    fi
    
    # Check port availability and find alternative if needed
    if ! check_port_availability "$app_port"; then
        print_warning "Port $app_port is already in use"
        local alt_port=$(find_available_port "$app_port")
        if [ $? -eq 0 ]; then
            print_info "Using alternative port: $alt_port"
            app_port=$alt_port
        else
            print_error "Could not find available port"
            exit 1
        fi
    fi
    
    # Build and publish if not skipping build
    if [ "$DEV_NO_BUILD" = false ]; then
        print_info "Building application for development..."
        check_prerequisites
        restore_packages
        build_application
        publish_application
    fi
    
    # Run migrations if requested
    if [ "$DEV_AUTO_MIGRATE" = true ]; then
        print_info "Running database migrations..."
        run_migrations
    fi
    
    # Prepare application URLs
    local app_urls="http://localhost:$app_port"
    if [ -n "$DEV_URLS" ]; then
        app_urls="$DEV_URLS"
    fi
    
    # Start the application
    print_info "Starting application..."
    print_info "Environment: $DEV_ENVIRONMENT"
    print_info "URLs: $app_urls"
    print_info "Port: $app_port"
    
    # Set additional environment variables for the application
    export ASPNETCORE_URLS="$app_urls"
    
    # Start application in background
    local app_dll="$BUILD_DIR/$PROJECT_NAME.dll"
    if [ ! -f "$app_dll" ]; then
        print_error "Application DLL not found: $app_dll"
        print_info "Make sure to build and publish the application first"
        exit 1
    fi
    
    # Start the application
    dotnet "$app_dll" &
    local app_pid=$!
    
    # Wait a moment to see if the application starts successfully
    sleep 2
    if ! kill -0 "$app_pid" 2>/dev/null; then
        print_error "Application failed to start"
        exit 1
    fi
    
    # Save session information
    save_dev_session "$app_pid" "$app_port" "$DEV_ENVIRONMENT"
    
    print_success "Application started successfully!"
    print_info "Application is running at: $app_urls"
    print_info "Process ID: $app_pid"
    print_info "Press Ctrl+C to stop the application"
    
    # Setup signal handlers for graceful shutdown
    trap 'cleanup_dev_session; exit 0' INT TERM
    
    # Wait for the application process
    wait "$app_pid"
}

# Function to show development status
show_dev_status() {
    print_info "Development Application Status"
    
    if [ -f "$DEV_SESSION_FILE" ]; then
        source "$DEV_SESSION_FILE"
        
        if [ -n "$DEV_APP_PID" ] && kill -0 "$DEV_APP_PID" 2>/dev/null; then
            print_success "Application is running"
            echo "  - Process ID: $DEV_APP_PID"
            echo "  - Port: $DEV_APP_PORT"
            echo "  - Environment: $DEV_APP_ENVIRONMENT"
            echo "  - Started: $DEV_APP_START_TIME"
            echo "  - URL: http://localhost:$DEV_APP_PORT"
            
            # Show process information
            if command -v ps &> /dev/null; then
                echo "  - Memory Usage: $(ps -o rss= -p $DEV_APP_PID 2>/dev/null | awk '{print int($1/1024)"MB"}' || echo "N/A")"
                echo "  - CPU Usage: $(ps -o %cpu= -p $DEV_APP_PID 2>/dev/null | awk '{print $1"%"}' || echo "N/A")"
            fi
        else
            print_warning "Application process not found (PID: $DEV_APP_PID)"
            print_info "Session file exists but process is not running"
        fi
    else
        print_info "No development session found"
        print_info "Use 'dev-run' command to start the application"
    fi
}

# Function to stop development application
stop_dev_application() {
    print_info "Stopping development application..."
    
    if [ -f "$DEV_SESSION_FILE" ]; then
        cleanup_dev_session
    else
        print_info "No development session found"
    fi
}

print_success "Build script completed successfully!"