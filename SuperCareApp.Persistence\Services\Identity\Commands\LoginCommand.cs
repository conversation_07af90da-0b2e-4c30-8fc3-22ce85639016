using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record LoginCommand(string Email, string Password) : ICommand<Result<AuthResponse>>;

public sealed class LoginCommandHandler : ICommandHandler<LoginCommand, Result<AuthResponse>>
{
    private readonly IAuthService _authService;

    public LoginCommandHandler(IAuthService authService)
    {
        _authService = authService;
    }

    public async Task<Result<AuthResponse>> Handle(
        LoginCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var result = await _authService.AuthenticateAsync(request.Email, request.Password);
            if (result.IsFailure)
            {
                return Result.Failure<AuthResponse>(result.Error);
            }
            return Result.Success(result.Value);
        }
        catch (Exception ex)
        {
            return Result.Failure<AuthResponse>(Error.Internal(ex.Message));
        }
    }
}
