﻿using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Address.Commands
{
    public record UpdateAddressCommand(Guid UserId, UpdateAddressRequest Request)
        : ICommand<Result<Guid>>;

    internal sealed class UpdateAddressCommandHandler
        : ICommandHandler<UpdateAddressCommand, Result<Guid>>
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<UpdateAddressCommandHandler> _logger;

        public UpdateAddressCommandHandler(
            IAddressService addressService,
            ILogger<UpdateAddressCommandHandler> logger
        )
        {
            _addressService = addressService;
            _logger = logger;
        }

        public async Task<Result<Guid>> Handle(
            UpdateAddressCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _addressService.UpdateAddressAsync(request.UserId, request.Request);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating address {AddressId} for user {UserId}",
                    request.Request.AddressId,
                    request.UserId
                );
                return Result.Failure<Guid>(
                    Error.Internal($"Error updating address: {ex.Message}")
                );
            }
        }
    }
}
