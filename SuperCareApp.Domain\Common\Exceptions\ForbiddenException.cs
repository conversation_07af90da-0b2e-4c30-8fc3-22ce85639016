﻿namespace SuperCareApp.Domain.Common.Exceptions
{
    /// <summary>
    /// Exception thrown when a user is authenticated but not allowed to access a resource
    /// </summary>
    public class ForbiddenException : SuperCareException
    {
        public ForbiddenException()
            : base("You do not have permission to access this resource.") { }

        public ForbiddenException(string message)
            : base(message) { }

        public ForbiddenException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}
