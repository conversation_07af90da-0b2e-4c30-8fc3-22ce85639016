namespace SuperCareApp.Application.Common.Interfaces.Mediator;

/// <summary>
/// Marker interface for asynchronous notifications.
/// </summary>
public interface IAsyncNotification { }

/// <summary>
/// Handler for asynchronous notifications.
/// </summary>
/// <typeparam name="TAsyncNotification">The type of async notification being handled.</typeparam>
public interface IAsyncNotificationHandler<in TAsyncNotification>
    where TAsyncNotification : IAsyncNotification
{
    /// <summary>
    /// Handles an async notification.
    /// </summary>
    /// <param name="notification">The notification.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    Task Handle(TAsyncNotification notification, CancellationToken cancellationToken);
}
