# Availability System Workflow

This document provides a detailed explanation of the availability system in the `SuperCareApp` solution. It covers how care providers set their availability, how clients can view available time slots, and how the system handles bookings and other scheduling conflicts.

## High-Level Overview

The availability system is designed to be flexible and robust, allowing care providers to define their working hours on a weekly basis, while also accommodating one-off leaves and existing bookings. The system is built around a few core concepts:

- **Weekly Availability:** Providers define their general availability for each day of the week, including specific time slots when they are available to work.
- **Leave Management:** Providers can add periods of leave, which will override their regular availability.
- **Real-time Slot Calculation:** When a client requests to see a provider's availability for a specific date, the system calculates the available time slots in real-time, taking into account the provider's weekly schedule, any scheduled leave, and existing bookings.
- **Buffer Time:** The system can be configured to add a buffer time between bookings to allow for travel or preparation.

## Workflow Diagram

The following diagram illustrates the workflow of the availability system, from the provider's perspective and the client's perspective.

```mermaid
graph TD
    subgraph "Provider's Workflow"
        A[Provider sets weekly availability] --> B{Availability & Slots stored in DB};
        C[Provider adds a leave period] --> D{Leave stored in DB};
    end

    subgraph "Client's Workflow"
        E[Client requests provider's availability for a date] --> F{System calculates available slots};
        F --> G[Client views available time slots];
        G --> H[Client selects a time slot and books];
    end

    subgraph "System's Calculation Process"
        F --> F1[1. Get provider's weekly availability for the given day];
        F1 --> F2[2. Get provider's scheduled leave for the date];
        F2 --> F3[3. Get provider's existing bookings for the date];
        F3 --> F4[4. Subtract leave and bookings from the weekly availability];
        F4 --> F5[5. Apply buffer time around existing bookings];
        F5 --> F;
    end

    B --> F1;
    D --> F2;
```

## Detailed Workflow

This section provides a step-by-step explanation of the availability system workflow.

### 1. Provider Sets Weekly Availability

- **Action:** The care provider uses the application to define their general availability for each day of the week.
- **Process:** For each day, the provider can specify one or more time slots when they are available to work (e.g., Monday, 9:00 AM - 12:00 PM and 1:00 PM - 5:00 PM).
- **System Response:** The application saves this information in the `Availability` and `AvailabilitySlot` tables in the database.

### 2. Provider Adds a Leave Period

- **Action:** The care provider can add a period of leave, such as for a vacation or a personal day.
- **Process:** The provider selects a start date and an end date for their leave.
- **System Response:** The application saves this information in the `Leave` table. This leave period will override the provider's regular weekly availability.

### 3. Client Requests to View Availability

- **Action:** A client browses the application and requests to see a specific care provider's availability for a given date.
- **Process:** The client selects a provider and a date from a calendar interface.
- **System Response:** The application triggers the `GetAvailableSlotsForDateAsync` method in the `BookingManagementService`.

### 4. System Calculates Available Time Slots

This is the core of the availability system. The `GetAvailableSlotsForDateAsync` method performs the following steps:

1.  **Get Weekly Availability:** The system retrieves the provider's general availability for the selected day of the week from the `Availability` and `AvailabilitySlot` tables.
2.  **Get Scheduled Leave:** The system checks the `Leave` table to see if the provider has any scheduled leave that overlaps with the selected date.
3.  **Get Existing Bookings:** The system queries the `Booking` and `BookingWindow` tables to find any existing bookings for the provider on the selected date.
4.  **Subtract Leave and Bookings:** The system takes the provider's general availability and subtracts the time slots that are blocked by leave or existing bookings.
5.  **Apply Buffer Time:** The system adds a buffer time (if configured) before and after each existing booking to prevent back-to-back appointments.
6.  **Return Available Slots:** The system returns a list of the final, calculated available time slots to the client.

### 5. Client Books a Time Slot

- **Action:** The client views the list of available time slots and selects one to book.
- **Process:** The client confirms the booking details, such as the service required and any special instructions.
- **System Response:** The application creates a new `Booking` record in the database, which will then be factored into future availability calculations.

## Slot Splitting Logic

A key feature of the availability system is its ability to intelligently split a large block of availability into smaller, bookable slots when a booking is made in the middle of it. This is handled by the `IntervalUtils` class, which provides a powerful `Subtract` method.

### Implementation

The slot-splitting logic is implemented in the following way:

1.  **`IntervalUtils.cs`:** The `SuperCareApp.Application/Common/Settings/Interval.cs` file contains the `IntervalUtils` class, which includes the `Subtract` method. This method is designed to take a source interval (e.g., a provider's 9-to-5 availability) and subtract a list of other intervals (e.g., bookings, leaves, and buffers) from it.

2.  **`BookingManagementService.cs`:** The `GetAvailableSlotsForDateAsync` method in `SuperCareApp.Persistence/Services/Bookings/BookingManagementService.cs` is where this logic is used. It first calculates all the "blockages" for a given day and then calls `IntervalUtils.Subtract` to get the final list of available slots.

### Usage Example

Consider the following scenario:

- A care provider is available from **9:00 AM to 5:00 PM**.
- A client books an appointment from **1:00 PM to 2:00 PM**.
- The system is configured with a **30-minute buffer** before and after each booking.

Here's how the system would calculate the new availability:

1.  The original availability is a single interval: `[09:00, 17:00]`.
2.  The booking creates a blockage from 1:00 PM to 2:00 PM.
3.  The buffer adds 30 minutes to each side, so the total blockage is `[12:30, 14:30]`.
4.  The `IntervalUtils.Subtract` method is called with the source interval `[09:00, 17:00]` and the subtraction interval `[12:30, 14:30]`.
5.  The method returns two new, smaller availability intervals:
    - `[09:00, 12:30]`
    - `[14:30, 17:00]`

This ensures that the provider's availability is always up-to-date and that clients can only book time slots that are truly available.
