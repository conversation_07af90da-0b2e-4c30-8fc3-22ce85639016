﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Categories
{
    /// <summary>
    /// Request model for adding or removing a care category to/from a provider
    /// </summary>
    public class ProviderCategoryRequest
    {
        /// <summary>
        /// The ID of the category to add or remove
        /// </summary>
        public Guid CategoryId { get; set; }
    }

    /// <summary>
    /// Request model for adding multiple care categories to a provider
    /// </summary>
    public class ProviderCategoriesRequest
    {
        /// <summary>
        /// The IDs of the categories to add
        /// </summary>
        public List<Guid> CategoryIds { get; set; } = new List<Guid>();
    }

    public class ProviderCategoryRequestValidator : AbstractValidator<ProviderCategoryRequest>
    {
        public ProviderCategoryRequestValidator()
        {
            RuleFor(x => x.CategoryId).NotEmpty().WithMessage("Category ID is required.");
        }
    }

    public class ProviderCategoriesRequestValidator : AbstractValidator<ProviderCategoriesRequest>
    {
        public ProviderCategoriesRequestValidator()
        {
            RuleFor(x => x.CategoryIds)
                .NotEmpty()
                .WithMessage("At least one category ID is required.")
                .Must(ids => ids.All(id => id != Guid.Empty))
                .WithMessage("All category IDs must be valid non-empty GUIDs.");
        }
    }
}
