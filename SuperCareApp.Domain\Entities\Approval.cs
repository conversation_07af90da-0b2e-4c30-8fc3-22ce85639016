﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class Approval : BaseEntity
    {
        // User who needs approval
        public Guid UserId { get; set; }

        // Type of approval
        public ApprovalType ApprovalType { get; set; }

        // Status of the approval
        public bool? IsApproved { get; set; }

        // Reason for rejection if applicable
        public string? RejectionReason { get; set; }

        // Additional data related to the approval (stored as JSON)
        public string? ApprovalData { get; set; }

        // Reference to related entity (if applicable)
        public Guid? RelatedEntityId { get; set; }

        // Admin who processed the approval
        public Guid? ProcessedBy { get; set; }

        // When the approval was processed
        public DateTime? ProcessedAt { get; set; }

        // Notes or comments about the approval
        public string? Notes { get; set; }

        // Navigation properties
        public ApplicationUser User { get; set; } = null!;

        // Optional navigation property for the admin who processed the approval
        public ApplicationUser? Processor { get; set; }
    }
}
