﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Shared.Constants;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Persistence.Services.Documents.Commands;
using SuperCareApp.Persistence.Services.Documents.Queries;
using Swashbuckle.AspNetCore.Annotations;

namespace super_care_app.Controllers;

/// <summary>
/// Controller for managing user documents
/// </summary>
[Authorize]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<Error>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<Error>))]
[ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<Error>))]
public class DocumentsController : BaseController
{
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<DocumentsController> _logger;

    public DocumentsController(
        IMediator mediator,
        ICurrentUserService currentUserService,
        ILogger<DocumentsController> logger
    )
    {
        _mediator = mediator;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    /// <summary>
    /// Uploads a document
    /// </summary>
    /// <param name="request">The upload document request containing the file and document type</param>
    /// <returns>Details of the uploaded document</returns>
    [HttpPost(ApiRoutes.Documents.Upload)]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<DocumentResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<Error>))]
    [SwaggerOperation(
        Summary = "Uploads a document",
        Description = "Uploads a document file. Supported formats: JPEG, PNG, PDF. Maximum size: 5MB.",
        OperationId = "Documents_Upload",
        Tags = new[] { "Documents" }
    )]
    public async Task<IActionResult> UploadDocument([FromForm] UploadDocumentRequest request)
    {
        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return UnauthorizedResponse<DocumentResponse>("User is not authenticated");
        }

        var command = new UploadDocumentCommand(
            request.File,
            request.DocumentType,
            userId,
            request.Issuer,
            request.Country,
            request.CertificationType,
            request.OtherCertificationType,
            request.CertificationNumber,
            request.ExpiryDate
        );
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<DocumentResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Document uploaded successfully");
    }

    /// <summary>
    /// Gets a document by ID
    /// </summary>
    /// <param name="documentId">ID of the document to retrieve</param>
    /// <param name="includeUnapproved">Whether to include unapproved documents (default: false)</param>
    /// <returns>Document details</returns>
    [HttpGet(ApiRoutes.Documents.GetById)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<DocumentResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<Error>))]
    [SwaggerOperation(
        Summary = "Gets a document by ID",
        Description = "Retrieves details of a specific document. By default, only approved documents are returned.",
        OperationId = "Documents_GetById",
        Tags = new[] { "Documents" }
    )]
    public async Task<IActionResult> GetDocumentById(
        Guid documentId,
        [FromQuery] bool includeUnapproved = false
    )
    {
        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return UnauthorizedResponse<DocumentResponse>("User is not authenticated");
        }

        var query = new GetDocumentByIdQuery(documentId, userId, includeUnapproved);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<DocumentResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Document retrieved successfully");
    }

    /// <summary>
    /// Gets all documents for the current user
    /// </summary>
    /// <param name="includeUnapproved">Whether to include unapproved documents (default: false)</param>
    /// <returns>List of documents</returns>
    [HttpGet(ApiRoutes.Documents.GetAll)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<DocumentResponse>>)
    )]
    [SwaggerOperation(
        Summary = "Gets all user documents",
        Description = "Retrieves all documents for the current user. By default, only approved documents are returned.",
        OperationId = "Documents_GetAll",
        Tags = new[] { "Documents" }
    )]
    public async Task<IActionResult> GetAllDocuments([FromQuery] bool includeUnapproved = false)
    {
        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return UnauthorizedResponse<IEnumerable<DocumentResponse>>("User is not authenticated");
        }

        var query = new GetDocumentsByUserIdQuery(userId, includeUnapproved);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<IEnumerable<DocumentResponse>>(result.Error);
        }

        return SuccessResponse(result.Value, "Documents retrieved successfully");
    }

    /// <summary>
    /// Gets all documents for the current user (cached version)
    /// </summary>
    /// <returns>List of documents</returns>
    [HttpGet(ApiRoutes.Documents.GetAllCached)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<DocumentResponse>>)
    )]
    [SwaggerOperation(
        Summary = "Gets all user documents (cached)",
        Description = "Retrieves all documents for the current user with caching",
        OperationId = "Documents_GetAllCached",
        Tags = new[] { "Documents" }
    )]
    public async Task<IActionResult> GetAllDocumentsCached()
    {
        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return UnauthorizedResponse<IEnumerable<DocumentResponse>>("User is not authenticated");
        }

        var query = new GetCachedDocumentsByUserIdQuery(userId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<IEnumerable<DocumentResponse>>(result.Error);
        }

        return SuccessResponse(result.Value, "Documents retrieved successfully (cached)");
    }

    /// <summary>
    /// Updates a document
    /// </summary>
    /// <param name="documentId">ID of the document to update</param>
    /// <param name="request">The update document request containing the file</param>
    /// <returns>Success or failure result</returns>
    [HttpPut(ApiRoutes.Documents.Update)]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<DocumentResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<Error>))]
    [SwaggerOperation(
        Summary = "Updates a document",
        Description = "Updates an existing document with a new file",
        OperationId = "Documents_Update",
        Tags = new[] { "Documents" }
    )]
    public async Task<IActionResult> UpdateDocument(
        Guid documentId,
        [FromForm] UpdateDocumentRequest request
    )
    {
        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return UnauthorizedResponse<DocumentResponse>("User is not authenticated");
        }

        var command = new UpdateDocumentCommand(
            request.File,
            documentId,
            userId,
            request.DocumentType,
            request.Issuer,
            request.Country,
            request.CertificationType,
            request.OtherCertificationType,
            request.CertificationNumber,
            request.ExpiryDate
        );
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<DocumentResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Document updated successfully");
    }

    /// <summary>
    /// Deletes a document
    /// </summary>
    /// <param name="documentId">ID of the document to delete</param>
    /// <returns>Success or failure result</returns>
    [HttpDelete(ApiRoutes.Documents.Delete)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<bool>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<Error>))]
    [SwaggerOperation(
        Summary = "Deletes a document",
        Description = "Deletes a document by ID",
        OperationId = "Documents_Delete",
        Tags = new[] { "Documents" }
    )]
    public async Task<IActionResult> DeleteDocument(Guid documentId)
    {
        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return UnauthorizedResponse<bool>("User is not authenticated");
        }

        var command = new DeleteDocumentCommand(documentId, userId);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<bool>(result.Error);
        }

        return SuccessResponse(result.Value, "Document deleted successfully");
    }
}
