using Microsoft.EntityFrameworkCore;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;

namespace SuperCareApp.Persistence.Test.Categories;

public class CareCategoryRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CareCategoryRepository _repository;

    public CareCategoryRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _repository = new CareCategoryRepository(_context);
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task AddAsync_WithIconAndColor_ShouldPersistCorrectly()
    {
        // Arrange
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 10.50m,
            Icon = "fas fa-heart",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        // Act
        var result = await _repository.AddAsync(category);
        await _context.SaveChangesAsync();

        // Assert
        Assert.True(result.IsSuccess);

        var savedCategory = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == category.Id
        );
        Assert.NotNull(savedCategory);
        Assert.Equal(category.Icon, savedCategory.Icon);
        Assert.Equal(category.Color, savedCategory.Color);
        Assert.Equal(category.Name, savedCategory.Name);
        Assert.Equal(category.Description, savedCategory.Description);
        Assert.Equal(category.IsActive, savedCategory.IsActive);
        Assert.Equal(category.PlatformFee, savedCategory.PlatformFee);
    }

    [Fact]
    public async Task AddAsync_WithNullIconAndColor_ShouldPersistCorrectly()
    {
        // Arrange
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category No Icon",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 5.00m,
            Icon = null,
            Color = null,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        // Act
        var result = await _repository.AddAsync(category);
        await _context.SaveChangesAsync();

        // Assert
        Assert.True(result.IsSuccess);

        var savedCategory = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == category.Id
        );
        Assert.NotNull(savedCategory);
        Assert.Null(savedCategory.Icon);
        Assert.Null(savedCategory.Color);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnIconAndColor()
    {
        // Arrange
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 12.75m,
            Icon = "fas fa-medical",
            Color = "#0066CC",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(category.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(category.Icon, result.Value.Icon);
        Assert.Equal(category.Color, result.Value.Color);
        Assert.Equal(category.Name, result.Value.Name);
        Assert.Equal(category.Id, result.Value.Id);
    }

    [Fact]
    public async Task GetActiveAsync_ShouldReturnIconAndColorForActiveCategories()
    {
        // Arrange
        var activeCategory1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-heart",
            Color = "#FF0000",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        var activeCategory2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = "fas fa-star",
            Color = "#00FF00",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 20.00m,
            Icon = "fas fa-times",
            Color = "#FF0000",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        _context.CareCategories.AddRange(activeCategory1, activeCategory2, inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(2, result.Value.Count());

        var categories = result.Value.ToList();

        var category1 = categories.First(c => c.Name == "Active Category 1");
        Assert.Equal("fas fa-heart", category1.Icon);
        Assert.Equal("#FF0000", category1.Color);

        var category2 = categories.First(c => c.Name == "Active Category 2");
        Assert.Equal("fas fa-star", category2.Icon);
        Assert.Equal("#00FF00", category2.Color);
    }

    [Fact]
    public async Task GetPaginatedAsync_ShouldReturnIconAndColorForPaginatedResults()
    {
        // Arrange
        var categories = new[]
        {
            new CareCategory
            {
                Id = Guid.NewGuid(),
                Name = "Category A",
                IsActive = true,
                PlatformFee = 10.00m,
                Icon = "fas fa-a",
                Color = "#AA0000",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.NewGuid(),
            },
            new CareCategory
            {
                Id = Guid.NewGuid(),
                Name = "Category B",
                IsActive = true,
                PlatformFee = 15.00m,
                Icon = "fas fa-b",
                Color = "#BB0000",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.NewGuid(),
            },
            new CareCategory
            {
                Id = Guid.NewGuid(),
                Name = "Category C",
                IsActive = true,
                PlatformFee = 20.00m,
                Icon = "fas fa-c",
                Color = "#CC0000",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.NewGuid(),
            },
        };

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 1, pageSize: 2);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);

        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(3, totalCount);
        Assert.Equal(2, paginatedCategories.Count());

        // Verify first two categories (ordered by name)
        var categoryList = paginatedCategories.ToList();
        Assert.Equal("Category A", categoryList[0].Name);
        Assert.Equal("fas fa-a", categoryList[0].Icon);
        Assert.Equal("#AA0000", categoryList[0].Color);

        Assert.Equal("Category B", categoryList[1].Name);
        Assert.Equal("fas fa-b", categoryList[1].Icon);
        Assert.Equal("#BB0000", categoryList[1].Color);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateIconAndColor()
    {
        // Arrange
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-old",
            Color = "#000000",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Modify the category
        category.Icon = "fas fa-new";
        category.Color = "#FFFFFF";
        category.Name = "Updated Category";
        category.UpdatedAt = DateTime.UtcNow;
        category.UpdatedBy = Guid.NewGuid();

        // Act
        var result = await _repository.UpdateAsync(category);
        await _context.SaveChangesAsync();

        // Assert
        Assert.True(result.IsSuccess);

        var updatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == category.Id
        );
        Assert.NotNull(updatedCategory);
        Assert.Equal("fas fa-new", updatedCategory.Icon);
        Assert.Equal("#FFFFFF", updatedCategory.Color);
        Assert.Equal("Updated Category", updatedCategory.Name);
        Assert.NotNull(updatedCategory.UpdatedAt);
    }

    [Fact]
    public async Task GetByProviderIdAsync_ShouldReturnCategoriesWithIconAndColor()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var userId = Guid.NewGuid();

        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-provider1",
            Color = "#FF5733",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = "fas fa-provider2",
            Color = "#33FF57",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        var providerCategory1 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category1.Id,
            HourlyRate = 50.00m,
        };

        var providerCategory2 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category2.Id,
            HourlyRate = 60.00m,
        };

        _context.CareCategories.AddRange(category1, category2);
        _context.CareProviderCategories.AddRange(providerCategory1, providerCategory2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(2, result.Value.Count());

        var categories = result.Value.ToList();

        var providerCat1 = categories.First(c => c.Name == "Provider Category 1");
        Assert.Equal("fas fa-provider1", providerCat1.Icon);
        Assert.Equal("#FF5733", providerCat1.Color);

        var providerCat2 = categories.First(c => c.Name == "Provider Category 2");
        Assert.Equal("fas fa-provider2", providerCat2.Icon);
        Assert.Equal("#33FF57", providerCat2.Color);
    }
}
