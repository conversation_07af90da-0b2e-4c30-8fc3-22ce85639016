using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Documents;
using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Storage;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Identity.Queries;

namespace SuperCareApp.Persistence.Test.Account;

public class GetSingleProfileQueryHandlerTest : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly Mock<IUserProfileService> _userProfileServiceMock;
    private readonly Mock<ICareProviderProfileService> _careProviderProfileServiceMock;
    private readonly Mock<ICurrentUserService> _currentUserServiceMock;
    private readonly Mock<UserManager<ApplicationUser>> _userManagerMock;
    private readonly Mock<IDocumentService> _documentServiceMock;
    private readonly Mock<IFileStorageService> _fileStorageServiceMock;
    private readonly GetSingleProfileQueryHandler _handler;

    public GetSingleProfileQueryHandlerTest()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);

        // Setup mocks
        _userProfileServiceMock = new Mock<IUserProfileService>();
        _careProviderProfileServiceMock = new Mock<ICareProviderProfileService>();
        _currentUserServiceMock = new Mock<ICurrentUserService>();
        _documentServiceMock = new Mock<IDocumentService>();
        _fileStorageServiceMock = new Mock<IFileStorageService>();

        // Setup UserManager mock
        var userStoreMock = new Mock<IUserStore<ApplicationUser>>();
        _userManagerMock = new Mock<UserManager<ApplicationUser>>(
            userStoreMock.Object,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        // Setup file storage service default behavior
        _fileStorageServiceMock
            .Setup(f => f.GetFileUrl(It.IsAny<string>()))
            .Returns<string>(path => $"https://storage/{path}");

        // Setup document service default behavior
        _documentServiceMock
            .Setup(d => d.GetAllDocumentsByUserIdAsync(It.IsAny<Guid>(), It.IsAny<bool>()))
            .ReturnsAsync(
                Result.Success<IEnumerable<DocumentResponse>>(new List<DocumentResponse>())
            );

        _handler = new GetSingleProfileQueryHandler(
            _userProfileServiceMock.Object,
            _careProviderProfileServiceMock.Object,
            _currentUserServiceMock.Object,
            _userManagerMock.Object,
            _documentServiceMock.Object,
            _context,
            _fileStorageServiceMock.Object,
            NullLogger<GetSingleProfileQueryHandler>.Instance
        );
    }

    public void Dispose()
    {
        _context.Database.EnsureDeleted();
        _context.Dispose();
    }

    #region Validation Tests

    [Fact]
    public async Task Handle_UnsupportedUserType_ReturnsFailure()
    {
        // Arrange
        var query = new GetSingleProfileQuery(Guid.NewGuid(), (UserType)999);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("BadRequest", result.Error.Code);
        Assert.Equal("Unsupported user type", result.Error.Message);
    }

    [Fact]
    public async Task Handle_EmptyUserId_ReturnsFailure()
    {
        // Arrange
        var query = new GetSingleProfileQuery(Guid.Empty, UserType.Client);

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(Guid.Empty))
            .ReturnsAsync(Result.Failure<UserProfile?>(Error.NotFound("User profile not found")));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("NotFound", result.Error.Code);
    }

    #endregion

    #region Client Profile Tests

    [Fact]
    public async Task Handle_ClientProfile_UserNotFound_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Failure<UserProfile?>(Error.NotFound("User profile not found")));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("NotFound", result.Error.Code);
        Assert.Equal("User profile not found or has been deleted", result.Error.Message);
    }

    [Fact]
    public async Task Handle_ClientProfile_UserDeleted_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        var deletedProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = true,
        };

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Success<UserProfile?>(deletedProfile));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("NotFound", result.Error.Code);
        Assert.Equal("User profile has been deleted", result.Error.Message);
    }

    [Fact]
    public async Task Handle_ClientProfile_Success_ReturnsProfileResponse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        var userProfile = new UserProfile
        {
            Id = profileId,
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "************",
            Gender = "Male",
            DateOfBirth = new DateTime(1990, 1, 1),
            ImagePath = "profile/image.jpg",
            Country = "USA",
            IsDeleted = false,
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        // Setup database data
        _context.Users.Add(applicationUser);
        await _context.SaveChangesAsync();

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(profileId, result.Value.Id);
        Assert.Equal(userId, result.Value.UserId);
        Assert.Equal("John", result.Value.FirstName);
        Assert.Equal("Doe", result.Value.LastName);
        Assert.Equal("<EMAIL>", result.Value.Email);
        Assert.Equal("************", result.Value.PhoneNumber);
        Assert.Equal("Male", result.Value.Gender);
        Assert.Equal(new DateTime(1990, 1, 1), result.Value.DateOfBirth);
        Assert.Equal("https://storage/profile/image.jpg", result.Value.ProfilePictureUrl);
        Assert.Equal("USA", result.Value.Country);
        Assert.Equal(UserType.Client, result.Value.UserType);
        Assert.Equal(0, result.Value.YearsExperience);
    }

    #endregion

    #region CareProvider Profile Tests

    [Fact]
    public async Task Handle_CareProviderProfile_NotFound_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.CareProvider);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("NotFound", result.Error.Code);
        Assert.Equal("Care provider profile not found", result.Error.Message);
    }

    [Fact]
    public async Task Handle_CareProviderProfile_PendingVerification_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.CareProvider);

        var careProviderProfile = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            VerificationStatus = VerificationStatus.Pending,
            IsDeleted = false,
        };

        _context.CareProviderProfiles.Add(careProviderProfile);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("BadRequest", result.Error.Code);
        Assert.Equal("Care provider profile is pending verification", result.Error.Message);
    }

    [Fact]
    public async Task Handle_CareProviderProfile_Success_ReturnsProfileResponse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.CareProvider);

        // Setup entities
        var careCategory = new CareCategory
        {
            Id = categoryId,
            Name = "Elderly Care",
            IsDeleted = false,
        };

        var careProviderProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            YearsExperience = 5,
            Bio = "Experienced caregiver",
            HourlyRate = 25.00m,
            ProvidesOvernight = true,
            ProvidesLiveIn = false,
            Qualifications = "CNA Certified",
            Rating = 4.5m,
            RatingCount = 10,
            IsDeleted = false,
            CareProviderCategories = new List<CareProviderCategory>
            {
                new CareProviderCategory
                {
                    CategoryId = categoryId,
                    ProviderId = providerId,
                    HourlyRate = 30.00m,
                    ExperienceYears = 3,
                    CareCategory = careCategory,
                },
            },
        };

        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "Jane",
            LastName = "Smith",
            PhoneNumber = "************",
            Gender = "Female",
            DateOfBirth = new DateTime(1985, 5, 15),
            ImagePath = "provider/image.jpg",
            IsDeleted = false,
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        // Setup database data
        _context.CareCategories.Add(careCategory);
        _context.CareProviderProfiles.Add(careProviderProfile);
        _context.UserProfiles.Add(userProfile);
        _context.Users.Add(applicationUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(providerId, result.Value.Id);
        Assert.Equal(userId, result.Value.UserId);
        Assert.Equal(providerId, result.Value.ProviderId);
        Assert.Equal("Jane", result.Value.FirstName);
        Assert.Equal("Smith", result.Value.LastName);
        Assert.Equal("<EMAIL>", result.Value.Email);
        Assert.Equal("************", result.Value.PhoneNumber);
        Assert.Equal("Female", result.Value.Gender);
        Assert.Equal(new DateTime(1985, 5, 15), result.Value.DateOfBirth);
        Assert.Equal(5, result.Value.YearsExperience);
        Assert.Equal("Experienced caregiver", result.Value.Bio);
        Assert.Equal(30.00m, result.Value.HourlyRate);
        Assert.True(result.Value.ProvidesOvernight);
        Assert.False(result.Value.ProvidesLiveIn);
        Assert.Equal("CNA Certified", result.Value.Qualifications);
        Assert.Equal(4.5m, result.Value.Rating);
        Assert.Equal(10, result.Value.RatingCount);
        Assert.Equal(UserType.CareProvider, result.Value.UserType);
        Assert.NotNull(result.Value.Categories);
        Assert.Single(result.Value.Categories);
        Assert.Equal(categoryId, result.Value.Categories.First().Id);
        Assert.Equal("Elderly Care", result.Value.Categories.First().Name);
        Assert.Equal(30.00m, result.Value.Categories.First().HourlyRate);
        Assert.Equal(3, result.Value.Categories.First().ExperienceLevel);
        Assert.NotNull(result.Value.TravelExperience);
        Assert.True(result.Value.TravelExperience.WillingToTravel);
    }

    #endregion

    #region Admin Profile Tests

    [Fact]
    public async Task Handle_AdminProfile_UserNotAdmin_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentUserId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Admin);

        var currentUser = new ApplicationUser { Id = currentUserId, Email = "<EMAIL>" };

        _currentUserServiceMock.Setup(s => s.UserId).Returns(currentUserId);
        _userManagerMock
            .Setup(m => m.FindByIdAsync(currentUserId.ToString()))
            .ReturnsAsync(currentUser);
        _userManagerMock.Setup(m => m.IsInRoleAsync(currentUser, "Admin")).ReturnsAsync(false);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Forbidden", result.Error.Code);
        Assert.Equal("You do not have permission to access this profile", result.Error.Message);
    }

    [Fact]
    public async Task Handle_AdminProfile_CurrentUserNotFound_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentUserId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Admin);

        _currentUserServiceMock.Setup(s => s.UserId).Returns(currentUserId);
        _userManagerMock
            .Setup(m => m.FindByIdAsync(currentUserId.ToString()))
            .ReturnsAsync((ApplicationUser)null);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Forbidden", result.Error.Code);
        Assert.Equal("You do not have permission to access this profile", result.Error.Message);
    }

    [Fact]
    public async Task Handle_AdminProfile_CareProviderNotFound_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentUserId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Admin);

        var currentUser = new ApplicationUser { Id = currentUserId, Email = "<EMAIL>" };

        _currentUserServiceMock.Setup(s => s.UserId).Returns(currentUserId);
        _userManagerMock
            .Setup(m => m.FindByIdAsync(currentUserId.ToString()))
            .ReturnsAsync(currentUser);
        _userManagerMock.Setup(m => m.IsInRoleAsync(currentUser, "Admin")).ReturnsAsync(true);
        _careProviderProfileServiceMock
            .Setup(s => s.GetByUserIdNoFilterAsync(userId))
            .ReturnsAsync(
                Result.Failure<CareProviderProfile?>(Error.NotFound("Care provider not found"))
            );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("NotFound", result.Error.Code);
        Assert.Equal(
            "Care provider profile not found, has been deleted, or is pending verification",
            result.Error.Message
        );
    }

    [Fact]
    public async Task Handle_AdminProfile_CareProviderDeleted_ReturnsFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentUserId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Admin);

        var currentUser = new ApplicationUser { Id = currentUserId, Email = "<EMAIL>" };

        var deletedCareProvider = new CareProviderProfile
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            IsDeleted = true,
        };

        _currentUserServiceMock.Setup(s => s.UserId).Returns(currentUserId);
        _userManagerMock
            .Setup(m => m.FindByIdAsync(currentUserId.ToString()))
            .ReturnsAsync(currentUser);
        _userManagerMock.Setup(m => m.IsInRoleAsync(currentUser, "Admin")).ReturnsAsync(true);
        _careProviderProfileServiceMock
            .Setup(s => s.GetByUserIdNoFilterAsync(userId))
            .ReturnsAsync(Result.Success<CareProviderProfile?>(deletedCareProvider));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("NotFound", result.Error.Code);
        Assert.Equal("Care provider profile has been deleted", result.Error.Message);
    }

    [Fact]
    public async Task Handle_AdminProfile_Success_ReturnsProfileResponse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var currentUserId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Admin);

        var currentUser = new ApplicationUser { Id = currentUserId, Email = "<EMAIL>" };

        var careProviderProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            YearsExperience = 3,
            Bio = "Admin viewed provider",
            HourlyRate = 20.00m,
            IsDeleted = false,
            CareProviderCategories = new List<CareProviderCategory>(),
        };

        var careCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Elderly Care",
            IsDeleted = false,
        };

        var careProviderCategory = new CareProviderCategory
        {
            CategoryId = careCategory.Id,
            ProviderId = providerId,
            HourlyRate = 20.00m,
            ExperienceYears = 3,
            CareCategory = careCategory,
        };

        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "Admin",
            LastName = "Provider",
            IsDeleted = false,
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        // Setup database data
        _context.UserProfiles.Add(userProfile);
        _context.CareCategories.Add(careCategory);
        _context.CareProviderProfiles.Add(careProviderProfile);
        _context.CareProviderCategories.Add(careProviderCategory);
        _context.Users.Add(applicationUser);
        await _context.SaveChangesAsync();

        _currentUserServiceMock.Setup(s => s.UserId).Returns(currentUserId);
        _userManagerMock
            .Setup(m => m.FindByIdAsync(currentUserId.ToString()))
            .ReturnsAsync(currentUser);
        _userManagerMock.Setup(m => m.IsInRoleAsync(currentUser, "Admin")).ReturnsAsync(true);
        _careProviderProfileServiceMock
            .Setup(s => s.GetByUserIdNoFilterAsync(userId))
            .ReturnsAsync(Result.Success<CareProviderProfile?>(careProviderProfile));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(providerId, result.Value.Id);
        Assert.Equal(userId, result.Value.UserId);
        Assert.Equal("Admin", result.Value.FirstName);
        Assert.Equal("Provider", result.Value.LastName);
        Assert.Equal("<EMAIL>", result.Value.Email);
        Assert.Equal(3, result.Value.YearsExperience);
        Assert.Equal("Admin viewed provider", result.Value.Bio);
        Assert.Equal(20.00m, result.Value.HourlyRate);
        Assert.Equal(UserType.CareProvider, result.Value.UserType);
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public async Task Handle_DatabaseException_ReturnsInternalError()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Internal", result.Error.Code);
        Assert.Contains("Error retrieving profile", result.Error.Message);
        Assert.Contains("Database connection failed", result.Error.Message);
    }

    [Fact]
    public async Task Handle_DocumentServiceFailure_StillReturnsSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        var userProfile = new UserProfile
        {
            Id = profileId,
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        // Setup database data
        _context.Users.Add(applicationUser);
        await _context.SaveChangesAsync();

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

        // Document service fails
        _documentServiceMock
            .Setup(d => d.GetAllDocumentsByUserIdAsync(userId, true))
            .ReturnsAsync(
                Result.Failure<IEnumerable<DocumentResponse>>(
                    Error.Internal("Document service failed")
                )
            );

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Null(result.Value.Documents); // Documents should be null when service fails
    }

    [Fact]
    public async Task Handle_FileStorageServiceFailure_StillReturnsSuccess()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        var userProfile = new UserProfile
        {
            Id = profileId,
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            ImagePath = "profile/image.jpg",
            IsDeleted = false,
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        // Setup database data
        _context.Users.Add(applicationUser);
        await _context.SaveChangesAsync();

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

        // File storage service throws exception
        _fileStorageServiceMock
            .Setup(f => f.GetFileUrl(It.IsAny<string>()))
            .Throws(new InvalidOperationException("Storage service unavailable"));

        // Act & Assert - Should not throw exception, but handle gracefully
        var result = await _handler.Handle(query, CancellationToken.None);

        // The handler should catch the exception and continue
        Assert.False(result.IsSuccess);
        Assert.NotNull(result.Error);
        Assert.Equal("Internal", result.Error.Code);
    }

    #endregion

    #region Edge Cases and Additional Scenarios

    [Fact]
    public async Task Handle_ClientProfile_WithAddressAndDocuments_ReturnsCompleteProfile()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var addressId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        var userProfile = new UserProfile
        {
            Id = profileId,
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            PhoneNumber = "************",
            IsDeleted = false,
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        var address = new Address
        {
            Id = addressId,
            StreetAddress = "123 Main St",
            City = "Anytown",
            State = "CA",
            PostalCode = "12345",
            Latitude = 37.7749m,
            Longitude = -122.4194m,
        };

        var userAddress = new UserAddress
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            AddressId = addressId,
            IsPrimary = true,
            Label = "Home",
            IsDeleted = false,
            Address = address,
        };

        var documents = new List<DocumentResponse>
        {
            new DocumentResponse
            {
                DocumentId = Guid.NewGuid(),
                FileName = "certificate.pdf",
                DocumentType = "Certificate",
                DocumentUrl = "https://storage/documents/certificate.pdf",
            },
        };

        // Setup database data
        _context.Users.Add(applicationUser);
        _context.Addresses.Add(address);
        _context.UserAddresses.Add(userAddress);
        await _context.SaveChangesAsync();

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

        _documentServiceMock
            .Setup(d => d.GetAllDocumentsByUserIdAsync(userId, true))
            .ReturnsAsync(Result.Success<IEnumerable<DocumentResponse>>(documents));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.PrimaryAddress);
        Assert.Equal("123 Main St", result.Value.PrimaryAddress.StreetAddress);
        Assert.Equal("Anytown", result.Value.PrimaryAddress.City);
        Assert.Equal("CA", result.Value.PrimaryAddress.State);
        Assert.Equal("12345", result.Value.PrimaryAddress.PostalCode);
        Assert.Equal(37.7749m, result.Value.PrimaryAddress.Latitude);
        Assert.Equal(-122.4194m, result.Value.PrimaryAddress.Longitude);
        Assert.Equal("Home", result.Value.PrimaryAddress.Label);
        Assert.NotNull(result.Value.Documents);
        Assert.Single(result.Value.Documents);
        Assert.Equal("certificate.pdf", result.Value.Documents.First().FileName);
    }

    [Fact]
    public async Task Handle_CareProviderProfile_WithMultipleCategories_ReturnsAllCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var category1Id = Guid.NewGuid();
        var category2Id = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.CareProvider);

        var category1 = new CareCategory
        {
            Id = category1Id,
            Name = "Elderly Care",
            IsDeleted = false,
        };
        var category2 = new CareCategory
        {
            Id = category2Id,
            Name = "Child Care",
            IsDeleted = false,
        };

        var userProfile = new UserProfile
        {
            Id = Guid.NewGuid(),
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
        };

        var careProviderProfile = new CareProviderProfile
        {
            Id = providerId,
            UserId = userId,
            VerificationStatus = VerificationStatus.Verified,
            YearsExperience = 7,
            IsDeleted = false,
            CareProviderCategories = new List<CareProviderCategory>
            {
                new CareProviderCategory
                {
                    CategoryId = category1Id,
                    ProviderId = providerId,
                    HourlyRate = 25.00m,
                    ExperienceYears = 5,
                    CareCategory = category1,
                },
                new CareProviderCategory
                {
                    CategoryId = category2Id,
                    ProviderId = providerId,
                    HourlyRate = 20.00m,
                    ExperienceYears = 2,
                    CareCategory = category2,
                },
            },
        };

        var applicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted = false,
        };

        // Setup database data
        _context.UserProfiles.Add(userProfile);
        _context.CareCategories.AddRange(category1, category2);
        _context.CareProviderProfiles.Add(careProviderProfile);
        _context.Users.Add(applicationUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.Categories);
        Assert.Equal(2, result.Value.Categories.Count);

        var elderlyCategory = result.Value.Categories.First(c => c.Name == "Elderly Care");
        Assert.Equal(category1Id, elderlyCategory.Id);
        Assert.Equal(25.00m, elderlyCategory.HourlyRate);
        Assert.Equal(5, elderlyCategory.ExperienceLevel);

        var childCategory = result.Value.Categories.First(c => c.Name == "Child Care");
        Assert.Equal(category2Id, childCategory.Id);
        Assert.Equal(20.00m, childCategory.HourlyRate);
        Assert.Equal(2, childCategory.ExperienceLevel);
    }

    [Fact]
    public async Task Handle_UserWithDeletedApplicationUser_ReturnsEmptyEmail()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var query = new GetSingleProfileQuery(userId, UserType.Client);

        var userProfile = new UserProfile
        {
            Id = profileId,
            ApplicationUserId = userId,
            FirstName = "John",
            LastName = "Doe",
            IsDeleted = false,
        };

        var deletedApplicationUser = new ApplicationUser
        {
            Id = userId,
            Email = "<EMAIL>",
            IsDeleted =
                true // User is deleted
            ,
        };

        // Setup database data
        _context.Users.Add(deletedApplicationUser);
        await _context.SaveChangesAsync();

        _userProfileServiceMock
            .Setup(s => s.GetByUserIdAsync(userId))
            .ReturnsAsync(Result.Success<UserProfile?>(userProfile));

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(string.Empty, result.Value.Email); // Should be empty for deleted user
    }

    #endregion
}
