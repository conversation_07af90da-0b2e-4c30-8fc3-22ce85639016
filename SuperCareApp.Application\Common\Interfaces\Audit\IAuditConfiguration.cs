namespace SuperCareApp.Application.Common.Interfaces;

/// <summary>
/// Configuration interface for audit logging
/// </summary>
public interface IAuditConfiguration
{
    /// <summary>
    /// Determines if an entity type should be audited
    /// </summary>
    /// <param name="entityType">The entity type name</param>
    /// <returns>True if the entity should be audited</returns>
    bool ShouldAuditEntity(string entityType);

    /// <summary>
    /// Determines if a property should be included in audit logs
    /// </summary>
    /// <param name="entityType">The entity type name</param>
    /// <param name="propertyName">The property name</param>
    /// <returns>True if the property should be audited</returns>
    bool ShouldAuditProperty(string entityType, string propertyName);

    /// <summary>
    /// Gets the list of sensitive properties that should be excluded from audit logs
    /// </summary>
    /// <returns>Array of sensitive property names</returns>
    string[] GetSensitiveProperties();

    /// <summary>
    /// Gets the list of entity types that should be excluded from audit logging
    /// </summary>
    /// <returns>Array of entity type names to exclude</returns>
    string[] GetExcludedEntityTypes();
}
