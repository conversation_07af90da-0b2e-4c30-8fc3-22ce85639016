using SuperCareApp.Application.Shared.Utility;

namespace SuperCareApp.Application.Common.Models.Identity
{
    /// <summary>
    /// Paged list of user profiles
    /// </summary>
    public class PagedProfileList
    {
        public List<ProfileResponse> Profiles { get; set; } = new List<ProfileResponse>();
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// Converts this paged list to a PaginationMetadata object
        /// </summary>
        public PaginationMetadata ToMetadata()
        {
            return new PaginationMetadata(PageNumber, TotalPages, TotalCount, PageSize);
        }
    }
}
