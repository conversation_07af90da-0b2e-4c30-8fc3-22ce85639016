using System.Collections.Concurrent;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Mediator;

namespace SuperCareApp.Persistence.Services;

public class Mediator : IMediator
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<Mediator> _logger;
    private readonly ConcurrentDictionary<Type, Delegate> _handlerCache = new();

    /// <summary>
    /// Initializes a new instance of the <see cref="Mediator"/> class.
    /// </summary>
    /// <param name="serviceProvider">The service provider.</param>
    /// <param name="logger">The logger.</param>
    public Mediator(IServiceProvider serviceProvider, ILogger<Mediator> logger)
    {
        _serviceProvider =
            serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc/>
    public async Task<TResponse> Send<TResponse>(
        IRequest<TResponse> request,
        CancellationToken cancellationToken = default
    )
    {
        ArgumentNullException.ThrowIfNull(request);

        _logger.LogDebug("Sending request {RequestType}", request.GetType().Name);

        try
        {
            var requestType = request.GetType();

            // Get all registered behaviors in order
            var behaviors = GetPipelineBehaviors<TResponse>(requestType);

            // Create the handler execution pipeline
            Task<TResponse> Pipeline()
            {
                var handlerType = typeof(IRequestHandler<,>).MakeGenericType(
                    requestType,
                    typeof(TResponse)
                );
                var handlerInstance = GetHandlerInstance(handlerType, requestType);

                // Get cached or create delegate
                var handleDelegate = _handlerCache.GetOrAdd(
                    handlerType,
                    t => CreateHandlerDelegate<TResponse>(handlerType)
                );

                // Execute the delegate
                return ((Func<object, object, CancellationToken, Task<TResponse>>)handleDelegate)(
                    handlerInstance,
                    request,
                    cancellationToken
                );
            }

            // Chain behaviors
            return await ExecuteBehaviorPipeline(request, behaviors, Pipeline, cancellationToken);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            _logger.LogError(ex, "Error sending request {RequestType}", request.GetType().Name);
            throw;
        }
    }

    private async Task<TResponse> ExecuteBehaviorPipeline<TResponse>(
        IRequest<TResponse> request,
        IList<IPipelineBehavior<IRequest<TResponse>, TResponse>> behaviors,
        RequestHandlerDelegate<TResponse> handler,
        CancellationToken cancellationToken
    )
    {
        RequestHandlerDelegate<TResponse> pipeline = handler;

        // Build the pipeline from the last behavior to the first
        for (int i = behaviors.Count - 1; i >= 0; i--)
        {
            var behavior = behaviors[i];
            var nextPipeline = pipeline;

            pipeline = () => behavior.Handle(request, nextPipeline, cancellationToken);
        }

        // Execute the entire pipeline
        return await pipeline();
    }

    /// <inheritdoc/>
    public async Task Send(IRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);

        _logger.LogDebug("Sending request {RequestType}", request.GetType().Name);

        try
        {
            var requestType = request.GetType();

            // Get all registered behaviors in order
            var behaviors = GetVoidPipelineBehaviors(requestType);

            // Create the handler execution pipeline
            Task Pipeline()
            {
                var handlerType = typeof(IRequestHandler<>).MakeGenericType(requestType);
                var handlerInstance = GetHandlerInstance(handlerType, requestType);

                // Get cached or create delegate
                var handleDelegate = _handlerCache.GetOrAdd(
                    handlerType,
                    t => CreateHandlerDelegate(handlerType)
                );

                // Execute the delegate
                return ((Func<object, object, CancellationToken, Task>)handleDelegate)(
                    handlerInstance,
                    request,
                    cancellationToken
                );
            }

            // Chain behaviors
            await ExecuteVoidBehaviorPipeline(request, behaviors, Pipeline, cancellationToken);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            _logger.LogError(ex, "Error sending request {RequestType}", request.GetType().Name);
            throw;
        }
    }

    private async Task ExecuteVoidBehaviorPipeline(
        IRequest request,
        IList<IPipelineBehavior<IRequest>> behaviors,
        RequestHandlerDelegate handler,
        CancellationToken cancellationToken
    )
    {
        RequestHandlerDelegate pipeline = handler;

        // Build the pipeline from the last behavior to the first
        for (int i = behaviors.Count - 1; i >= 0; i--)
        {
            var behavior = behaviors[i];
            var nextPipeline = pipeline;

            pipeline = () => behavior.Handle(request, nextPipeline, cancellationToken);
        }

        // Execute the entire pipeline
        await pipeline();
    }

    /// <inheritdoc/>
    public async Task Publish(
        INotification notification,
        CancellationToken cancellationToken = default
    )
    {
        ArgumentNullException.ThrowIfNull(notification);

        _logger.LogDebug("Publishing notification {NotificationType}", notification.GetType().Name);

        try
        {
            var notificationType = notification.GetType();
            var handlerType = typeof(INotificationHandler<>).MakeGenericType(notificationType);

            var handlers = GetNotificationHandlerInstances(handlerType, notificationType);
            if (handlers.Count == 0)
            {
                _logger.LogWarning(
                    "No handlers found for notification {NotificationType}",
                    notificationType.Name
                );
                return;
            }

            // Get cached or create delegate
            var handleDelegate = _handlerCache.GetOrAdd(
                handlerType,
                t => CreateNotificationHandlerDelegate(handlerType)
            );

            // Execute handlers in parallel
            var tasks = new List<Task>(handlers.Count);
            foreach (var handler in handlers)
            {
                tasks.Add(
                    ((Func<object, object, CancellationToken, Task>)handleDelegate)(
                        handler,
                        notification,
                        cancellationToken
                    )
                );
            }

            await Task.WhenAll(tasks);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            _logger.LogError(
                ex,
                "Error publishing notification {NotificationType}",
                notification.GetType().Name
            );
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task Publish(
        IAsyncNotification notification,
        CancellationToken cancellationToken = default
    )
    {
        if (notification == null)
        {
            throw new ArgumentNullException(nameof(notification));
        }

        _logger.LogDebug(
            "Publishing async notification {NotificationType}",
            notification.GetType().Name
        );

        try
        {
            var notificationType = notification.GetType();
            var handlerType = typeof(IAsyncNotificationHandler<>).MakeGenericType(notificationType);

            var handlers = GetNotificationHandlerInstances(handlerType, notificationType);
            if (handlers.Count == 0)
            {
                _logger.LogWarning(
                    "No handlers found for async notification {NotificationType}",
                    notificationType.Name
                );
                return;
            }

            // Get cached or create delegate
            var handleDelegate = _handlerCache.GetOrAdd(
                handlerType,
                t => CreateAsyncNotificationHandlerDelegate(handlerType)
            );

            // Execute handlers in parallel
            var tasks = new List<Task>(handlers.Count);
            foreach (var handler in handlers)
            {
                tasks.Add(
                    ((Func<object, object, CancellationToken, Task>)handleDelegate)(
                        handler,
                        notification,
                        cancellationToken
                    )
                );
            }

            await Task.WhenAll(tasks);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            _logger.LogError(
                ex,
                "Error publishing async notification {NotificationType}",
                notification.GetType().Name
            );
            throw;
        }
    }

    private object GetHandlerInstance(Type handlerType, Type requestType)
    {
        var handler = _serviceProvider.GetService(handlerType);

        if (handler == null)
        {
            var message = $"No handler registered for request {requestType.Name}";
            _logger.LogError(message);
            throw new InvalidOperationException(message);
        }

        return handler;
    }

    private IList<
        IPipelineBehavior<IRequest<TResponse>, TResponse>
    > GetPipelineBehaviors<TResponse>(Type requestType)
    {
        var openBehaviorType = typeof(IPipelineBehavior<,>);
        var closedBehaviorType = openBehaviorType.MakeGenericType(
            typeof(IRequest<TResponse>),
            typeof(TResponse)
        );
        var specificBehaviorType = openBehaviorType.MakeGenericType(requestType, typeof(TResponse));

        var behaviors = new List<IPipelineBehavior<IRequest<TResponse>, TResponse>>();

        // Get general behaviors (for IRequest<TResponse>)
        try
        {
            var generalBehaviors = _serviceProvider
                .GetServices(closedBehaviorType)
                .Select(b => b as IPipelineBehavior<IRequest<TResponse>, TResponse>)
                .Where(b => b != null)
                .ToList();

            behaviors.AddRange(generalBehaviors!);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Error getting general behaviors for {RequestType}",
                requestType.Name
            );
        }

        // Get specific behaviors (for the concrete request type)
        try
        {
            var specificBehaviors = _serviceProvider
                .GetServices(specificBehaviorType)
                .Select(b =>
                {
                    // Create a dynamic proxy that adapts the specific behavior to the general interface
                    if (b is IPipelineBehavior<IRequest<TResponse>, TResponse> directCast)
                    {
                        return directCast;
                    }

                    // If direct cast fails, create an adapter
                    var behaviorAdapter = new PipelineBehaviorAdapter<TResponse>(
                        b,
                        requestType,
                        _logger
                    );
                    return behaviorAdapter;
                })
                .Where(b => b != null)
                .ToList();

            // Add specific behaviors that aren't already in the list
            foreach (var behavior in specificBehaviors)
            {
                if (!behaviors.Any(b => b.GetType() == behavior.GetType()))
                {
                    behaviors.Add(behavior);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Error getting specific behaviors for {RequestType}",
                requestType.Name
            );
        }

        return behaviors;
    }

    /// <summary>
    /// Adapter class to bridge between specific behavior types and the general IPipelineBehavior interface
    /// </summary>
    private class PipelineBehaviorAdapter<TResponse>
        : IPipelineBehavior<IRequest<TResponse>, TResponse>
    {
        private readonly object _behavior;
        private readonly Type _requestType;
        private readonly ILogger _logger;
        private readonly MethodInfo _handleMethod;

        public PipelineBehaviorAdapter(object behavior, Type requestType, ILogger logger)
        {
            _behavior = behavior;
            _requestType = requestType;
            _logger = logger;

            // Find the Handle method on the behavior
            _handleMethod = _behavior
                .GetType()
                .GetMethod("Handle", BindingFlags.Public | BindingFlags.Instance);
        }

        public async Task<TResponse> Handle(
            IRequest<TResponse> request,
            RequestHandlerDelegate<TResponse> next,
            CancellationToken cancellationToken
        )
        {
            try
            {
                if (_handleMethod != null)
                {
                    // Invoke the Handle method dynamically
                    return await (Task<TResponse>)
                        _handleMethod.Invoke(
                            _behavior,
                            new object[] { request, next, cancellationToken }
                        );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error invoking behavior adapter for {RequestType}",
                    _requestType.Name
                );
            }

            // Fall back to just calling the next delegate if anything goes wrong
            return await next();
        }
    }

    private IList<IPipelineBehavior<IRequest>> GetVoidPipelineBehaviors(Type requestType)
    {
        var openBehaviorType = typeof(IPipelineBehavior<>);
        var closedBehaviorType = openBehaviorType.MakeGenericType(typeof(IRequest));
        var specificBehaviorType = openBehaviorType.MakeGenericType(requestType);

        var behaviors = new List<IPipelineBehavior<IRequest>>();

        // Get general behaviors (for IRequest)
        try
        {
            var generalBehaviors = _serviceProvider
                .GetServices(closedBehaviorType)
                .Select(b => b as IPipelineBehavior<IRequest>)
                .Where(b => b != null)
                .ToList();

            behaviors.AddRange(generalBehaviors!);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Error getting general void behaviors for {RequestType}",
                requestType.Name
            );
        }

        // Get specific behaviors (for the concrete request type)
        try
        {
            var specificBehaviors = _serviceProvider
                .GetServices(specificBehaviorType)
                .Select(b =>
                {
                    // Create a dynamic proxy that adapts the specific behavior to the general interface
                    if (b is IPipelineBehavior<IRequest> directCast)
                    {
                        return directCast;
                    }

                    // If direct cast fails, create an adapter
                    var behaviorAdapter = new VoidPipelineBehaviorAdapter(b, requestType, _logger);
                    return behaviorAdapter;
                })
                .Where(b => b != null)
                .ToList();

            // Add specific behaviors that aren't already in the list
            foreach (var behavior in specificBehaviors)
            {
                if (!behaviors.Any(b => b.GetType() == behavior.GetType()))
                {
                    behaviors.Add(behavior);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Error getting specific void behaviors for {RequestType}",
                requestType.Name
            );
        }

        return behaviors;
    }

    /// <summary>
    /// Adapter class to bridge between specific behavior types and the general IPipelineBehavior interface for void requests
    /// </summary>
    private class VoidPipelineBehaviorAdapter : IPipelineBehavior<IRequest>
    {
        private readonly object _behavior;
        private readonly Type _requestType;
        private readonly ILogger _logger;
        private readonly MethodInfo _handleMethod;

        public VoidPipelineBehaviorAdapter(object behavior, Type requestType, ILogger logger)
        {
            _behavior = behavior;
            _requestType = requestType;
            _logger = logger;

            // Find the Handle method on the behavior
            _handleMethod = _behavior
                .GetType()
                .GetMethod("Handle", BindingFlags.Public | BindingFlags.Instance);
        }

        public async Task Handle(
            IRequest request,
            RequestHandlerDelegate next,
            CancellationToken cancellationToken
        )
        {
            try
            {
                if (_handleMethod != null)
                {
                    // Invoke the Handle method dynamically
                    await (Task)
                        _handleMethod.Invoke(
                            _behavior,
                            new object[] { request, next, cancellationToken }
                        );
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error invoking void behavior adapter for {RequestType}",
                    _requestType.Name
                );
            }

            // Fall back to just calling the next delegate if anything goes wrong
            await next();
        }
    }

    private List<object> GetNotificationHandlerInstances(Type handlerType, Type notificationType)
    {
        return _serviceProvider.GetServices(handlerType).ToList();
    }

    private static Delegate CreateHandlerDelegate<TResponse>(Type handlerType)
    {
        var handleMethod = handlerType.GetMethod("Handle");
        if (handleMethod == null)
        {
            throw new InvalidOperationException(
                $"No 'Handle' method found in handler {handlerType.Name}."
            );
        }

        return (Func<object, object, CancellationToken, Task<TResponse>>)(
            async (handler, request, cancellationToken) =>
            {
                return await (Task<TResponse>)
                    handleMethod.Invoke(handler, new[] { request, cancellationToken });
            }
        );
    }

    private static Delegate CreateHandlerDelegate(Type handlerType)
    {
        var handleMethod = handlerType.GetMethod("Handle");
        if (handleMethod == null)
        {
            throw new InvalidOperationException(
                $"No 'Handle' method found in handler {handlerType.Name}."
            );
        }

        return (Func<object, object, CancellationToken, Task>)(
            async (handler, request, cancellationToken) =>
            {
                await (Task)handleMethod.Invoke(handler, new[] { request, cancellationToken });
            }
        );
    }

    private static Delegate CreateNotificationHandlerDelegate(Type handlerType)
    {
        var handleMethod = handlerType.GetMethod("Handle");
        if (handleMethod == null)
        {
            throw new InvalidOperationException(
                $"No 'Handle' method found in handler {handlerType.Name}."
            );
        }

        return (Func<object, object, CancellationToken, Task>)(
            async (handler, notification, cancellationToken) =>
            {
                await (Task)handleMethod.Invoke(handler, new[] { notification, cancellationToken });
            }
        );
    }

    private static Delegate CreateAsyncNotificationHandlerDelegate(Type handlerType)
    {
        var handleMethod = handlerType.GetMethod("Handle");
        if (handleMethod == null)
        {
            throw new InvalidOperationException(
                $"No 'Handle' method found in handler {handlerType.Name}."
            );
        }

        return (Func<object, object, CancellationToken, Task>)(
            async (handler, notification, cancellationToken) =>
            {
                await (Task)handleMethod.Invoke(handler, new[] { notification, cancellationToken });
            }
        );
    }
}
