# Per-Day Booking Windows & Custom Times: Detailed Implementation Plan

**Version**: 2.1
**Date**: July 2025
**Status**: Implementation Ready

---

## 1. Overview

This document provides a detailed, developer-focused plan for implementing per-day booking windows. It expands on the initial plan by providing specific code-level guidance for modifying services, request/response models, and interfaces.

**Core Objective**: Transition from a single `StartTime`/`EndTime` pair in the `Booking` entity to a collection of `BookingWindow` entities, each representing a specific day within a multi-day booking.

---

## 2. Application Layer Changes (IBookingService)

The `IBookingService` interface requires a new method to handle the creation of bookings with per-day windows. The existing `CreateBookingAsync` will be preserved for backward compatibility.

**File to Modify**: `SuperCareApp.Application/Common/Interfaces/Bookings/IBookingService.cs`

### New Method: `CreateBookingWithWindowsAsync`

This method will accept a collection of `BookingWindowRequest` objects.

```csharp
// In IBookingService.cs

using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Common.Results;
using System.Collections.Generic;

public interface IBookingService
{
    // ... existing methods

    Task<Result<Guid>> CreateBookingWithWindowsAsync(
        Guid userId,
        Guid providerId,
        Guid categoryId,
        DateTime startDate,
        DateTime endDate,
        bool isRecurring,
        string? specialInstructions,
        List<BookingWindowRequest> bookingWindows
    );
}
```

---

## 3. Application Layer Changes (Request/Response Models)

### 3.1. `BookingWindowRequest` (New Model)

This model will represent a single day's booking window within the `CreateBookingRequest`.

**File to Create**: `SuperCareApp.Application/Common/Models/Bookings/BookingWindowRequest.cs`

```csharp
// In BookingWindowRequest.cs

using System;
using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class BookingWindowRequest
    {
        public DateTime Date { get; set; }
        public TimeOnly StartTime { get; set; }
        public TimeOnly EndTime { get; set; }
        public decimal? DailyRate { get; set; }
        public string? DaySpecialInstructions { get; set; }
    }

    public class BookingWindowRequestValidator : AbstractValidator<BookingWindowRequest>
    {
        public BookingWindowRequestValidator()
        {
            RuleFor(x => x.Date).NotEmpty().WithMessage("Date is required for each window.");
            RuleFor(x => x.StartTime).NotEmpty().WithMessage("Start time is required for each window.");
            RuleFor(x => x.EndTime).NotEmpty().WithMessage("End time is required for each window.");
            RuleFor(x => x.EndTime).GreaterThan(x => x.StartTime).WithMessage("End time must be after start time.");
            RuleFor(x => x.DailyRate).GreaterThanOrEqualTo(0).When(x => x.DailyRate.HasValue).WithMessage("Daily rate must be a positive value.");
            RuleFor(x => x.DaySpecialInstructions).MaximumLength(1000).WithMessage("Instructions for a single day cannot exceed 1000 characters.");
        }
    }
}
```

### 3.2. `CreateBookingRequest` (Modification)

The existing `CreateBookingRequest` will be updated to include a list of `BookingWindowRequest` objects. The `StartTime` and `EndTime` properties will become optional.

**File to Modify**: `SuperCareApp.Application/Common/Models/Bookings/CreateBookingRequest.cs`

```csharp
// In CreateBookingRequest.cs

using FluentValidation;
using System.Collections.Generic;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class CreateBookingRequest
    {
        public Guid ProviderId { get; set; }
        public Guid CategoryId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsRecurring { get; set; }
        public string? SpecialInstructions { get; set; }

        // Nullable for backward compatibility
        public TimeOnly? StartTime { get; set; }
        public TimeOnly? EndTime { get; set; }

        // New property for per-day windows
        public List<BookingWindowRequest>? BookingWindows { get; set; }
    }

    public class CreateBookingRequestValidator : AbstractValidator<CreateBookingRequest>
    {
        public CreateBookingRequestValidator()
        {
            // ... existing rules for ProviderId, CategoryId, StartDate, EndDate

            // Rule for when BookingWindows are provided
            When(x => x.BookingWindows != null && x.BookingWindows.Any(), () =>
            {
                RuleFor(x => x.BookingWindows).Must((request, windows) =>
                {
                    // Ensure all window dates fall within the main StartDate and EndDate
                    return windows.All(w => w.Date.Date >= request.StartDate.Date && w.Date.Date <= request.EndDate.Date);
                }).WithMessage("All booking window dates must be within the specified start and end dates.");

                RuleForEach(x => x.BookingWindows).SetValidator(new BookingWindowRequestValidator());
            });

            // Rule for when BookingWindows are NOT provided (legacy support)
            When(x => x.BookingWindows == null || !x.BookingWindows.Any(), () =>
            {
                RuleFor(x => x.StartTime).NotNull().WithMessage("Start time is required for single-window bookings.");
                RuleFor(x => x.EndTime).NotNull().WithMessage("End time is required for single-window bookings.");
                RuleFor(x => x.EndTime).GreaterThan(x => x.StartTime).WithMessage("End time must be after start time.");
            });
        }
    }
}
```

### 3.3. `BookingWindowResponse` (New Model)

This model will be used to return the details of each booking window in the `BookingResponse`.

**File to Create**: `SuperCareApp.Application/Common/Models/Bookings/BookingWindowResponse.cs`

```csharp
// In BookingWindowResponse.cs

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class BookingWindowResponse
    {
        public DateTime Date { get; set; }
        public TimeOnly StartTime { get; set; }
        public TimeOnly EndTime { get; set; }
        public double DurationHours { get; set; }
        public decimal? DailyRate { get; set; }
        public string? DaySpecialInstructions { get; set; }
    }
}
```

### 3.4. `BookingResponse` (Modification)

The `BookingResponse` will be updated to include a collection of `BookingWindowResponse` objects.

**File to Modify**: `SuperCareApp.Application/Common/Models/Bookings/BookingResponse.cs`

```csharp
// In BookingResponse.cs

using System.Collections.Generic;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class BookingResponse
    {
        public Guid BookingId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalAmount { get; set; }
        public double TotalDurationHours { get; set; } // New computed property
        public string Status { get; set; }
        public string ServiceType { get; set; }
        public Client Clients { get; set; }
        public CareProvider CareProviders { get; set; }

        // New property
        public List<BookingWindowResponse> BookingWindows { get; set; }

        // ... existing nested classes (Client, Location, CareProvider)
    }
}
```

---

## 4. Persistence Layer Changes (BookingService)

The `BookingService` will implement the new `CreateBookingWithWindowsAsync` method and modify the existing `CreateBookingAsync` to handle both legacy and new requests.

**File to Modify**: `SuperCareApp.Persistence/Services/Bookings/BookingService.cs`

### 4.1. Implementing `CreateBookingWithWindowsAsync`

```csharp
// In BookingService.cs

public async Task<Result<Guid>> CreateBookingWithWindowsAsync(
    Guid userId,
    Guid providerId,
    Guid categoryId,
    DateTime startDate,
    DateTime endDate,
    bool isRecurring,
    string? specialInstructions,
    List<BookingWindowRequest> bookingWindows)
{
    try
    {
        var validationResult = await ValidateBookingEntitiesAsync(userId, providerId, categoryId);
        if (!validationResult.IsSuccess)
            return Result.Failure<Guid>(validationResult.Error);

        var (client, providerProfile, category) = validationResult.Value;

        // 1. Validate each window against provider availability
        foreach (var window in bookingWindows)
        {
            var availabilityResult = await ValidateAvailabilityAsync(providerId, window.Date, window.StartTime, window.EndTime);
            if (!availabilityResult.IsSuccess)
            {
                return Result.Failure<Guid>(Error.Conflict($"Provider is not available on {window.Date.ToShortDateString()} from {window.StartTime} to {window.EndTime}."));
            }
        }

        // 2. Calculate total amounts from windows
        var totalAmount = bookingWindows.Sum(w => w.DailyRate ?? providerProfile.HourlyRate * (decimal)(w.EndTime - w.StartTime).TotalHours);
        var serviceFee = category.PlatformFee; // Or calculate based on total
        var providerAmount = totalAmount / (1 + serviceFee);


        // 3. Create Booking entity (without StartTime/EndTime)
        var booking = new Booking
        {
            // ... set properties like ClientId, ProviderId, etc.
            StartDate = startDate,
            EndDate = endDate,
            TotalAmount = totalAmount,
            PlatformFee = serviceFee,
            ProviderAmount = providerAmount,
            SpecialInstructions = specialInstructions,
            CreatedBy = userId,
            IsRecurring = isRecurring
        };

        // 4. Create BookingWindow entities
        var bookingWindowEntities = bookingWindows.Select(w => new BookingWindow
        {
            BookingId = booking.Id,
            Date = w.Date,
            StartTime = w.StartTime,
            EndTime = w.EndTime,
            DailyRate = w.DailyRate,
            DaySpecialInstructions = w.DaySpecialInstructions,
            CreatedBy = userId
        }).ToList();

        booking.BookingWindows = bookingWindowEntities;

        // 5. Save to database
        await _context.Bookings.AddAsync(booking);
        // BookingWindows will be saved automatically due to the relationship
        await _context.SaveChangesAsync();

        return Result.Success(booking.Id);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error creating booking with windows for user {UserId}", userId);
        return Result.Failure<Guid>(Error.Internal("Error creating booking with windows."));
    }
}
```

### 4.2. Modifying `CreateBookingAsync` (Legacy Support)

The existing `CreateBookingAsync` will be adapted to handle requests that use the new `BookingWindows` property.

```csharp
// In BookingService.cs, inside the BookingsController

// This is a conceptual change for the controller action that calls the service.
// The controller will decide which service method to call.

// In BookingsController.cs
public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequest request)
{
    // ... validation ...
    var userId = _currentUserService.UserId.Value;

    Result<Guid> bookingResult;

    if (request.BookingWindows != null && request.BookingWindows.Any())
    {
        // New flow
        bookingResult = await _bookingService.CreateBookingWithWindowsAsync(
            userId,
            request.ProviderId,
            request.CategoryId,
            request.StartDate,
            request.EndDate,
            request.IsRecurring,
            request.SpecialInstructions,
            request.BookingWindows
        );
    }
    else
    {
        // Legacy flow
        // This can be refactored to call CreateBookingWithWindowsAsync
        // by converting the single time window into a list of BookingWindowRequest
        var windows = new List<BookingWindowRequest>();
        for (var date = request.StartDate.Date; date <= request.EndDate.Date; date = date.AddDays(1))
        {
            windows.Add(new BookingWindowRequest
            {
                Date = date,
                StartTime = request.StartTime.Value,
                EndTime = request.EndTime.Value
            });
        }
        bookingResult = await _bookingService.CreateBookingWithWindowsAsync(
            userId,
            request.ProviderId,
            request.CategoryId,
            request.StartDate,
            request.EndDate,
            request.IsRecurring,
            request.SpecialInstructions,
            windows
        );
    }

    if (bookingResult.IsFailure)
    {
        // ... handle error
    }

    return SuccessResponse(...);
}
```

### 4.3. Updating `GetBookingByIdAsync` and `GetAllBookingsAsync`

The projection logic in these methods must be updated to populate the `BookingWindows` property in the `BookingResponse`.

```csharp
// In BookingService.cs, inside CreateBookingResponseProjection

private static Expression<Func<Booking, BookingResponse>> CreateBookingResponseProjection() =>
    booking => new BookingResponse
    {
        BookingId = booking.Id,
        StartDate = booking.StartDate,
        EndDate = booking.EndDate,
        TotalAmount = booking.TotalAmount,
        Status = booking.Status != null ? booking.Status.Status.GetDescription() : string.Empty,
        ServiceType = booking.Category != null ? booking.Category.Name : string.Empty,
        Clients = CreateClientResponse(booking),
        CareProviders = CreateCareProviderResponse(booking),

        // Populate BookingWindows
        BookingWindows = booking.BookingWindows.Select(w => new BookingWindowResponse
        {
            Date = w.Date,
            StartTime = w.StartTime,
            EndTime = w.EndTime,
            DurationHours = (w.EndTime - w.StartTime).TotalHours,
            DailyRate = w.DailyRate,
            DaySpecialInstructions = w.DaySpecialInstructions
        }).ToList(),

        // Calculate TotalDurationHours from windows
        TotalDurationHours = booking.BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours)
    };
```

---

## 5. API Layer Changes (BookingsController)

The `CreateBooking` endpoint in `BookingsController` will now transparently handle both legacy and per-day window requests.

**File to Modify**: `super-care-app/Controllers/BookingsController.cs`

```csharp
// In BookingsController.cs

[HttpPost(ApiRoutes.Bookings.CreateBooking)]
public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequest request)
{
    var validator = await _requestValidator.ValidateAsync(request, new CreateBookingRequestValidator());
    if (!validator.IsSuccess)
    {
        return ErrorResponseFromError<object>(Error.Validation("Validation failed", validator.Error.ValidationErrors));
    }

    if (!_currentUserService.IsAuthenticated)
    {
        return Unauthorized("User is not authenticated.");
    }
    var userId = _currentUserService.UserId.Value;

    Result<Guid> bookingResult;

    if (request.BookingWindows != null && request.BookingWindows.Any())
    {
        // New flow with per-day windows
        var command = new CreateBookingWithWindowsCommand(userId, request); // A new command for this
        bookingResult = await _mediator.Send(command);
    }
    else
    {
        // Legacy flow, create a command that handles the old structure
        var command = new CreateLegacyBookingCommand(userId, request);
        bookingResult = await _mediator.Send(command);
    }

    if (bookingResult.IsFailure)
    {
        _logger.LogError("Error creating booking: {Error}", bookingResult.Error);
        return ErrorResponseFromError<object>(bookingResult.Error);
    }

    return SuccessResponse(new GenericObjectResponse("Booking created successfully", bookingResult.Value));
}
```

This implies that you should create two new commands `CreateBookingWithWindowsCommand` and `CreateLegacyBookingCommand` and their handlers to encapsulate the business logic. The handlers will call the respective `IBookingService` methods.

---

## 6. Handling Updates to Bookings

The existing `UpdateBooking` endpoint will be enhanced to support modifications to per-day booking windows without requiring a new endpoint. The logic will be contained within the existing `UpdateBookingCommandHandler`.

**File to Modify**: `super-care-app/Controllers/BookingsController.cs`
**Associated Command Handler**: `UpdateBookingCommandHandler`

### 6.1. `UpdateBookingRequest` (Modification)

The `UpdateBookingRequest` model will be updated to include the `BookingWindows` collection, similar to the `CreateBookingRequest`.

**File to Modify**: `SuperCareApp.Application/Common/Models/Bookings/UpdateBookingRequest.cs`

```csharp
// In UpdateBookingRequest.cs

public class UpdateBookingRequest
{
    // ... existing properties like ProviderId, CategoryId, etc.

    // Add the BookingWindows collection
    public List<BookingWindowRequest>? BookingWindows { get; set; }
}

public class UpdateBookingRequestValidator : AbstractValidator<UpdateBookingRequest>
{
    public UpdateBookingRequestValidator()
    {
        // ... existing validation rules

        // Add validation for the BookingWindows collection
        When(x => x.BookingWindows != null && x.BookingWindows.Any(), () =>
        {
            RuleForEach(x => x.BookingWindows).SetValidator(new BookingWindowRequestValidator());
        });
    }
}
```

### 6.2. `UpdateBooking` Logic in `BookingService`

The `UpdateBookingAsync` method in `BookingService` will contain the logic to handle the update. It will perform a "diff" between the existing `BookingWindow` entities and the incoming `BookingWindowRequest` data.

**File to Modify**: `SuperCareApp.Persistence/Services/Bookings/BookingService.cs`

```csharp
// In BookingService.cs

public async Task<Result> UpdateBookingAsync(Guid userId, Guid bookingId, UpdateBookingRequest request)
{
    var booking = await _context.Bookings
        .Include(b => b.BookingWindows)
        .FirstOrDefaultAsync(b => b.Id == bookingId);

    if (booking == null)
        return Result.Failure(Error.NotFound("Booking not found."));

    // 1. Update parent booking properties (dates, instructions, etc.)
    booking.StartDate = request.StartDate;
    booking.EndDate = request.EndDate;
    booking.SpecialInstructions = request.SpecialInstructions;
    // ... other properties

    // 2. Handle BookingWindows update
    if (request.BookingWindows != null)
    {
        // Simple approach: Remove existing and add new ones.
        // For a more sophisticated implementation, you could perform a diff
        // to update existing, delete removed, and add new windows.
        _context.BookingWindows.RemoveRange(booking.BookingWindows);

        var newWindows = request.BookingWindows.Select(w => new BookingWindow
        {
            BookingId = booking.Id,
            Date = w.Date,
            StartTime = w.StartTime,
            EndTime = w.EndTime,
            DailyRate = w.DailyRate,
            DaySpecialInstructions = w.DaySpecialInstructions,
            CreatedBy = userId
        }).ToList();

        booking.BookingWindows = newWindows;
    }

    // 3. Recalculate totals
    // ... logic to recalculate TotalAmount, etc.

    await _context.SaveChangesAsync();

    return Result.Success();
}
```

---

## 7. Retrieving Booking Details

The `GetBookingByIdAsync` and `GetAllBookingsAsync` methods will already return the `BookingWindows` data due to the changes in the `BookingResponse` model and the projection logic (Section 4.3). No further changes are needed for the "get" operations.

---

## 8. End-to-End Booking Flow (Mermaid Flowchart)

This flowchart illustrates the complete booking lifecycle, from creation to completion, designed to be easily understood by a junior developer.

```mermaid
graph TD
    subgraph Client_Interaction
        A[Client initiates booking] --> B{Request has BookingWindows?}
    end

    subgraph API_Layer_BookingsController
        B -- Yes --> C[CreateBookingWithWindowsCommand]
        B -- No --> D[CreateLegacyBookingCommand]
        C --> E{Mediator}
        D --> E
    end

    subgraph Application_Layer_Commands_and_Services
        E --> F[CommandHandler]
        F --> G[IBookingService]
        G --> H{Validate Provider Availability}
    end

    subgraph Persistence_Layer_Database
        H -- Available --> I[Create Booking & BookingWindows]
        I --> J[Save to Database]
        H -- Not Available --> K[Return Availability Error]
    end

    subgraph Response_Flow
        J --> L["Return Success \\(Booking ID\\)"]
        K --> L
        L --> M[API sends response to Client]
    end

    subgraph Update_Flow
        U_A[Client initiates update] --> U_B[UpdateBookingCommand]
        U_B --> U_C{Mediator}
        U_C --> U_D[UpdateBookingCommandHandler]
        U_D --> U_E[IBookingService.UpdateBookingAsync]
        U_E --> U_F{Load Booking & Windows}
        U_F --> U_G[Apply Changes & Recalculate]
        U_G --> U_H[Save to Database]
        U_H --> U_I[Return Success]
        U_I --> U_J[API sends response to Client]
    end

    subgraph Get_Flow
        G_A[Client requests booking details] --> G_B[GetBookingByIdQuery]
        G_B --> G_C{Mediator}
        G_C --> G_D[GetBookingByIdQueryHandler]
        G_D --> G_E[IBookingService.GetBookingByIdAsync]
        G_E --> G_F{Project to BookingResponse}
        G_F --> G_H[Return Response with Windows]
        G_H --> G_I[API sends response to Client]
    end

```

---

## 9. Conclusion

This detailed plan outlines the necessary changes across the application, persistence, and API layers. By following this guide, the development team can implement the per-day booking window functionality in a structured and backward-compatible manner. The inclusion of update and retrieval logic, along with a clear flowchart, ensures that even junior developers can understand the end-to-end process and contribute effectively.
