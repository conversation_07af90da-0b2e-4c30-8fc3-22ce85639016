# Design Document

## Overview

The Stripe payment integration will transform the current basic payment system into a comprehensive financial platform that handles client payments, provider payouts, KYC verification, and split payment processing. The system will leverage Stripe's robust APIs for secure payment processing, Connect for marketplace functionality, and webhooks for real-time synchronization.

The design follows Clean Architecture principles with CQRS implementation, providing specialized services for payment processing, KYC management, payout handling, and financial reporting. The system will maintain PCI compliance through Stripe's secure infrastructure while providing a seamless user experience.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
│  PaymentsController, PayoutsController, KYCController      │
├─────────────────────────────────────────────────────────────┤
│              Application Layer (CQRS Handlers)              │
│  Commands: ProcessPayment, SetupPayout, VerifyKYC          │
│  Queries: GetPayments, GetPayouts, GetKYCStatus            │
├─────────────────────────────────────────────────────────────┤
│              Application Services (Specialized)             │
│  IStripePaymentService, IKYCService, IPayoutService        │
│  ISplitPaymentService, IWebhookService                     │
├─────────────────────────────────────────────────────────────┤
│                   Domain Layer (Enhanced)                   │
│  StripeAccount, PaymentMethod, KYCVerification             │
├─────────────────────────────────────────────────────────────┤
│                   External Services                         │
│  Stripe API, Stripe Connect, Stripe Webhooks               │
└─────────────────────────────────────────────────────────────┘
```

### Integration Points

1. **Stripe Connect**: Marketplace functionality for provider payouts
2. **Stripe Payment Intents**: Secure payment processing with 3D Secure
3. **Stripe Identity**: KYC verification and document collection
4. **Stripe Webhooks**: Real-time event synchronization
5. **Existing Booking System**: Integration with booking and invoice workflows## Co
mponents and Interfaces

### Enhanced Domain Model

#### New Stripe-Related Entities

```csharp
public class StripeAccount : BaseEntity
{
    public Guid UserId { get; set; }
    public string StripeAccountId { get; set; } = string.Empty;
    public string AccountType { get; set; } = "express"; // express, standard, custom
    public StripeAccountStatus Status { get; set; } = StripeAccountStatus.Pending;
    public bool ChargesEnabled { get; set; }
    public bool PayoutsEnabled { get; set; }
    public DateTime? OnboardingCompletedAt { get; set; }
    public string? OnboardingUrl { get; set; }
    public string? BusinessType { get; set; }
    public string? Country { get; set; }
    public string? Currency { get; set; }
    public string? Email { get; set; }
    public DateTime? KYCVerifiedAt { get; set; }
    public string? KYCStatus { get; set; }
    public string? RequirementsJson { get; set; } // JSON of pending requirements
    
    // Navigation properties
    public ApplicationUser User { get; set; } = null!;
    public ICollection<BankAccount> BankAccounts { get; set; } = new List<BankAccount>();
    public ICollection<Payout> Payouts { get; set; } = new List<Payout>();
    
    // Computed properties
    public bool IsFullyOnboarded => ChargesEnabled && PayoutsEnabled && KYCVerifiedAt.HasValue;
    public bool CanReceivePayouts => PayoutsEnabled && BankAccounts.Any(ba => ba.IsDefault);
}

public enum StripeAccountStatus
{
    Pending,
    Restricted,
    Active,
    Rejected
}

public class PaymentMethod : BaseEntity
{
    public Guid UserId { get; set; }
    public string StripePaymentMethodId { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // card, bank_account, etc.
    public string Last4 { get; set; } = string.Empty;
    public string? Brand { get; set; }
    public int? ExpiryMonth { get; set; }
    public int? ExpiryYear { get; set; }
    public string? Country { get; set; }
    public bool IsDefault { get; set; }
    public bool IsExpired { get; set; }
    public DateTime? LastUsedAt { get; set; }
    
    // Navigation properties
    public ApplicationUser User { get; set; } = null!;
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
}

public class BankAccount : BaseEntity
{
    public Guid StripeAccountId { get; set; }
    public string StripeBankAccountId { get; set; } = string.Empty;
    public string AccountHolderName { get; set; } = string.Empty;
    public string Last4 { get; set; } = string.Empty;
    public string BankName { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string Currency { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // new, validated, verified, verification_failed, errored
    public bool IsDefault { get; set; }
    public DateTime? VerifiedAt { get; set; }
    
    // Navigation properties
    public StripeAccount StripeAccount { get; set; } = null!;
}

public class KYCVerification : BaseEntity
{
    public Guid UserId { get; set; }
    public string StripeVerificationSessionId { get; set; } = string.Empty;
    public KYCStatus Status { get; set; } = KYCStatus.Pending;
    public string? VerificationUrl { get; set; }
    public DateTime? SubmittedAt { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public string? FailureReason { get; set; }
    public string? RequiredDocuments { get; set; }
    public int AttemptCount { get; set; } = 0;
    
    // Navigation properties
    public ApplicationUser User { get; set; } = null!;
}

public enum KYCStatus
{
    Pending,
    RequiresInput,
    Processing,
    Verified,
    Failed,
    Canceled
}
```#
### Enhanced Payment Entity

```csharp
public class Payment : BaseEntity
{
    // Existing properties
    public Guid BookingId { get; set; }
    public decimal Amount { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public PaymentStatus Status { get; set; }
    public DateTime PaymentDateTime { get; set; }
    public string? InvoiceNumber { get; set; }
    
    // New Stripe-specific properties
    public string? StripePaymentIntentId { get; set; }
    public string? StripePaymentMethodId { get; set; }
    public string? StripeChargeId { get; set; }
    public decimal PlatformFeeAmount { get; set; }
    public decimal ProviderAmount { get; set; }
    public string Currency { get; set; } = "USD";
    public string? ClientSecret { get; set; }
    public string? FailureReason { get; set; }
    public string? ReceiptUrl { get; set; }
    public bool RequiresAction { get; set; }
    public DateTime? CapturedAt { get; set; }
    public string? RefundId { get; set; }
    public decimal RefundedAmount { get; set; }
    
    // Split payment tracking
    public string? TransferGroupId { get; set; }
    public bool IsHeld { get; set; } // For escrow functionality
    public DateTime? HoldUntil { get; set; }
    public DateTime? ReleasedAt { get; set; }
    
    // Navigation properties
    public Booking Booking { get; set; } = null!;
    public PaymentMethod? PaymentMethodEntity { get; set; }
    public ICollection<Payout> Payouts { get; set; } = new List<Payout>();
    
    // Computed properties
    public bool IsRefundable => Status == PaymentStatus.Completed && RefundedAmount < Amount;
    public decimal RefundableAmount => Amount - RefundedAmount;
    public bool IsFullyRefunded => RefundedAmount >= Amount;
}

public class Payout : BaseEntity
{
    public Guid StripeAccountId { get; set; }
    public Guid? PaymentId { get; set; }
    public string StripePayoutId { get; set; } = string.Empty;
    public string? StripeTransferId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public PayoutStatus Status { get; set; } = PayoutStatus.Pending;
    public DateTime? ArrivalDate { get; set; }
    public string? Description { get; set; }
    public string? FailureReason { get; set; }
    public string? StatementDescriptor { get; set; }
    
    // Navigation properties
    public StripeAccount StripeAccount { get; set; } = null!;
    public Payment? Payment { get; set; }
}

public enum PayoutStatus
{
    Pending,
    InTransit,
    Paid,
    Failed,
    Canceled
}
```### Ap
plication Services

#### Stripe Payment Service

```csharp
public interface IStripePaymentService
{
    Task<Result<PaymentIntent>> CreatePaymentIntentAsync(CreatePaymentIntentRequest request, CancellationToken ct = default);
    Task<Result<PaymentIntent>> ConfirmPaymentIntentAsync(string paymentIntentId, string? paymentMethodId = null, CancellationToken ct = default);
    Task<Result<Refund>> ProcessRefundAsync(ProcessRefundRequest request, CancellationToken ct = default);
    Task<Result<PaymentMethod>> AttachPaymentMethodAsync(string paymentMethodId, Guid userId, CancellationToken ct = default);
    Task<Result<bool>> DetachPaymentMethodAsync(string paymentMethodId, CancellationToken ct = default);
    Task<Result<List<PaymentMethod>>> GetPaymentMethodsAsync(Guid userId, CancellationToken ct = default);
}

public record CreatePaymentIntentRequest(
    Guid BookingId,
    decimal Amount,
    string Currency,
    Guid ClientId,
    Guid ProviderId,
    string? PaymentMethodId = null,
    bool CaptureMethod = true,
    string? Description = null
);

public record ProcessRefundRequest(
    Guid PaymentId,
    decimal? Amount = null,
    string? Reason = null,
    bool RefundApplicationFee = false
);

internal class StripePaymentService : IStripePaymentService
{
    private readonly StripeClient _stripeClient;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<StripePaymentService> _logger;
    private readonly IOptions<StripeSettings> _settings;

    public StripePaymentService(
        StripeClient stripeClient,
        ApplicationDbContext context,
        ILogger<StripePaymentService> logger,
        IOptions<StripeSettings> settings)
    {
        _stripeClient = stripeClient;
        _context = context;
        _logger = logger;
        _settings = settings;
    }

    public async Task<Result<PaymentIntent>> CreatePaymentIntentAsync(CreatePaymentIntentRequest request, CancellationToken ct = default)
    {
        try
        {
            // Get booking and validate
            var booking = await _context.Bookings
                .Include(b => b.Provider)
                .FirstOrDefaultAsync(b => b.Id == request.BookingId, ct);

            if (booking == null)
                return Result.Failure<PaymentIntent>(Error.NotFound("Booking not found"));

            // Get provider's Stripe account
            var providerAccount = await _context.StripeAccounts
                .FirstOrDefaultAsync(sa => sa.UserId == booking.ProviderId, ct);

            if (providerAccount == null || !providerAccount.ChargesEnabled)
                return Result.Failure<PaymentIntent>(Error.BusinessRule("Provider is not set up to receive payments"));

            // Calculate platform fee
            var platformFeeAmount = CalculatePlatformFee(request.Amount, booking.CategoryId);
            var providerAmount = request.Amount - platformFeeAmount;

            // Create transfer group for split payment
            var transferGroup = $"booking_{request.BookingId}_{DateTime.UtcNow.Ticks}";

            var paymentIntentOptions = new PaymentIntentCreateOptions
            {
                Amount = (long)(request.Amount * 100), // Convert to cents
                Currency = request.Currency.ToLower(),
                PaymentMethod = request.PaymentMethodId,
                Customer = await GetOrCreateStripeCustomerAsync(request.ClientId, ct),
                Description = request.Description ?? $"Payment for booking {request.BookingId}",
                CaptureMethod = request.CaptureMethod ? "automatic" : "manual",
                TransferGroup = transferGroup,
                ApplicationFeeAmount = (long)(platformFeeAmount * 100),
                TransferData = new PaymentIntentTransferDataOptions
                {
                    Destination = providerAccount.StripeAccountId
                },
                Metadata = new Dictionary<string, string>
                {
                    ["booking_id"] = request.BookingId.ToString(),
                    ["client_id"] = request.ClientId.ToString(),
                    ["provider_id"] = request.ProviderId.ToString(),
                    ["platform_fee"] = platformFeeAmount.ToString("F2"),
                    ["provider_amount"] = providerAmount.ToString("F2")
                }
            };

            var service = new PaymentIntentService(_stripeClient);
            var paymentIntent = await service.CreateAsync(paymentIntentOptions, cancellationToken: ct);

            // Save payment record
            var payment = new Payment
            {
                BookingId = request.BookingId,
                Amount = request.Amount,
                PlatformFeeAmount = platformFeeAmount,
                ProviderAmount = providerAmount,
                Currency = request.Currency,
                StripePaymentIntentId = paymentIntent.Id,
                StripePaymentMethodId = request.PaymentMethodId,
                TransferGroupId = transferGroup,
                Status = PaymentStatus.Pending,
                PaymentDateTime = DateTime.UtcNow,
                ClientSecret = paymentIntent.ClientSecret,
                PaymentMethod = "stripe"
            };

            await _context.Payments.AddAsync(payment, ct);
            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("Payment intent created: {PaymentIntentId} for booking {BookingId}", 
                paymentIntent.Id, request.BookingId);

            return Result.Success(paymentIntent);
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error creating payment intent for booking {BookingId}", request.BookingId);
            return Result.Failure<PaymentIntent>(Error.External($"Payment processing error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment intent for booking {BookingId}", request.BookingId);
            return Result.Failure<PaymentIntent>(Error.Internal("Error processing payment"));
        }
    }

    private async Task<string> GetOrCreateStripeCustomerAsync(Guid userId, CancellationToken ct)
    {
        var user = await _context.Users
            .Include(u => u.UserProfile)
            .FirstOrDefaultAsync(u => u.Id == userId, ct);

        if (user == null)
            throw new InvalidOperationException("User not found");

        // Check if customer already exists
        if (!string.IsNullOrEmpty(user.StripeCustomerId))
            return user.StripeCustomerId;

        // Create new Stripe customer
        var customerOptions = new CustomerCreateOptions
        {
            Email = user.Email,
            Name = $"{user.UserProfile?.FirstName} {user.UserProfile?.LastName}".Trim(),
            Phone = user.PhoneNumber,
            Metadata = new Dictionary<string, string>
            {
                ["user_id"] = userId.ToString()
            }
        };

        var customerService = new CustomerService(_stripeClient);
        var customer = await customerService.CreateAsync(customerOptions, cancellationToken: ct);

        // Update user with Stripe customer ID
        user.StripeCustomerId = customer.Id;
        await _context.SaveChangesAsync(ct);

        return customer.Id;
    }

    private decimal CalculatePlatformFee(decimal amount, Guid categoryId)
    {
        // This could be configurable per category
        var feePercentage = _settings.Value.DefaultPlatformFeePercentage;
        return Math.Round(amount * feePercentage / 100, 2);
    }
}
```#
### KYC Service

```csharp
public interface IKYCService
{
    Task<Result<KYCVerification>> StartKYCVerificationAsync(Guid userId, CancellationToken ct = default);
    Task<Result<KYCVerification>> GetKYCStatusAsync(Guid userId, CancellationToken ct = default);
    Task<Result<bool>> CompleteKYCVerificationAsync(string verificationSessionId, CancellationToken ct = default);
    Task<Result<StripeAccount>> CreateConnectAccountAsync(Guid userId, CreateConnectAccountRequest request, CancellationToken ct = default);
    Task<Result<string>> GetOnboardingUrlAsync(Guid userId, CancellationToken ct = default);
}

public record CreateConnectAccountRequest(
    string BusinessType,
    string Country,
    string Email,
    string? BusinessUrl = null
);

internal class KYCService : IKYCService
{
    private readonly StripeClient _stripeClient;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<KYCService> _logger;
    private readonly IOptions<StripeSettings> _settings;

    public KYCService(
        StripeClient stripeClient,
        ApplicationDbContext context,
        ILogger<KYCService> logger,
        IOptions<StripeSettings> settings)
    {
        _stripeClient = stripeClient;
        _context = context;
        _logger = logger;
        _settings = settings;
    }

    public async Task<Result<StripeAccount>> CreateConnectAccountAsync(Guid userId, CreateConnectAccountRequest request, CancellationToken ct = default)
    {
        try
        {
            var user = await _context.Users
                .Include(u => u.UserProfile)
                .FirstOrDefaultAsync(u => u.Id == userId, ct);

            if (user == null)
                return Result.Failure<StripeAccount>(Error.NotFound("User not found"));

            // Check if account already exists
            var existingAccount = await _context.StripeAccounts
                .FirstOrDefaultAsync(sa => sa.UserId == userId, ct);

            if (existingAccount != null)
                return Result.Failure<StripeAccount>(Error.Conflict("Stripe account already exists"));

            // Create Stripe Connect account
            var accountOptions = new AccountCreateOptions
            {
                Type = "express", // or "standard" based on requirements
                Country = request.Country,
                Email = request.Email,
                BusinessType = request.BusinessType,
                Capabilities = new AccountCapabilitiesOptions
                {
                    CardPayments = new AccountCapabilitiesCardPaymentsOptions { Requested = true },
                    Transfers = new AccountCapabilitiesTransfersOptions { Requested = true }
                },
                BusinessProfile = new AccountBusinessProfileOptions
                {
                    Url = request.BusinessUrl
                },
                Metadata = new Dictionary<string, string>
                {
                    ["user_id"] = userId.ToString(),
                    ["platform"] = "supercare"
                }
            };

            var accountService = new AccountService(_stripeClient);
            var stripeAccount = await accountService.CreateAsync(accountOptions, cancellationToken: ct);

            // Save to database
            var account = new StripeAccount
            {
                UserId = userId,
                StripeAccountId = stripeAccount.Id,
                AccountType = "express",
                Status = StripeAccountStatus.Pending,
                ChargesEnabled = stripeAccount.ChargesEnabled,
                PayoutsEnabled = stripeAccount.PayoutsEnabled,
                BusinessType = request.BusinessType,
                Country = request.Country,
                Email = request.Email,
                CreatedAt = DateTime.UtcNow
            };

            await _context.StripeAccounts.AddAsync(account, ct);
            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("Stripe Connect account created: {AccountId} for user {UserId}", 
                stripeAccount.Id, userId);

            return Result.Success(account);
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error creating Connect account for user {UserId}", userId);
            return Result.Failure<StripeAccount>(Error.External($"Account creation error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Connect account for user {UserId}", userId);
            return Result.Failure<StripeAccount>(Error.Internal("Error creating account"));
        }
    }

    public async Task<Result<string>> GetOnboardingUrlAsync(Guid userId, CancellationToken ct = default)
    {
        try
        {
            var account = await _context.StripeAccounts
                .FirstOrDefaultAsync(sa => sa.UserId == userId, ct);

            if (account == null)
                return Result.Failure<string>(Error.NotFound("Stripe account not found"));

            var options = new AccountLinkCreateOptions
            {
                Account = account.StripeAccountId,
                RefreshUrl = $"{_settings.Value.BaseUrl}/kyc/refresh",
                ReturnUrl = $"{_settings.Value.BaseUrl}/kyc/complete",
                Type = "account_onboarding"
            };

            var service = new AccountLinkService(_stripeClient);
            var accountLink = await service.CreateAsync(options, cancellationToken: ct);

            // Update account with onboarding URL
            account.OnboardingUrl = accountLink.Url;
            await _context.SaveChangesAsync(ct);

            return Result.Success(accountLink.Url);
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error getting onboarding URL for user {UserId}", userId);
            return Result.Failure<string>(Error.External($"Onboarding error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting onboarding URL for user {UserId}", userId);
            return Result.Failure<string>(Error.Internal("Error getting onboarding URL"));
        }
    }
}
```#
### Webhook Service

```csharp
public interface IStripeWebhookService
{
    Task<Result<bool>> ProcessWebhookAsync(string payload, string signature, CancellationToken ct = default);
    Task<Result<bool>> HandlePaymentIntentSucceededAsync(PaymentIntent paymentIntent, CancellationToken ct = default);
    Task<Result<bool>> HandlePaymentIntentFailedAsync(PaymentIntent paymentIntent, CancellationToken ct = default);
    Task<Result<bool>> HandleAccountUpdatedAsync(Account account, CancellationToken ct = default);
    Task<Result<bool>> HandlePayoutPaidAsync(Payout payout, CancellationToken ct = default);
}

internal class StripeWebhookService : IStripeWebhookService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<StripeWebhookService> _logger;
    private readonly IOptions<StripeSettings> _settings;
    private readonly IInvoiceGenerationService _invoiceService;

    public StripeWebhookService(
        ApplicationDbContext context,
        ILogger<StripeWebhookService> logger,
        IOptions<StripeSettings> settings,
        IInvoiceGenerationService invoiceService)
    {
        _context = context;
        _logger = logger;
        _settings = settings;
        _invoiceService = invoiceService;
    }

    public async Task<Result<bool>> ProcessWebhookAsync(string payload, string signature, CancellationToken ct = default)
    {
        try
        {
            var stripeEvent = EventUtility.ConstructEvent(
                payload,
                signature,
                _settings.Value.WebhookSecret
            );

            _logger.LogInformation("Processing Stripe webhook: {EventType} - {EventId}", 
                stripeEvent.Type, stripeEvent.Id);

            // Check for duplicate processing
            var existingEvent = await _context.StripeWebhookEvents
                .FirstOrDefaultAsync(e => e.StripeEventId == stripeEvent.Id, ct);

            if (existingEvent != null)
            {
                _logger.LogInformation("Webhook event {EventId} already processed", stripeEvent.Id);
                return Result.Success(true);
            }

            // Process event based on type
            var result = stripeEvent.Type switch
            {
                "payment_intent.succeeded" => await HandlePaymentIntentSucceededAsync(
                    stripeEvent.Data.Object as PaymentIntent, ct),
                "payment_intent.payment_failed" => await HandlePaymentIntentFailedAsync(
                    stripeEvent.Data.Object as PaymentIntent, ct),
                "account.updated" => await HandleAccountUpdatedAsync(
                    stripeEvent.Data.Object as Account, ct),
                "payout.paid" => await HandlePayoutPaidAsync(
                    stripeEvent.Data.Object as Payout, ct),
                "transfer.created" => await HandleTransferCreatedAsync(
                    stripeEvent.Data.Object as Transfer, ct),
                _ => Result.Success(true) // Ignore unhandled events
            };

            // Record processed event
            await _context.StripeWebhookEvents.AddAsync(new StripeWebhookEvent
            {
                StripeEventId = stripeEvent.Id,
                EventType = stripeEvent.Type,
                ProcessedAt = DateTime.UtcNow,
                Success = result.IsSuccess,
                ErrorMessage = result.IsFailure ? result.Error.Message : null
            }, ct);

            await _context.SaveChangesAsync(ct);

            if (result.IsFailure)
            {
                _logger.LogError("Failed to process webhook {EventId}: {Error}", 
                    stripeEvent.Id, result.Error.Message);
            }

            return result;
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe webhook signature verification failed");
            return Result.Failure<bool>(Error.External("Invalid webhook signature"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Stripe webhook");
            return Result.Failure<bool>(Error.Internal("Webhook processing failed"));
        }
    }

    public async Task<Result<bool>> HandlePaymentIntentSucceededAsync(PaymentIntent paymentIntent, CancellationToken ct = default)
    {
        try
        {
            var payment = await _context.Payments
                .Include(p => p.Booking)
                .FirstOrDefaultAsync(p => p.StripePaymentIntentId == paymentIntent.Id, ct);

            if (payment == null)
            {
                _logger.LogWarning("Payment not found for PaymentIntent {PaymentIntentId}", paymentIntent.Id);
                return Result.Success(true);
            }

            // Update payment status
            payment.Status = PaymentStatus.Completed;
            payment.CapturedAt = DateTime.UtcNow;
            payment.StripeChargeId = paymentIntent.LatestChargeId;
            payment.ReceiptUrl = paymentIntent.Charges?.Data?.FirstOrDefault()?.ReceiptUrl;

            // Update booking status if needed
            if (payment.Booking.CurrentStatusType != BookingStatusType.Confirmed)
            {
                var statusResult = payment.Booking.AddStatus(BookingStatusType.Confirmed, 
                    Guid.Empty, "Payment completed");
                
                if (!statusResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to update booking status for payment {PaymentId}", payment.Id);
                }
            }

            await _context.SaveChangesAsync(ct);

            // Generate invoice if not exists
            try
            {
                await _invoiceService.GenerateInvoiceFromBookingAsync(payment.BookingId, ct);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate invoice for booking {BookingId}", payment.BookingId);
            }

            _logger.LogInformation("Payment {PaymentId} marked as completed", payment.Id);
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling payment_intent.succeeded for {PaymentIntentId}", paymentIntent.Id);
            return Result.Failure<bool>(Error.Internal("Failed to process payment success"));
        }
    }

    public async Task<Result<bool>> HandleAccountUpdatedAsync(Account account, CancellationToken ct = default)
    {
        try
        {
            var stripeAccount = await _context.StripeAccounts
                .FirstOrDefaultAsync(sa => sa.StripeAccountId == account.Id, ct);

            if (stripeAccount == null)
            {
                _logger.LogWarning("StripeAccount not found for account {AccountId}", account.Id);
                return Result.Success(true);
            }

            // Update account status
            stripeAccount.ChargesEnabled = account.ChargesEnabled;
            stripeAccount.PayoutsEnabled = account.PayoutsEnabled;
            
            if (account.ChargesEnabled && account.PayoutsEnabled && !stripeAccount.OnboardingCompletedAt.HasValue)
            {
                stripeAccount.OnboardingCompletedAt = DateTime.UtcNow;
                stripeAccount.Status = StripeAccountStatus.Active;
            }

            // Update requirements
            if (account.Requirements != null)
            {
                stripeAccount.RequirementsJson = JsonSerializer.Serialize(new
                {
                    currently_due = account.Requirements.CurrentlyDue,
                    eventually_due = account.Requirements.EventuallyDue,
                    past_due = account.Requirements.PastDue,
                    pending_verification = account.Requirements.PendingVerification
                });
            }

            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("StripeAccount {AccountId} updated", account.Id);
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling account.updated for {AccountId}", account.Id);
            return Result.Failure<bool>(Error.Internal("Failed to process account update"));
        }
    }
}

// Supporting entity for webhook event tracking
public class StripeWebhookEvent : BaseEntity
{
    public string StripeEventId { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
```### Confi
guration

```csharp
public class StripeSettings
{
    public string PublishableKey { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public string WebhookSecret { get; set; } = string.Empty;
    public string BaseUrl { get; set; } = string.Empty;
    public decimal DefaultPlatformFeePercentage { get; set; } = 10.0m;
    public string DefaultCurrency { get; set; } = "USD";
    public bool EnableAutomaticPayouts { get; set; } = true;
    public int PayoutDelayDays { get; set; } = 2;
    public decimal MinimumPayoutAmount { get; set; } = 10.0m;
    public List<string> SupportedCountries { get; set; } = new() { "US", "CA", "GB", "AU" };
}
```

### Command Handlers

#### Process Payment Command

```csharp
public record ProcessPaymentCommand(
    Guid BookingId,
    Guid ClientId,
    string? PaymentMethodId = null,
    bool SavePaymentMethod = false
) : ICommand<Result<ProcessPaymentResponse>>;

public record ProcessPaymentResponse(
    Guid PaymentId,
    string PaymentIntentId,
    string? ClientSecret,
    bool RequiresAction,
    PaymentStatus Status
);

internal sealed class ProcessPaymentCommandHandler : ICommandHandler<ProcessPaymentCommand, Result<ProcessPaymentResponse>>
{
    private readonly IStripePaymentService _paymentService;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ProcessPaymentCommandHandler> _logger;

    public ProcessPaymentCommandHandler(
        IStripePaymentService paymentService,
        ApplicationDbContext context,
        ILogger<ProcessPaymentCommandHandler> logger)
    {
        _paymentService = paymentService;
        _context = context;
        _logger = logger;
    }

    public async Task<Result<ProcessPaymentResponse>> Handle(ProcessPaymentCommand command, CancellationToken ct)
    {
        try
        {
            // Get booking details
            var booking = await _context.Bookings
                .Include(b => b.Provider)
                .FirstOrDefaultAsync(b => b.Id == command.BookingId, ct);

            if (booking == null)
                return Result.Failure<ProcessPaymentResponse>(Error.NotFound("Booking not found"));

            // Validate client authorization
            if (booking.ClientId != command.ClientId)
                return Result.Failure<ProcessPaymentResponse>(Error.Unauthorized("Not authorized for this booking"));

            // Check if payment already exists
            var existingPayment = await _context.Payments
                .FirstOrDefaultAsync(p => p.BookingId == command.BookingId && 
                                        p.Status == PaymentStatus.Completed, ct);

            if (existingPayment != null)
                return Result.Failure<ProcessPaymentResponse>(Error.Conflict("Payment already completed for this booking"));

            // Create payment intent
            var paymentIntentRequest = new CreatePaymentIntentRequest(
                command.BookingId,
                booking.TotalAmount,
                "USD", // Could be configurable
                command.ClientId,
                booking.ProviderId,
                command.PaymentMethodId,
                true,
                $"Payment for SuperCare booking #{booking.Id}"
            );

            var paymentIntentResult = await _paymentService.CreatePaymentIntentAsync(paymentIntentRequest, ct);
            if (!paymentIntentResult.IsSuccess)
                return Result.Failure<ProcessPaymentResponse>(paymentIntentResult.Error);

            var paymentIntent = paymentIntentResult.Value;

            // Get the created payment record
            var payment = await _context.Payments
                .FirstOrDefaultAsync(p => p.StripePaymentIntentId == paymentIntent.Id, ct);

            if (payment == null)
                return Result.Failure<ProcessPaymentResponse>(Error.Internal("Payment record not found"));

            var response = new ProcessPaymentResponse(
                payment.Id,
                paymentIntent.Id,
                paymentIntent.ClientSecret,
                paymentIntent.Status == "requires_action",
                payment.Status
            );

            _logger.LogInformation("Payment processing initiated for booking {BookingId}", command.BookingId);
            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment for booking {BookingId}", command.BookingId);
            return Result.Failure<ProcessPaymentResponse>(Error.Internal("Error processing payment"));
        }
    }
}
```

### Error Handling

```csharp
public static class StripeErrors
{
    public static Error PaymentFailed(string reason) => 
        Error.External($"Payment failed: {reason}");
    
    public static Error AccountNotSetup(Guid userId) => 
        Error.BusinessRule($"Stripe account not set up for user {userId}");
    
    public static Error KYCNotCompleted(Guid userId) => 
        Error.BusinessRule($"KYC verification not completed for user {userId}");
    
    public static Error InsufficientFunds() => 
        Error.External("Insufficient funds for this transaction");
    
    public static Error PaymentMethodRequired() => 
        Error.Validation("Payment method is required");
    
    public static Error RefundFailed(string reason) => 
        Error.External($"Refund failed: {reason}");
    
    public static Error WebhookVerificationFailed() => 
        Error.External("Webhook signature verification failed");
    
    public static Error UnsupportedCountry(string country) => 
        Error.BusinessRule($"Country {country} is not supported for payments");
}
```

This design provides a comprehensive Stripe integration that handles all aspects of payment processing, KYC verification, split payments, and webhook management while maintaining security and compliance standards.