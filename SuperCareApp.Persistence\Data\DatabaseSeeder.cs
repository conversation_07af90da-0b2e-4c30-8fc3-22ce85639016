﻿using Microsoft.Extensions.Hosting;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Data
{
    /// <summary>
    /// Seeds initial data into the database
    /// </summary>
    public static class DatabaseSeeder
    {
        /// <summary>
        /// Seeds the database with initial data
        /// </summary>
        /// <param name="host">The host to seed</param>
        /// <param name="developmentOnly">Only seed in development environment</param>
        public static IHost SeedDatabase(this IHost host, bool developmentOnly = true)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
                var context = services.GetService<ApplicationDbContext>();
                var userManager = services.GetService<UserManager<ApplicationUser>>();
                var roleManager = services.GetService<RoleManager<ApplicationRole>>();
                var environment = services.GetService<IHostEnvironment>();

                // Skip seeding if not in development and developmentOnly is true
                if (developmentOnly && !environment.IsDevelopment())
                {
                    logger.LogInformation(
                        "Skipping database seeding in non-development environment"
                    );
                    return host;
                }

                try
                {
                    logger.LogInformation("Seeding database");

                    if (context != null && userManager != null && roleManager != null)
                    {
                        SeedCareCategories(context).Wait();
                        SeedAdminUser(context, userManager, roleManager).Wait();
                        SeedRolesAsync(context, roleManager).Wait();
                        SeedCareProviders(context, userManager, roleManager).Wait();

                        // Verify first 6 care providers were seeded
                        logger.LogInformation("Verifying care provider seeding");
                        VerifyCareProviders(context, logger);

                        // Seed client users
                        logger.LogInformation("Starting to seed client users");
                        SeedClientUsers(context, userManager, roleManager).Wait();
                        logger.LogInformation("Client users seeding completed");

                        // Seed approvals and log the result
                        logger.LogInformation("Starting to seed approvals");
                        SeedApprovals(context, userManager).Wait();

                        // Verify approvals were seeded
                        var approvalCount = context.Approvals.Count();
                        logger.LogInformation("Seeded {ApprovalCount} approvals", approvalCount);

                        // Log details of the first few approvals
                        var sampleApprovals = context.Approvals.Take(3).ToList();
                        foreach (var approval in sampleApprovals)
                        {
                            logger.LogInformation(
                                "Approval: Id={Id}, UserId={UserId}, Type={Type}, IsApproved={IsApproved}",
                                approval.Id,
                                approval.UserId,
                                approval.ApprovalType,
                                approval.IsApproved
                            );
                        }
                        SeedBookings(context, userManager).Wait(); // Added call to seed bookings
                        // Log booking seeding results
                        var bookingCount = context.Bookings.Count();
                        logger.LogInformation("Seeded {BookingCount} bookings", bookingCount);
                    }

                    logger.LogInformation("Database seeding completed");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "An error occurred while seeding the database");
                }
            }

            return host;
        }

        private static async Task SeedCareCategories(ApplicationDbContext context)
        {
            // Define categories to seed
            var categoriesToSeed = new List<CareCategory>
            {
                new CareCategory
                {
                    Id = SeedConstants.CareCategories.ChildCareId,
                    Name = "Child Care",
                    Description =
                        "Care for children under 18 years, including infants, toddlers, and preschoolers",
                    IsActive = true,
                    PlatformFee = 0.250000m,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareCategory
                {
                    Id = SeedConstants.CareCategories.ElderlyCareId,
                    Name = "Elderly Care",
                    Description = "Care for elderly individuals over 65 years",
                    IsActive = true,
                    PlatformFee = 0.200500m,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareCategory
                {
                    Id = SeedConstants.CareCategories.DisabilityCareId,
                    Name = "Disability Care",
                    Description = "Care for individuals with disabilities",
                    IsActive = true,
                    PlatformFee = 0.300750m,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
            };

            // Get existing categories to avoid duplicates
            var existingCategories = await context
                .CareCategories.Where(c => !c.IsDeleted)
                .Select(c => c.Name.ToLower())
                .ToListAsync();

            // Filter out categories that already exist
            var newCategories = categoriesToSeed
                .Where(c => !existingCategories.Contains(c.Name.ToLower()))
                .ToList();

            // Only add new categories
            if (newCategories.Any())
            {
                await context.CareCategories.AddRangeAsync(newCategories);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SeedAdminUser(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager
        )
        {
            // Check if admin role exists
            var adminRoleName = UserRoleType.Admin.ToString();
            if (!await roleManager.RoleExistsAsync(adminRoleName))
            {
                // Create admin role
                var role = new ApplicationRole(adminRoleName)
                {
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                };
                await roleManager.CreateAsync(role);
            }

            // Check if admin user exists
            var adminEmail = "<EMAIL>";
            if (await userManager.FindByEmailAsync(adminEmail) == null)
            {
                // Create admin user
                var admin = new ApplicationUser
                {
                    Id = SeedConstants.Admin.UserId,
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailVerified = true,
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                };

                var result = await userManager.CreateAsync(admin, "Admin@2345");
                if (result.Succeeded)
                {
                    // Add admin role to user
                    await userManager.AddToRoleAsync(admin, adminRoleName);

                    // Create admin profile
                    var adminProfile = new UserProfile
                    {
                        Id = SeedConstants.Admin.ProfileId,
                        ApplicationUserId = SeedConstants.Admin.UserId,
                        FirstName = "System",
                        LastName = "Administrator",
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty,
                    };

                    await context.UserProfiles.AddAsync(adminProfile);
                    await context.SaveChangesAsync();
                }
            }
        }

        private static async Task SeedRolesAsync(
            ApplicationDbContext context,
            RoleManager<ApplicationRole> roleManager
        )
        {
            if (roleManager == null)
            {
                throw new ArgumentNullException(nameof(roleManager));
            }

            var roles = new List<ApplicationRole>
            {
                new ApplicationRole(UserRoleType.Admin.ToString())
                {
                    Id = SeedConstants.Roles.AdminRoleId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new ApplicationRole(UserRoleType.Client.ToString())
                {
                    Id = SeedConstants.Roles.ClientRoleId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new ApplicationRole(UserRoleType.CareProvider.ToString())
                {
                    Id = SeedConstants.Roles.CareProviderRoleId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
            };

            foreach (var role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role.Name))
                {
                    await roleManager.CreateAsync(role);
                }
            }
        }

        /// <summary>
        /// Seeds 16 care provider users with all related entities (6 existing + 10 new)
        /// </summary>
        private static async Task SeedCareProviders(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager
        )
        {
            // Check if care providers already exist
            if (await context.CareProviderProfiles.AnyAsync())
            {
                return; // Care providers already seeded
            }

            // Ensure care categories exist
            var careCategories = await context.CareCategories.ToListAsync();
            if (!careCategories.Any())
            {
                await SeedCareCategories(context);
                careCategories = await context.CareCategories.ToListAsync();
            }

            // Create 16 care provider users (6 existing + 10 new) with hardcoded GUIDs
            var careProviders = new List<(ApplicationUser User, string Password)>
            {
                // Provider 1 - Sarah Johnson
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider1UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 2 - Michael Smith
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider2UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 3 - Emily Davis
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider3UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 4 - David Wilson
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider4UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 5 - Jennifer Brown
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider5UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 6 - Robert Taylor
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider6UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // NEW PROVIDERS (7-16)
                // Provider 7 - Lisa Anderson
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider7UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 8 - James Martinez
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider8UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 9 - Maria Garcia
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider9UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 10 - Thomas Lee
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider10UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 11 - Patricia White
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider11UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 12 - Christopher Harris
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider12UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 13 - Nancy Clark
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider13UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 14 - Daniel Lewis
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider14UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 15 - Karen Robinson
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider15UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
                // Provider 16 - Mark Walker
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.CareProviders.Provider16UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+**********",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty, // System
                    },
                    "Password123!"
                ),
            };

            // Create users and assign roles
            foreach (var (user, password) in careProviders)
            {
                var result = await userManager.CreateAsync(user, password);
                if (result.Succeeded)
                {
                    await userManager.AddToRolesAsync(
                        user,
                        new[] { UserRoleType.CareProvider.ToString() }
                    );
                }
                else
                {
                    throw new Exception(
                        $"Failed to create care provider user {user.Email}: {string.Join(", ", result.Errors.Select(e => e.Description))}"
                    );
                }
            }

            // Save changes to ensure users are in database before creating profiles
            await context.SaveChangesAsync();

            // Create user profiles
            var userProfiles = new List<UserProfile>
            {
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider1ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider1UserId,
                    FirstName = "Sarah",
                    LastName = "Johnson",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1985, 5, 15), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "sarah_johnson_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider2ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider2UserId,
                    FirstName = "Michael",
                    LastName = "Smith",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1980, 8, 22), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "michael_smith_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider3ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider3UserId,
                    FirstName = "Emily",
                    LastName = "Davis",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1990, 3, 10), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "emily_davis_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider4ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider4UserId,
                    FirstName = "David",
                    LastName = "Wilson",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1978, 11, 5), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "david_wilson_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider5ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider5UserId,
                    FirstName = "Jennifer",
                    LastName = "Brown",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1988, 7, 18), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "jennifer_brown_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider6ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider6UserId,
                    FirstName = "Robert",
                    LastName = "Taylor",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1982, 4, 30), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "robert_taylor_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1504257432389-52343af06ae3?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider7ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider7UserId,
                    FirstName = "Lisa",
                    LastName = "Anderson",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1987, 9, 12), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "lisa_anderson_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1524504388940-b7144b1043bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider8ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider8UserId,
                    FirstName = "James",
                    LastName = "Martinez",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1983, 2, 28), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "james_martinez_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1506794778202-c761f7d2e3e6?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider9ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider9UserId,
                    FirstName = "Maria",
                    LastName = "Garcia",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1991, 6, 8), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "maria_garcia_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1516321318423-8e6e4b47e5b4?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider10ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider10UserId,
                    FirstName = "Thomas",
                    LastName = "Lee",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1979, 12, 3), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "thomas_lee_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1503443207922-d34e7e9e3c5b?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider11ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider11UserId,
                    FirstName = "Patricia",
                    LastName = "White",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1986, 1, 20), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "patricia_white_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider12ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider12UserId,
                    FirstName = "Christopher",
                    LastName = "Harris",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(
                        new DateTime(1984, 10, 15),
                        DateTimeKind.Utc
                    ),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "christopher_harris_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1507081323647-4d250478b919?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider13ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider13UserId,
                    FirstName = "Nancy",
                    LastName = "Clark",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1989, 4, 7), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "nancy_clark_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1492106087820-8be3d57d79d6?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider14ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider14UserId,
                    FirstName = "Daniel",
                    LastName = "Lewis",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1981, 8, 25), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "daniel_lewis_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1506794778202-c761f7d2e3e6?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider15ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider15UserId,
                    FirstName = "Karen",
                    LastName = "Robinson",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(
                        new DateTime(1992, 11, 14),
                        DateTimeKind.Utc
                    ),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "karen_robinson_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1517365830460-955ce3f6b1f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
                new UserProfile
                {
                    Id = SeedConstants.CareProviders.Provider16ProfileId,
                    ApplicationUserId = SeedConstants.CareProviders.Provider16UserId,
                    FirstName = "Mark",
                    LastName = "Walker",
                    PhoneNumber = "+**********",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1977, 5, 9), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    ImageName = "mark_walker_profile",
                    ImagePath =
                        "https://images.unsplash.com/photo-1508341591425-234d6f1a78c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=200",
                },
            };

            await context.UserProfiles.AddRangeAsync(userProfiles);
            await context.SaveChangesAsync();

            // Create care provider profiles
            var careProviderProfiles = new List<CareProviderProfile>
            {
                // Existing 6 providers
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider1ProfileId,
                    UserId = SeedConstants.CareProviders.Provider1UserId,
                    Bio =
                        "Experienced caregiver with a passion for helping elderly patients. Specializes in dementia care and medication management.",
                    YearsExperience = 8,
                    HourlyRate = 25.50m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"CNA\",\"BLS\",\"CPR\"],\"education\":\"Bachelor's in Nursing\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider2ProfileId,
                    UserId = SeedConstants.CareProviders.Provider2UserId,
                    Bio =
                        "Dedicated caregiver with experience in pediatric care. Specializes in working with children with special needs and developmental disabilities.",
                    YearsExperience = 5,
                    HourlyRate = 30.00m,
                    ProvidesOvernight = false,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"PCA\",\"First Aid\",\"CPR\"],\"education\":\"Associate's in Child Development\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider3ProfileId,
                    UserId = SeedConstants.CareProviders.Provider3UserId,
                    Bio =
                        "Compassionate caregiver with a focus on rehabilitation and recovery. Experienced in post-surgery care and physical therapy assistance.",
                    YearsExperience = 6,
                    HourlyRate = 28.75m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = true,
                    Qualifications =
                        "{\"certifications\":[\"HHA\",\"BLS\",\"CPR\"],\"education\":\"Bachelor's in Health Sciences\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider4ProfileId,
                    UserId = SeedConstants.CareProviders.Provider4UserId,
                    Bio =
                        "Experienced caregiver specializing in elderly care and companionship. Provides assistance with daily activities and medication management.",
                    YearsExperience = 10,
                    HourlyRate = 32.50m,
                    ProvidesOvernight = false,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"CNA\",\"CPR\",\"First Aid\"],\"education\":\"Bachelor's in Psychology\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider5ProfileId,
                    UserId = SeedConstants.CareProviders.Provider5UserId,
                    Bio =
                        "Dedicated caregiver with experience in both pediatric and geriatric care. Specializes in creating engaging activities and providing emotional support.",
                    YearsExperience = 7,
                    HourlyRate = 27.00m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"HHA\",\"CPR\",\"First Aid\"],\"education\":\"Associate's in Healthcare Administration\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider6ProfileId,
                    UserId = SeedConstants.CareProviders.Provider6UserId,
                    Bio =
                        "Experienced caregiver with a background in physical therapy. Specializes in mobility assistance and rehabilitation exercises.",
                    YearsExperience = 9,
                    HourlyRate = 35.00m,
                    ProvidesOvernight = false,
                    ProvidesLiveIn = true,
                    Qualifications =
                        "{\"certifications\":[\"PCA\",\"BLS\",\"CPR\"],\"education\":\"Bachelor's in Exercise Science\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                // NEW PROVIDERS (7-16) - Care Provider Profiles
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider7ProfileId,
                    UserId = SeedConstants.CareProviders.Provider7UserId,
                    Bio =
                        "Skilled caregiver with expertise in mental health support and behavioral care. Specializes in working with clients with anxiety and depression.",
                    YearsExperience = 4,
                    HourlyRate = 26.00m,
                    ProvidesOvernight = false,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"Mental Health First Aid\",\"CPR\",\"CNA\"],\"education\":\"Bachelor's in Psychology\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider8ProfileId,
                    UserId = SeedConstants.CareProviders.Provider8UserId,
                    Bio =
                        "Experienced caregiver specializing in mobility assistance and physical rehabilitation. Provides support for clients recovering from injuries.",
                    YearsExperience = 12,
                    HourlyRate = 38.00m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = true,
                    Qualifications =
                        "{\"certifications\":[\"Physical Therapy Assistant\",\"BLS\",\"CPR\"],\"education\":\"Associate's in Physical Therapy\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider9ProfileId,
                    UserId = SeedConstants.CareProviders.Provider9UserId,
                    Bio =
                        "Bilingual caregiver with experience in cultural-sensitive care. Specializes in elderly care and chronic disease management.",
                    YearsExperience = 6,
                    HourlyRate = 29.50m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"CNA\",\"Diabetes Care\",\"CPR\"],\"education\":\"Certificate in Healthcare\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider10ProfileId,
                    UserId = SeedConstants.CareProviders.Provider10UserId,
                    Bio =
                        "Dedicated caregiver with extensive experience in hospice and palliative care. Provides compassionate end-of-life support.",
                    YearsExperience = 15,
                    HourlyRate = 42.00m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = true,
                    Qualifications =
                        "{\"certifications\":[\"Hospice Care\",\"BLS\",\"CPR\",\"CNA\"],\"education\":\"Bachelor's in Nursing\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider11ProfileId,
                    UserId = SeedConstants.CareProviders.Provider11UserId,
                    Bio =
                        "Experienced pediatric caregiver with special needs expertise. Provides developmental support and educational assistance.",
                    YearsExperience = 8,
                    HourlyRate = 31.00m,
                    ProvidesOvernight = false,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"Special Needs Care\",\"CPR\",\"First Aid\"],\"education\":\"Bachelor's in Special Education\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider12ProfileId,
                    UserId = SeedConstants.CareProviders.Provider12UserId,
                    Bio =
                        "Skilled caregiver with expertise in wound care and post-surgical recovery. Provides medical support and monitoring.",
                    YearsExperience = 11,
                    HourlyRate = 36.50m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"Wound Care\",\"BLS\",\"CPR\",\"CNA\"],\"education\":\"Associate's in Nursing\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider13ProfileId,
                    UserId = SeedConstants.CareProviders.Provider13UserId,
                    Bio =
                        "Compassionate caregiver specializing in memory care and dementia support. Creates safe and engaging environments for clients.",
                    YearsExperience = 9,
                    HourlyRate = 33.00m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = true,
                    Qualifications =
                        "{\"certifications\":[\"Dementia Care\",\"CPR\",\"First Aid\"],\"education\":\"Certificate in Gerontology\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider14ProfileId,
                    UserId = SeedConstants.CareProviders.Provider14UserId,
                    Bio =
                        "Experienced caregiver with background in nutrition and meal planning. Specializes in dietary management for chronic conditions.",
                    YearsExperience = 7,
                    HourlyRate = 28.00m,
                    ProvidesOvernight = false,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"Nutrition Aide\",\"CPR\",\"Food Safety\"],\"education\":\"Certificate in Nutrition\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider15ProfileId,
                    UserId = SeedConstants.CareProviders.Provider15UserId,
                    Bio =
                        "Dedicated caregiver with experience in medication management and health monitoring. Provides reliable and professional care.",
                    YearsExperience = 5,
                    HourlyRate = 27.50m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = false,
                    Qualifications =
                        "{\"certifications\":[\"Medication Administration\",\"CPR\",\"CNA\"],\"education\":\"Certificate in Healthcare\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new CareProviderProfile
                {
                    Id = SeedConstants.CareProviders.Provider16ProfileId,
                    UserId = SeedConstants.CareProviders.Provider16UserId,
                    Bio =
                        "Experienced caregiver with expertise in respiratory care and oxygen therapy. Provides specialized medical support.",
                    YearsExperience = 13,
                    HourlyRate = 40.00m,
                    ProvidesOvernight = true,
                    ProvidesLiveIn = true,
                    Qualifications =
                        "{\"certifications\":[\"Respiratory Therapy\",\"BLS\",\"CPR\",\"CNA\"],\"education\":\"Associate's in Respiratory Therapy\"}",
                    VerificationStatus = VerificationStatus.Pending,
                    Rating = null,
                    RatingCount = 0,
                    BufferDuration= 15,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
            };

            await context.CareProviderProfiles.AddRangeAsync(careProviderProfiles);
            await context.SaveChangesAsync();

            // Create care provider categories
            var careProviderCategories = new List<CareProviderCategory>();

            // Get all care provider profiles
            var providerProfiles = await context.CareProviderProfiles.ToListAsync();

            // Assign categories to providers (each provider gets 2-3 categories)
            var random = new Random();
            foreach (var profile in providerProfiles)
            {
                // Select 2-3 random categories for each provider
                var categoryCount = random.Next(2, 4);
                var selectedCategories = careCategories
                    .OrderBy(x => random.Next())
                    .Take(categoryCount)
                    .ToList();

                foreach (var category in selectedCategories)
                {
                    careProviderCategories.Add(
                        new CareProviderCategory
                        {
                            ProviderId = profile.Id,
                            CategoryId = category.Id,
                            HourlyRate = random.Next(20, 50), // Random hourly rate between $20 and $50
                            ExperienceYears = random.Next(1, 10),
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = Guid.Empty,
                        }
                    );
                }
            }

            await context.CareProviderCategories.AddRangeAsync(careProviderCategories);
            await context.SaveChangesAsync();

            // ---------- FIXED AVAILABILITY ----------
            var daysOfWeek = new[] { "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday","Sunday" };

            foreach (var profile in providerProfiles)
            {
                foreach (var day in daysOfWeek)
                {
                    // decide how many slots this day gets
                    var slotCount = random.Next(0, 4);   // 0-3
                    var isAvailable = slotCount > 0;

                    var availability = new Availability
                    {
                        ProviderId = profile.Id,
                        DayOfWeek = day,
                        IsAvailable = isAvailable,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty
                    };

                    context.Availabilities.Add(availability);
                    await context.SaveChangesAsync();   // now availability.Id is populated

                    // if we have slots, add them right away
                    var startHours = new List<int>();
                    for (int s = 0; s < slotCount; s++)
                    {
                        int startHour;
                        do
                        {
                            startHour = random.Next(8, 20);
                        } while (startHours.Contains(startHour) ||
                                 startHours.Contains(startHour - 1) ||
                                 startHours.Contains(startHour + 1));

                        startHours.Add(startHour);
                        int endHour = Math.Min(startHour + random.Next(1, 4), 22);

                        context.AvailabilitySlots.Add(new AvailabilitySlot
                        {
                            AvailabilityId = availability.Id,   // real Id
                            StartTime = new TimeOnly(startHour, 0),
                            EndTime = new TimeOnly(endHour, 0)
                        });
                    }
                }
            }

            await context.SaveChangesAsync();
            // ---------- END FIXED ----------
        }

        /// <summary>
        /// Seeds approval records for care provider profiles
        /// </summary>
        private static async Task SeedApprovals(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager
        )
        {
            // Check if approvals already exist
            if (await context.Approvals.AnyAsync())
            {
                return; // Approvals already seeded
            }

            // Get admin user for ProcessedBy field
            var adminUser = await userManager.FindByEmailAsync("<EMAIL>");
            if (adminUser == null)
            {
                // If admin user doesn't exist, we can't proceed
                return;
            }

            // Create approval records for all 16 care providers using hardcoded GUIDs
            var providerApprovalData = new[]
            {
                (
                    UserId: SeedConstants.CareProviders.Provider1UserId,
                    ProfileId: SeedConstants.CareProviders.Provider1ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider1ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider2UserId,
                    ProfileId: SeedConstants.CareProviders.Provider2ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider2ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider3UserId,
                    ProfileId: SeedConstants.CareProviders.Provider3ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider3ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider4UserId,
                    ProfileId: SeedConstants.CareProviders.Provider4ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider4ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider5UserId,
                    ProfileId: SeedConstants.CareProviders.Provider5ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider5ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider6UserId,
                    ProfileId: SeedConstants.CareProviders.Provider6ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider6ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider7UserId,
                    ProfileId: SeedConstants.CareProviders.Provider7ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider7ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider8UserId,
                    ProfileId: SeedConstants.CareProviders.Provider8ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider8ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider9UserId,
                    ProfileId: SeedConstants.CareProviders.Provider9ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider9ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider10UserId,
                    ProfileId: SeedConstants.CareProviders.Provider10ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider10ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider11UserId,
                    ProfileId: SeedConstants.CareProviders.Provider11ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider11ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider12UserId,
                    ProfileId: SeedConstants.CareProviders.Provider12ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider12ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider13UserId,
                    ProfileId: SeedConstants.CareProviders.Provider13ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider13ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider14UserId,
                    ProfileId: SeedConstants.CareProviders.Provider14ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider14ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider15UserId,
                    ProfileId: SeedConstants.CareProviders.Provider15ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider15ApprovalId
                ),
                (
                    UserId: SeedConstants.CareProviders.Provider16UserId,
                    ProfileId: SeedConstants.CareProviders.Provider16ProfileId,
                    ApprovalId: SeedConstants.Approvals.Provider16ApprovalId
                ),
            };

            var approvals = new List<Approval>();

            // Create approval records for all providers
            foreach (var (userId, profileId, approvalId) in providerApprovalData)
            {
                var approval = new Approval
                {
                    Id = approvalId,
                    UserId = userId,
                    ApprovalType = ApprovalType.CareProviderVerification,
                    RelatedEntityId = profileId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                    IsApproved = false, // Pending state
                    ProcessedBy = null,
                    ProcessedAt = null,
                    Notes = "Pending review",
                    RejectionReason = null,
                };

                approvals.Add(approval);
            }

            await context.Approvals.AddRangeAsync(approvals);
            await context.SaveChangesAsync();

            // Ensure all care provider profiles have Pending verification status
            foreach (var approval in approvals)
            {
                if (approval.RelatedEntityId.HasValue)
                {
                    var profile = await context.CareProviderProfiles.FindAsync(
                        approval.RelatedEntityId.Value
                    );
                    if (profile != null)
                    {
                        // Set all profiles to Pending status
                        profile.VerificationStatus = VerificationStatus.Verified;
                    }
                }
            }

            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Seeds 10 client users with profiles and addresses
        /// </summary>
        private static async Task SeedClientUsers(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager
        )
        {
            // Check if client users already exist by looking for users with Client role
            var clientRoleName = UserRoleType.Client.ToString();
            var clientRole = await roleManager.FindByNameAsync(clientRoleName);

            if (clientRole != null)
            {
                var clientUsersExist = await context.UserRoles.AnyAsync(ur =>
                    ur.RoleId == clientRole.Id
                );

                if (clientUsersExist)
                {
                    return; // Client users already seeded
                }
            }

            // Create 5 client users with diverse data using hardcoded GUIDs
            var clientUsers = new List<(ApplicationUser User, string Password)>
            {
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.Clients.Client1UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+1987654321",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty,
                    },
                    "Password123!"
                ),
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.Clients.Client2UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+1987654322",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty,
                    },
                    "Password123!"
                ),
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.Clients.Client3UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+1987654323",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty,
                    },
                    "Password123!"
                ),
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.Clients.Client4UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+1987654324",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty,
                    },
                    "Password123!"
                ),
                (
                    new ApplicationUser
                    {
                        Id = SeedConstants.Clients.Client5UserId,
                        UserName = "<EMAIL>",
                        Email = "<EMAIL>",
                        EmailVerified = true,
                        EmailConfirmed = true,
                        IsActive = true,
                        PhoneNumber = "+1987654325",
                        PhoneNumberConfirmed = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Empty,
                    },
                    "Password123!"
                ),
            };

            // Create users and assign Client role
            foreach (var (user, password) in clientUsers)
            {
                var result = await userManager.CreateAsync(user, password);
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(user, clientRoleName);
                }
                else
                {
                    throw new Exception(
                        $"Failed to create client user {user.Email}: {string.Join(", ", result.Errors.Select(e => e.Description))}"
                    );
                }
            }

            // Save changes to ensure users are in database before creating profiles
            await context.SaveChangesAsync();

            // Create user profiles with diverse demographic data using hardcoded GUIDs
            var userProfiles = new List<UserProfile>
            {
                new UserProfile
                {
                    Id = SeedConstants.Clients.Client1ProfileId,
                    ApplicationUserId = SeedConstants.Clients.Client1UserId,
                    FirstName = "John",
                    LastName = "Doe",
                    PhoneNumber = "+1987654321",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1975, 3, 12), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new UserProfile
                {
                    Id = SeedConstants.Clients.Client2ProfileId,
                    ApplicationUserId = SeedConstants.Clients.Client2UserId,
                    FirstName = "Jane",
                    LastName = "Smith",
                    PhoneNumber = "+1987654322",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1982, 7, 24), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new UserProfile
                {
                    Id = SeedConstants.Clients.Client3ProfileId,
                    ApplicationUserId = SeedConstants.Clients.Client3UserId,
                    FirstName = "Alex",
                    LastName = "Johnson",
                    PhoneNumber = "+1987654323",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1990, 11, 5), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new UserProfile
                {
                    Id = SeedConstants.Clients.Client4ProfileId,
                    ApplicationUserId = SeedConstants.Clients.Client4UserId,
                    FirstName = "Maria",
                    LastName = "Rodriguez",
                    PhoneNumber = "+1987654324",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1988, 4, 17), DateTimeKind.Utc),
                    Gender = "Female",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
                new UserProfile
                {
                    Id = SeedConstants.Clients.Client5ProfileId,
                    ApplicationUserId = SeedConstants.Clients.Client5UserId,
                    FirstName = "James",
                    LastName = "Wilson",
                    PhoneNumber = "+1987654325",
                    DateOfBirth = DateTime.SpecifyKind(new DateTime(1979, 9, 30), DateTimeKind.Utc),
                    Gender = "Male",
                    Country = "United States",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                },
            };

            await context.UserProfiles.AddRangeAsync(userProfiles);
            await context.SaveChangesAsync();

            // Create addresses for each client
            var addresses = new List<Address>();
            var userAddresses = new List<UserAddress>();

            // Sample addresses with realistic data for 5 clients
            var addressData = new[]
            {
                (
                    Id: SeedConstants.Addresses.Address1Id,
                    UserAddressId: SeedConstants.UserAddresses.UserAddress1Id,
                    UserId: SeedConstants.Clients.Client1UserId,
                    Street: "123 Main St",
                    City: "New York",
                    State: "NY",
                    PostalCode: "10001",
                    Lat: 40.7128m,
                    Lng: -74.0060m
                ),
                (
                    Id: SeedConstants.Addresses.Address2Id,
                    UserAddressId: SeedConstants.UserAddresses.UserAddress2Id,
                    UserId: SeedConstants.Clients.Client2UserId,
                    Street: "456 Oak Ave",
                    City: "Los Angeles",
                    State: "CA",
                    PostalCode: "90001",
                    Lat: 34.0522m,
                    Lng: -118.2437m
                ),
                (
                    Id: SeedConstants.Addresses.Address3Id,
                    UserAddressId: SeedConstants.UserAddresses.UserAddress3Id,
                    UserId: SeedConstants.Clients.Client3UserId,
                    Street: "789 Pine Rd",
                    City: "Chicago",
                    State: "IL",
                    PostalCode: "60601",
                    Lat: 41.8781m,
                    Lng: -87.6298m
                ),
                (
                    Id: SeedConstants.Addresses.Address4Id,
                    UserAddressId: SeedConstants.UserAddresses.UserAddress4Id,
                    UserId: SeedConstants.Clients.Client4UserId,
                    Street: "321 Maple Dr",
                    City: "Houston",
                    State: "TX",
                    PostalCode: "77001",
                    Lat: 29.7604m,
                    Lng: -95.3698m
                ),
                (
                    Id: SeedConstants.Addresses.Address5Id,
                    UserAddressId: SeedConstants.UserAddresses.UserAddress5Id,
                    UserId: SeedConstants.Clients.Client5UserId,
                    Street: "654 Cedar Ln",
                    City: "Phoenix",
                    State: "AZ",
                    PostalCode: "85001",
                    Lat: 33.4484m,
                    Lng: -112.0740m
                ),
            };

            // Create addresses and link to users using hardcoded GUIDs
            foreach (var addressInfo in addressData)
            {
                var address = new Address
                {
                    Id = addressInfo.Id,
                    StreetAddress = addressInfo.Street,
                    City = addressInfo.City,
                    State = addressInfo.State,
                    PostalCode = addressInfo.PostalCode,
                    Latitude = addressInfo.Lat,
                    Longitude = addressInfo.Lng,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                };

                addresses.Add(address);

                var userAddress = new UserAddress
                {
                    Id = addressInfo.UserAddressId,
                    UserId = addressInfo.UserId,
                    AddressId = addressInfo.Id,
                    IsPrimary = true,
                    Label = "Home",
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty,
                };

                userAddresses.Add(userAddress);
            }

            await context.Addresses.AddRangeAsync(addresses);
            await context.SaveChangesAsync();

            await context.UserAddresses.AddRangeAsync(userAddresses);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Seeds 15 booking records with realistic data and associated status history
        /// </summary>
        private static async Task SeedBookings(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager
        )
        {
            // Check if bookings already exist
            if (await context.Bookings.AnyAsync())
            {
                return; // Bookings already seeded
            }

            // Get all clients, providers, categories, and admin user
            var clientRoleName = UserRoleType.Client.ToString();
            var clients = await userManager.GetUsersInRoleAsync(clientRoleName);
            var providers = await context.CareProviderProfiles.Include(p => p.User).ToListAsync();
            var categories = await context.CareCategories.ToListAsync();
            var adminUser = await userManager.FindByEmailAsync("<EMAIL>");

            if (!clients.Any() || !providers.Any() || !categories.Any() || adminUser == null)
            {
                return; // Cannot seed bookings without required entities
            }

            var random = new Random();
            var bookings = new List<Booking>();
            var bookingStatuses = new List<Domain.Entities.BookingStatus>();
            var specialInstructions = new[]
            {
                "Please assist with medication reminders.",
                "Provide companionship and light meal prep.",
                "Focus on mobility exercises and physical therapy.",
                "Ensure child-friendly activities are included.",
                "Assist with daily living activities and personal care.",
                null, // Some bookings may have no special instructions
            };

            // Possible status progressions
            var statusProgressions = new List<BookingStatusType>[]
            {
                new List<BookingStatusType> { BookingStatusType.Requested },
                new List<BookingStatusType>
                {
                    BookingStatusType.Requested,
                    BookingStatusType.Confirmed,
                },
                new List<BookingStatusType>
                {
                    BookingStatusType.Requested,
                    BookingStatusType.Confirmed,
                    BookingStatusType.Completed,
                },
                new List<BookingStatusType>
                {
                    BookingStatusType.Requested,
                    BookingStatusType.Rejected,
                },
                new List<BookingStatusType>
                {
                    BookingStatusType.Requested,
                    BookingStatusType.Confirmed,
                    BookingStatusType.Cancelled,
                },
                new List<BookingStatusType>
                {
                    BookingStatusType.Requested,
                    BookingStatusType.Confirmed,
                    BookingStatusType.Cancelled,
                },
            };

            // Generate 15 bookings with hardcoded GUIDs
            var bookingIds = new[]
            {
                SeedConstants.Bookings.Booking1Id,
                SeedConstants.Bookings.Booking2Id,
                SeedConstants.Bookings.Booking3Id,
                SeedConstants.Bookings.Booking4Id,
                SeedConstants.Bookings.Booking5Id,
                SeedConstants.Bookings.Booking6Id,
                SeedConstants.Bookings.Booking7Id,
                SeedConstants.Bookings.Booking8Id,
                SeedConstants.Bookings.Booking9Id,
                SeedConstants.Bookings.Booking10Id,
                SeedConstants.Bookings.Booking11Id,
                SeedConstants.Bookings.Booking12Id,
                SeedConstants.Bookings.Booking13Id,
                SeedConstants.Bookings.Booking14Id,
                SeedConstants.Bookings.Booking15Id,
            };

            for (int i = 0; i < 15; i++)
            {
                // Select random client, provider, and category
                var client = clients[random.Next(clients.Count)];
                var providerProfile = providers[random.Next(providers.Count)];
                var category = categories[random.Next(categories.Count)];

                // Ensure provider offers the selected category
                var providerCategories = await context
                    .CareProviderCategories.Where(cpc => cpc.ProviderId == providerProfile.Id)
                    .Select(cpc => cpc.CategoryId)
                    .ToListAsync();
                if (!providerCategories.Contains(category.Id))
                {
                    // Pick a category the provider offers
                    category =
                        await context
                            .CareCategories.Where(c => providerCategories.Contains(c.Id))
                            .FirstOrDefaultAsync() ?? categories.First();
                }

                // Generate booking date within the next 30 days
                var daysOffset = random.Next(0, 31);
                var bookingDate = DateTime.UtcNow.Date.AddDays(daysOffset);

                // Generate random start time between 8 AM and 6 PM
                var startHour = random.Next(8, 19);
                var startTime = new TimeOnly(startHour, 0);

                // Generate duration between 1-4 hours
                var durationHours = random.Next(1, 5);
                var endTime = startTime.AddHours(durationHours);

                // Calculate costs with null check for HourlyRate
                var hourlyRate = providerProfile.HourlyRate ?? 25.50m; // Assuming HourlyRate is decimal
                var totalHours = (decimal)durationHours;
                var providerAmount = hourlyRate * totalHours; // decimal * decimal = decimal
                var platformFee = providerAmount * 0.1m; // decimal * decimal = decimal
                var totalAmount = providerAmount + platformFee; // decimal + decimal = decimal

                // Create booking
                var booking = new Booking
                {
                    Id = bookingIds[i],
                    ClientId = client.Id,
                    ProviderId = providerProfile.Id,
                    CategoryId = category.Id,
                    SpecialInstructions = specialInstructions[
                        random.Next(specialInstructions.Length)
                    ],
                    TotalAmount = Math.Round(totalAmount, 2),
                    PlatformFee = Math.Round(platformFee, 2),
                    ProviderAmount = Math.Round(providerAmount, 2),
                    Status = null, // Not setting Status property, using BookingStatus entity
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = client.Id, // Client creates the booking
                    Client = client,
                    Provider = providerProfile,
                    Category = category,
                    Payments = new List<Payment>(),
                    Reviews = new List<Review>(),
                    BookingWindows = new List<BookingWindow>
                    {
                        new BookingWindow
                        {
                            Date = DateOnly.FromDateTime(bookingDate),
                            StartTime = startTime,
                            EndTime = endTime,
                            CreatedBy = client.Id,
                        },
                    },
                };

                bookings.Add(booking);

                // Create status history
                var statusProgression = statusProgressions[random.Next(statusProgressions.Length)];
                var statusTimestamp = bookingDate.AddDays(-random.Next(1, 5)); // Status changes before booking date
                var statusIndex = 0;
                foreach (var status in statusProgression)
                {
                    var createdById =
                        status == BookingStatusType.Requested ? client.Id : adminUser.Id; // Client requests, admin confirms/rejects
                    var notes = status switch
                    {
                        BookingStatusType.Requested => "Booking requested by client",
                        BookingStatusType.Confirmed => "Booking confirmed by provider",
                        BookingStatusType.Completed => "Service completed successfully",
                        BookingStatusType.Rejected => "Provider unavailable",
                        BookingStatusType.Cancelled => "Cancelled by client",
                        BookingStatusType.NoShow => "Client did not show up",
                        _ => null,
                    };

                    var bookingStatus = new Domain.Entities.BookingStatus
                    {
                        Id = SeedConstants.BookingStatuses.GetBookingStatusId(i * 10 + statusIndex),
                        BookingId = booking.Id,
                        Status = status,
                        CreatedBy = createdById,
                        Notes = notes,
                        CreatedAt = statusTimestamp,
                        CreatedByUser = status == BookingStatusType.Requested ? client : adminUser,
                        Booking = booking,
                    };

                    bookingStatuses.Add(bookingStatus);
                    statusTimestamp = statusTimestamp.AddHours(random.Next(1, 24)); // Increment timestamp for next status
                    statusIndex++; // Increment status index for unique GUID generation
                }
            }

            await context.Bookings.AddRangeAsync(bookings);
            await context.SaveChangesAsync();

            await context.Set<Domain.Entities.BookingStatus>().AddRangeAsync(bookingStatuses);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Verifies that the first 6 care providers were seeded correctly and updates their verification status
        /// </summary>
        private static void VerifyCareProviders(ApplicationDbContext context, ILogger logger)
        {
            try
            {
                // Get the first 6 care providers using hardcoded GUIDs
                var providerIds = new[]
                {
                    SeedConstants.CareProviders.Provider1UserId,
                    SeedConstants.CareProviders.Provider2UserId,
                    SeedConstants.CareProviders.Provider3UserId,
                    SeedConstants.CareProviders.Provider4UserId,
                    SeedConstants.CareProviders.Provider5UserId,
                    SeedConstants.CareProviders.Provider6UserId,
                };

                var verifiedCount = 0;
                foreach (var providerId in providerIds)
                {
                    var provider = context.Users.FirstOrDefault(u => u.Id == providerId);
                    var profile = context.UserProfiles.FirstOrDefault(p =>
                        p.ApplicationUserId == providerId
                    );
                    var careProviderProfile = context.CareProviderProfiles.FirstOrDefault(cp =>
                        cp.UserId == providerId
                    );

                    if (provider != null && profile != null && careProviderProfile != null)
                    {
                        // Update verification status to Verified for first 6 providers
                        careProviderProfile.VerificationStatus = VerificationStatus.Verified;
                        verifiedCount++;

                        logger.LogInformation(
                            $"Verified care provider: {provider.Email} ({profile.FirstName} {profile.LastName})"
                        );
                    }
                    else
                    {
                        logger.LogWarning(
                            $"Care provider with ID {providerId} not found or incomplete"
                        );
                    }
                }

                if (verifiedCount > 0)
                {
                    context.SaveChanges();
                    logger.LogInformation($"Successfully verified {verifiedCount} care providers");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error verifying care providers");
            }
        }
    }
}
