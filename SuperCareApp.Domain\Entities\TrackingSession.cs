﻿using System.ComponentModel.DataAnnotations;
using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class TrackingSession : BaseEntity
    {
        public Guid BookingWindowId { get; set; }
        public Guid ProviderId { get; set; }

        [Required]
        public DateTime StartTime { get; set; }

        public DateTime? EndTime { get; set; }

        [Required]
        public TrackingSessionStatus Status { get; set; } = TrackingSessionStatus.Running;

        public string? TrackingData { get; set; }

        public string? Notes { get; set; }

        public decimal? TotalHours { get; set; }

        public DateTimeOffset? PausedAt { get; set; }

        public TimeSpan TotalPausedDuration { get; set; } = TimeSpan.Zero;

        // Navigation properties
        public BookingWindow BookingWindow { get; set; } = null!;
        public ApplicationUser Provider { get; set; } = null!;

        public bool IsActive => Status.IsActive();
        public bool IsCompleted => Status.IsFinal();
        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }
}
