﻿using Microsoft.Extensions.Hosting;

namespace SuperCareApp.Persistence.Extensions
{
    /// <summary>
    /// Extension methods for database migrations
    /// </summary>
    public static class MigrationExtensions
    {
        /// <summary>
        /// Applies any pending migrations for the context to the database.
        /// Will create the database if it does not already exist.
        /// </summary>
        /// <param name="host">The host to migrate</param>
        /// <param name="developmentOnly">Only run migrations in development environment</param>
        public static async Task<IHost> MigrateDatabaseAsync<TContext>(
            this IHost host,
            bool developmentOnly = true
        )
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var environment = services.GetRequiredService<IHostEnvironment>();

                // Skip migration if not in development and developmentOnly is true
                if (developmentOnly && !environment.IsDevelopment())
                {
                    logger.LogInformation(
                        "Skipping database migration in non-development environment"
                    );
                    return host;
                }

                // Try to resolve the context - this might fail if there are dependency issues
                var context = services.GetService<TContext>();
                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}. Skipping database migration.",
                        typeof(TContext).Name
                    );
                    return host;
                }

                logger.LogInformation(
                    "Starting database migration for context {DbContextName}",
                    typeof(TContext).Name
                );

                // Check if database exists first
                var canConnect = await context.Database.CanConnectAsync();

                if (!canConnect)
                {
                    logger.LogInformation(
                        "Database doesn't exist, creating database and applying all migrations"
                    );

                    // This will create the database and apply all migrations
                    await context.Database.MigrateAsync();

                    logger.LogInformation(
                        "Database created and all migrations applied for context {DbContextName}",
                        typeof(TContext).Name
                    );
                }
                else
                {
                    // Database exists, check for pending migrations
                    var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                    var pendingMigrationsList = pendingMigrations.ToList();

                    if (pendingMigrationsList.Any())
                    {
                        logger.LogInformation(
                            "Found {Count} pending migrations: {Migrations}",
                            pendingMigrationsList.Count,
                            string.Join(", ", pendingMigrationsList)
                        );

                        // Apply migrations
                        await context.Database.MigrateAsync();

                        logger.LogInformation(
                            "Successfully applied {Count} migrations for context {DbContextName}",
                            pendingMigrationsList.Count,
                            typeof(TContext).Name
                        );
                    }
                    else
                    {
                        logger.LogInformation(
                            "No pending migrations found for context {DbContextName}",
                            typeof(TContext).Name
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(
                    ex,
                    "An error occurred while migrating the database for context {DbContextName}",
                    typeof(TContext).Name
                );

                // Re-throw in development to make issues visible
                var environment = services.GetService<IHostEnvironment>();
                if (environment?.IsDevelopment() == true)
                {
                    throw;
                }
            }

            return host;
        }

        /// <summary>
        /// Synchronous version of database migration
        /// </summary>
        /// <param name="host">The host to migrate</param>
        /// <param name="developmentOnly">Only run migrations in development environment</param>
        public static IHost MigrateDatabase<TContext>(this IHost host, bool developmentOnly = true)
            where TContext : DbContext
        {
            return host.MigrateDatabaseAsync<TContext>(developmentOnly).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Ensures the database exists without applying migrations
        /// </summary>
        /// <param name="host">The host</param>
        /// <param name="developmentOnly">Only run in development environment</param>
        public static async Task<IHost> EnsureDatabaseAsync<TContext>(
            this IHost host,
            bool developmentOnly = true
        )
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var environment = services.GetRequiredService<IHostEnvironment>();

                if (developmentOnly && !environment.IsDevelopment())
                {
                    logger.LogInformation(
                        "Skipping database creation in non-development environment"
                    );
                    return host;
                }

                var context = services.GetService<TContext>();
                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}. Skipping database creation.",
                        typeof(TContext).Name
                    );
                    return host;
                }

                logger.LogInformation(
                    "Ensuring database exists for context {DbContextName}",
                    typeof(TContext).Name
                );

                var created = await context.Database.EnsureCreatedAsync();

                if (created)
                {
                    logger.LogInformation(
                        "Database created for context {DbContextName}",
                        typeof(TContext).Name
                    );
                }
                else
                {
                    logger.LogInformation(
                        "Database already exists for context {DbContextName}",
                        typeof(TContext).Name
                    );
                }
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(
                    ex,
                    "An error occurred while ensuring database exists for context {DbContextName}",
                    typeof(TContext).Name
                );

                var environment = services.GetService<IHostEnvironment>();
                if (environment?.IsDevelopment() == true)
                {
                    throw;
                }
            }

            return host;
        }

        /// <summary>
        /// Gets information about the database and migrations
        /// </summary>
        /// <param name="host">The host</param>
        public static async Task<IHost> DatabaseInfoAsync<TContext>(this IHost host)
            where TContext : DbContext
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var logger = services.GetRequiredService<ILogger<TContext>>();
                var context = services.GetService<TContext>();

                if (context == null)
                {
                    logger.LogWarning(
                        "Could not resolve DbContext of type {ContextType}",
                        typeof(TContext).Name
                    );
                    return host;
                }

                logger.LogInformation(
                    "=== Database Information for {ContextType} ===",
                    typeof(TContext).Name
                );

                var canConnect = await context.Database.CanConnectAsync();
                logger.LogInformation("Can connect to database: {CanConnect}", canConnect);

                if (canConnect)
                {
                    var appliedMigrations = await context.Database.GetAppliedMigrationsAsync();
                    var pendingMigrations = await context.Database.GetPendingMigrationsAsync();

                    logger.LogInformation(
                        "Applied migrations ({Count}): {Migrations}",
                        appliedMigrations.Count(),
                        string.Join(", ", appliedMigrations)
                    );

                    logger.LogInformation(
                        "Pending migrations ({Count}): {Migrations}",
                        pendingMigrations.Count(),
                        string.Join(", ", pendingMigrations)
                    );
                }

                logger.LogInformation("=== End Database Information ===");
            }
            catch (Exception ex)
            {
                var logger = services.GetService<ILogger<TContext>>();
                logger?.LogError(ex, "Error getting database information");
            }

            return host;
        }
    }
}
