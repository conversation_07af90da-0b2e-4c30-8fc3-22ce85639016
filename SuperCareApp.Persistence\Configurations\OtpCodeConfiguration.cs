using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;


namespace SuperCareApp.Persistence.Configurations
{
    public class OtpCodeConfiguration : IEntityTypeConfiguration<OtpCode>
    {
        public void Configure(EntityTypeBuilder<OtpCode> builder)
        {
            builder.HasKey(x => x.Id);

            builder.Property(x => x.ApplicationUserId).IsRequired();

            builder.Property(x => x.Code).IsRequired().HasMaxLength(6).IsFixedLength();

            builder.Property(x => x.ExpiresAt).IsRequired();

            builder.Property(x => x.CreatedAt).IsRequired();

            builder.Property(x => x.IsUsed).IsRequired().HasDefaultValue(false);

            // Configure the relationship with ApplicationUser using ApplicationUserId
            builder
                .HasOne(x => x.User)
                .WithMany(u => u.OtpCodes)
                .HasForeignKey(x => x.ApplicationUserId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}