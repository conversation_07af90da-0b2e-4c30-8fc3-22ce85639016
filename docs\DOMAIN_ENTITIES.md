# SuperCareApp Domain Entities

This document provides a detailed overview of the domain entities in the `SuperCareApp` solution. It includes an explanation of the base entity, a comprehensive Entity Relationship Diagram (ERD), and a detailed breakdown of each entity.

## Base Entity

Most domain entities inherit from the `BaseEntity` class, which provides a common set of properties for auditing and tracking.

- **`Id`** (Guid): The primary key for the entity.
- **`CreatedAt`** (DateTime): The date and time when the entity was created.
- **`CreatedBy`** (Guid): The ID of the user who created the entity.
- **`UpdatedAt`** (DateTime?): The date and time when the entity was last updated.
- **`UpdatedBy`** (Guid?): The ID of the user who last updated the entity.
- **`IsDeleted`** (bool): A flag to indicate if the entity has been soft-deleted.
- **`DeletedAt`** (DateTime?): The date and time when the entity was soft-deleted.
- **`DeletedBy`** (Guid?): The ID of the user who soft-deleted the entity.

## Entity Relationship Diagram (ERD)

The following diagram illustrates the relationships between the domain entities.

```mermaid
erDiagram
    ApplicationUser {
        Guid Id
        string AuthProvider
        string AuthProviderId
        bool EmailVerified
        bool IsActive
        DateTime LastLogin
        DateTime CreatedAt
        Guid CreatedBy
        DateTime UpdatedAt
        Guid UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        Guid DeletedBy
    }

    ApplicationRole {
        Guid Id
        string Name
        DateTime CreatedAt
        Guid CreatedBy
        DateTime UpdatedAt
        Guid UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        Guid DeletedBy
    }

    UserProfile {
        Guid Id
        Guid ApplicationUserId
        string FirstName
        string LastName
        string PhoneNumber
        DateTime DateOfBirth
        string Gender
        string ImageName
        string ImagePath
        string Country
        string Preferences
    }

    CareProviderProfile {
        Guid Id
        Guid UserId
        string Bio
        int YearsExperience
        decimal HourlyRate
        string CareDescription
        bool ProvidesOvernight
        bool ProvidesLiveIn
        bool ProvidesRecurringBooking
        int WorkingHours
        string Qualifications
        VerificationStatus VerificationStatus
        decimal Rating
        int RatingCount
        int BufferDuration
    }

    Address {
        Guid Id
        string StreetAddress
        string City
        string State
        string PostalCode
        decimal Latitude
        decimal Longitude
    }

    UserAddress {
        Guid Id
        Guid UserId
        Guid AddressId
        bool IsPrimary
        string Label
    }

    CareCategory {
        Guid Id
        string Name
        string Description
        bool IsActive
        decimal PlatformFee
        string Color
        string Icon
    }

    CareProviderCategory {
        Guid Id
        Guid ProviderId
        Guid CategoryId
        string ProviderSpecificDescription
        decimal HourlyRate
        int MaxHoursPerWeek
        int MinHoursPerWeek
        int ExperienceYears
    }

    Booking {
        Guid Id
        Guid ClientId
        Guid ProviderId
        Guid CategoryId
        int WorkingHours
        string SpecialInstructions
        decimal TotalAmount
        decimal PlatformFee
        decimal ProviderAmount
    }

    BookingStatus {
        Guid Id
        Guid BookingId
        BookingStatusType Status
        Guid CreatedBy
        string Notes
    }

    BookingWindow {
        Guid Id
        Guid BookingId
        DateOnly Date
        TimeOnly StartTime
        TimeOnly EndTime
        decimal DailyRate
        string DaySpecialInstructions
        int DurationMinutes
    }

    Availability {
        Guid Id
        Guid ProviderId
        string DayOfWeek
        bool IsAvailable
    }

    AvailabilitySlot {
        Guid Id
        Guid AvailabilityId
        TimeOnly StartTime
        TimeOnly EndTime
    }

    Leave {
        Guid Id
        Guid ProviderId
        DateTime StartDate
        DateTime EndDate
        string Reason
    }

    Review {
        Guid Id
        Guid BookingId
        Guid ReviewerId
        Guid RevieweeId
        int Rating
        string Comment
    }

    Payment {
        Guid Id
        Guid BookingId
        decimal Amount
        string PaymentMethod
        string TransactionId
        PaymentStatus Status
        DateTime PaymentDateTime
        string InvoiceNumber
    }

    Invoice {
        Guid Id
        Guid BookingId
        string InvoiceNumber
        DateTime InvoiceDate
        string Status
        string PaymentStatus
        decimal TotalAmount
        string Currency
    }

    TrackingSession {
        Guid Id
        Guid BookingId
        Guid ProviderId
        DateTime StartTime
        DateTime EndTime
        TrackingSessionStatus Status
        string TrackingData
        string Notes
        decimal TotalHours
        DateTime PausedAt
        TimeSpan TotalPausedDuration
    }

    Conversation {
        Guid Id
        Guid ClientId
        Guid ProviderId
        DateTime LastMessageAt
    }

    Message {
        Guid Id
        Guid ConversationId
        Guid SenderId
        string Content
        bool IsRead
        DateTime ReadAt
    }

    Attachment {
        Guid Id
        Guid MessageId
        string FileUrl
        string FileType
        string FileName
        int FileSize
    }

    Notification {
        Guid Id
        string Title
        string Content
        NotificationType NotificationType
        string ActionUrl
    }

    UserNotification {
        Guid Id
        Guid ApplicationUserId
        Guid NotificationId
        bool IsRead
        DateTime ReadAt
    }

    Document {
        Guid Id
        Guid UserId
        string DocumentName
        string DocumentType
        string DocumentUrl
        string Issuer
        VerificationStatus VerificationStatus
        DateTime UploadedAt
        DateTime VerifiedAt
        Guid VerifiedBy
        string RejectionReason
        Country Country
        CertificationType CertificationType
        string OtherCertificationType
        string CertificationNumber
        DateTime ExpiryDate
    }

    Approval {
        Guid Id
        Guid UserId
        ApprovalType ApprovalType
        bool IsApproved
        string RejectionReason
        string ApprovalData
        Guid RelatedEntityId
        Guid ProcessedBy
        DateTime ProcessedAt
        string Notes
    }

    Subscription {
        Guid Id
        Guid ProviderId
        SubscriptionType SubscriptionType
        DateTime StartDate
        DateTime EndDate
        decimal Amount
        PaymentStatus Status
        string PaymentMethod
    }

    SystemSettings {
        Guid Id
        string SettingKey
        string SettingValue
        string SettingDescription
    }

    AuditLog {
        Guid Id
        string EntityType
        Guid EntityId
        string Action
        string OldValues
        string NewValues
        Guid UserId
        DateTime Timestamp
    }

    OtpCode {
        Guid Id
        Guid ApplicationUserId
        string Code
        DateTime CreatedAt
        DateTime ExpiresAt
        bool IsUsed
    }

    ApplicationUser ||--o{ UserProfile : "Has one"
    ApplicationUser ||--o{ CareProviderProfile : "Can have one"
    ApplicationUser ||--|{ UserAddress : "Has many"
    ApplicationUser ||--|{ UserNotification : "Has many"
    ApplicationUser ||--|{ OtpCode : "Has many"
    ApplicationUser ||--|{ Booking : "Client"
    ApplicationUser ||--|{ Conversation : "Client"
    ApplicationUser ||--|{ Conversation : "Provider"
    ApplicationUser ||--|{ Message : "Sender"
    ApplicationUser ||--|{ Review : "Reviewer"
    ApplicationUser ||--|{ Review : "Reviewee"
    ApplicationUser ||--|{ Subscription : "Provider"
    ApplicationUser ||--|{ Approval : "User"
    ApplicationUser ||--|{ Approval : "Processor"
    ApplicationUser ||--|{ TrackingSession : "Provider"

    Address ||--|{ UserAddress : "Used in"

    CareProviderProfile ||--|{ CareProviderCategory : "Has many"
    CareProviderProfile ||--|{ Availability : "Has many"
    CareProviderProfile ||--|{ Leave : "Has many"
    CareProviderProfile ||--|{ Booking : "Provider"

    CareCategory ||--|{ CareProviderCategory : "Used in"
    CareCategory ||--o{ Booking : "Category"

    Booking ||--o{ BookingStatus : "Has one current"
    Booking ||--|{ BookingStatus : "Has history of"
    Booking ||--|{ BookingWindow : "Has many"
    Booking ||--|{ Payment : "Has many"
    Booking ||--|{ Review : "Has many"
    Booking ||--|{ TrackingSession : "Has many"
    Booking ||--o{ Invoice : "Has one"

    Availability ||--|{ AvailabilitySlot : "Has many"

    Conversation ||--|{ Message : "Has many"
    Message ||--|{ Attachment : "Has many"

    Notification ||--|{ UserNotification : "Sent to many"
    UserProfile ||--|{ Document : "Has many"
```

## Entity Details

This section provides a detailed breakdown of each domain entity, including its purpose, properties, and relationships.

### Identity Entities

#### ApplicationUser

Represents a user in the system. It extends the `IdentityUser` from ASP.NET Core Identity and includes auditing properties.

- **Inherits from:** `IdentityUser<Guid>`
- **Properties:**
  - `AuthProvider` (string): The authentication provider (e.g., "Google", "Facebook").
  - `AuthProviderId` (string): The user's ID from the authentication provider.
  - `EmailVerified` (bool): Indicates if the user's email has been verified.
  - `IsActive` (bool): Indicates if the user's account is active.
  - `LastLogin` (DateTime?): The last login date and time.
- **Relationships:**
  - Has one `UserProfile`.
  - Can have one `CareProviderProfile`.
  - Has many `UserAddress`, `UserNotification`, `OtpCode`, `Booking` (as client), `Conversation` (as client or provider), `Message` (as sender), `Review` (as reviewer or reviewee), `Subscription` (as provider), `Approval` (as user or processor), and `TrackingSession` (as provider).

#### ApplicationRole

Represents a role in the system. It extends the `IdentityRole` from ASP.NET Core Identity and includes auditing properties.

- **Inherits from:** `IdentityRole<Guid>`

### Core Domain Entities

#### Address

Represents a physical address.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Used in many `UserAddress` records.

#### Approval

Represents a request for approval, such as for a new care provider.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser` (the user requesting approval).
  - Can be processed by one `ApplicationUser` (an admin).

#### Attachment

Represents a file attached to a message.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Message`.

#### AuditLog

Represents a log of changes to entities.

- **Does not inherit from `BaseEntity`.**

#### Availability

Represents the weekly availability of a care provider.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `CareProviderProfile`.
  - Has many `AvailabilitySlot` records.

#### AvailabilitySlot

Represents a specific time slot within a provider's availability.

- **Does not inherit from `BaseEntity`.**
- **Relationships:**
  - Belongs to one `Availability`.

#### Booking

Represents a booking for a care service.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser` (the client) and one `CareProviderProfile` (the provider).
  - Belongs to one `CareCategory`.
  - Has one current `BookingStatus` and a history of `BookingStatus` records.
  - Has many `BookingWindow`, `Payment`, `Review`, and `TrackingSession` records.
  - Has one `Invoice`.

#### BookingStatus

Represents the status of a booking at a point in time.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Booking`.
  - Created by one `ApplicationUser`.

#### BookingWindow

Represents a specific time window for a booking.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Booking`.

#### CareCategory

Represents a category of care service.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Used in many `CareProviderCategory` records.
  - Associated with many `Booking` records.

#### CareProviderCategory

Links a care provider to a care category, specifying their experience and rates.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `CareProviderProfile` and one `CareCategory`.

#### CareProviderProfile

Represents the professional profile of a care provider.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser`.
  - Has many `CareProviderCategory`, `Availability`, and `Leave` records.
  - Associated with many `Booking` records.

#### Conversation

Represents a conversation between a client and a provider.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser` (the client) and one `ApplicationUser` (the provider).
  - Has many `Message` records.

#### Document

Represents a document uploaded by a user, such as a certification.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `UserProfile`.

#### Invoice

Represents an invoice for a booking.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Booking`.

#### Leave

Represents a period of leave for a care provider.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `CareProviderProfile`.

#### Message

Represents a message in a conversation.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Conversation`.
  - Sent by one `ApplicationUser`.
  - Has many `Attachment` records.

#### Notification

Represents a notification to be sent to users.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Sent to many users through the `UserNotification` join table.

#### OtpCode

Represents a one-time password for verification.

- **Does not inherit from `BaseEntity`.**
- **Relationships:**
  - Belongs to one `ApplicationUser`.

#### Payment

Represents a payment for a booking.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Booking`.

#### Review

Represents a review for a booking.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Booking`.
  - Submitted by one `ApplicationUser` (the reviewer) for another `ApplicationUser` (the reviewee).

#### Subscription

Represents a subscription for a care provider.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser` (the provider).

#### SystemSettings

Represents a system-wide setting.

- **Inherits from:** `BaseEntity`

#### TrackingSession

Represents a tracking session for a booking.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `Booking`.
  - Associated with one `ApplicationUser` (the provider).

#### UserAddress

Links a user to an address.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser` and one `Address`.

#### UserNotification

Links a user to a notification.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser` and one `Notification`.

#### UserProfile

Represents the personal profile of a user.

- **Inherits from:** `BaseEntity`
- **Relationships:**
  - Belongs to one `ApplicationUser`.
  - Has many `Document` records.
