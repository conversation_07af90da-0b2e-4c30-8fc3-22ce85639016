using SuperCareApp.Application.Common.Interfaces.Mediator;

namespace SuperCareApp.Application.Common.Interfaces.Messages.Query;

/// <summary>
/// Represents a query request that returns data of type TResponse.
/// Implements IRequest to integrate with the mediator pattern.
/// </summary>
/// <typeparam name="TResponse">The type of data returned by the query</typeparam>
public interface IQuery<TResponse> : IRequest<TResponse> { }

/// <summary>
/// Represents a handler for processing queries of type TQuery.
/// Implements IRequestHandler to handle the query and return results of type TResponse.
/// </summary>
/// <typeparam name="TQuery">The type of the query being handled</typeparam>
/// <typeparam name="TResponse">The type of data returned by the query handler</typeparam>
public interface IQueryHandler<TQuery, TResponse> : IRequestHandler<TQuery, TResponse>
    where TQuery : IQuery<TResponse> { }
