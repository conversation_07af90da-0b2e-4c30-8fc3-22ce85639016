using System.Text.Json;
using Microsoft.Extensions.Logging;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Admin;
using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Provider;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Identity.Commands;

namespace SuperCareApp.Persistence.Test.Identity;

public class RegisterUserCommandHandlerTests
{
    private readonly Mock<IAuthService> _authServiceMock;
    private readonly Mock<ICareProviderProfileService> _careProviderProfileServiceMock;
    private readonly Mock<IApprovalService> _approvalServiceMock;
    private readonly Mock<IAvailabilityTemplateService> _availabilityTemplateServiceMock;
    private readonly Mock<ILogger<RegisterUserCommandHandler>> _loggerMock;
    private readonly Mock<IMediator> _mediatorMock; // Although not used in the handler, it's a dependency
    private readonly RegisterUserCommandHandler _handler;

    public RegisterUserCommandHandlerTests()
    {
        _authServiceMock = new Mock<IAuthService>();
        _careProviderProfileServiceMock = new Mock<ICareProviderProfileService>();
        _approvalServiceMock = new Mock<IApprovalService>();
        _availabilityTemplateServiceMock = new Mock<IAvailabilityTemplateService>();
        _loggerMock = new Mock<ILogger<RegisterUserCommandHandler>>();
        _mediatorMock = new Mock<IMediator>();

        _handler = new RegisterUserCommandHandler(
            _authServiceMock.Object,
            _careProviderProfileServiceMock.Object,
            _approvalServiceMock.Object,
            _availabilityTemplateServiceMock.Object,
            _loggerMock.Object,
            _mediatorMock.Object
        );
    }

    [Fact]
    public async Task Handle_ValidClientRegistration_ReturnsSuccess()
    {
        var command = new RegisterUserCommand(
            Email: "<EMAIL>",
            Password: "SecurePass123!",
            // PhoneNumber: "************", // OLD - Invalid format
            PhoneNumber: "+12025550123", // NEW - Valid US format (example)
            IsCareProvider: false,
            FirstName: "John",
            LastName: "Doe"
        );
        var userId = Guid.NewGuid();
        // --- FIX: Set expected normalized phone to the correct E164 format ---
        // Assuming PhoneNumber.Create("************", "US") produces this:
        var normalizedPhone = "+12025550123"; // NEW - Correct E164 for the input

        // Mock AuthService.RegisterAsync
        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(
                    command.Email,
                    command.Password,
                    normalizedPhone, // Use the corrected expected value
                    command.IsCareProvider
                )
            )
            .ReturnsAsync(Result.Success(userId));

        // Mock AuthService.AddToRoleAsync
        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "Client"))
            .ReturnsAsync(Result.Success());

        // Mock AuthService.CreateUserProfileAsync
        var profileId = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    normalizedPhone // Use the corrected expected value
                )
            )
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileId))
            ); // Ensure type matches IAuthService

        // Mock AuthService.AuthenticateAsync
        var authResponse = new AuthResponse(
            "access_token",
            DateTime.UtcNow.AddHours(1),
            "refresh_token",
            null,
            new AuthUserResponse(userId.ToString(), command.Email, normalizedPhone) // Use normalizedPhone
        );
        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(command.Email, command.Password))
            .ReturnsAsync(Result.Success(authResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(authResponse.accessToken, result.Value.accessToken);
        // Add assertion for user role if it's part of AuthResponse/AuthUserResponse
        // Assert.Contains("Client", result.Value.user.Roles); // Example, adjust based on actual DTO

        // Verify interactions with the CORRECT normalized phone number
        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(
                    command.Email,
                    command.Password,
                    normalizedPhone, // Verify called with normalized value
                    command.IsCareProvider
                ),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "Client"),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    normalizedPhone // Verify called with normalized value
                ),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AuthenticateAsync(command.Email, command.Password),
            Times.Once
        );

        // Verify other services were NOT called for client
        _careProviderProfileServiceMock.Verify(
            cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()),
            Times.Never
        );
        _availabilityTemplateServiceMock.Verify(
            at => at.CreateDefaultAvailabilityTemplateAsync(It.IsAny<Guid>()),
            Times.Never
        );
        _approvalServiceMock.Verify(
            a =>
                a.CreateApprovalAsync(
                    It.IsAny<Guid>(),
                    It.IsAny<ApprovalType>(),
                    It.IsAny<string>(),
                    It.IsAny<Guid>()
                ),
            Times.Never
        );

        _loggerMock.Verify(
            x =>
                x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>(
                        (v, t) =>
                            v.ToString().Contains($"Registering user with email: {command.Email}")
                    ),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
    }

    [Fact]
    public async Task Handle_ValidCareProviderRegistration_ReturnsSuccess()
    {
        // Arrange
        var command = new RegisterUserCommand(
            Email: "<EMAIL>",
            Password: "SecurePass123!",
            PhoneNumber: "+12025550123",
            IsCareProvider: true,
            FirstName: "Jane",
            LastName: "Smith"
        );
        var userId = Guid.NewGuid();
        var normalizedPhone = "+12025550123";
        var profileId = Guid.NewGuid();

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(
                    command.Email,
                    command.Password,
                    normalizedPhone,
                    command.IsCareProvider
                )
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"))
            .ReturnsAsync(Result.Success());

        var profileIdResponse = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    normalizedPhone
                )
            )
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileIdResponse))
            );

        var careProviderProfile = new CareProviderProfile
        {
            Id = profileId,
            UserId = userId,
            YearsExperience = 0,
            VerificationStatus = VerificationStatus.Pending,
        };
        _careProviderProfileServiceMock
            .Setup(cp =>
                cp.CreateAsync(
                    It.Is<CareProviderProfile>(p =>
                        p.UserId == userId
                        && p.YearsExperience == 0
                        && p.VerificationStatus == VerificationStatus.Pending
                    )
                )
            )
            .ReturnsAsync(Result.Success(profileId));

        _availabilityTemplateServiceMock
            .Setup(at => at.CreateDefaultAvailabilityTemplateAsync(profileId))
            .ReturnsAsync(Result.Success());

        var expectedApprovalData = JsonSerializer.Serialize(
            new
            {
                Email = command.Email,
                PhoneNumber = normalizedPhone,
                ProfileId = profileId,
            }
        );
        var approvalId = Guid.NewGuid();
        _approvalServiceMock
            .Setup(a =>
                a.CreateApprovalAsync(
                    userId,
                    ApprovalType.CareProviderVerification,
                    It.Is<string>(data => data == expectedApprovalData), // Verify serialized data
                    profileId
                )
            )
            .ReturnsAsync(
                Result.Success(
                    new Approval
                    {
                        Id = approvalId,
                        UserId = userId,
                        ApprovalType = ApprovalType.CareProviderVerification,
                    }
                )
            );

        var authResponse = new AuthResponse(
            "access_token_cp",
            DateTime.UtcNow.AddHours(1),
            "refresh_token_cp",
            null,
            new AuthUserResponse(
                userId.ToString(),
                command.Email,
                normalizedPhone,
                profileId.ToString()
            )
        );
        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(command.Email, command.Password))
            .ReturnsAsync(Result.Success(authResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(authResponse.accessToken, result.Value.accessToken);
        Assert.Equal(profileId.ToString(), result.Value.user.providerId);

        // Verify interactions
        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(
                    command.Email,
                    command.Password,
                    normalizedPhone,
                    command.IsCareProvider
                ),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    normalizedPhone
                ),
            Times.Once
        );
        _careProviderProfileServiceMock.Verify(
            cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()),
            Times.Once
        );
        _availabilityTemplateServiceMock.Verify(
            at => at.CreateDefaultAvailabilityTemplateAsync(profileId),
            Times.Once
        );
        _approvalServiceMock.Verify(
            a =>
                a.CreateApprovalAsync(
                    userId,
                    ApprovalType.CareProviderVerification,
                    It.IsAny<string>(),
                    profileId
                ),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AuthenticateAsync(command.Email, command.Password),
            Times.Once
        );

        _loggerMock.Verify(
            x =>
                x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>(
                        (v, t) =>
                            v.ToString().Contains($"Registering user with email: {command.Email}")
                    ),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
        _loggerMock.Verify(
            x =>
                x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>(
                        (v, t) =>
                            v.ToString()
                                .Contains(
                                    $"Created approval request {approvalId} for care provider {command.Email}"
                                )
                    ),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
    }

    [Fact]
    public async Task Handle_InvalidPhoneNumberFormat_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand(
            Email: "<EMAIL>",
            Password: "SecurePass123!",
            PhoneNumber: "not-a-phone-number", // Invalid format
            IsCareProvider: false
        );

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Invalid phone number format.", result.Error.Message);
        Assert.Equal("Validation", result.Error.Code);

        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<bool>()
                ),
            Times.Never
        );
        _loggerMock.Verify(
            x =>
                x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>(
                        (v, t) =>
                            v.ToString()
                                .Contains($"Invalid phone number format for email: {command.Email}")
                    ),
                    It.IsAny<FormatException>(), // Verify FormatException is passed
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
    }

    [Fact]
    public async Task Handle_AuthServiceRegistrationFails_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, false);
        var expectedError = Error.Conflict("Email already exists.");

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ReturnsAsync(Result.Failure<Guid>(expectedError));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(expectedError.Code, result.Error.Code);
        Assert.Equal(expectedError.Message, result.Error.Message);

        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        // No further steps should be executed
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(It.IsAny<string>(), It.IsAny<string>()),
            Times.Never
        );
    }

    [Fact]
    public async Task Handle_AddToRoleFails_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, true); // CareProvider to test role
        var userId = Guid.NewGuid();
        var expectedError = Error.Internal("Failed to assign role.");

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"))
            .ReturnsAsync(Result.Failure(expectedError));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(expectedError.Code, result.Error.Code);
        Assert.Equal(expectedError.Message, result.Error.Message);

        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"),
            Times.Once
        );
        // No further steps should be executed
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()
                ),
            Times.Never
        );
    }

    [Fact]
    public async Task Handle_CreateUserProfileFails_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "<EMAIL>",
            "password",
            "**********",
            false
        );
        var userId = Guid.NewGuid();
        var expectedError = Error.Internal("Profile creation failed.");
        var normalizedPhone = "+1**********";

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(
                    command.Email,
                    command.Password,
                    normalizedPhone,
                    command.IsCareProvider
                )
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "Client"))
            .ReturnsAsync(Result.Success());

        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    normalizedPhone
                )
            )
            .ReturnsAsync(Result.Failure<CreateUserProfileResponse>(expectedError));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(expectedError.Code, result.Error.Code);
    }

    [Fact]
    public async Task Handle_CareProviderProfileCreationFails_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, true);
        var userId = Guid.NewGuid();
        var expectedError = Error.Internal("Failed to create care provider profile.");

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"))
            .ReturnsAsync(Result.Success());

        var profileIdResponse = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                )
            )
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileIdResponse))
            );

        _careProviderProfileServiceMock
            .Setup(cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()))
            .ReturnsAsync(Result.Failure<Guid>(expectedError));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(expectedError.Code, result.Error.Code);
        Assert.Equal(expectedError.Message, result.Error.Message);

        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                ),
            Times.Once
        );
        _careProviderProfileServiceMock.Verify(
            cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()),
            Times.Once
        );
        // No further steps should be executed for care provider flow after this failure
        _availabilityTemplateServiceMock.Verify(
            at => at.CreateDefaultAvailabilityTemplateAsync(It.IsAny<Guid>()),
            Times.Never
        );
    }

    [Fact]
    public async Task Handle_AvailabilityTemplateCreationFails_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, true);
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var expectedError = Error.Internal("Failed to create default availability template.");

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"))
            .ReturnsAsync(Result.Success());

        var profileIdResponse = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                )
            )
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileIdResponse))
            );

        _careProviderProfileServiceMock
            .Setup(cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()))
            .ReturnsAsync(Result.Success(profileId));

        _availabilityTemplateServiceMock
            .Setup(at => at.CreateDefaultAvailabilityTemplateAsync(profileId))
            .ReturnsAsync(Result.Failure(expectedError)); // This is the failure point

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(expectedError.Code, result.Error.Code);
        Assert.Equal(expectedError.Message, result.Error.Message);

        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                ),
            Times.Once
        );
        _careProviderProfileServiceMock.Verify(
            cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()),
            Times.Once
        );
        _availabilityTemplateServiceMock.Verify(
            at => at.CreateDefaultAvailabilityTemplateAsync(profileId),
            Times.Once
        );
        // Approval creation should not happen if template creation fails
        _approvalServiceMock.Verify(
            a =>
                a.CreateApprovalAsync(
                    It.IsAny<Guid>(),
                    It.IsAny<ApprovalType>(),
                    It.IsAny<string>(),
                    It.IsAny<Guid>()
                ),
            Times.Never
        );
    }

    [Fact]
    public async Task Handle_ApprovalCreationFails_LogsError_ButContinues()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, true);
        var userId = Guid.NewGuid();
        var profileId = Guid.NewGuid();
        var expectedApprovalError = Error.Internal("Approval service unavailable.");
        // The handler logs the approval error but continues to authenticate.

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"))
            .ReturnsAsync(Result.Success());

        var profileIdResponse = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                )
            )
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileIdResponse))
            );

        _careProviderProfileServiceMock
            .Setup(cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()))
            .ReturnsAsync(Result.Success(profileId));

        _availabilityTemplateServiceMock
            .Setup(at => at.CreateDefaultAvailabilityTemplateAsync(profileId))
            .ReturnsAsync(Result.Success());

        _approvalServiceMock
            .Setup(a =>
                a.CreateApprovalAsync(
                    userId,
                    ApprovalType.CareProviderVerification,
                    It.IsAny<string>(),
                    profileId
                )
            )
            .ReturnsAsync(Result.Failure<Approval>(expectedApprovalError)); // Approval fails

        // BUT authentication should still succeed
        var authResponse = new AuthResponse(
            "token_a",
            DateTime.UtcNow.AddHours(1),
            "token_r",
            null,
            new AuthUserResponse(userId.ToString(), command.Email, null, profileId.ToString())
        );
        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(command.Email, command.Password))
            .ReturnsAsync(Result.Success(authResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        // The overall result should be SUCCESS because authentication succeeded, despite the approval failure.
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(authResponse.accessToken, result.Value.accessToken);

        // Verify all steps were called, including the failed approval
        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "CareProvider"),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                ),
            Times.Once
        );
        _careProviderProfileServiceMock.Verify(
            cp => cp.CreateAsync(It.IsAny<CareProviderProfile>()),
            Times.Once
        );
        _availabilityTemplateServiceMock.Verify(
            at => at.CreateDefaultAvailabilityTemplateAsync(profileId),
            Times.Once
        );
        _approvalServiceMock.Verify(
            a =>
                a.CreateApprovalAsync(
                    userId,
                    ApprovalType.CareProviderVerification,
                    It.IsAny<string>(),
                    profileId
                ),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AuthenticateAsync(command.Email, command.Password),
            Times.Once
        );

        // Verify the error was logged for the approval failure
        _loggerMock.Verify(
            x =>
                x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>(
                        (v, t) =>
                            v.ToString()
                                .Contains(
                                    $"Failed to create approval request for care provider {command.Email}"
                                )
                    ),
                    null, // Assuming Error.Message is passed, not the exception object itself in this case
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
    }

    [Fact]
    public async Task Handle_AuthenticationAfterRegistrationFails_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, false);
        var userId = Guid.NewGuid();
        var expectedAuthError = Error.Unauthorized("Authentication failed after registration.");

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "Client"))
            .ReturnsAsync(Result.Success());

        var profileIdResponse = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                )
            )
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileIdResponse))
            );

        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(command.Email, command.Password))
            .ReturnsAsync(Result.Failure<AuthResponse>(expectedAuthError)); // This is the failure

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal(expectedAuthError.Code, result.Error.Code);
        Assert.Equal(expectedAuthError.Message, result.Error.Message);

        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AddToRoleAsync(userId.ToString(), "Client"),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                ),
            Times.Once
        );
        _authServiceMock.Verify(
            auth => auth.AuthenticateAsync(command.Email, command.Password),
            Times.Once
        );
        // If auth fails, the process stops here, and success response is not returned.
    }

    [Fact]
    public async Task Handle_ExceptionThrown_ReturnsFailure()
    {
        // Arrange
        var command = new RegisterUserCommand("<EMAIL>", "password", null, false);
        var exceptionMessage = "Simulated database error";

        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            )
            .ThrowsAsync(new InvalidOperationException(exceptionMessage));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains(
            "An unexpected error occurred during user registration.",
            result.Error.Message
        );
        Assert.Equal("Internal", result.Error.Code);
    }

    [Fact]
    public async Task Handle_EmptyOrNullPhoneNumber_SkipsNormalization_CallsAuthServiceWithNull()
    {
        // Arrange
        var command = new RegisterUserCommand(
            Email: "<EMAIL>",
            Password: "SecurePass123!",
            PhoneNumber: "", // Empty string
            IsCareProvider: false,
            FirstName: "No",
            LastName: "Phone"
        );
        var userId = Guid.NewGuid();

        // Setup AuthService to expect null phone number
        _authServiceMock
            .Setup(auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider)
            ) // Expect null
            .ReturnsAsync(Result.Success(userId));

        _authServiceMock
            .Setup(auth => auth.AddToRoleAsync(userId.ToString(), "Client"))
            .ReturnsAsync(Result.Success());

        var profileIdResponse = Guid.NewGuid();
        _authServiceMock
            .Setup(auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                )
            ) // Expect null
            .ReturnsAsync(
                Result.Success(new CreateUserProfileResponse("Profile created", profileIdResponse))
            );

        var authResponse = new AuthResponse(
            "access_token_np",
            DateTime.UtcNow.AddHours(1),
            "refresh_token_np",
            null,
            new AuthUserResponse(userId.ToString(), command.Email, null)
        );
        _authServiceMock
            .Setup(auth => auth.AuthenticateAsync(command.Email, command.Password))
            .ReturnsAsync(Result.Success(authResponse));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        _authServiceMock.Verify(
            auth =>
                auth.RegisterAsync(command.Email, command.Password, null, command.IsCareProvider),
            Times.Once
        );
        _authServiceMock.Verify(
            auth =>
                auth.CreateUserProfileAsync(
                    command.Email,
                    command.FirstName,
                    command.LastName,
                    null
                ),
            Times.Once
        );
        _loggerMock.Verify(
            x =>
                x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>(
                        (v, t) =>
                            v.ToString().Contains($"Registering user with email: {command.Email}")
                    ),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
            Times.Once
        );
    }
}

internal class CreateProfileResponse
{
    private string v;
    private Guid profileId;

    public CreateProfileResponse(string v, Guid profileId)
    {
        this.v = v;
        this.profileId = profileId;
    }
}
