namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Response model for admin profile retrieval
    /// </summary>
    public class GetAdminProfileResponse
    {
        /// <summary>
        /// Admin user ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Admin's first name
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Ad<PERSON>'s last name
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Admin's full name (computed from first and last name)
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Admin's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Ad<PERSON>'s phone number (normalized E.164 format)
        /// </summary>
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// Ad<PERSON>'s gender
        /// </summary>
        public string Gender { get; set; } = string.Empty;

        /// <summary>
        /// Profile picture URL (if uploaded)
        /// </summary>
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// Admin's country
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// When the profile was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the profile was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// When the admin last logged in
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// Whether the admin account is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Whether the admin's email is verified
        /// </summary>
        public bool EmailVerified { get; set; }

        /// <summary>
        /// Admin roles
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();
    }
}
