using System.ComponentModel;

namespace SuperCareApp.Domain.Enums
{
    public enum TrackingSessionStatus
    {
        [Description("Running")]
        Running,

        [Description("Paused")]
        Paused,

        [Description("Stopped")]
        Stopped,
    }

    public static class TrackingSessionStatusExtensions
    {
        public static string GetDescription(this TrackingSessionStatus status)
        {
            return status switch
            {
                TrackingSessionStatus.Running => "Running",
                TrackingSessionStatus.Paused => "Paused",
                TrackingSessionStatus.Stopped => "Stopped",
                _ => "Unknown",
            };
        }

        public static readonly IReadOnlyCollection<TrackingSessionStatus> ActiveStatuses = new[]
        {
            TrackingSessionStatus.Running,
            TrackingSessionStatus.Paused,
        };

        public static readonly IReadOnlyCollection<TrackingSessionStatus> FinalStatuses = new[]
        {
            TrackingSessionStatus.Stopped,
        };

        public static bool IsActive(this TrackingSessionStatus status) =>
            ActiveStatuses.Contains(status);

        public static bool IsFinal(this TrackingSessionStatus status) =>
            FinalStatuses.Contains(status);
    }
}
