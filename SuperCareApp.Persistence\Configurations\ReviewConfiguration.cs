﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class ReviewConfiguration : IEntityTypeConfiguration<Review>
    {
        public void Configure(EntityTypeBuilder<Review> builder)
        {
            // Primary Key
            builder.HasKey(r => r.Id);
            builder.HasIndex(r => r.Id).IncludeProperties(r => new { r.Rating, r.BookingId }); // Covering index for common lookups

            // Properties
            builder
                .Property(r => r.Rating)
                .HasColumnType("decimal(3,2)") // e.g., 5.00 max, two decimal places
                .IsRequired()
                .HasDefaultValue(0.0m);

            builder
                .Property(r => r.Comment)
                .HasColumnType("text") // Use 'text' for large strings in PostgreSQL
                .HasMaxLength(1000); // Still enforce max length in code/app layer

            // Relationships
            builder
                .HasOne(r => r.Booking)
                .WithMany(b => b.Reviews)
                .HasForeignKey(r => r.Book<PERSON>Id)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasOne(r => r.Reviewer)
                .WithMany() // Assuming User.ReviewsAsReviewer or similar (optional)
                .HasForeignKey(r => r.ReviewerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasOne(r => r.Reviewee)
                .WithMany() // Assuming User.ReviewsAsReviewee
                .HasForeignKey(r => r.RevieweeId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes - Important for performance in PostgreSQL
            builder
                .HasIndex(r => r.BookingId)
                .IsUnique(false)
                .HasDatabaseName("IX_Reviews_BookingId");

            builder.HasIndex(r => r.ReviewerId).HasDatabaseName("IX_Reviews_ReviewerId");

            builder.HasIndex(r => r.RevieweeId).HasDatabaseName("IX_Reviews_RevieweeId");

            builder.HasIndex(r => r.Rating).HasDatabaseName("IX_Reviews_Rating");

            builder
                .HasIndex(r => new { r.BookingId, r.ReviewerId })
                .IsUnique()
                .HasDatabaseName("IX_Reviews_BookingId_ReviewerId"); // Enforce one review per booking per reviewer

            // Composite index for common queries: e.g., get reviews for a provider (reviewee) with rating/time
            builder
                .HasIndex(r => new
                {
                    r.RevieweeId,
                    r.Rating,
                    r.CreatedAt,
                })
                .HasDatabaseName("IX_Reviews_RevieweeId_Rating_CreatedAt");
        }
    }
}
