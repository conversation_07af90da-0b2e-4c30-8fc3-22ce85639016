.PHONY: run build clean migrate migrate-up migrate-down release release-linux release-win release-mac release-run-linux release-run-win release-run-mac help

MIGRATION_NAME ?= InitialMigration
PROJECT = super-care-app
OUT_DIR = bin/release

run:
	dotnet run --project $(PROJECT)

build:
	dotnet build 

clean:
	dotnet clean

migrate:
	dotnet ef migrations add $(MIGRATION_NAME) \
		--project SuperCareApp.Persistence \
		--startup-project $(PROJECT) \
		--context SuperCareApp.Persistence.Context.ApplicationDbContext \
		--output-dir Migrations

migrate-up:
	dotnet ef database update \
		--project SuperCareApp.Persistence \
		--startup-project $(PROJECT) \
		--context SuperCareApp.Persistence.Context.ApplicationDbContext

migrate-down:
	dotnet ef database revert \
		--project SuperCareApp.Persistence \
		--startup-project $(PROJECT) \
		--context SuperCareApp.Persistence.Context.ApplicationDbContext \
		--target-migration $(MIGRATION_NAME)

migrate-script:
	dotnet ef migrations script \
		--project SuperCareApp.Persistence \
		--startup-project $(PROJECT) \
		--context SuperCareApp.Persistence.Context.ApplicationDbContext \
		--idempotent


release: release-linux release-win release-mac

release-linux:
	dotnet publish -c Debug -r linux-x64 -o $(OUT_DIR)/linux
	@echo "✅ Linux build completed: $(OUT_DIR)/linux"

release-win:
	dotnet publish -c Debug -r win-x64 -o $(OUT_DIR)/windows

release-mac:
	dotnet publish -c Debug -r osx-x64 -o $(OUT_DIR)/mac
	@echo "✅ macOS build completed: $(OUT_DIR)/mac"

release-run-linux: release-linux
	cd bin/release/linux && dotnet SuperCareApp.dll

release-run-win: release-win
	cd bin/release/windows && dotnet SuperCareApp.dll

release-run-mac: release-mac
	cd bin/release/mac && dotnet SuperCareApp.dll

help:
	@echo "Usage: make [target]"
	@echo ""
	@echo "Core Commands:"
	@echo "  run               Run the application (development mode)"
	@echo "  build             Build the application in Release mode"
	@echo "  clean             Clean the build artifacts"
	@echo ""
	@echo "Migrations:"
	@echo "  migrate           Add a new EF Core migration (default name: MIGRATION_NAME=InitialMigration)"
	@echo "  migrate-up        Apply all pending EF Core migrations to the database"
	@echo "  migrate-down      Revert the last EF Core migration (or target with MIGRATION_NAME=...)"
	@echo ""
	@echo "Releases (Framework-dependent):"
	@echo "  release           Build for all platforms (Linux, Windows, macOS)"
	@echo "  release-linux     Build for Linux (linux-x64)"
	@echo "  release-win       Build for Windows (win-x64)"
	@echo "  release-mac       Build for macOS (osx-x64)"
	@echo ""
	@echo "Release and Run:"
	@echo "  release-run-linux Run the Linux build after publishing"
	@echo "  release-run-win   Run the Windows build after publishing"
	@echo "  release-run-mac   Run the macOS build after publishing"