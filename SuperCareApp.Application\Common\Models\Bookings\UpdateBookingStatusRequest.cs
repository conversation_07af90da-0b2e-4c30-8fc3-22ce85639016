﻿using FluentValidation;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Bookings
{
    public class UpdateBookingStatusRequest
    {
        /// <summary>
        /// The new status for the booking.
        /// The API will automatically bind a string like "InProgress" to the correct enum member.
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Notes or a reason for the status change. Required for certain statuses.
        /// </summary>
        public string? Notes { get; set; }
    }

    public class UpdateBookingStatusRequestValidator : AbstractValidator<UpdateBookingStatusRequest>
    {
        public UpdateBookingStatusRequestValidator()
        {
            RuleFor(x => x.Notes)
                .MaximumLength(500)
                .WithMessage("Notes, if provided, cannot exceed 500 characters.");

            When(
                x => BookingStatusEnumExtensions.RequiresNotes.Contains(x.Status),
                () =>
                {
                    RuleFor(x => x.Notes)
                        .NotEmpty()
                        // The error message uses our new extension method for a user-friendly display.
                        .WithMessage(req =>
                            $"A note is required when updating the status to '{req.Status}'."
                        );
                }
            );
        }
    }
}
