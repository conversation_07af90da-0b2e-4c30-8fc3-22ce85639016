using System.Text.Json;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using SuperCareApp.Application.Shared.Utility;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Swagger operation filter to add examples to API responses
    /// </summary>
    public class SwaggerExampleFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Add examples to responses
            foreach (var response in operation.Responses)
            {
                // Skip if no content is defined
                if (response.Value.Content == null || !response.Value.Content.Any())
                    continue;

                foreach (var mediaType in response.Value.Content)
                {
                    // Add endpoint-specific examples
                    if (context.MethodInfo != null)
                    {
                        var controllerName = context.MethodInfo.DeclaringType?.Name.Replace(
                            "Controller",
                            ""
                        );
                        var actionName = context.MethodInfo.Name;

                        // Auth controller examples
                        if (controllerName == "Auth" && response.Key == "200")
                        {
                            if (actionName == "Login")
                            {
                                mediaType.Value.Examples.Add(
                                    "application/json",
                                    SwaggerResponseExamples.GetLoginResponseExample()
                                );
                                continue;
                            }
                            else if (actionName == "Register")
                            {
                                mediaType.Value.Examples.Add(
                                    "application/json",
                                    SwaggerResponseExamples.GetRegisterResponseExample()
                                );
                                continue;
                            }
                            else if (actionName == "RefreshToken")
                            {
                                mediaType.Value.Examples.Add(
                                    "application/json",
                                    SwaggerResponseExamples.GetRefreshTokenResponseExample()
                                );
                                continue;
                            }
                        }

                        // User controller examples
                        if (controllerName == "User" && response.Key == "200")
                        {
                            if (actionName == "GetUser" || actionName == "GetUserById")
                            {
                                mediaType.Value.Examples.Add(
                                    "application/json",
                                    SwaggerResponseExamples.GetUserResponseExample()
                                );
                                continue;
                            }
                            else if (actionName == "GetUsers" || actionName == "GetAllUsers")
                            {
                                mediaType.Value.Examples.Add(
                                    "application/json",
                                    SwaggerResponseExamples.GetUsersResponseExample()
                                );
                                continue;
                            }
                        }
                    }

                    // Add generic examples based on status code
                    switch (response.Key)
                    {
                        case "200":
                            AddSuccessExample(mediaType.Value);
                            break;
                        case "400":
                            mediaType.Value.Examples.Add(
                                "application/json",
                                SwaggerResponseExamples.GetValidationErrorExample()
                            );
                            break;
                        case "401":
                            mediaType.Value.Examples.Add(
                                "application/json",
                                SwaggerResponseExamples.GetUnauthorizedErrorExample()
                            );
                            break;
                        case "403":
                            mediaType.Value.Examples.Add(
                                "application/json",
                                SwaggerResponseExamples.GetForbiddenErrorExample()
                            );
                            break;
                        case "404":
                            mediaType.Value.Examples.Add(
                                "application/json",
                                SwaggerResponseExamples.GetNotFoundErrorExample()
                            );
                            break;
                        case "500":
                            mediaType.Value.Examples.Add(
                                "application/json",
                                SwaggerResponseExamples.GetInternalServerErrorExample()
                            );
                            break;
                    }
                }
            }
        }

        private void AddSuccessExample(OpenApiMediaType mediaType)
        {
            var example = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Success,
                "The operation completed successfully.",
                new
                {
                    id = Guid.NewGuid(),
                    name = "Example Data",
                    createdAt = DateTime.UtcNow,
                }
            );

            var openApiExample = new OpenApiExample
            {
                Summary = "Successful response",
                Description = "Example of a successful response",
                Value = CreateOpenApiExample(example),
            };

            mediaType.Examples.Add("application/json", openApiExample);
        }

        private IOpenApiAny CreateOpenApiExample<T>(T example)
        {
            var json = JsonSerializer.Serialize(
                example,
                new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true,
                }
            );

            return new OpenApiString(json);
        }
    }
}
