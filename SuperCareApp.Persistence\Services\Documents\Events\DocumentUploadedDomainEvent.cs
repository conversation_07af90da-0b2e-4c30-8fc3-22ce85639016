﻿using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Documents.Events;

public class DocumentUploadedDomainEvent : IDomainEvent
{
    public Guid DocumentId { get; }
    public Guid UserId { get; }
    public string FileName { get; }
    public DateTime UploadedAt { get; }

    public DocumentUploadedDomainEvent(
        Guid documentId,
        Guid userId,
        string fileName,
        DateTime uploadedAt
    )
    {
        DocumentId = documentId;
        UserId = userId;
        FileName = fileName;
        UploadedAt = uploadedAt;
    }
}

public sealed class DocumentUploadedDomainEventHandler
    : IDomainEventHandler<DocumentUploadedDomainEvent>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IApprovalService _approvalService;
    private readonly ILogger<DocumentUploadedDomainEventHandler> _logger;

    public DocumentUploadedDomainEventHandler(
        ApplicationDbContext dbContext,
        IApprovalService approvalService,
        ILogger<DocumentUploadedDomainEventHandler> logger
    )
    {
        _logger = logger;
        _approvalService = approvalService;
        _dbContext = dbContext;
    }

    public async Task Handle(
        DocumentUploadedDomainEvent notification,
        CancellationToken cancellationToken
    )
    {
        _logger.LogInformation(
            "Handling DocumentUploadedDomainEvent for DocumentId: {DocumentId}, UserId: {UserId}, FileName: {FileName}, UploadedAt: {UploadedAt}",
            notification.DocumentId,
            notification.UserId,
            notification.FileName,
            notification.UploadedAt
        );

        // Here you can add logic to handle the event, such as updating the database or notifying other services.
        // For example, you might want to log the event or update a document status in the database.
        var approvalResult = await _approvalService.CreateApprovalAsync(
            notification.UserId,
            ApprovalType.DocumentVerification,
            null,
            notification.DocumentId
        );

        if (approvalResult.IsSuccess)
        {
            _logger.LogInformation(
                "Approval created successfully for DocumentId: {DocumentId}, UserId: {UserId}",
                notification.DocumentId,
                notification.UserId
            );
        }
        else
        {
            _logger.LogError(
                "Failed to create approval for DocumentId: {DocumentId}, UserId: {UserId}. Error: {Error}",
                notification.DocumentId,
                notification.UserId,
                approvalResult.Error.Message
            );
        }
    }
}
