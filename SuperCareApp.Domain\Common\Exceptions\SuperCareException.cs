﻿namespace SuperCareApp.Domain.Common.Exceptions
{
    /// <summary>
    /// Base exception for all application-specific exceptions
    /// </summary>
    public class SuperCareException : Exception
    {
        public string Code { get; }

        public SuperCareException(string message)
            : base(message)
        {
            Code = GetType().Name;
        }

        public SuperCareException(string message, Exception innerException)
            : base(message, innerException)
        {
            Code = GetType().Name;
        }

        public SuperCareException(string code, string message)
            : base(message)
        {
            Code = code;
        }

        public SuperCareException(string code, string message, Exception innerException)
            : base(message, innerException)
        {
            Code = code;
        }
    }
}
