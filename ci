pipeline{
    agent any
    stages{
        stage('Git pull & Verify'){
            steps{
                git branch: 'develop', credentialsId: 'ci', url: 'https://gitlab.pitangent.net/intellexio/CareAppBackend.git'
                sh 'ls -alF'
            }
        }
        stage('Copy Project'){
            steps{
                sh 'cp -rf . /var/www/careappbackend/'
            }
        }
        stage('Build careapp backend'){
            steps{
                dir('/var/www/careappbackend/')
                {
                sh 'ls -alF'
                sh 'docker compose up -d --build'
                }
            }
        }
   }
}
