namespace SuperCareApp.Application.Common.Interfaces.Mediator;

/// <summary>
/// Marker interface for synchronous notifications.
/// </summary>
public interface INotification { }

/// <summary>
/// Handler for synchronous notifications.
/// </summary>
/// <typeparam name="TNotification">The type of notification being handled.</typeparam>
public interface INotificationHandler<in TNotification>
    where TNotification : INotification
{
    /// <summary>
    /// Handles a notification.
    /// </summary>
    /// <param name="notification">The notification.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>Task representing the asynchronous operation.</returns>
    Task Handle(TNotification notification, CancellationToken cancellationToken);
}
