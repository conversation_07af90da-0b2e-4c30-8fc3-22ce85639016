﻿using System.Text.Json;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Shared.Utility;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Example response models for Booking API documentation
    /// </summary>
    public static class BookingExamples
    {
        // Reusable JsonSerializerOptions
        private static readonly JsonSerializerOptions _jsonOptions = new() { WriteIndented = true };

        public static string GetAllBookingsExample()
        {
            return @"{
  ""apiResponseId"": ""fe76d6b3-9233-425f-9236-c5ea6fbadd96"",
  ""success"": true,
  ""statusCode"": 200,
  ""message"": ""Bookings retrieved successfully"",
  ""payload"": [
    {
      ""bookingId"": ""a4b5c6d7-e8f9-0011-2233-993455667788"",
      ""startDate"": ""2025-07-30T00:00:00Z"",
      ""endDate"": ""2025-07-30T00:00:00Z"",
      ""startTime"": ""14:00"",
      ""endTime"": ""15:00"",
      ""duration"": ""1h 0m"",
      ""description"": null,
      ""status"": ""Cancelled"",
      ""serviceType"": ""Disability Care"",
      ""totalAmount"": 32.45,
      ""clients"": {
        ""name"": ""James Wilson"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""location"": {
          ""streetAddress"": ""654 Cedar Ln"",
          ""city"": ""Phoenix"",
          ""state"": ""AZ"",
          ""country"": ""United States"",
          ""postalCode"": ""85001"",
          ""longitude"": -112.074,
          ""latitude"": 33.4484
        }
      },
      ""careProviders"": {
        ""Providerid"": ""a9b0c1d2-e3f4-5566-7788-223344556677"",
        ""name"": ""Maria Garcia"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""yearsOfExperience"": 6,
        ""rating"": 0
      }
    },
    {
      ""bookingId"": ""b9c0d1e2-f3a4-5566-7788-443455667788"",
      ""startDate"": ""2025-07-02T00:00:00Z"",
      ""endDate"": ""2025-07-02T00:00:00Z"",
      ""startTime"": ""13:00"",
      ""endTime"": ""16:00"",
      ""duration"": ""3h 0m"",
      ""description"": null,
      ""status"": ""Cancelled"",
      ""serviceType"": ""Child Care"",
      ""totalAmount"": 92.4,
      ""clients"": {
        ""name"": ""James Wilson"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""location"": {
          ""streetAddress"": ""654 Cedar Ln"",
          ""city"": ""Phoenix"",
          ""state"": ""AZ"",
          ""country"": ""United States"",
          ""postalCode"": ""85001"",
          ""longitude"": -112.074,
          ""latitude"": 33.4484
        }
      },
      ""careProviders"": {
        ""Providerid"": ""e9f0a1b2-c3d4-5566-7788-223344556677"",
        ""name"": ""Daniel Lewis"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""yearsOfExperience"": 7,
        ""rating"": 0
      }
    },
    {
      ""bookingId"": ""d5e6f7a8-b9c0-1122-3344-003455667788"",
      ""startDate"": ""2025-07-17T00:00:00Z"",
      ""endDate"": ""2025-07-17T00:00:00Z"",
      ""startTime"": ""16:00"",
      ""endTime"": ""18:00"",
      ""duration"": ""2h 0m"",
      ""description"": null,
      ""status"": ""Rejected"",
      ""serviceType"": ""Child Care"",
      ""totalAmount"": 83.6,
      ""clients"": {
        ""name"": ""James Wilson"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""location"": {
          ""streetAddress"": ""654 Cedar Ln"",
          ""city"": ""Phoenix"",
          ""state"": ""AZ"",
          ""country"": ""United States"",
          ""postalCode"": ""85001"",
          ""longitude"": -112.074,
          ""latitude"": 33.4484
        }
      },
      ""careProviders"": {
        ""Providerid"": ""e7f8a9b0-c1d2-3344-5566-001122334455"",
        ""name"": ""James Martinez"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""yearsOfExperience"": 12,
        ""rating"": 0
      }
    },
    {
      ""bookingId"": ""f3a4b5c6-d7e8-9900-1122-883455667788"",
      ""startDate"": ""2025-07-19T00:00:00Z"",
      ""endDate"": ""2025-07-19T00:00:00Z"",
      ""startTime"": ""09:00"",
      ""endTime"": ""11:00"",
      ""duration"": ""2h 0m"",
      ""description"": null,
      ""status"": ""Cancelled"",
      ""serviceType"": ""Child Care"",
      ""totalAmount"": 80.3,
      ""clients"": {
        ""name"": ""James Wilson"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""location"": {
          ""streetAddress"": ""654 Cedar Ln"",
          ""city"": ""Phoenix"",
          ""state"": ""AZ"",
          ""country"": ""United States"",
          ""postalCode"": ""85001"",
          ""longitude"": -112.074,
          ""latitude"": 33.4484
        }
      },
      ""careProviders"": {
        ""Providerid"": ""a5b6c7d8-e9f0-1122-3344-889900112233"",
        ""name"": ""Christopher Harris"",
        ""phoneNumber"": ""+**********"",
        ""email"": ""<EMAIL>"",
        ""yearsOfExperience"": 11,
        ""rating"": 0
      }
    }
  ],
  ""meta"": {
    ""currentPage"": 1,
    ""totalPages"": 1,
    ""totalCount"": 4,
    ""pageSize"": 10,
    ""hasPreviousPage"": false,
    ""hasNextPage"": false
  },
  ""timestamp"": ""2025-07-02T10:56:32.0739232Z""
}";
        }

        public static string GetBookingByIdExample()
        {
            return @"{
  ""apiResponseId"": ""0c23b835-74dd-4613-90fc-80d973885011"",
  ""success"": true,
  ""statusCode"": 200,
  ""message"": ""Operation completed successfully"",
  ""payload"": {
    ""bookingId"": ""a4b5c6d7-e8f9-0011-2233-993455667788"",
    ""startDate"": ""2025-07-30T00:00:00Z"",
    ""endDate"": ""2025-07-30T00:00:00Z"",
    ""startTime"": ""14:00"",
    ""endTime"": ""15:00"",
    ""duration"": ""1h 0m"",
    ""description"": null,
    ""status"": ""Cancelled"",
    ""serviceType"": ""Disability Care"",
    ""totalAmount"": 32.45,
    ""clients"": {
      ""name"": ""James Wilson"",
      ""phoneNumber"": ""+**********"",
      ""email"": ""<EMAIL>"",
      ""location"": {
        ""streetAddress"": ""654 Cedar Ln"",
        ""city"": ""Phoenix"",
        ""state"": ""AZ"",
        ""country"": ""United States"",
        ""postalCode"": ""85001"",
        ""longitude"": -112.074,
        ""latitude"": 33.4484
      }
    },
    ""careProviders"": {
      ""Providerid"": ""a9b0c1d2-e3f4-5566-7788-223344556677"",
      ""name"": ""Maria Garcia"",
      ""phoneNumber"": ""+**********"",
      ""email"": ""<EMAIL>"",
      ""yearsOfExperience"": 6,
      ""rating"": 0
    }
  },
  ""timestamp"": ""2025-07-02T12:07:38.2092979Z""
}";
        }

        public static string GetProviderAvailabilityExample()
        {
            return @"{
  ""apiResponseId"": ""f9b84d6b-13ae-4703-8948-551e772db217"",
  ""success"": true,
  ""statusCode"": 200,
  ""message"": ""Provider availabilities retrieved successfully"",
  ""payload"": [
    {
      ""id"": ""8f1a6ab9-61e8-465e-b06b-98b1e66abd65"",
      ""day"": ""Friday"",
      ""available"": true,
      ""slots"": [
        {
          ""startTime"": ""16:00"",
          ""endTime"": ""18:00""
        },
        {
          ""startTime"": ""13:00"",
          ""endTime"": ""16:00""
        }
      ]
    },
    {
      ""id"": ""67d1b3fe-34be-4066-a9ad-11a309f6157c"",
      ""day"": ""Saturday"",
      ""available"": true,
      ""slots"": [
        {
          ""startTime"": ""16:00"",
          ""endTime"": ""19:00""
        },
        {
          ""startTime"": ""12:00"",
          ""endTime"": ""14:00""
        },
        {
          ""startTime"": ""19:00"",
          ""endTime"": ""22:00""
        }
      ]
    },
    {
      ""id"": ""f9c85f8c-bf13-4604-8d76-b79f183447a6"",
      ""day"": ""Sunday"",
      ""available"": true,
      ""slots"": [
        {
          ""startTime"": ""19:00"",
          ""endTime"": ""21:00""
        },
        {
          ""startTime"": ""13:00"",
          ""endTime"": ""14:00""
        },
        {
          ""startTime"": ""11:00"",
          ""endTime"": ""13:00""
        }
      ]
    },
    {
      ""id"": ""a8d56861-9edc-4760-b7e8-ac852440341f"",
      ""day"": ""Thursday"",
      ""available"": true,
      ""slots"": [
        {
          ""startTime"": ""11:00"",
          ""endTime"": ""12:00""
        },
        {
          ""startTime"": ""17:00"",
          ""endTime"": ""20:00""
        },
        {
          ""startTime"": ""09:00"",
          ""endTime"": ""12:00""
        }
      ]
    },
    {
      ""id"": ""b9c2c2b3-08b5-4a13-9323-d3fbdd21a61b"",
      ""day"": ""Tuesday"",
      ""available"": true,
      ""slots"": [
        {
          ""startTime"": ""16:00"",
          ""endTime"": ""17:00""
        }
      ]
    }
  ],
  ""timestamp"": ""2025-05-27T09:41:03.7032247Z""
}";
        }

        /// <summary>
        /// Gets an example of a successful get available time slots response
        /// </summary>
        public static string GetAvailableTimeSlotsExample()
        {
            var timeSlots = new List<TimeSlotResponse>
            {
                new TimeSlotResponse { StartTime = "09:00", EndTime = "10:00" },
                new TimeSlotResponse { StartTime = "10:30", EndTime = "11:30" },
                new TimeSlotResponse { StartTime = "13:00", EndTime = "14:00" },
                new TimeSlotResponse { StartTime = "15:30", EndTime = "16:30" },
            };

            var response = new ApiResponseModel<IEnumerable<TimeSlotResponse>>(
                ApiResponseStatusEnum.Success,
                "Available time slots retrieved successfully",
                timeSlots
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful enhanced availability response
        /// </summary>
        public static string GetEnhancedAvailabilityExample()
        {
            var enhancedAvailability = new EnhancedAvailabilityResponse
            {
                AvailableSlots = new Dictionary<string, List<TimeSlotResponse>>
                {
                    ["Monday"] = new List<TimeSlotResponse>(),
                    ["Tuesday"] = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "16:00", EndTime = "17:00" },
                    },
                    ["Wednesday"] = new List<TimeSlotResponse>(),
                    ["Thursday"] = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "09:00", EndTime = "12:00" },
                        new TimeSlotResponse { StartTime = "11:00", EndTime = "12:00" },
                        new TimeSlotResponse { StartTime = "17:00", EndTime = "20:00" },
                    },
                    ["Friday"] = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "13:00", EndTime = "16:00" },
                        new TimeSlotResponse { StartTime = "16:00", EndTime = "18:00" },
                    },
                    ["Saturday"] = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "12:00", EndTime = "14:00" },
                        new TimeSlotResponse { StartTime = "16:00", EndTime = "19:00" },
                        new TimeSlotResponse { StartTime = "19:00", EndTime = "22:00" },
                    },
                    ["Sunday"] = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "11:00", EndTime = "13:00" },
                        new TimeSlotResponse { StartTime = "13:00", EndTime = "14:00" },
                        new TimeSlotResponse { StartTime = "19:00", EndTime = "21:00" },
                    },
                },
                AvailableDaysByMonth = new List<MonthlyAvailabilityResponse>
                {
                    new MonthlyAvailabilityResponse
                    {
                        Month = "August",
                        Year = 2025,
                        Days = new List<AvailableDayResponse>
                        {
                            new AvailableDayResponse { Date = "2025-08-01", DayOfWeek = "Friday" },
                            new AvailableDayResponse
                            {
                                Date = "2025-08-02",
                                DayOfWeek = "Saturday",
                            },
                        },
                    },
                    new MonthlyAvailabilityResponse
                    {
                        Month = "September",
                        Year = 2025,
                        Days = new List<AvailableDayResponse>
                        {
                            new AvailableDayResponse { Date = "2025-09-01", DayOfWeek = "Monday" },
                        },
                    },
                    new MonthlyAvailabilityResponse
                    {
                        Month = "October",
                        Year = 2025,
                        Days = new List<AvailableDayResponse>
                        {
                            new AvailableDayResponse
                            {
                                Date = "2025-10-01",
                                DayOfWeek = "Wednesday",
                            },
                        },
                    },
                },
            };

            var response = new ApiResponseModel<EnhancedAvailabilityResponse>(
                ApiResponseStatusEnum.Success,
                "Provider enhanced availability retrieved successfully",
                enhancedAvailability
            );

            return JsonSerializer.Serialize(
                response,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example of a successful get all availabilities response
        /// </summary>
        public static string GetAllAvailabilitiesExample()
        {
            var availabilities = new List<AvailabilityResponse>
            {
                new AvailabilityResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    Day = "Monday",
                    Available = true,
                    Slots = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "09:00", EndTime = "12:00" },
                        new TimeSlotResponse { StartTime = "13:00", EndTime = "17:00" },
                    },
                },
                new AvailabilityResponse
                {
                    Id = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    Day = "Tuesday",
                    Available = true,
                    Slots = new List<TimeSlotResponse>
                    {
                        new TimeSlotResponse { StartTime = "10:00", EndTime = "14:00" },
                        new TimeSlotResponse { StartTime = "15:00", EndTime = "18:00" },
                    },
                },
                new AvailabilityResponse
                {
                    Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Day = "Wednesday",
                    Available = false,
                    Slots = new List<TimeSlotResponse>(),
                },
            };

            var pagination = new PaginationMetadata(
                currentPage: 1,
                totalPages: 1,
                totalCount: 3,
                pageSize: 10
            );

            var response = new PaginatedResponseModel<IEnumerable<AvailabilityResponse>>(
                ApiResponseStatusEnum.Success,
                "Availabilities retrieved successfully",
                availabilities,
                pagination
            );

            return JsonSerializer.Serialize(
                response,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example of a successful is date available response
        /// </summary>
        public static string IsDateAvailableExample()
        {
            var response = new ApiResponseModel<bool>(
                ApiResponseStatusEnum.Success,
                "Provider is available on the specified date",
                true
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful create leave response
        /// </summary>
        public static string CreateLeaveExample()
        {
            var response = new ApiResponseModel<Guid>(
                ApiResponseStatusEnum.Success,
                "Leave created successfully",
                Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479")
            );

            return JsonSerializer.Serialize(
                response,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example of a successful get leave response
        /// </summary>
        public static string GetLeaveExample()
        {
            var leaveResponse = new LeaveResponse
            {
                Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                ProviderId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                StartDate = DateTime.UtcNow.AddDays(5),
                EndDate = DateTime.UtcNow.AddDays(10),
                Reason = "Annual vacation",
                CreatedAt = DateTime.UtcNow.AddDays(-2),
            };

            var response = new ApiResponseModel<LeaveResponse>(
                ApiResponseStatusEnum.Success,
                "Leave retrieved successfully",
                leaveResponse
            );

            return JsonSerializer.Serialize(
                response,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example of a successful get all leaves response
        /// </summary>
        public static string GetAllLeavesExample()
        {
            var leaves = new List<LeaveResponse>
            {
                new LeaveResponse
                {
                    Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    ProviderId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    StartDate = DateTime.UtcNow.AddDays(5),
                    EndDate = DateTime.UtcNow.AddDays(10),
                    Reason = "Annual vacation",
                    CreatedAt = DateTime.UtcNow.AddDays(-2),
                },
                new LeaveResponse
                {
                    Id = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    ProviderId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    StartDate = DateTime.UtcNow.AddDays(20),
                    EndDate = DateTime.UtcNow.AddDays(22),
                    Reason = "Personal leave",
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                },
            };

            var pagination = new PaginationMetadata(
                currentPage: 1,
                totalPages: 1,
                totalCount: 2,
                pageSize: 10
            );

            var response = new PaginatedResponseModel<IEnumerable<LeaveResponse>>(
                ApiResponseStatusEnum.Success,
                "Leaves retrieved successfully",
                leaves,
                pagination
            );

            return JsonSerializer.Serialize(
                response,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example request for creating an availability
        /// </summary>
        public static string CreateAvailabilityRequestExample()
        {
            var request = new CreateAvailabilityRequest
            {
                DayOfWeek = "Monday",
                IsAvailable = true,
                Slots = new List<CreateAvailabilitySlotRequest>
                {
                    new CreateAvailabilitySlotRequest
                    {
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(12, 0),
                    },
                    new CreateAvailabilitySlotRequest
                    {
                        StartTime = new TimeOnly(13, 0),
                        EndTime = new TimeOnly(17, 0),
                    },
                },
            };

            return JsonSerializer.Serialize(
                request,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example request for bulk updating availabilities
        /// </summary>
        public static string BulkUpdateAvailabilityRequestExample()
        {
            var request = new BulkUpdateAvailabilityRequest
            {
                Availabilities = new List<AvailabilityUpdateItem>
                {
                    new AvailabilityUpdateItem
                    {
                        DayOfWeek = "Monday",
                        IsAvailable = true,
                        Slots = new List<CreateAvailabilitySlotRequest>
                        {
                            new CreateAvailabilitySlotRequest
                            {
                                StartTime = new TimeOnly(9, 0),
                                EndTime = new TimeOnly(12, 0),
                            },
                            new CreateAvailabilitySlotRequest
                            {
                                StartTime = new TimeOnly(13, 0),
                                EndTime = new TimeOnly(17, 0),
                            },
                        },
                    },
                    new AvailabilityUpdateItem
                    {
                        DayOfWeek = "Tuesday",
                        IsAvailable = true,
                        Slots = new List<CreateAvailabilitySlotRequest>
                        {
                            new CreateAvailabilitySlotRequest
                            {
                                StartTime = new TimeOnly(10, 0),
                                EndTime = new TimeOnly(14, 0),
                            },
                            new CreateAvailabilitySlotRequest
                            {
                                StartTime = new TimeOnly(15, 0),
                                EndTime = new TimeOnly(18, 0),
                            },
                        },
                    },
                    new AvailabilityUpdateItem
                    {
                        DayOfWeek = "Wednesday",
                        IsAvailable = false,
                        Slots = new List<CreateAvailabilitySlotRequest>(),
                    },
                },
            };

            return JsonSerializer.Serialize(
                request,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        /// <summary>
        /// Gets an example request for creating a leave period
        /// </summary>
        public static string CreateLeaveRequestExample()
        {
            var request = new LeaveRequest
            {
                StartDate = DateTime.UtcNow.AddDays(5),
                EndDate = DateTime.UtcNow.AddDays(10),
                Reason = "Annual vacation",
            };

            return JsonSerializer.Serialize(
                request,
                new JsonSerializerOptions { WriteIndented = true }
            );
        }

        public static string GetProviderLeavesExample()
        {
            return @"{
              ""apiResponseId"": ""f9b84d6b-13ae-4703-8948-551e772db217"",
              ""success"": true,
              ""statusCode"": 200,
              ""message"": ""Provider leaves retrieved successfully"",
              ""payload"": [
                {
                    ""Id"": ""f1a2b3c4-d5e6-7890-abcd-1234567890ef"",
                    ""ProviderId"": ""a1b2c3d4-e5f6-7890-abcd-ef1234567890"",
                    ""StartDate"": ""2025-06-01T00:00:00Z"",
                    ""EndDate"": ""2025-06-05T23:59:59Z"",
                    ""Reason"": ""Vacation"",
                    ""CreatedAt"": ""2025-05-20T08:00:00Z""
                },
                {
                    ""Id"": ""c2d3e4f5-a6b7-8901-bcde-2345678901fe"",
                    ""ProviderId"": ""a1b2c3d4-e5f6-7890-abcd-ef1234567890"",
                    ""StartDate"": ""2025-07-10T00:00:00Z"",
                    ""EndDate"": ""2025-07-12T23:59:59Z"",
                    ""Reason"": ""Personal Leave"",
                    ""CreatedAt"": ""2025-05-25T14:30:00Z""
                }
              ],
              ""timestamp"": ""2025-05-27T09:41:03.7032247Z""
            }";
        }

        public static string GetMyLeavesExample()
        {
            return @"{
              ""apiResponseId"": ""f9b84d6b-13ae-4703-8948-551e772db217"",
              ""success"": true,
              ""statusCode"": 200,
              ""message"": ""Leaves retrieved successfully"",
              ""payload"": [
                {
                    ""Id"": ""b2c3d4e5-f6a7-8901-bcde-3456789012ab"",
                    ""ProviderId"": ""c3d4e5f6-a7b8-9012-cdef-4567890123bc"",
                    ""StartDate"": ""2025-06-10T00:00:00Z"",
                    ""EndDate"": ""2025-06-15T23:59:59Z"",
                    ""Reason"": ""Medical Leave"",
                    ""CreatedAt"": ""2025-05-27T10:49:00Z""
                },
                {
                    ""Id"": ""d4e5f6a7-b8c9-0123-def4-5678901234cd"",
                    ""ProviderId"": ""c3d4e5f6-a7b8-9012-cdef-4567890123bc"",
                    ""StartDate"": ""2025-08-01T00:00:00Z"",
                    ""EndDate"": ""2025-08-03T23:59:59Z"",
                    ""Reason"": ""Personal Leave"",
                    ""CreatedAt"": ""2025-05-27T14:19:00Z""
                }
              ],
              ""timestamp"": ""2025-05-27T09:41:03.7032247Z""
            }";
        }
    }
}
