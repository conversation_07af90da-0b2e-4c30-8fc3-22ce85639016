﻿using SuperCareApp.Application.Common.Interfaces.Mail;

namespace super_care_app.Extensions;

public static class OtpExtension
{
    public static void UserDevelopmentOtp(this IApplicationBuilder app, bool isDevelopment = false)
    {
        var defautlOtp = isDevelopment ? "123456" : null;
        var mailSender = app.ApplicationServices.GetRequiredService<IMailSender>();
        mailSender
            .SendMail("<EMAIL>", "Your One-Time Password (OTP)", defautlOtp)
            .GetAwaiter()
            .GetResult();
    }
}
