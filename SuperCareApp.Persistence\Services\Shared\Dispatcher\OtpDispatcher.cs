﻿using System.Security.Cryptography;
using SuperCareApp.Application.Common.Interfaces.Repositories;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Application.Shared.Dispatcher;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Services.Shared.Utility;

namespace SuperCareApp.Persistence.Services.Shared.Dispatcher;

/// <summary>
/// Dispatcher responsible for generating and sending One-Time Passwords (OTPs)
/// </summary>
internal sealed class OtpDispatcher : IOtpDispatcher
{
    private readonly ITwilioManager _twilioManager;
    private readonly IMailSender _mailSender;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<OtpDispatcher> _logger;
    private readonly IOtpCodeRepository _otpRepository;
    private readonly OtpSettings _otpSettings;
    private const int OTP_LENGTH = 6;
    private const int OTP_EXPIRY_MINUTES = 10;

    public OtpDispatcher(
        ITwilioManager twilioManager,
        IMailSender mailSender,
        UserManager<ApplicationUser> userManager,
        ILogger<OtpDispatcher> logger,
        IOtpCodeRepository otpRepository,
        IOptions<OtpSettings> otpSettings
    )
    {
        _twilioManager = twilioManager ?? throw new ArgumentNullException(nameof(twilioManager));
        _mailSender = mailSender ?? throw new ArgumentNullException(nameof(mailSender));
        _userManager = userManager;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _otpRepository = otpRepository ?? throw new ArgumentNullException(nameof(otpRepository));
        _otpSettings = otpSettings?.Value ?? throw new ArgumentNullException(nameof(otpSettings));
    }

    /// <summary>
    /// Generates and dispatches an OTP to the provided contact method(s) (email, phone, or both).
    /// At least one contact method should be provided.
    /// </summary>
    /// <param name="email">Optional email address to send OTP to</param>
    /// <param name="phoneNumber">Optional phone number to send OTP to</param>
    /// <param name="isDevMode">Whether the application is running in development mode</param>
    /// <returns>Result indicating success or failure, containing the OTP if in dev mode</returns>
    public async Task<Result> DispatchAsync(
        string? email,
        string? phoneNumber,
        bool isDevMode = false
    )
    {
        try
        {
            // Generate a secure OTP
            string otp = GenerateOtp();

            // In dev mode, return the OTP without sending (for testing purposes)
            if (isDevMode)
            {
                string contactMethod = "";
                if (!string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(phoneNumber))
                {
                    contactMethod = $"Email: {email}, Phone: {phoneNumber}";
                }
                else if (!string.IsNullOrEmpty(email))
                {
                    contactMethod = $"Email: {email}";
                }
                else if (!string.IsNullOrEmpty(phoneNumber))
                {
                    contactMethod = $"Phone: {phoneNumber}";
                }
                else
                {
                    contactMethod = "unknown";
                }

                _logger.LogInformation(
                    "Development mode: OTP {OTP} would be sent to {ContactMethod}",
                    otp,
                    contactMethod
                );
                return Result.Success(otp); // Return the OTP in the result for dev mode
            }

            // Find the user based on the provided contact information
            ApplicationUser? user = null;

            // If both email and phone are provided, try to find the user by email first
            if (!string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(phoneNumber))
            {
                user = await _userManager.Users.FirstOrDefaultAsync(u =>
                    u.Email == email && u.PhoneNumber == phoneNumber
                );

                // If user not found with both, try email
                if (user == null)
                {
                    user = await _userManager.Users.FirstOrDefaultAsync(u => u.Email == email);
                }

                // If still not found, try phone
                if (user == null)
                {
                    user = await _userManager.Users.FirstOrDefaultAsync(u =>
                        u.PhoneNumber == phoneNumber
                    );
                }
            }
            // Try to find by email only
            else if (!string.IsNullOrEmpty(email))
            {
                user = await _userManager.Users.FirstOrDefaultAsync(u => u.Email == email);
            }
            // Try to find by phone only
            else if (!string.IsNullOrEmpty(phoneNumber))
            {
                user = await _userManager.Users.FirstOrDefaultAsync(u =>
                    u.PhoneNumber == phoneNumber
                );
            }

            // If user not found, return error
            if (user == null)
            {
                return Result.Failure(Error.BadRequest("User not found"));
            }

            bool smsSuccess = true;
            bool emailSuccess = true;

            // Send via SMS if phone number is provided
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                smsSuccess = await SendOtpViaSmsAsync(phoneNumber, otp);
                if (!smsSuccess)
                {
                    _logger.LogWarning("Failed to send OTP via SMS to {PhoneNumber}", phoneNumber);
                }
            }

            // Send via email if email is provided
            if (!string.IsNullOrEmpty(email))
            {
                emailSuccess = await SendOtpViaEmailAsync(email, otp);
                if (!emailSuccess)
                {
                    _logger.LogWarning("Failed to send OTP via email to {Email}", email);
                }
            }

            // If both methods failed, return error
            if (!smsSuccess && !emailSuccess)
            {
                return Result.Failure(Error.BadRequest("Failed to send OTP via any method"));
            }

            // Store the OTP
            await StoreOtpAsync(otp, user.Id);

            // Return success with information about which methods succeeded
            if (!string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(phoneNumber))
            {
                if (smsSuccess && emailSuccess)
                {
                    return Result.Success("OTP sent via both SMS and email");
                }

                if (smsSuccess)
                {
                    return Result.Success("OTP sent via SMS only");
                }

                return Result.Success("OTP sent via email only");
            }
            else if (!string.IsNullOrEmpty(phoneNumber) && smsSuccess)
            {
                return Result.Success("OTP sent via SMS");
            }
            else if (!string.IsNullOrEmpty(email) && emailSuccess)
            {
                return Result.Success("OTP sent via email");
            }

            // No contact method provided
            _logger.LogWarning("No contact method provided for OTP dispatch");
            return Result.Failure(
                Error.BadRequest("Either email or phone number must be provided")
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dispatching OTP");
            return Result.Failure(Error.Internal("An error occurred while dispatching OTP"));
        }
    }

    /// <summary>
    /// Generates a cryptographically secure OTP of the specified length
    /// </summary>
    /// <returns>A numeric OTP string</returns>
    private string GenerateOtp()
    {
        var otpValue = new byte[4];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(otpValue);
        }

        // Convert to positive integer and take only the last OTP_LENGTH digits
        uint randomValue = BitConverter.ToUInt32(otpValue, 0);
        string otp = (randomValue % (uint)Math.Pow(10, OTP_LENGTH)).ToString($"D{OTP_LENGTH}");

        return otp;
    }

    /// <summary>
    /// Sends the OTP via SMS
    /// </summary>
    /// <param name="phoneNumber">Recipient's phone number</param>
    /// <param name="otp">The OTP to send</param>
    /// <returns>True if sending was successful, false otherwise</returns>
    private async Task<bool> SendOtpViaSmsAsync(string phoneNumber, string otp)
    {
        string message =
            $"Your verification code is: {otp}. This code will expire in {_otpSettings.OtpExpiryInMinutes} minutes. Do not share this code with anyone.";
        return await _twilioManager.SendMessageAsync(phoneNumber, message);
    }

    public async Task<Result> VerifyOtpAsync(string code, Guid userId)
    {
        try
        {
            if (
                _otpSettings.UseDefaultOtpForDevelopment
                && !string.IsNullOrEmpty(_otpSettings.DefaultOtp)
            )
            {
                if (code.Equals(_otpSettings.DefaultOtp))
                {
                    return Result.Success();
                }
                else
                {
                    return Result.Failure(Error.BadRequest("Invalid or expired OTP"));
                }
            }

            var otpCode = await _otpRepository.GetValidCodeAsync(code, userId);

            if (otpCode == null || otpCode.ExpiresAt < DateTime.UtcNow)
            {
                return Result.Failure(Error.BadRequest("Invalid or expired OTP"));
            }

            if (otpCode.IsUsed)
            {
                return Result.Failure(Error.BadRequest("OTP already used"));
            }

            otpCode.IsUsed = true;
            await _otpRepository.UpdateAsync(otpCode);
            await _otpRepository.SaveChangesAsync();

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying OTP");
            return Result.Failure(
                Error.Internal($"An error occurred while verifying OTP {ex.Message}")
            );
        }
    }

    private async Task<bool> SendOtpViaEmailAsync(string email, string otp)
    {
        string subject = "Your Verification Code";
        string body =
            $"Your SuperCareApp verification code is: {otp}. This code expires in {OTP_EXPIRY_MINUTES} minutes.";

        try
        {
            await _mailSender.SendMail(email, subject, body);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send OTP email to {Email}", email);
            return false;
        }
    }

    private async Task StoreOtpAsync(string otp, Guid userId)
    {
        var otpCode = new OtpCode
        {
            Code = otp,
            ApplicationUserId = userId,
            ExpiresAt = DateTime.UtcNow.AddMinutes(OTP_EXPIRY_MINUTES),
            IsUsed = false,
        };

        await _otpRepository.AddAsync(otpCode);
        await _otpRepository.SaveChangesAsync();
    }
}
