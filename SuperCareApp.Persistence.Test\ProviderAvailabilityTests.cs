﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings.Queries;

namespace SuperCareApp.Persistence.Test;

public class GetProviderAvailabilityForDateQueryHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _db;
    private readonly GetProviderAvailabilityForDateQueryHandler _sut;

    private readonly Guid _providerId;
    private readonly DateOnly _monday = new(2025, 07, 21); // 21 Jul 2025 is Monday

    public GetProviderAvailabilityForDateQueryHandlerTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _db = new ApplicationDbContext(options);
        _sut = new GetProviderAvailabilityForDateQueryHandler(
            _db,
            NullLogger<GetProviderAvailabilityForDateQueryHandler>.Instance
        );

        _providerId = Guid.NewGuid();

        // Provider profile
        _db.CareProviderProfiles.Add(
            new CareProviderProfile { Id = _providerId, BufferDuration = 30 }
        );

        // Availability: Monday 12:00-16:00
        _db.Availabilities.Add(
            new Domain.Entities.Availability
            {
                Id = Guid.NewGuid(),
                ProviderId = _providerId,
                DayOfWeek = "Monday",
                IsAvailable = true,
                AvailabilitySlots = new[]
                {
                    new AvailabilitySlot
                    {
                        StartTime = new TimeOnly(12, 0),
                        EndTime = new TimeOnly(16, 0),
                    },
                },
            }
        );

        _db.SaveChanges();
    }

    public void Dispose() => _db.Dispose();

    [Fact]
    public async Task Handle_WhenProviderIsAvailableAndNoBookings_ReturnsFullSlots()
    {
        var result = await _sut.Handle(
            new GetProviderAvailabilityForDateQuery(_providerId, _monday),
            CancellationToken.None
        );

        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Available);
        Assert.Single(result.Value.Slots);
        Assert.Equal("12:00", result.Value.Slots.First().StartTime);
        Assert.Equal("16:00", result.Value.Slots.Last().EndTime);
    }

    [Fact]
    public async Task Handle_WhenBookingExists_ReturnsSlotsWithBufferApplied()
    {
        // 13:00-14:00 + 30 min buffer => 12:30-14:30 blocked
        var bookingId = Guid.NewGuid();
        _db.Bookings.Add(
            new Booking
            {
                Id = bookingId,
                ProviderId = _providerId,
                ClientId = Guid.NewGuid(),
                CategoryId = Guid.NewGuid(),
                WorkingHours = 1,
                TotalAmount = 100,
                PlatformFee = 10,
                ProviderAmount = 90,
            }
        );

        _db.BookingWindows.Add(
            new BookingWindow
            {
                Id = Guid.NewGuid(),
                BookingId = bookingId,
                Date = _monday,
                StartTime = new TimeOnly(13, 0),
                EndTime = new TimeOnly(14, 0),
                DurationMinutes = 60,
                DailyRate = 100,
            }
        );
        await _db.SaveChangesAsync();

        var result = await _sut.Handle(
            new GetProviderAvailabilityForDateQuery(_providerId, _monday),
            CancellationToken.None
        );

        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Available);
        var slots = result.Value.Slots;
        Assert.Equal(2, slots.Count);

        Assert.Equal("12:00", slots[0].StartTime);
        Assert.Equal("12:30", slots[0].EndTime);

        Assert.Equal("14:30", slots[1].StartTime);
        Assert.Equal("16:00", slots[1].EndTime);
    }

    [Fact]
    public async Task Handle_WhenProviderIsOnLeave_ReturnsNotAvailable()
    {
        _db.Leaves.Add(
            new Leave
            {
                ProviderId = _providerId,
                StartDate = new DateTime(2025, 07, 21),
                EndDate = new DateTime(2025, 07, 21),
            }
        );
        await _db.SaveChangesAsync();

        var result = await _sut.Handle(
            new GetProviderAvailabilityForDateQuery(_providerId, _monday),
            CancellationToken.None
        );

        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }

    [Fact]
    public async Task Handle_WhenProviderIsNotAvailableOnWeekday_ReturnsNotAvailable()
    {
        // Change availability to false
        var av = _db.Availabilities.Single(a => a.ProviderId == _providerId);
        av.IsAvailable = false;
        await _db.SaveChangesAsync();

        var result = await _sut.Handle(
            new GetProviderAvailabilityForDateQuery(_providerId, _monday),
            CancellationToken.None
        );

        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }

    [Fact]
    public async Task Handle_WhenProviderDoesNotExist_ReturnsNotAvailable()
    {
        var nonExisting = Guid.NewGuid();
        var result = await _sut.Handle(
            new GetProviderAvailabilityForDateQuery(nonExisting, _monday),
            CancellationToken.None
        );

        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }
}
