﻿using System.Collections.Concurrent;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Domain.Common.Resilience
{
    /// <summary>
    /// Implements the Bulkhead pattern to isolate failures and limit concurrent operations
    /// </summary>
    public class Bulkhead
    {
        private readonly SemaphoreSlim _semaphore;
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _partitionedSemaphores;
        private readonly int _maxConcurrentOperations;

        public Bulkhead(int maxConcurrentOperations = 10)
        {
            _maxConcurrentOperations = maxConcurrentOperations;
            _semaphore = new SemaphoreSlim(maxConcurrentOperations, maxConcurrentOperations);
            _partitionedSemaphores = new ConcurrentDictionary<string, SemaphoreSlim>();
        }

        /// <summary>
        /// Executes an operation through the bulkhead
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result<T>> ExecuteAsync<T>(Func<Task<Result<T>>> operation)
        {
            if (!await _semaphore.WaitAsync(0))
            {
                return Result.Failure<T>(
                    Error.ExternalService("Bulkhead is full. Too many concurrent operations.")
                );
            }

            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                return Result.Failure<T>(Error.Internal(ex.Message));
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Executes an operation through the bulkhead
        /// </summary>
        /// <param name="operation">The operation to execute</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result> ExecuteAsync(Func<Task<Result>> operation)
        {
            if (!await _semaphore.WaitAsync(0))
            {
                return Result.Failure(
                    Error.ExternalService("Bulkhead is full. Too many concurrent operations.")
                );
            }

            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Executes an operation through a partitioned bulkhead
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="partitionKey">The key to partition operations by</param>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxConcurrentOperationsPerPartition">Maximum concurrent operations per partition</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result<T>> ExecutePartitionedAsync<T>(
            string partitionKey,
            Func<Task<Result<T>>> operation,
            int maxConcurrentOperationsPerPartition = 5
        )
        {
            var semaphore = _partitionedSemaphores.GetOrAdd(
                partitionKey,
                _ => new SemaphoreSlim(
                    maxConcurrentOperationsPerPartition,
                    maxConcurrentOperationsPerPartition
                )
            );

            if (!await semaphore.WaitAsync(0))
            {
                return Result.Failure<T>(
                    Error.ExternalService($"Bulkhead for partition '{partitionKey}' is full.")
                );
            }

            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                return Result.Failure<T>(Error.Internal(ex.Message));
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// Executes an operation through a partitioned bulkhead
        /// </summary>
        /// <param name="partitionKey">The key to partition operations by</param>
        /// <param name="operation">The operation to execute</param>
        /// <param name="maxConcurrentOperationsPerPartition">Maximum concurrent operations per partition</param>
        /// <returns>The result of the operation</returns>
        public async Task<Result> ExecutePartitionedAsync(
            string partitionKey,
            Func<Task<Result>> operation,
            int maxConcurrentOperationsPerPartition = 5
        )
        {
            var semaphore = _partitionedSemaphores.GetOrAdd(
                partitionKey,
                _ => new SemaphoreSlim(
                    maxConcurrentOperationsPerPartition,
                    maxConcurrentOperationsPerPartition
                )
            );

            if (!await semaphore.WaitAsync(0))
            {
                return Result.Failure(
                    Error.ExternalService($"Bulkhead for partition '{partitionKey}' is full.")
                );
            }

            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                return Result.Failure(Error.Internal(ex.Message));
            }
            finally
            {
                semaphore.Release();
            }
        }
    }
}
