﻿using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Documents;

public interface IDocumentService
{
    Task<Result<DocumentResponse>> UploadDocumentAsync(
        IFormFile file,
        string documentType,
        Guid userId,
        string issuer,
        string country,
        string certificationType,
        string? otherCertificationType,
        string? certificationNumber,
        DateTime? expiryDate
    );
    Task<Result<bool>> DeleteDocumentAsync(Guid documentId);
    Task<Result<DocumentResponse>> UpdateDocumentAsync(
        IFormFile? file,
        Guid documentId,
        string? documentType = null,
        string? issuer = null,
        string? country = null,
        string? certificationType = null,
        string? otherCertificationType = null,
        string? certificationNumber = null,
        DateTime? expiryDate = null
    );
    Task<Result<DocumentResponse>> GetDocumentByIdAsync(
        Guid documentId,
        bool includeUnapproved = false
    );
    Task<Result<IEnumerable<DocumentResponse>>> GetAllDocumentsByUserIdAsync(
        Guid userId,
        bool includeUnapproved = false
    );

    // New methods for document approval workflow
    Task<Result<DocumentResponse>> ApproveDocumentAsync(Guid documentId, Guid adminId);
    Task<Result<DocumentResponse>> RejectDocumentAsync(
        Guid documentId,
        Guid adminId,
        string rejectionReason
    );
    Task<Result<IEnumerable<DocumentResponse>>> GetPendingDocumentsAsync();
}
