﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings;

public class PaymentMethodRequest
{
    public string PaymentMethod { get; init; }
    public string Type { get; init; }
    public string LastFour { get; init; }
    public int ExpMonth { get; init; }
    public int ExpYear { get; init; }
    public bool IsDefault { get; init; }
}

public class PaymentMethodRequestValidator : AbstractValidator<PaymentMethodRequest>
{
    public PaymentMethodRequestValidator()
    {
        RuleFor(x => x.PaymentMethod)
            .NotEmpty()
            .WithMessage("Payment method identifier is required.");

        RuleFor(x => x.Type).NotEmpty().WithMessage("Payment method type is required.");

        RuleFor(x => x.LastFour)
            .NotEmpty()
            .Matches("^[0-9]{4}$")
            .WithMessage("LastFour must be exactly 4 digits.");

        RuleFor(x => x.ExpMonth)
            .InclusiveBetween(1, 12)
            .WithMessage("Expiration month must be between 1 and 12.");

        RuleFor(x => x.ExpYear)
            .GreaterThanOrEqualTo(DateTime.UtcNow.Year)
            .WithMessage("Expiration year cannot be in the past.");

        When(
            x => x.ExpYear == DateTime.UtcNow.Year,
            () =>
            {
                RuleFor(x => x.ExpMonth)
                    .GreaterThanOrEqualTo(DateTime.UtcNow.Month)
                    .WithMessage("This card has already expired.");
            }
        );

        RuleFor(x => x.IsDefault).NotNull();
    }
}
