﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SuperCareApp.Application.Shared.Utility;

namespace super_care_app.Filters;

///<summary>
/// Filter for validation model states
///</summary>
public class ValidationFilter : IActionFilter
{
    private readonly IWebHostEnvironment _env;

    public ValidationFilter(IWebHostEnvironment env)
    {
        _env = env;
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        // Check if the model state is valid
        if (!context.ModelState.IsValid)
        {
            // Collect all validation errors
            var validationErrors = context
                .ModelState.Where(e => e.Value!.Errors.Count > 0)
                .Select(e => new
                {
                    Field = e.Key,
                    Errors = e.Value!.Errors.Select(err => err.ErrorMessage).ToList(),
                })
                .ToList();

            // Convert validation errors to a dictionary for the payload
            var errorsDictionary = context
                .ModelState.Where(e => e.Value!.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value!.Errors.Select(err => err.ErrorMessage).ToArray()
                );

            // Create a detailed error message
            var errorMessage = "Validation failed";

            // Create API response using the new model
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.BadRequest,
                errorMessage,
                errorsDictionary
            );

            // Return a BadRequest with the formatted response
            context.Result = new ObjectResult(response)
            {
                StatusCode = (int)HttpStatusCode.BadRequest,
            };
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // No implementation needed
    }
}
