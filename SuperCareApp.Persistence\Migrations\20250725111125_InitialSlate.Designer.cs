﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using SuperCareApp.Persistence.Context;

#nullable disable

namespace SuperCareApp.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250725111125_InitialSlate")]
    partial class InitialSlate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.14")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("city");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(10,7)")
                        .HasColumnName("latitude");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(10,7)")
                        .HasColumnName("longitude");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("postal_code");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("state");

                    b.Property<string>("StreetAddress")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("street_address");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_addresses");

                    b.ToTable("addresses", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Approval", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ApprovalData")
                        .HasColumnType("jsonb")
                        .HasColumnName("approval_data");

                    b.Property<int>("ApprovalType")
                        .HasColumnType("integer")
                        .HasColumnName("approval_type");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool?>("IsApproved")
                        .HasColumnType("boolean")
                        .HasColumnName("is_approved");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.Property<Guid?>("ProcessedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("processed_by");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("rejection_reason");

                    b.Property<Guid?>("RelatedEntityId")
                        .HasColumnType("uuid")
                        .HasColumnName("related_entity_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_approvals");

                    b.HasIndex("ProcessedBy")
                        .HasDatabaseName("ix_approvals_processed_by");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_approvals_user_id");

                    b.ToTable("approvals", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Attachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("file_name");

                    b.Property<int>("FileSize")
                        .HasColumnType("integer")
                        .HasColumnName("file_size");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("file_type");

                    b.Property<string>("FileUrl")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("file_url");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uuid")
                        .HasColumnName("message_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_attachments");

                    b.HasIndex("MessageId")
                        .HasDatabaseName("ix_attachments_message_id");

                    b.ToTable("attachments", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Action")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("action");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uuid")
                        .HasColumnName("entity_id");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("entity_type");

                    b.Property<string>("NewValues")
                        .HasColumnType("text")
                        .HasColumnName("new_values");

                    b.Property<string>("OldValues")
                        .HasColumnType("text")
                        .HasColumnName("old_values");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_audit_logs");

                    b.ToTable("audit_logs", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Availability", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("DayOfWeek")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("character varying")
                        .HasColumnName("day_of_week");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsAvailable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_available");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_availabilities");

                    b.HasIndex("ProviderId", "DayOfWeek")
                        .HasDatabaseName("ix_availabilities_provider_id_day_of_week");

                    b.ToTable("availabilities", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.AvailabilitySlot", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AvailabilityId")
                        .HasColumnType("uuid")
                        .HasColumnName("availability_id");

                    b.Property<TimeOnly>("EndTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("end_time");

                    b.Property<TimeOnly>("StartTime")
                        .HasColumnType("time without time zone")
                        .HasColumnName("start_time");

                    b.HasKey("Id")
                        .HasName("pk_availability_slots");

                    b.HasIndex("AvailabilityId")
                        .HasDatabaseName("ix_availability_slots_availability_id");

                    b.ToTable("availability_slots", null, t =>
                        {
                            t.HasCheckConstraint("CK_AvailabilitySlot_EndAfterStart", "\"end_time\" > \"start_time\"");
                        });
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Booking", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<decimal>("PlatformFee")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("platform_fee");

                    b.Property<decimal>("ProviderAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("provider_amount");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("special_instructions");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("total_amount");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<int?>("WorkingHours")
                        .HasColumnType("integer")
                        .HasColumnName("working_hours");

                    b.HasKey("Id")
                        .HasName("pk_bookings");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_bookings_category_id");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("IX_Bookings_ClientId")
                        .HasFilter("\"is_deleted\" = false");

                    b.HasIndex("ProviderId")
                        .HasDatabaseName("IX_Bookings_ProviderId")
                        .HasFilter("\"is_deleted\" = false");

                    b.HasIndex("WorkingHours")
                        .HasDatabaseName("ix_bookings_working_hours");

                    b.ToTable("bookings", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.BookingStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BookingId")
                        .HasColumnType("uuid")
                        .HasColumnName("booking_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("notes");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_booking_statuses");

                    b.HasIndex("BookingId")
                        .IsUnique()
                        .HasDatabaseName("ix_booking_statuses_booking_id");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("ix_booking_statuses_created_by");

                    b.ToTable("booking_statuses", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.BookingWindow", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BookingId")
                        .HasColumnType("uuid")
                        .HasColumnName("booking_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<decimal?>("DailyRate")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("daily_rate");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date")
                        .HasColumnName("date");

                    b.Property<string>("DaySpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("day_special_instructions");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<int?>("DurationMinutes")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("integer")
                        .HasColumnName("duration_minutes")
                        .HasComputedColumnSql("EXTRACT(EPOCH FROM (\"end_time\" - \"start_time\")) / 60", true);

                    b.Property<TimeOnly>("EndTime")
                        .HasColumnType("time")
                        .HasColumnName("end_time");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<TimeOnly>("StartTime")
                        .HasColumnType("time")
                        .HasColumnName("start_time");

                    b.Property<string>("Status")
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_booking_windows");

                    b.HasIndex("Date")
                        .HasDatabaseName("IX_BookingWindows_Date");

                    b.HasIndex("EndTime")
                        .HasDatabaseName("IX_BookingWindows_EndTime");

                    b.HasIndex("StartTime")
                        .HasDatabaseName("IX_BookingWindows_StartTime");

                    b.HasIndex("BookingId", "Date")
                        .IsUnique()
                        .HasDatabaseName("IX_BookingWindows_BookingId_Date");

                    b.ToTable("booking_windows", null, t =>
                        {
                            t.HasCheckConstraint("CK_BookingWindows_ValidTimes", "\"end_time\" > \"start_time\"");
                        });
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .HasMaxLength(7)
                        .IsUnicode(false)
                        .HasColumnType("character varying(7)")
                        .HasColumnName("color")
                        .HasComment("Hex color code for UI representation");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("Icon")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<decimal>("PlatformFee")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(0m)
                        .HasColumnName("platform_fee");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_care_categories");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("ix_care_categories_is_active");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_care_categories_name");

                    b.ToTable("care_categories", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareProviderCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<int?>("ExperienceYears")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("experience_years");

                    b.Property<decimal>("HourlyRate")
                        .HasColumnType("decimal(18,6)")
                        .HasColumnName("hourly_rate");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int?>("MaxHoursPerWeek")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("max_hours_per_week");

                    b.Property<int?>("MinHoursPerWeek")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("min_hours_per_week");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<string>("ProviderSpecificDescription")
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("provider_specific_description");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_care_provider_categories");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_care_provider_categories_category_id");

                    b.HasIndex("ProviderId", "CategoryId")
                        .IsUnique()
                        .HasDatabaseName("ix_care_provider_categories_provider_id_category_id");

                    b.ToTable("care_provider_categories", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareProviderProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bio")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("bio");

                    b.Property<int>("BufferDuration")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("buffer_duration");

                    b.Property<string>("CareDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("care_description");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<decimal?>("HourlyRate")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("hourly_rate");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("ProvidesLiveIn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("provides_live_in");

                    b.Property<bool?>("ProvidesOvernight")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("provides_overnight");

                    b.Property<bool?>("ProvidesRecurringBooking")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("provides_recurring_booking");

                    b.Property<string>("Qualifications")
                        .HasColumnType("jsonb")
                        .HasColumnName("qualifications");

                    b.Property<decimal?>("Rating")
                        .HasColumnType("decimal(3,2)")
                        .HasColumnName("rating");

                    b.Property<int?>("RatingCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("rating_count");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("VerificationStatus")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("verification_status");

                    b.Property<int?>("WorkingHours")
                        .HasColumnType("integer")
                        .HasColumnName("working_hours");

                    b.Property<int>("YearsExperience")
                        .HasColumnType("int")
                        .HasColumnName("years_experience");

                    b.HasKey("Id")
                        .HasName("pk_care_provider_profiles");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("ix_care_provider_profiles_user_id");

                    b.ToTable("care_provider_profiles", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Conversation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uuid")
                        .HasColumnName("client_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("LastMessageAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_message_at");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_conversations");

                    b.HasIndex("ProviderId")
                        .HasDatabaseName("ix_conversations_provider_id");

                    b.HasIndex("ClientId", "ProviderId")
                        .IsUnique()
                        .HasDatabaseName("ix_conversations_client_id_provider_id");

                    b.ToTable("conversations", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CertificationNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("certification_number");

                    b.Property<string>("CertificationType")
                        .HasColumnType("text")
                        .HasColumnName("certification_type");

                    b.Property<string>("Country")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("Other")
                        .HasColumnName("country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("DocumentName")
                        .HasColumnType("text")
                        .HasColumnName("document_name");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("document_type");

                    b.Property<string>("DocumentUrl")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("document_url");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiry_date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issuer")
                        .HasColumnType("text")
                        .HasColumnName("issuer");

                    b.Property<string>("OtherCertificationType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("other_certification_type");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("rejection_reason");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("uploaded_at");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("VerificationStatus")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("verification_status");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("verified_at");

                    b.Property<Guid?>("VerifiedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("verified_by");

                    b.HasKey("Id")
                        .HasName("pk_documents");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_documents_user_id");

                    b.ToTable("documents", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("amount");

                    b.Property<Guid>("BookingWindowId")
                        .HasColumnType("uuid")
                        .HasColumnName("booking_window_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("due_date");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("file_name");

                    b.Property<string>("FileUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("file_url");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("invoice_date");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("invoice_number");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<decimal>("Tax")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("tax");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("total_amount");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_invoices");

                    b.HasIndex("BookingWindowId")
                        .IsUnique()
                        .HasDatabaseName("ix_invoices_booking_window_id");

                    b.HasIndex("DueDate")
                        .HasDatabaseName("ix_invoices_due_date");

                    b.HasIndex("InvoiceNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_invoices_invoice_number");

                    b.HasIndex("Status")
                        .HasDatabaseName("ix_invoices_status");

                    b.ToTable("invoices", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Leave", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("CareProviderProfileId")
                        .HasColumnType("uuid")
                        .HasColumnName("care_provider_profile_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("reason");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_leaves");

                    b.HasIndex("CareProviderProfileId")
                        .HasDatabaseName("ix_leaves_care_provider_profile_id");

                    b.HasIndex("ProviderId", "StartDate", "EndDate")
                        .HasDatabaseName("ix_leaves_provider_id_start_date_end_date");

                    b.ToTable("leaves", null, t =>
                        {
                            t.HasCheckConstraint("CK_Leave_EndDateAfterStartDate", "\"end_date\" >= \"start_date\"");
                        });
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Message", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)")
                        .HasColumnName("content");

                    b.Property<Guid>("ConversationId")
                        .HasColumnType("uuid")
                        .HasColumnName("conversation_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_read");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("read_at");

                    b.Property<Guid>("SenderId")
                        .HasColumnType("uuid")
                        .HasColumnName("sender_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_messages");

                    b.HasIndex("SenderId")
                        .HasDatabaseName("ix_messages_sender_id");

                    b.HasIndex("ConversationId", "CreatedAt")
                        .HasDatabaseName("ix_messages_conversation_id_created_at");

                    b.ToTable("messages", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ActionUrl")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)")
                        .HasColumnName("action_url");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("NotificationType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("notification_type");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_notifications");

                    b.ToTable("notifications", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.OtpCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("application_user_id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("character(6)")
                        .HasColumnName("code")
                        .IsFixedLength();

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expires_at");

                    b.Property<bool>("IsUsed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_used");

                    b.HasKey("Id")
                        .HasName("pk_otp_codes");

                    b.HasIndex("ApplicationUserId")
                        .HasDatabaseName("ix_otp_codes_application_user_id");

                    b.ToTable("otp_codes", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.OutboxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id")
                        .HasDefaultValueSql("gen_random_uuid()");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("content");

                    b.Property<DateTime>("OccurredOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("occurred_on")
                        .HasDefaultValueSql("NOW()");

                    b.Property<bool>("Processed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("processed");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk__outbox_messages");

                    b.HasIndex("OccurredOn")
                        .HasDatabaseName("ix_outbox_messages_occurred_on");

                    b.HasIndex("Processed")
                        .HasDatabaseName("ix_outbox_messages_processed");

                    b.HasIndex("ProcessedAt")
                        .HasDatabaseName("ix_outbox_messages_processed_at")
                        .HasFilter("processed = true");

                    b.HasIndex("Type")
                        .HasDatabaseName("ix_outbox_messages_type");

                    b.HasIndex("Processed", "OccurredOn")
                        .HasDatabaseName("ix_outbox_messages_processed_occurred_on")
                        .HasFilter("NOT processed");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Processed", "OccurredOn"), new[] { "Type", "Content" });

                    b.HasIndex("Processed", "ProcessedAt")
                        .HasDatabaseName("ix_outbox_messages_processed_processed_at")
                        .HasFilter("processed = true");

                    b.HasIndex("Type", "Processed", "OccurredOn")
                        .HasDatabaseName("ix_outbox_messages_type_processed_occurred_on")
                        .HasFilter("NOT processed");

                    b.ToTable("_outbox_messages", null, t =>
                        {
                            t.HasComment("Outbox table to store outgoing messages");

                            t.HasCheckConstraint("ck_outbox_messages_processed_at", "(processed = false AND processed_at IS NULL) OR (processed = true AND processed_at IS NOT NULL)");
                        });
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("amount");

                    b.Property<Guid?>("BookingId")
                        .HasColumnType("uuid")
                        .HasColumnName("booking_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("invoice_number");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime>("PaymentDateTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("payment_date_time");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("payment_method");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("transaction_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_payments");

                    b.HasIndex("BookingId")
                        .HasDatabaseName("ix_payments_booking_id");

                    b.HasIndex("InvoiceId")
                        .HasDatabaseName("ix_payments_invoice_id");

                    b.HasIndex("InvoiceNumber")
                        .HasDatabaseName("ix_payments_invoice_number");

                    b.HasIndex("TransactionId")
                        .HasDatabaseName("ix_payments_transaction_id");

                    b.ToTable("payments", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Review", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BookingId")
                        .HasColumnType("uuid")
                        .HasColumnName("booking_id");

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("comment");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("Rating")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("rating");

                    b.Property<Guid>("RevieweeId")
                        .HasColumnType("uuid")
                        .HasColumnName("reviewee_id");

                    b.Property<Guid>("ReviewerId")
                        .HasColumnType("uuid")
                        .HasColumnName("reviewer_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_reviews");

                    b.HasIndex("RevieweeId")
                        .HasDatabaseName("ix_reviews_reviewee_id");

                    b.HasIndex("ReviewerId")
                        .HasDatabaseName("ix_reviews_reviewer_id");

                    b.HasIndex("BookingId", "ReviewerId")
                        .IsUnique()
                        .HasDatabaseName("ix_reviews_booking_id_reviewer_id");

                    b.ToTable("reviews", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("amount");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("payment_method");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<string>("SubscriptionType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("subscription_type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_subscriptions");

                    b.HasIndex("ProviderId", "EndDate")
                        .HasDatabaseName("ix_subscriptions_provider_id_end_date");

                    b.ToTable("subscriptions", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.TrackingSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BookingWindowId")
                        .HasColumnType("uuid")
                        .HasColumnName("booking_window_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_time");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<DateTimeOffset?>("PausedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("paused_at");

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uuid")
                        .HasColumnName("provider_id");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_time");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("status");

                    b.Property<decimal?>("TotalHours")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)")
                        .HasColumnName("total_hours");

                    b.Property<TimeSpan>("TotalPausedDuration")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("interval")
                        .HasColumnName("total_paused_duration")
                        .HasDefaultValueSql("interval '0 seconds'");

                    b.Property<string>("TrackingData")
                        .HasColumnType("jsonb")
                        .HasColumnName("tracking_data");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_tracking_sessions");

                    b.HasIndex("BookingWindowId")
                        .HasDatabaseName("IX_TrackingSessions_BookingWindowId");

                    b.HasIndex("ProviderId")
                        .HasDatabaseName("IX_TrackingSessions_ProviderId");

                    b.HasIndex("StartTime")
                        .HasDatabaseName("IX_TrackingSessions_StartTime");

                    b.HasIndex("ProviderId", "Status")
                        .HasDatabaseName("IX_TrackingSessions_Provider_Status")
                        .HasFilter("\"status\" <> 'Stopped'");

                    b.ToTable("tracking_sessions", null, t =>
                        {
                            t.HasCheckConstraint("CK_TrackingSessions_EndAfterStart", "\"end_time\" IS NULL OR \"end_time\" > \"start_time\"");

                            t.HasCheckConstraint("CK_TrackingSessions_NonNegativeTotalHours", "\"total_hours\" IS NULL OR \"total_hours\" >= 0");
                        });
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uuid")
                        .HasColumnName("address_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsPrimary")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_primary");

                    b.Property<string>("Label")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("label");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_addresses");

                    b.HasIndex("AddressId")
                        .HasDatabaseName("ix_user_addresses_address_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_user_addresses_user_id");

                    b.ToTable("user_addresses", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserNotification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("application_user_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_read");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid")
                        .HasColumnName("notification_id");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("read_at");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_user_notification");

                    b.HasIndex("NotificationId")
                        .HasDatabaseName("ix_user_notification_notification_id");

                    b.HasIndex("ApplicationUserId", "CreatedAt")
                        .HasDatabaseName("ix_user_notification_application_user_id_created_at");

                    b.HasIndex("ApplicationUserId", "IsRead")
                        .HasDatabaseName("ix_user_notification_application_user_id_is_read");

                    b.ToTable("user_notification", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ApplicationUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("application_user_id");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_of_birth");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("first_name");

                    b.Property<string>("Gender")
                        .HasColumnType("text")
                        .HasColumnName("gender");

                    b.Property<string>("ImageName")
                        .HasColumnType("text")
                        .HasColumnName("image_name");

                    b.Property<string>("ImagePath")
                        .HasColumnType("text")
                        .HasColumnName("image_path");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_name");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<string>("Preferences")
                        .HasColumnType("jsonb")
                        .HasColumnName("preferences");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_user_profiles");

                    b.HasIndex("ApplicationUserId")
                        .IsUnique()
                        .HasDatabaseName("ix_user_profiles_application_user_id");

                    b.ToTable("user_profiles", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("name");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_roles");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationRoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.HasKey("Id")
                        .HasName("pk_role_claims");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_role_claims_role_id");

                    b.ToTable("role_claims", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer")
                        .HasColumnName("access_failed_count");

                    b.Property<string>("AuthProvider")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("auth_provider");

                    b.Property<string>("AuthProviderId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("auth_provider_id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("deleted_by");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("email_confirmed");

                    b.Property<bool>("EmailVerified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("email_verified");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("lockout_enabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("lockout_end");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_user_name");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text")
                        .HasColumnName("password_hash");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("phone_number_confirmed");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text")
                        .HasColumnName("security_stamp");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("user_name");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_claims");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_user_claims_user_id");

                    b.ToTable("user_claims", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserLogin", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text")
                        .HasColumnName("provider_key");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text")
                        .HasColumnName("provider_display_name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("LoginProvider", "ProviderKey")
                        .HasName("pk_user_logins");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_user_logins_user_id");

                    b.ToTable("user_logins", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId")
                        .HasName("pk_user_roles");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_user_roles_role_id");

                    b.ToTable("user_roles", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiry_date");

                    b.Property<bool>("IsExpired")
                        .HasColumnType("boolean")
                        .HasColumnName("is_expired");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("boolean")
                        .HasColumnName("is_revoked");

                    b.Property<Guid>("LoginSessionId")
                        .HasColumnType("uuid")
                        .HasColumnName("login_session_id");

                    b.Property<string>("Token")
                        .HasColumnType("text")
                        .HasColumnName("token");

                    b.Property<string>("TokenType")
                        .HasColumnType("text")
                        .HasColumnName("token_type");

                    b.Property<string>("Value")
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("UserId", "LoginProvider", "Name")
                        .HasName("pk_user_tokens");

                    b.HasIndex("ExpiryDate")
                        .HasDatabaseName("ix_user_tokens_expiry_date");

                    b.HasIndex("IsExpired")
                        .HasDatabaseName("ix_user_tokens_is_expired");

                    b.HasIndex("IsRevoked")
                        .HasDatabaseName("ix_user_tokens_is_revoked");

                    b.HasIndex("LoginSessionId")
                        .HasDatabaseName("ix_user_tokens_login_session_id");

                    b.HasIndex("Token")
                        .HasDatabaseName("ix_user_tokens_token");

                    b.HasIndex("UserId", "TokenType")
                        .HasDatabaseName("ix_user_tokens_user_id_token_type");

                    b.ToTable("user_tokens", (string)null);
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Approval", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Processor")
                        .WithMany()
                        .HasForeignKey("ProcessedBy")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_approvals_users_processed_by");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_approvals_users_user_id");

                    b.Navigation("Processor");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Attachment", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Message", "Message")
                        .WithMany("Attachments")
                        .HasForeignKey("MessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_attachments_messages_message_id");

                    b.Navigation("Message");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Availability", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.CareProviderProfile", "CareProviderProfile")
                        .WithMany("Availabilities")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_availabilities_care_provider_profiles_provider_id");

                    b.Navigation("CareProviderProfile");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.AvailabilitySlot", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Availability", "Availability")
                        .WithMany("AvailabilitySlots")
                        .HasForeignKey("AvailabilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_availability_slots_availabilities_availability_id");

                    b.Navigation("Availability");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Booking", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.CareCategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_bookings_care_categories_category_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_bookings_users_client_id");

                    b.HasOne("SuperCareApp.Domain.Entities.CareProviderProfile", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_bookings_care_provider_profiles_provider_id");

                    b.Navigation("Category");

                    b.Navigation("Client");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.BookingStatus", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Booking", "Booking")
                        .WithOne("Status")
                        .HasForeignKey("SuperCareApp.Domain.Entities.BookingStatus", "BookingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_booking_statuses_bookings_booking_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_booking_statuses_users_created_by");

                    b.Navigation("Booking");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.BookingWindow", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Booking", "Booking")
                        .WithMany("BookingWindows")
                        .HasForeignKey("BookingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_booking_windows_bookings_booking_id");

                    b.Navigation("Booking");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareProviderCategory", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.CareCategory", "CareCategory")
                        .WithMany("CareProviderCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_care_provider_categories_care_categories_category_id");

                    b.HasOne("SuperCareApp.Domain.Entities.CareProviderProfile", "CareProviderProfile")
                        .WithMany("CareProviderCategories")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_care_provider_categories_care_provider_profiles_provider_id");

                    b.Navigation("CareCategory");

                    b.Navigation("CareProviderProfile");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareProviderProfile", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithOne()
                        .HasForeignKey("SuperCareApp.Domain.Entities.CareProviderProfile", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_care_provider_profiles_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Conversation", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_conversations_users_client_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_conversations_users_provider_id");

                    b.Navigation("Client");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Document", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.UserProfile", "UserProfile")
                        .WithMany("Documents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_documents_user_profiles_user_id");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Invoice", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.BookingWindow", "BookingWindow")
                        .WithOne("Invoice")
                        .HasForeignKey("SuperCareApp.Domain.Entities.Invoice", "BookingWindowId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_invoices_booking_windows_booking_window_id");

                    b.Navigation("BookingWindow");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Leave", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.CareProviderProfile", null)
                        .WithMany("Leaves")
                        .HasForeignKey("CareProviderProfileId")
                        .HasConstraintName("fk_leaves_care_provider_profiles_care_provider_profile_id");

                    b.HasOne("SuperCareApp.Domain.Entities.CareProviderProfile", "CareProviderProfile")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_leaves_care_provider_profiles_provider_id");

                    b.Navigation("CareProviderProfile");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Message", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Conversation", "Conversation")
                        .WithMany("Messages")
                        .HasForeignKey("ConversationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_messages_conversations_conversation_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Sender")
                        .WithMany()
                        .HasForeignKey("SenderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_messages_users_sender_id");

                    b.Navigation("Conversation");

                    b.Navigation("Sender");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.OtpCode", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany("OtpCodes")
                        .HasForeignKey("ApplicationUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_otp_codes_users_application_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Payment", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Booking", null)
                        .WithMany("Payments")
                        .HasForeignKey("BookingId")
                        .HasConstraintName("fk_payments_bookings_booking_id");

                    b.HasOne("SuperCareApp.Domain.Entities.Invoice", "Invoice")
                        .WithMany("Payments")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_payments_invoices_invoice_id");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Review", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Booking", "Booking")
                        .WithMany("Reviews")
                        .HasForeignKey("BookingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_reviews_bookings_booking_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Reviewee")
                        .WithMany()
                        .HasForeignKey("RevieweeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_reviews_users_reviewee_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Reviewer")
                        .WithMany()
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_reviews_users_reviewer_id");

                    b.Navigation("Booking");

                    b.Navigation("Reviewee");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Subscription", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_subscriptions_users_provider_id");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.TrackingSession", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.BookingWindow", "BookingWindow")
                        .WithMany("TrackingSessions")
                        .HasForeignKey("BookingWindowId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_tracking_sessions_booking_windows_booking_window_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "Provider")
                        .WithMany()
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_tracking_sessions_users_provider_id");

                    b.Navigation("BookingWindow");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserAddress", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Entities.Address", "Address")
                        .WithMany("UserAddresses")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_addresses_addresses_address_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany("UserAddresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_addresses_users_user_id");

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserNotification", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "ApplicationUser")
                        .WithMany("UserNotifications")
                        .HasForeignKey("ApplicationUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_notification_users_application_user_id");

                    b.HasOne("SuperCareApp.Domain.Entities.Notification", "Notification")
                        .WithMany("UserNotifications")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_notification_notifications_notification_id");

                    b.Navigation("ApplicationUser");

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserProfile", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithOne("UserProfile")
                        .HasForeignKey("SuperCareApp.Domain.Entities.UserProfile", "ApplicationUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_profiles_users_application_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationRoleClaim", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationRole", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_claims_roles_role_id");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserClaim", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_claims_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserLogin", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_logins_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserRole", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationRole", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_roles_roles_role_id");

                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_roles_users_user_id");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUserToken", b =>
                {
                    b.HasOne("SuperCareApp.Domain.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_tokens_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Address", b =>
                {
                    b.Navigation("UserAddresses");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Availability", b =>
                {
                    b.Navigation("AvailabilitySlots");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Booking", b =>
                {
                    b.Navigation("BookingWindows");

                    b.Navigation("Payments");

                    b.Navigation("Reviews");

                    b.Navigation("Status");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.BookingWindow", b =>
                {
                    b.Navigation("Invoice");

                    b.Navigation("TrackingSessions");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareCategory", b =>
                {
                    b.Navigation("CareProviderCategories");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.CareProviderProfile", b =>
                {
                    b.Navigation("Availabilities");

                    b.Navigation("CareProviderCategories");

                    b.Navigation("Leaves");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Conversation", b =>
                {
                    b.Navigation("Messages");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Invoice", b =>
                {
                    b.Navigation("Payments");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Message", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.Notification", b =>
                {
                    b.Navigation("UserNotifications");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Entities.UserProfile", b =>
                {
                    b.Navigation("Documents");
                });

            modelBuilder.Entity("SuperCareApp.Domain.Identity.ApplicationUser", b =>
                {
                    b.Navigation("OtpCodes");

                    b.Navigation("UserAddresses");

                    b.Navigation("UserNotifications");

                    b.Navigation("UserProfile");
                });
#pragma warning restore 612, 618
        }
    }
}
