using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.UnitOfWork;

namespace SuperCareApp.Persistence.Test.Categories;

public class CareCategoryServiceTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CareCategoryService _service;

    public CareCategoryServiceTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        ICareCategoryRepository repository = new CareCategoryRepository(_context);
        IUnitOfWork unitOfWork = new UnitOfWork.UnitOfWork(_context);
        _service = new CareCategoryService(
            repository,
            _context,
            unitOfWork,
            NullLogger<CareCategoryService>.Instance
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    [Fact]
    public async Task CreateCategoryAsync_WithIconAndColor_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 10.50m,
            Icon = "fas fa-heart",
            Color = "#FF5733",
        };

        // Act
        var result = await _service.CreateCategoryAsync(request, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(request.Name, result.Value.Name);
        Assert.Equal(request.Description, result.Value.Description);
        Assert.Equal(request.IsActive, result.Value.IsActive);
        Assert.Equal(request.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(request.Icon, result.Value.Icon);
        Assert.Equal(request.Color, result.Value.Color);

        // Verify in database
        var categoryInDb = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == result.Value.Id
        );
        Assert.NotNull(categoryInDb);
        Assert.Equal(request.Icon, categoryInDb.Icon);
        Assert.Equal(request.Color, categoryInDb.Color);
    }

    [Fact]
    public async Task CreateCategoryAsync_WithNullIconAndColor_ShouldCreateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category No Icon",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 5.00m,
            Icon = null,
            Color = null,
        };

        // Act
        var result = await _service.CreateCategoryAsync(request, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Null(result.Value.Icon);
        Assert.Null(result.Value.Color);

        // Verify in database
        var categoryInDb = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == result.Value.Id
        );
        Assert.NotNull(categoryInDb);
        Assert.Null(categoryInDb.Icon);
        Assert.Null(categoryInDb.Color);
    }

    [Fact]
    public async Task UpdateCategoryAsync_WithIconAndColor_ShouldUpdateSuccessfully()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = "fas fa-user",
            Color = "#000000",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Description = "Updated Description",
            IsActive = false,
            PlatformFee = 20.00m,
            Icon = "fas fa-star",
            Color = "#00FF00",
        };

        // Act
        var result = await _service.UpdateCategoryAsync(category.Id, updateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(updateRequest.Name, result.Value.Name);
        Assert.Equal(updateRequest.Description, result.Value.Description);
        Assert.Equal(updateRequest.IsActive, result.Value.IsActive);
        Assert.Equal(updateRequest.PlatformFee, result.Value.PlatformFee);
        Assert.Equal(updateRequest.Icon, result.Value.Icon);
        Assert.Equal(updateRequest.Color, result.Value.Color);

        // Verify in database
        var updatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == category.Id
        );
        Assert.NotNull(updatedCategory);
        Assert.Equal(updateRequest.Icon, updatedCategory.Icon);
        Assert.Equal(updateRequest.Color, updatedCategory.Color);
    }

    [Fact]
    public async Task UpdateCategoryAsync_WithNullIconAndColor_ShouldKeepOriginalValues()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var originalIcon = "fas fa-heart";
        var originalColor = "#FF5733";

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Original Category",
            Description = "Original Description",
            IsActive = true,
            PlatformFee = 15.00m,
            Icon = originalIcon,
            Color = originalColor,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = null,
            Color = null,
        };

        // Act
        var result = await _service.UpdateCategoryAsync(category.Id, updateRequest, userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(originalIcon, result.Value.Icon);
        Assert.Equal(originalColor, result.Value.Color);

        // Verify in database
        var updatedCategory = await _context.CareCategories.FirstOrDefaultAsync(c =>
            c.Id == category.Id
        );
        Assert.NotNull(updatedCategory);
        Assert.Equal(originalIcon, updatedCategory.Icon);
        Assert.Equal(originalColor, updatedCategory.Color);
    }

    [Fact]
    public async Task GetCategoryByIdAsync_ShouldReturnIconAndColor()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 12.50m,
            Icon = "fas fa-medical",
            Color = "#0066CC",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoryByIdAsync(category.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(category.Icon, result.Value.Icon);
        Assert.Equal(category.Color, result.Value.Color);
    }

    [Fact]
    public async Task GetAllCategoriesAsync_ShouldReturnIconAndColorForAllCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categories = new[]
        {
            new CareCategory
            {
                Id = Guid.NewGuid(),
                Name = "Category 1",
                IsActive = true,
                PlatformFee = 10.00m,
                Icon = "fas fa-heart",
                Color = "#FF0000",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userId,
            },
            new CareCategory
            {
                Id = Guid.NewGuid(),
                Name = "Category 2",
                IsActive = true,
                PlatformFee = 15.00m,
                Icon = "fas fa-star",
                Color = "#00FF00",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userId,
            },
            new CareCategory
            {
                Id = Guid.NewGuid(),
                Name = "Category 3",
                IsActive = true,
                PlatformFee = 20.00m,
                Icon = null,
                Color = null,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userId,
            },
        };

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetAllCategoriesAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(3, result.Value.Count());

        var categoryList = result.Value.ToList();

        var category1 = categoryList.First(c => c.Name == "Category 1");
        Assert.Equal("fas fa-heart", category1.Icon);
        Assert.Equal("#FF0000", category1.Color);

        var category2 = categoryList.First(c => c.Name == "Category 2");
        Assert.Equal("fas fa-star", category2.Icon);
        Assert.Equal("#00FF00", category2.Color);

        var category3 = categoryList.First(c => c.Name == "Category 3");
        Assert.Null(category3.Icon);
        Assert.Null(category3.Color);
    }

    [Fact]
    public async Task GetCategoriesByProviderIdAsync_ShouldReturnIconAndColorForProviderCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-user-md",
            Color = "#4CAF50",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
        };

        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m,
            ExperienceYears = 5,
            ProviderSpecificDescription = "Provider specific description",
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetCategoriesByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Single(result.Value);

        var providerCategoryResponse = result.Value.First();
        Assert.Equal(category.Icon, providerCategoryResponse.Icon);
        Assert.Equal(category.Color, providerCategoryResponse.Color);
        Assert.Equal(50.00m, providerCategoryResponse.HourlyRate);
        Assert.Equal(5, providerCategoryResponse.ExperienceYears);
    }
}
