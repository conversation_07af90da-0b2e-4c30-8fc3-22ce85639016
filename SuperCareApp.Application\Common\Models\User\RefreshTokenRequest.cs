﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.User;

public class RefreshTokenRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}

public class RefreshTokenRequestValidator : AbstractValidator<RefreshTokenRequest>
{
    public RefreshTokenRequestValidator()
    {
        RuleFor(x => x.RefreshToken)
            .NotEmpty()
            .WithMessage("Refresh token is required.")
            .Must(token => !string.IsNullOrWhiteSpace(token))
            .WithMessage("Refresh token must be a valid non-whitespace string.");
    }
}
