using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Otp
{
    /// <summary>
    /// Request model for generating an OTP
    /// </summary>
    public class GenerateOtpRequest
    {
        /// <summary>
        /// Email address to send the OTP to (required if DeliveryMethod is Email or Both)
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Phone number to send the OTP to (required if DeliveryMethod is Sms or Both)
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Method to deliver the OTP (Email, SMS, or Both)
        /// </summary>
        public string DeliveryMethod { get; set; }

        /// <summary>
        /// Purpose of the OTP (e.g., "Registration", "PasswordReset")
        /// </summary>
        public string ActionType { get; set; } = "Verification";
    }

    public class GenerateOtpRequestValidator : AbstractValidator<GenerateOtpRequest>
    {
        public GenerateOtpRequestValidator()
        {
            RuleFor(x => x.DeliveryMethod)
                .NotEmpty()
                .WithMessage("Delivery method is required.")
                .Must(method => method == "Email" || method == "Sms" || method == "Both")
                .WithMessage("Invalid delivery method. Allowed: Email, Sms, Both.");

            RuleFor(x => x.ActionType)
                .NotEmpty()
                .WithMessage("Action type is required.")
                .Must(action => action == "Registration" || action == "PasswordReset")
                .WithMessage("Invalid action type. Allowed: Registration, PasswordReset.");

            When(
                x => x.DeliveryMethod == "Email" || x.DeliveryMethod == "Both",
                () =>
                {
                    RuleFor(x => x.Email)
                        .NotEmpty()
                        .WithMessage("Email is required for Email or Both delivery method.")
                        .Matches(@"^[^@\s]+@[^@\s]+\.[^@\s]+$")
                        .WithMessage("Invalid email address format.");
                }
            );

            When(
                x => x.DeliveryMethod == "Sms" || x.DeliveryMethod == "Both",
                () =>
                {
                    RuleFor(x => x.PhoneNumber)
                        .NotEmpty()
                        .WithMessage("Phone number is required for Sms or Both delivery method.")
                        .Matches(@"^\+?\d{10,15}$")
                        .WithMessage("Invalid phone number format.");
                }
            );
        }
    }
}
