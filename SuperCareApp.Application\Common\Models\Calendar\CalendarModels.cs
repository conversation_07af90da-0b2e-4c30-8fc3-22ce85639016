using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Calendar
{
    /// <summary>
    /// Response model for monthly calendar view
    /// </summary>
    public class MonthlyCalendarResponse
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public List<CalendarDayResponse> Days { get; set; } = new List<CalendarDayResponse>();
        public CalendarStatistics Statistics { get; set; } = new CalendarStatistics();
    }

    /// <summary>
    /// Response model for a single calendar day
    /// </summary>
    public class CalendarDayResponse
    {
        public DateTime Date { get; set; }
        public string DayOfWeek { get; set; } = string.Empty;
        public bool IsAvailable { get; set; }
        public bool IsOnLeave { get; set; }
        public int TotalSlots { get; set; }
        public int AvailableSlots { get; set; }
        public int BookedSlots { get; set; }
        public List<CalendarSlotResponse> Slots { get; set; } = new List<CalendarSlotResponse>();
        public List<CalendarBookingResponse> Bookings { get; set; } =
            new List<CalendarBookingResponse>();
    }

    /// <summary>
    /// Response model for calendar time slots
    /// </summary>
    public class CalendarSlotResponse
    {
        public string StartTime { get; set; } = string.Empty;
        public string EndTime { get; set; } = string.Empty;
        public bool IsAvailable { get; set; }
        public bool IsBooked { get; set; }
        public Guid? BookingId { get; set; }
    }

    /// <summary>
    /// Response model for calendar bookings
    /// </summary>
    public class CalendarBookingResponse
    {
        public Guid Id { get; set; }
        public string StartTime { get; set; } = string.Empty;
        public string EndTime { get; set; } = string.Empty;
        public string ClientName { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string? SpecialInstructions { get; set; }
    }

    /// <summary>
    /// Calendar statistics
    /// </summary>
    public class CalendarStatistics
    {
        public int TotalDays { get; set; }
        public int AvailableDays { get; set; }
        public int LeaveDays { get; set; }
        public int TotalSlots { get; set; }
        public int BookedSlots { get; set; }
        public int AvailableSlots { get; set; }
        public decimal TotalRevenue { get; set; }
        public double UtilizationRate { get; set; }
    }

    /// <summary>
    /// Request parameters for calendar range
    /// </summary>
    public class CalendarRangeParams
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IncludeBookings { get; set; } = true;
        public bool IncludeLeave { get; set; } = true;
        public bool IncludeStatistics { get; set; } = false;
    }

    /// <summary>
    /// Response for calendar range
    /// </summary>
    public class CalendarRangeResponse
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<CalendarDayResponse> Days { get; set; } = new List<CalendarDayResponse>();
        public CalendarStatistics? Statistics { get; set; }
    }

    /// <summary>
    /// Request parameters for next available slots
    /// </summary>
    public class NextAvailableSlotsParams
    {
        public DateTime? StartDate { get; set; }
        public int DurationMinutes { get; set; } = 60;
        public int MaxResults { get; set; } = 10;
        public int MaxDaysAhead { get; set; } = 30;
    }

    /// <summary>
    /// Response for next available slots
    /// </summary>
    public class NextAvailableSlotsResponse
    {
        public List<AvailableSlotResponse> Slots { get; set; } = new List<AvailableSlotResponse>();
        public DateTime SearchedUntil { get; set; }
        public bool HasMoreSlots { get; set; }
    }

    /// <summary>
    /// Available slot response
    /// </summary>
    public class AvailableSlotResponse
    {
        public DateTime Date { get; set; }
        public string DayOfWeek { get; set; } = string.Empty;
        public string StartTime { get; set; } = string.Empty;
        public string EndTime { get; set; } = string.Empty;
        public int DurationMinutes { get; set; }
    }

    /// <summary>
    /// Request parameters for availability check
    /// </summary>
    public class CheckAvailabilityParams
    {
        public DateTime Date { get; set; }
        public string StartTime { get; set; } = string.Empty;
        public string EndTime { get; set; } = string.Empty;
        public int? DurationMinutes { get; set; }
    }

    /// <summary>
    /// Response for availability check
    /// </summary>
    public class CheckAvailabilityResponse
    {
        public bool IsAvailable { get; set; }
        public string Reason { get; set; } = string.Empty;
        public List<string> Conflicts { get; set; } = new List<string>();
        public List<AvailableSlotResponse> AlternativeSlots { get; set; } =
            new List<AvailableSlotResponse>();
    }

    /// <summary>
    /// Request parameters for available providers
    /// </summary>
    public class AvailableProvidersParams
    {
        public DateTime Date { get; set; }
        public string? StartTime { get; set; }
        public string? EndTime { get; set; }
        public int? DurationMinutes { get; set; }
        public Guid? CategoryId { get; set; }
        public string? Location { get; set; }
        public decimal? MaxPrice { get; set; }
    }

    /// <summary>
    /// Response for available providers
    /// </summary>
    public class AvailableProvidersResponse
    {
        public List<AvailableProviderResponse> Providers { get; set; } =
            new List<AvailableProviderResponse>();
        public DateTime SearchDate { get; set; }
        public string? SearchTimeSlot { get; set; }
    }

    /// <summary>
    /// Available provider response
    /// </summary>
    public class AvailableProviderResponse
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string? ProfilePicture { get; set; }
        public decimal Rating { get; set; }
        public int ReviewCount { get; set; }
        public string Location { get; set; } = string.Empty;
        public List<string> Categories { get; set; } = new List<string>();
        public List<AvailableSlotResponse> AvailableSlots { get; set; } =
            new List<AvailableSlotResponse>();
        public decimal? HourlyRate { get; set; }
    }

    /// <summary>
    /// Request parameters for filtered calendar
    /// </summary>
    public class FilteredCalendarParams
    {
        public string? Status { get; set; }
        public Guid? CategoryId { get; set; }
        public bool? IncludeLeave { get; set; } = true;
        public bool? IncludeBookings { get; set; } = true;
        public bool? AvailableOnly { get; set; } = false;
    }

    /// <summary>
    /// Validator for CalendarRangeParams
    /// </summary>
    public class CalendarRangeParamsValidator : AbstractValidator<CalendarRangeParams>
    {
        public CalendarRangeParamsValidator()
        {
            RuleFor(x => x.StartDate).NotEmpty().WithMessage("Start date is required");

            RuleFor(x => x.EndDate)
                .NotEmpty()
                .GreaterThanOrEqualTo(x => x.StartDate)
                .WithMessage("End date must be greater than or equal to start date");

            RuleFor(x => x.EndDate)
                .Must((model, endDate) => (endDate - model.StartDate).TotalDays <= 365)
                .WithMessage("Date range cannot exceed 365 days");
        }
    }

    /// <summary>
    /// Validator for NextAvailableSlotsParams
    /// </summary>
    public class NextAvailableSlotsParamsValidator : AbstractValidator<NextAvailableSlotsParams>
    {
        public NextAvailableSlotsParamsValidator()
        {
            RuleFor(x => x.DurationMinutes)
                .GreaterThan(0)
                .LessThanOrEqualTo(480) // 8 hours max
                .WithMessage("Duration must be between 1 and 480 minutes");

            RuleFor(x => x.MaxResults)
                .GreaterThan(0)
                .LessThanOrEqualTo(100)
                .WithMessage("Max results must be between 1 and 100");

            RuleFor(x => x.MaxDaysAhead)
                .GreaterThan(0)
                .LessThanOrEqualTo(365)
                .WithMessage("Max days ahead must be between 1 and 365");
        }
    }

    /// <summary>
    /// Validator for CheckAvailabilityParams
    /// </summary>
    public class CheckAvailabilityParamsValidator : AbstractValidator<CheckAvailabilityParams>
    {
        public CheckAvailabilityParamsValidator()
        {
            RuleFor(x => x.Date)
                .NotEmpty()
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Date must be today or in the future");

            RuleFor(x => x.StartTime)
                .NotEmpty()
                .Matches(@"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")
                .WithMessage("Start time must be in HH:mm format");

            RuleFor(x => x.EndTime)
                .NotEmpty()
                .Matches(@"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")
                .WithMessage("End time must be in HH:mm format");
        }
    }

    /// <summary>
    /// Validator for AvailableProvidersParams
    /// </summary>
    public class AvailableProvidersParamsValidator : AbstractValidator<AvailableProvidersParams>
    {
        public AvailableProvidersParamsValidator()
        {
            RuleFor(x => x.Date)
                .NotEmpty()
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Date must be today or in the future");

            RuleFor(x => x.StartTime)
                .Matches(@"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")
                .WithMessage("Start time must be in HH:mm format")
                .When(x => !string.IsNullOrEmpty(x.StartTime));

            RuleFor(x => x.EndTime)
                .Matches(@"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")
                .WithMessage("End time must be in HH:mm format")
                .When(x => !string.IsNullOrEmpty(x.EndTime));

            RuleFor(x => x.DurationMinutes)
                .GreaterThan(0)
                .LessThanOrEqualTo(480)
                .WithMessage("Duration must be between 1 and 480 minutes")
                .When(x => x.DurationMinutes.HasValue);

            RuleFor(x => x.MaxPrice)
                .GreaterThan(0)
                .WithMessage("Max price must be greater than 0")
                .When(x => x.MaxPrice.HasValue);
        }
    }
}
