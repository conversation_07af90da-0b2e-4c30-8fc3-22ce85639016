﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

// Command
public record UpdateAvailabilityCommand(
    Guid UserId,
    Guid AvailabilityId,
    bool IsAvailable,
    List<AvailabilitySlot> Slots
) : ICommand<Result>;

// Handler
public class UpdateAvailabilityCommandHandler : ICommandHandler<UpdateAvailabilityCommand, Result>
{
    private readonly IAvailabilityService _availabilityService;

    public UpdateAvailabilityCommandHandler(IAvailabilityService availabilityService)
    {
        _availabilityService = availabilityService;
    }

    public async Task<Result> Handle(UpdateAvailabilityCommand request, CancellationToken ct)
    {
        return await _availabilityService.UpdateAvailabilityAsync(
            request.UserId,
            request.AvailabilityId,
            request.IsAvailable,
            request.Slots
        );
    }
}
