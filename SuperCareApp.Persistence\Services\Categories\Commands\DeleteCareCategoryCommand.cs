﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to delete a care category
    /// </summary>
    public record DeleteCareCategoryCommand(Guid CategoryId, Guid UserId) : ICommand<Result>;

    /// <summary>
    /// Handler for the DeleteCareCategoryCommand
    /// </summary>
    public sealed class DeleteCareCategoryCommandHandler
        : ICommandHandler<DeleteCareCategoryCommand, Result>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<DeleteCareCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public DeleteCareCategoryCommandHandler(
            ICareCategoryService categoryService,
            ILogger<DeleteCareCategoryCommandHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result> Handle(
            DeleteCareCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _categoryService.DeleteCategoryAsync(
                    request.CategoryId,
                    request.UserId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error deleting care category with ID {CategoryId}",
                    request.CategoryId
                );
                return Result.Failure(Error.Internal(ex.Message));
            }
        }
    }
}
