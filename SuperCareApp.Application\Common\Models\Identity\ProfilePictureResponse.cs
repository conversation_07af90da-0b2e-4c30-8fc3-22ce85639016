﻿namespace SuperCareApp.Application.Common.Models.Identity;

/// <summary>
/// Response model for profile picture operations
/// </summary>
public class ProfilePictureResponse
{
    /// <summary>
    /// URL to access the profile picture
    /// </summary>
    public string ImageUrl { get; set; } = string.Empty;

    /// <summary>
    /// File name of the profile picture
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Date and time when the profile picture was uploaded
    /// </summary>
    public DateTime UploadedAt { get; set; }
}
