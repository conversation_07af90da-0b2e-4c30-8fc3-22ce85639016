﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin.Commands
{
    public record RejectRequestCommand(
        Guid RequestId,
        Guid AdminId,
        string RejectionReason,
        string? Notes = null
    ) : ICommand<Result<Approval>>;

    internal sealed class RejectRequestCommandHandler
        : ICommandHandler<RejectRequestCommand, Result<Approval>>
    {
        private readonly IApprovalService _approvalService;
        private readonly ICareProviderProfileService _careProviderProfileService;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<RejectRequestCommandHandler> _logger;

        public RejectRequestCommandHandler(
            IApprovalService approvalService,
            ICareProviderProfileService careProviderProfileService,
            ApplicationDbContext context,
            ILogger<RejectRequestCommandHandler> logger
        )
        {
            _approvalService = approvalService;
            _careProviderProfileService = careProviderProfileService;
            _context = context;
            _logger = logger;
        }

        public async Task<Result<Approval>> Handle(
            RejectRequestCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Get the approval request
                var approvalResult = await _approvalService.GetApprovalByIdAsync(request.RequestId);
                if (approvalResult.IsFailure)
                {
                    return Result.Failure<Approval>(approvalResult.Error);
                }

                var approval = approvalResult.Value;

                // Reject the request
                var result = await _approvalService.RejectAsync(
                    request.RequestId,
                    request.AdminId,
                    request.RejectionReason,
                    request.Notes
                );

                if (result.IsFailure)
                {
                    return Result.Failure<Approval>(result.Error);
                }

                // If this is a care provider verification, update the care provider profile
                if (
                    approval.ApprovalType == ApprovalType.CareProviderVerification
                    && approval.RelatedEntityId.HasValue
                )
                {
                    // Get the care provider profile
                    var careProviderProfile =
                        await _context.CareProviderProfiles.FirstOrDefaultAsync(cp =>
                            cp.Id == approval.RelatedEntityId.Value
                        );

                    if (careProviderProfile != null)
                    {
                        // Update verification status
                        careProviderProfile.VerificationStatus = VerificationStatus.Rejected;
                        careProviderProfile.UpdatedAt = DateTime.UtcNow;
                        careProviderProfile.UpdatedBy = request.AdminId;

                        await _context.SaveChangesAsync(cancellationToken);

                        _logger.LogInformation(
                            "Care provider profile {ProfileId} rejected by admin {AdminId}",
                            careProviderProfile.Id,
                            request.AdminId
                        );
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting request {RequestId}", request.RequestId);
                return Result.Failure<Approval>(Error.Internal(ex.Message));
            }
        }
    }
}
