using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;

namespace SuperCareApp.Persistence.Services.Bookings.Commands
{
    /// <summary>
    /// Command to cancel a booking
    /// </summary>
    /// <param name="UserId">The ID of the user cancelling the booking</param>
    /// <param name="BookingId">The ID of the booking to cancel</param>
    /// <param name="Reason">Optional reason for cancellation</param>
    /// <param name="NotifyOtherParty">Whether to notify the other party</param>
    /// <param name="ForceCancel">Whether to force cancel even within cancellation window</param>
    public record CancelBookingCommand(
        Guid UserId,
        Guid BookingId,
        string? Reason = null,
        bool NotifyOtherParty = true,
        bool ForceCancel = false
    ) : ICommand<Result>;

    /// <summary>
    /// Handler for CancelBookingCommand
    /// </summary>
    public class CancelBookingCommandHandler : ICommandHandler<CancelBookingCommand, Result>
    {
        private readonly IBookingService _bookingService;
        private readonly ILogger<CancelBookingCommandHandler> _logger;

        public CancelBookingCommandHandler(
            IBookingService bookingService,
            ILogger<CancelBookingCommandHandler> logger
        )
        {
            _bookingService = bookingService;
            _logger = logger;
        }

        public async Task<Result> Handle(
            CancelBookingCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _bookingService.CancelBookingAsync(
                    request.UserId,
                    request.BookingId,
                    request.Reason,
                    request.NotifyOtherParty,
                    request.ForceCancel
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error handling CancelBookingCommand for booking {BookingId}",
                    request.BookingId
                );
                return Result.Failure(Error.Internal("Error cancelling booking"));
            }
        }
    }
}
