﻿using System.Reflection;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Context
{
    public class ApplicationDbContext
        : IdentityDbContext<
            ApplicationUser,
            ApplicationRole,
            Guid,
            ApplicationUserClaim,
            ApplicationUserRole,
            ApplicationUserLogin,
            ApplicationRoleClaim,
            ApplicationUserToken
        >
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options) { }

        // DbSets for entities
        public DbSet<UserProfile> UserProfiles => Set<UserProfile>();
        public DbSet<Document> Documents => Set<Document>();
        public DbSet<Booking> Bookings => Set<Booking>();
        public DbSet<BookingStatus> BookingStatuses => Set<BookingStatus>();
        public DbSet<BookingWindow> BookingWindows => Set<BookingWindow>();
        public DbSet<Payment> Payments => Set<Payment>();
        public DbSet<Subscription> Subscriptions => Set<Subscription>();
        public DbSet<OtpCode> OtpCodes => Set<OtpCode>();
        public DbSet<CareCategory> CareCategories => Set<CareCategory>();
        public DbSet<CareProviderProfile> CareProviderProfiles => Set<CareProviderProfile>();
        public DbSet<CareProviderCategory> CareProviderCategories => Set<CareProviderCategory>();
        public DbSet<Availability> Availabilities => Set<Availability>();
        public DbSet<AvailabilitySlot> AvailabilitySlots => Set<AvailabilitySlot>();
        public DbSet<Review> Reviews => Set<Review>();
        public DbSet<Conversation> Conversations => Set<Conversation>();
        public DbSet<Message> Messages => Set<Message>();
        public DbSet<Attachment> Attachments => Set<Attachment>();
        public DbSet<Notification> Notifications => Set<Notification>();
        public DbSet<TrackingSession> TrackingSessions => Set<TrackingSession>();
        public DbSet<Address> Addresses => Set<Address>();
        public DbSet<UserAddress> UserAddresses => Set<UserAddress>();
        public DbSet<Approval> Approvals => Set<Approval>();
        public DbSet<Leave> Leaves => Set<Leave>();
        public DbSet<AuditLog> AuditLogs => Set<AuditLog>();
        public DbSet<Invoice> Invoices => Set<Invoice>();
        public DbSet<OutboxMessage> OutboxMessages => Set<OutboxMessage>();

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
                throw new InvalidOperationException("DbContext is not configured.");

            optionsBuilder.UseSnakeCaseNamingConvention();

            // Uncomment this line if you want to enable lazy loading.
            // optionsBuilder.UseLazyLoadingProxies();
            // Uncomment this line if you want to use a different database provider.
            // optionsBuilder.UseSqlServer(connectionString);
            // Uncomment this line if you want to use a different connection string.
            // optionsBuilder.UseNpgsql(connectionString);

            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Identity tables
            modelBuilder.Entity<ApplicationUser>().ToTable("users");
            modelBuilder.Entity<ApplicationRole>().ToTable("roles");
            modelBuilder.Entity<ApplicationUserRole>().ToTable("user_roles");
            modelBuilder.Entity<ApplicationUserClaim>().ToTable("user_claims");
            modelBuilder.Entity<ApplicationUserLogin>().ToTable("user_logins");
            modelBuilder.Entity<ApplicationRoleClaim>().ToTable("role_claims");
            modelBuilder.Entity<ApplicationUserToken>().ToTable("user_tokens");

            // Apply configurations
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Add global query filters to related entities
            modelBuilder.Entity<ApplicationUserClaim>().HasQueryFilter(e => !e.User.IsDeleted);
            modelBuilder.Entity<ApplicationUserLogin>().HasQueryFilter(e => !e.User.IsDeleted);
            modelBuilder.Entity<ApplicationUserToken>().HasQueryFilter(e => !e.User.IsDeleted);
            modelBuilder
                .Entity<ApplicationUserRole>()
                .HasQueryFilter(e => !e.User.IsDeleted && !e.Role.IsDeleted);
            modelBuilder.Entity<ApplicationRoleClaim>().HasQueryFilter(e => !e.Role.IsDeleted);
        }
    }
}
