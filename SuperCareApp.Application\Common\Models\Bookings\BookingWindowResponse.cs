using System.Text.Json.Serialization;

namespace SuperCareApp.Application.Common.Models.Bookings;

public class BookingWindowResponse
{
    public Guid BookingWindowId { get; set; }
    public DateTime Date { get; set; }
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public string? Status { get; set; }
    public string? InvoiceFileName { get; set; }
    public string? InvoiceFileUrl { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public Guid? TrackingSessionId { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? TrackingSessionStatus { get; set; }
}
