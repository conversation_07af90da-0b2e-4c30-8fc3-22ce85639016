﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

public class InvoiceConfiguration : IEntityTypeConfiguration<Invoice>
{
    public void Configure(EntityTypeBuilder<Invoice> builder)
    {
        // Primary key from base entity
        builder.HasKey(i => i.Id);

        // Required properties
        builder.Property(i => i.BookingWindowId).IsRequired();

        builder.Property(i => i.InvoiceNumber).IsRequired().HasMaxLength(50);

        builder.Property(i => i.InvoiceDate).IsRequired();

        builder.Property(i => i.DueDate).IsRequired();

        builder.Property(i => i.Status).IsRequired().HasConversion<string>();

        // Decimal properties with precision
        builder.Property(i => i.Amount).IsRequired().HasColumnType("decimal(18,2)");

        builder.Property(i => i.Tax).IsRequired().HasColumnType("decimal(18,2)");

        builder.Property(i => i.TotalAmount).IsRequired().HasColumnType("decimal(18,2)");

        builder.Property(i => i.Currency).IsRequired().HasMaxLength(3); // ISO 4217 currency code
        // FileName and FileUrl properties
        builder.Property(i => i.FileName).HasMaxLength(255).IsRequired(false); // Nullable, as it may not be set until PDF is generated

        builder.Property(i => i.FileUrl).HasMaxLength(500).IsRequired(false); // Nullable, as it may not be set until PDF is generated

        // Relationships
        builder
            .HasOne(i => i.BookingWindow)
            .WithOne(bw => bw.Invoice)
            .HasForeignKey<Invoice>(i => i.BookingWindowId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasMany(i => i.Payments)
            .WithOne(p => p.Invoice)
            .HasForeignKey(p => p.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes for performance
        builder.HasIndex(i => i.InvoiceNumber).IsUnique();
        builder.HasIndex(i => i.BookingWindowId);
        builder.HasIndex(i => i.Status);
        builder.HasIndex(i => i.DueDate);
    }
}
