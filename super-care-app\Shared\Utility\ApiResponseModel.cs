using System.Text.Json.Serialization;

namespace SuperCareApp.Shared.Utility
{
    /// <summary>
    /// Standard response model for API
    /// </summary>
    public class ApiResponseModel<T>
    {
        /// <summary>
        /// Unique identifier for the API response
        /// </summary>
        [JsonPropertyName("apiResponseId")]
        public Guid ApiResponseId { get; set; }

        /// <summary>
        /// Indicates whether the operation was successful
        /// </summary>
        [JsonPropertyName("success")]
        public bool? Success { get; set; }

        /// <summary>
        /// HTTP status code
        /// </summary>
        [JsonPropertyName("statusCode")]
        public int StatusCode { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Response payload
        /// </summary>
        [JsonPropertyName("payload")]
        public T? Payload { get; set; }

        /// <summary>
        /// Timestamp of when the response was created
        /// </summary>
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public ApiResponseModel()
        {
            ApiResponseId = Guid.NewGuid();
        }

        public ApiResponseModel(
            ApiResponseStatusEnum status,
            string message,
            T? payload,
            string? statusCode = null,
            Guid? apiResponseId = null
        )
        {
            ApiResponseId = apiResponseId ?? Guid.NewGuid();
            Success = IsSuccessStatus(status);
            StatusCode = statusCode != null ? Convert.ToInt32(statusCode) : GetStatusCode(status);
            Message = message;
            Payload = payload;
            Timestamp = DateTime.UtcNow;
        }

        private int GetStatusCode(ApiResponseStatusEnum status) =>
            status switch
            {
                ApiResponseStatusEnum.Warning => 429,
                ApiResponseStatusEnum.BadRequest => 400,
                ApiResponseStatusEnum.NotFound => 404,
                ApiResponseStatusEnum.Unauthorized => 401,
                ApiResponseStatusEnum.Forbidden => 403,
                ApiResponseStatusEnum.InternalServerError => 500,
                ApiResponseStatusEnum.Success => 200,
                ApiResponseStatusEnum.Info => 100,
                ApiResponseStatusEnum.Error => 500,
                _ => 0,
            };

        private bool IsSuccessStatus(ApiResponseStatusEnum status) =>
            status switch
            {
                ApiResponseStatusEnum.Success => true,
                ApiResponseStatusEnum.Info => true,
                _ => false,
            };
    }

    /// <summary>
    /// Paginated response model for API
    /// </summary>
    public class PaginatedResponseModel<T> : ApiResponseModel<T>
    {
        /// <summary>
        /// Pagination metadata
        /// </summary>
        [JsonPropertyName("meta")]
        public PaginationMetadata Meta { get; set; }

        public PaginatedResponseModel(
            ApiResponseStatusEnum status,
            string message,
            T? data,
            PaginationMetadata meta,
            string? statusCode = null,
            Guid? apiResponseId = null
        )
            : base(status, message, data, statusCode, apiResponseId)
        {
            Meta = meta;
        }

        public PaginatedResponseModel(
            ApiResponseStatusEnum status,
            string message,
            T? data,
            int currentPage,
            int totalPages,
            int totalCount,
            int pageSize,
            string? statusCode = null,
            Guid? apiResponseId = null
        )
            : base(status, message, data, statusCode, apiResponseId)
        {
            Meta = new PaginationMetadata(currentPage, totalPages, totalCount, pageSize);
        }
    }

    /// <summary>
    /// Enumeration of API response status types
    /// </summary>
    public enum ApiResponseStatusEnum
    {
        NotSet = 0,
        Success,
        Warning,
        Info,
        Error,
        NotFound,
        BadRequest,
        Unauthorized,
        Forbidden,
        InternalServerError,
    }
}
