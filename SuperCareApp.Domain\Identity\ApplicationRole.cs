﻿using Microsoft.AspNetCore.Identity;
using SuperCareApp.Domain.Common;

namespace SuperCareApp.Domain.Identity
{
    public class ApplicationRole : IdentityRole<Guid>
    {
        public ApplicationRole()
            : base() { }

        public ApplicationRole(string roleName)
            : base(roleName) { }

        // Audit properties from BaseEntity
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? UpdatedBy { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public Guid? DeletedBy { get; set; }
    }
}
