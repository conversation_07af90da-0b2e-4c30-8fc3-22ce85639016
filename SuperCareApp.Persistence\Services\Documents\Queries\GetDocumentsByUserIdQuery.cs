﻿using SuperCareApp.Application.Common.Interfaces.Documents;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Documents;

namespace SuperCareApp.Persistence.Services.Documents.Queries;

/// <summary>
/// Query to get all documents for a user
/// </summary>
/// <param name="UserId">ID of the user whose documents to retrieve</param>
/// <param name="IncludeUnapproved">Whether to include unapproved documents</param>
public record GetDocumentsByUserIdQuery(Guid UserId, bool IncludeUnapproved = false)
    : IQuery<Result<IEnumerable<DocumentResponse>>>;

/// <summary>
/// Handler for the GetDocumentsByUserIdQuery
/// </summary>
internal sealed class GetDocumentsByUserIdQueryHandler
    : IQueryHandler<GetDocumentsByUserIdQuery, Result<IEnumerable<DocumentResponse>>>
{
    private readonly IDocumentService _documentService;
    private readonly ILogger<GetDocumentsByUserIdQueryHandler> _logger;

    public GetDocumentsByUserIdQueryHandler(
        IDocumentService documentService,
        ILogger<GetDocumentsByUserIdQueryHandler> logger
    )
    {
        _documentService = documentService;
        _logger = logger;
    }

    public async Task<Result<IEnumerable<DocumentResponse>>> Handle(
        GetDocumentsByUserIdQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var documents = await _documentService.GetAllDocumentsByUserIdAsync(
                request.UserId,
                true
            );
            return documents;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents for user {UserId}", request.UserId);
            return Result.Failure<IEnumerable<DocumentResponse>>(
                Error.Internal("An error occurred while retrieving the documents")
            );
        }
    }
}
