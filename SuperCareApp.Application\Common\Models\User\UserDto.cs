﻿using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.User
{
    /// <summary>
    /// Data transfer object for User entity
    /// </summary>
    public class UserDto
    {
        public Guid Id { get; set; }
        public string Email { get; set; } = string.Empty;
        public bool EmailVerified { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastLogin { get; set; }
        public string? AuthProvider { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public UserProfileDto? Profile { get; set; }
        public List<UserRoleType> Roles { get; set; } = new List<UserRoleType>();
    }
}
