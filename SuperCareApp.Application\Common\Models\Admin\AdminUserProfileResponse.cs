﻿using System.Text.Json.Serialization;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Admin
{
    /// <summary>
    /// Response model for user profiles in admin endpoints
    /// </summary>
    public class AdminUserProfileResponse
    {
        /// <summary>
        /// User ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Provider ID (if applicable)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public Guid? ProviderId { get; set; }

        /// <summary>
        /// User's full name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// User's phone number
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User's gender
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// User's date of birth
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// URL to user's profile picture
        /// </summary>
        public string? ProfilePictureUrl { get; set; }

        /// <summary>
        /// Whether the user is a care provider
        /// </summary>
        public bool IsCareProvider { get; set; }

        /// <summary>
        /// Years of experience (for care providers)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
        public int YearsExperience { get; set; }

        /// <summary>
        /// Verification status as string (for care providers)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? VerificationStatus { get; set; }

        /// <summary>
        /// User's roles
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();

        /// <summary>
        /// Whether the user's email is verified
        /// </summary>
        public bool EmailVerified { get; set; }

        /// <summary>
        /// Whether the user is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// When the user was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the user last logged in
        /// </summary>
        public DateTime? LastLogin { get; set; }
    }
}
