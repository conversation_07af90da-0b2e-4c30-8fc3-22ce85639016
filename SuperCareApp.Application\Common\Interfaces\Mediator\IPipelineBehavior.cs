namespace SuperCareApp.Application.Common.Interfaces.Mediator;

/// <summary>
/// Delegate representing a request handler execution.
/// </summary>
/// <typeparam name="TResponse">Response type.</typeparam>
/// <returns>A task containing the response.</returns>
public delegate Task<TResponse> RequestHandlerDelegate<TResponse>();

/// <summary>
/// Delegate representing a void request handler execution.
/// </summary>
/// <returns>A task representing the asynchronous operation.</returns>
public delegate Task RequestHandlerDelegate();

/// <summary>
/// Pipeline behavior for requests that return a response.
/// </summary>
/// <typeparam name="TRequest">Request type.</typeparam>
/// <typeparam name="TResponse">Response type.</typeparam>
public interface IPipelineBehavior<in TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    /// <summary>
    /// Pipeline handler. Handles the request and calls the next middleware in the pipeline.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="next">The next handler in the pipeline.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>Response from the request.</returns>
    Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken
    );
}

/// <summary>
/// Pipeline behavior for requests that do not return a response.
/// </summary>
/// <typeparam name="TRequest">Request type.</typeparam>
public interface IPipelineBehavior<in TRequest>
    where TRequest : IRequest
{
    /// <summary>
    /// Pipeline handler. Handles the request and calls the next middleware in the pipeline.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="next">The next handler in the pipeline.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// /// <returns>Task representing the asynchronous operation.</returns>
    Task Handle(TRequest request, RequestHandlerDelegate next, CancellationToken cancellationToken);
}
