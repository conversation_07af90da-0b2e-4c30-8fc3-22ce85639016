﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities.ValueObjects;

namespace SuperCareApp.Persistence.Services.Bookings;

internal sealed class ProviderAvailabilityReadModel : IProviderAvailabilityReadModel
{
    private readonly ApplicationDbContext _ctx;

    public ProviderAvailabilityReadModel(ApplicationDbContext ctx) => _ctx = ctx;

    public async Task<ProviderAvailabilitySnapshot> GetAsync(
        Guid providerId,
        DateOnly date,
        CancellationToken ct
    )
    {
        var day = date.DayOfWeek.ToString();
        var bufferTask = _ctx
            .CareProviderProfiles.Where(p => p.Id == providerId)
            .Select(p => p.BufferDuration)
            .SingleOrDefaultAsync(ct);

        var onLeave = await _ctx.Leaves.AnyAsync(
            l =>
                l.ProviderId == providerId
                && date >= DateOnly.FromDateTime(l.StartDate)
                && date <= DateOnly.FromDateTime(l.EndDate),
            ct
        );

        var availabilityTask = _ctx
            .Availabilities.Where(a => a.ProviderId == providerId && a.DayOfWeek == day)
            .Select(a => new { a.IsAvailable, Slots = a.AvailabilitySlots })
            .SingleOrDefaultAsync(ct);

        var bookingsTask = _ctx
            .BookingWindows.Where(b => b.Booking.ProviderId == providerId && b.Date == date)
            .Select(b => new { b.StartTime, b.EndTime })
            .ToListAsync(ct);

        await Task.WhenAll(bufferTask, availabilityTask, bookingsTask);

        var buffer = await bufferTask;
        var av = await availabilityTask;
        var bookings = await bookingsTask;

        return new ProviderAvailabilitySnapshot
        {
            IsOnLeave = onLeave,
            IsAvailableOnWeekday = av?.IsAvailable ?? false,
            AvailableRanges =
                av?.Slots.Select(s => new TimeRange(s.StartTime, s.EndTime)).ToList()
                ?? new List<TimeRange>(),
            BookedSlots = bookings.Select(b => new TimeRange(b.StartTime, b.EndTime)).ToList(),
            BufferMinutes = buffer,
        };
    }
}
