﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Context;

namespace SuperCareApp.Persistence.Services.Admin.Commands
{
    /// <summary>
    /// Command to remove suspension from a care provider
    /// </summary>
    /// <param name="ProviderId">The ID of the care provider to reinstate</param>
    /// <param name="AdminId">The ID of the admin performing the action</param>
    /// <param name="RemovalReason">Optional reason for removing the suspension</param>
    /// <param name="Notes">Optional additional notes</param>
    public record RemoveSuspensionCommand(
        Guid ProviderId,
        Guid AdminId,
        string? RemovalReason,
        string? Notes
    ) : ICommand<Result<CareProviderProfile>>;

    /// <summary>
    /// Handler for the RemoveSuspensionCommand
    /// </summary>
    public class RemoveSuspensionCommandHandler
        : ICommandHandler<RemoveSuspensionCommand, Result<CareProviderProfile>>
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<RemoveSuspensionCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public RemoveSuspensionCommandHandler(
            ApplicationDbContext context,
            ILogger<RemoveSuspensionCommandHandler> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command to remove suspension from a care provider
        /// </summary>
        public async Task<Result<CareProviderProfile>> Handle(
            RemoveSuspensionCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Find the care provider profile
                var providerProfile = await _context
                    .CareProviderProfiles.Include(cp => cp.User)
                    .FirstOrDefaultAsync(
                        cp => cp.Id == request.ProviderId && !cp.IsDeleted,
                        cancellationToken
                    );

                if (providerProfile == null)
                {
                    return Result.Failure<CareProviderProfile>(
                        Error.NotFound("Care provider profile not found")
                    );
                }

                // Check if the provider is currently suspended
                if (providerProfile.VerificationStatus != VerificationStatus.Suspended)
                {
                    return Result.Failure<CareProviderProfile>(
                        Error.Conflict("Care provider is not currently suspended")
                    );
                }

                // Update the verification status back to Verified
                providerProfile.VerificationStatus = VerificationStatus.Verified;

                // Update audit fields
                providerProfile.UpdatedAt = DateTime.UtcNow;
                providerProfile.UpdatedBy = request.AdminId;
                var approvalData = new
                {
                    removalReason = request.RemovalReason ?? "No reason provided",
                };

                // Create a record of the suspension removal for audit purposes
                var suspensionRemovalRecord = new Approval
                {
                    Id = Guid.NewGuid(),
                    UserId = providerProfile.UserId,
                    ApprovalType = ApprovalType.CareProviderVerification,
                    ApprovalData = JsonSerializer.Serialize(approvalData),
                    RelatedEntityId = providerProfile.Id,
                    IsApproved = true,
                    ProcessedBy = request.AdminId,
                    ProcessedAt = DateTime.UtcNow,
                    Notes = request.Notes,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = request.AdminId,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = request.AdminId,
                };

                await _context.Approvals.AddAsync(suspensionRemovalRecord, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                return Result.Success(providerProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error removing suspension from care provider {ProviderId}",
                    request.ProviderId
                );
                return Result.Failure<CareProviderProfile>(
                    Error.Internal($"Error removing suspension from care provider: {ex.Message}")
                );
            }
        }
    }
}
