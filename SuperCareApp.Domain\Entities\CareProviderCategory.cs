﻿using SuperCareApp.Domain.Common;

namespace SuperCareApp.Domain.Entities
{
    public class CareProviderCategory : BaseEntity
    {
        public Guid ProviderId { get; set; }
        public Guid CategoryId { get; set; }
        public string? ProviderSpecificDescription { get; set; }
        public decimal HourlyRate { get; set; }
        public int? MaxHoursPerWeek { get; set; }
        public int? MinHoursPerWeek { get; set; }
        public int? ExperienceYears { get; set; }

        // Navigation properties
        public CareProviderProfile CareProviderProfile { get; set; } = null!;
        public CareCategory CareCategory { get; set; } = null!;
    }
}
