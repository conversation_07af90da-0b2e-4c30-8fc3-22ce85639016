// Custom Swagger UI JavaScript to override the default authorization behavior
(function () {
    // Wait for Swagger UI to load
    const interval = setInterval(() => {
        if (window.ui) {
            clearInterval(interval);
            customizeSwaggerUI();
        }
    }, 100);

    function customizeSwaggerUI() {
        // Add styles for the custom auth form
        const style = document.createElement('style');
        style.textContent = `
            .swagger-ui .auth-wrapper .authorize {
                margin-right: 10px;
            }
            .swagger-ui .auth-container .auth-container-title {
                margin-bottom: 10px;
                font-size: 16px;
            }
            .swagger-ui .auth-btn-wrapper {
                display: flex;
                justify-content: flex-start;
                margin-top: 15px;
            }
            .swagger-ui .auth-btn-wrapper button {
                min-width: 80px;
            }
            .swagger-ui .auth-container .btn-done {
                margin-top: 15px;
            }
            .swagger-ui .auth-container input {
                width: 100%;
            }
            .swagger-ui .auth-container label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            .swagger-ui .auth-status {
                margin-top: 10px;
                padding: 8px;
                border-radius: 4px;
            }
            .swagger-ui .auth-status.success {
                color: #155724;
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
            }
            .swagger-ui .auth-status.error {
                color: #721c24;
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
            }
            .swagger-ui .email-password-auth {
                padding: 10px;
            }
            .swagger-ui .auth-form .form-group {
                margin-bottom: 10px;
            }
        `;
        document.head.appendChild(style);

        // Override the default authorize button click handler
        const originalAuthorizeActions = window.ui.authActions;

        // Store the original showDefinitions method
        const originalShowDefinitions = originalAuthorizeActions.showDefinitions;

        // Override the showDefinitions method
        originalAuthorizeActions.showDefinitions = function () {
            // Call the original method first
            const result = originalShowDefinitions.apply(this, arguments);

            // After the modal is shown, customize it
            setTimeout(() => {
                customizeAuthModal();
            }, 100);

            return result;
        };

        function customizeAuthModal() {
            // Get the auth modal container
            const authModal = document.querySelector('.auth-container');
            if (!authModal) {
                console.error('Auth modal not found');
                return;
            }

            // Find the value input field
            const valueInput = authModal.querySelector('input[type="text"]');
            if (!valueInput) {
                console.error('Value input not found');
                return;
            }

            // Find the authorize button
            const authorizeButton = authModal.querySelector('.btn.authorize');
            if (!authorizeButton) {
                console.error('Authorize button not found');
                return;
            }

            // Hide the original input field
            valueInput.style.display = 'none';

            // Create the email/password form
            const authForm = document.createElement('div');
            authForm.className = 'email-password-auth';
            authForm.innerHTML = `
                <div class="auth-form">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" class="form-control" placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" class="form-control" placeholder="Enter your password">
                    </div>
                    <div id="auth-status" class="auth-status"></div>
                </div>
            `;

            // Insert the form before the authorize button
            valueInput.parentNode.insertBefore(authForm, valueInput);

            // Override the authorize button click handler
            authorizeButton.addEventListener('click', async (e) => {
                e.preventDefault();
                e.stopPropagation();

                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const statusElement = document.getElementById('auth-status');

                if (!email || !password) {
                    statusElement.textContent = 'Please enter email and password';
                    statusElement.className = 'auth-status error';
                    return;
                }

                try {
                    statusElement.textContent = 'Authenticating...';
                    statusElement.className = 'auth-status';

                    const response = await fetch('/api/v1/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });

                    const data = await response.json();
                    console.log('Auth response:', data);

                    if (response.ok && data.success) {
                        // Extract the token from the response
                        const token = data.payload.accessToken ||
                            data.payload.token ||
                            (typeof data.payload === 'string' ? data.payload : null);

                        if (token) {
                            // Set the value in the original input field with proper Bearer prefix
                            valueInput.value = `Bearer ${token}`;

                            // Log the token for debugging
                            console.log('Setting token:', `Bearer ${token}`);

                            // Trigger the original authorize button click
                            setTimeout(() => {
                                // Use the original click handler
                                const originalClick = authorizeButton.onclick;
                                if (originalClick) {
                                    originalClick.call(authorizeButton);
                                } else {
                                    // Fallback: dispatch a click event
                                    authorizeButton.dispatchEvent(new MouseEvent('click', {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window
                                    }));
                                }

                                // Close the modal
                                const closeButton = document.querySelector('.auth-container .btn-done');
                                if (closeButton) {
                                    closeButton.click();
                                }

                                // Add a global Swagger UI interceptor to ensure the token is included in all requests
                                const originalFetch = window.fetch;
                                window.fetch = function (url, options = {}) {
                                    // Only intercept Swagger UI API calls
                                    if (url.toString().includes('/api/')) {
                                        options = options || {};
                                        options.headers = options.headers || {};

                                        // Add Authorization header if not already present
                                        if (!options.headers['Authorization']) {
                                            options.headers['Authorization'] = `Bearer ${token}`;
                                            console.log('Adding Authorization header to request:', url);
                                        }
                                    }

                                    return originalFetch(url, options);
                                };
                            }, 100);

                            // Store token in localStorage for persistence
                            localStorage.setItem('swagger_token', token);

                            statusElement.textContent = 'Authentication successful';
                            statusElement.className = 'auth-status success';
                        } else {
                            console.error('Token not found in response:', data);
                            statusElement.textContent = 'Authentication failed: Token not found in response';
                            statusElement.className = 'auth-status error';
                        }
                    } else {
                        statusElement.textContent = data.message || 'Authentication failed';
                        statusElement.className = 'auth-status error';
                    }
                } catch (error) {
                    console.error('Authentication error:', error);
                    statusElement.textContent = 'Authentication failed: ' + error.message;
                    statusElement.className = 'auth-status error';
                }
            });

            // Check for stored token and apply it
            const storedToken = localStorage.getItem('swagger_token');
            if (storedToken) {
                valueInput.value = `Bearer ${storedToken}`;

                // Automatically authorize if we have a stored token
                setTimeout(() => {
                    authorizeButton.click();
                }, 100);
            }
        }
    }
})();
