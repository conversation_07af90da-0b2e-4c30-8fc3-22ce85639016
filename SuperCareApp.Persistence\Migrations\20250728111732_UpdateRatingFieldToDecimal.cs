﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SuperCareApp.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRatingFieldToDecimal : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameIndex(
                name: "ix_reviews_reviewer_id",
                table: "reviews",
                newName: "IX_Reviews_ReviewerId"
            );

            migrationBuilder.RenameIndex(
                name: "ix_reviews_reviewee_id",
                table: "reviews",
                newName: "IX_Reviews_RevieweeId"
            );

            migrationBuilder.RenameIndex(
                name: "ix_reviews_booking_id_reviewer_id",
                table: "reviews",
                newName: "IX_Reviews_BookingId_ReviewerId"
            );

            migrationBuilder.AlterColumn<decimal>(
                name: "rating",
                table: "reviews",
                type: "numeric(3,2)",
                nullable: false,
                defaultValue: 0.0m,
                oldClrType: typeof(int),
                oldType: "integer",
                oldDefaultValue: 0
            );

            migrationBuilder.AlterColumn<string>(
                name: "comment",
                table: "reviews",
                type: "text",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(1000)",
                oldMaxLength: 1000,
                oldNullable: true
            );

            migrationBuilder.CreateIndex(
                name: "IX_Reviews_BookingId",
                table: "reviews",
                column: "booking_id"
            );

            migrationBuilder
                .CreateIndex(name: "ix_reviews_id", table: "reviews", column: "id")
                .Annotation("Npgsql:IndexInclude", new[] { "rating", "booking_id" });

            migrationBuilder.CreateIndex(
                name: "IX_Reviews_Rating",
                table: "reviews",
                column: "rating"
            );

            migrationBuilder.CreateIndex(
                name: "IX_Reviews_RevieweeId_Rating_CreatedAt",
                table: "reviews",
                columns: new[] { "reviewee_id", "rating", "created_at" }
            );
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(name: "IX_Reviews_BookingId", table: "reviews");

            migrationBuilder.DropIndex(name: "ix_reviews_id", table: "reviews");

            migrationBuilder.DropIndex(name: "IX_Reviews_Rating", table: "reviews");

            migrationBuilder.DropIndex(
                name: "IX_Reviews_RevieweeId_Rating_CreatedAt",
                table: "reviews"
            );

            migrationBuilder.RenameIndex(
                name: "IX_Reviews_ReviewerId",
                table: "reviews",
                newName: "ix_reviews_reviewer_id"
            );

            migrationBuilder.RenameIndex(
                name: "IX_Reviews_RevieweeId",
                table: "reviews",
                newName: "ix_reviews_reviewee_id"
            );

            migrationBuilder.RenameIndex(
                name: "IX_Reviews_BookingId_ReviewerId",
                table: "reviews",
                newName: "ix_reviews_booking_id_reviewer_id"
            );

            migrationBuilder.AlterColumn<int>(
                name: "rating",
                table: "reviews",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(decimal),
                oldType: "numeric(3,2)",
                oldDefaultValue: 0.0m
            );

            migrationBuilder.AlterColumn<string>(
                name: "comment",
                table: "reviews",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldMaxLength: 1000,
                oldNullable: true
            );
        }
    }
}
