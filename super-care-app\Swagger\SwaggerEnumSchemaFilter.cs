using System.Text.Json.Serialization;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Swagger schema filter to improve the display of enums
    /// </summary>
    public class SwaggerEnumSchemaFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (context.Type.IsEnum)
            {
                // Add enum values to description
                schema.Description = $"{schema.Description ?? context.Type.Name} enum values:";

                // Get enum names and values
                var enumValues = Enum.GetValues(context.Type);
                var enumNames = Enum.GetNames(context.Type);

                // Add enum values to description
                for (int i = 0; i < enumNames.Length; i++)
                {
                    var enumValue = Convert.ToInt32(enumValues.GetValue(i));
                    var enumName = enumNames[i];

                    // Check for JsonPropertyName attribute
                    var memberInfo = context.Type.GetMember(enumName).FirstOrDefault();
                    var jsonPropertyAttribute =
                        memberInfo
                            ?.GetCustomAttributes(typeof(JsonPropertyNameAttribute), false)
                            .FirstOrDefault() as JsonPropertyNameAttribute;

                    var displayName = jsonPropertyAttribute?.Name ?? enumName.ToLowerInvariant();

                    schema.Description += $"<br/>{enumValue} = {displayName}";
                }

                // Set schema type to string if using string enum values
                var enumMember = context.Type.GetMembers().FirstOrDefault();
                var hasJsonConverter =
                    enumMember
                        ?.GetCustomAttributes(typeof(JsonConverterAttribute), false)
                        .Any(attr =>
                            ((JsonConverterAttribute)attr).ConverterType.Name
                            == "JsonStringEnumConverter"
                        ) ?? false;

                if (hasJsonConverter)
                {
                    schema.Type = "string";
                    schema.Enum = enumNames
                        .Select(name => new OpenApiString(name.ToLowerInvariant()))
                        .Cast<IOpenApiAny>()
                        .ToList();
                }
                else
                {
                    schema.Type = "integer";
                    schema.Format = "int32";
                    schema.Enum = enumValues
                        .Cast<int>()
                        .Select(value => new OpenApiInteger(value))
                        .Cast<IOpenApiAny>()
                        .ToList();
                }
            }
        }
    }
}
