using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Models.Doc;
using super_care_app.Shared.Constants;
using super_care_app.Shared.Utility;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Admin.Commands;
using SuperCareApp.Persistence.Services.Admin.Queries;
using SuperCareApp.Persistence.Services.Categories.Commands;
using SuperCareApp.Persistence.Services.Documents.Commands;
using SuperCareApp.Persistence.Services.Documents.Queries;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.Filters;

namespace super_care_app.Controllers
{
    /// <summary>
    /// Controller for admin operations
    /// </summary>
    [Authorize(Roles = "Admin")]
    public class AdminController : BaseController
    {
        private readonly IMediator _mediator;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<AdminController> _logger;
        private readonly RequestValidator _requestValidator;

        public AdminController(
            IMediator mediator,
            ICurrentUserService currentUserService,
            ILogger<AdminController> logger,
            RequestValidator requestValidator
        )
        {
            _mediator = mediator;
            _currentUserService = currentUserService;
            _logger = logger;
            _requestValidator = requestValidator;
        }

        /// <summary>
        /// Gets a paginated list of user profiles
        /// </summary>
        /// <param name="parameters">Filter and pagination parameters</param>
        /// <returns>Paginated list of user profiles</returns>
        [HttpGet(ApiRoutes.Admin.GetUserProfiles)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(PaginatedResponseModel<IEnumerable<AdminUserProfileResponse>>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets a paginated list of user profiles",
            Description = "Returns a paginated list of user profiles with filtering options",
            OperationId = "Admin_GetUserProfiles",
            Tags = new[] { "Admin" }
        )]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AdminExamples))]
        public async Task<IActionResult> GetUserProfiles(
            [FromQuery] UserProfileListParams parameters
        )
        {
            var query = new GetUserProfilesQuery(parameters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<PagedUserProfileList>(result.Error);
            }

            return PaginatedResponse(
                result.Value.Profiles,
                result.Value.ToMetadata(),
                "User profiles retrieved successfully"
            );
        }

        /// <summary>
        /// Gets a paginated list of approval requests
        /// </summary>
        /// <param name="parameters">Filter and pagination parameters</param>
        /// <returns>Paginated list of approval requests</returns>
        [HttpGet(ApiRoutes.Admin.GetApprovalRequests)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(PaginatedResponseModel<PagedApprovalRequestList>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets a paginated list of approval requests",
            Description = "Returns a paginated list of approval requests with filtering options",
            OperationId = "Admin_GetApprovalRequests",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> GetApprovalRequests(
            [FromQuery] ApprovalRequestListParams parameters
        )
        {
            var query = new GetApprovalRequestsQuery(parameters);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<PagedApprovalRequestList>(result.Error);
            }

            return PaginatedResponse(
                result.Value.Requests,
                result.Value.ToMetadata(),
                "Approval requests retrieved successfully"
            );
        }

        /// <summary>
        /// Gets an approval request by ID
        /// </summary>
        /// <param name="requestId">Approval request ID</param>
        /// <returns>Approval request details</returns>
        [HttpGet(ApiRoutes.Admin.GetApprovalRequest)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<ApprovalRequestResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets an approval request by ID",
            Description = "Returns the details of a specific approval request",
            OperationId = "Admin_GetApprovalRequest",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> GetApprovalRequest(Guid requestId)
        {
            var query = new GetApprovalRequestByIdQuery(requestId);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<ApprovalRequestResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Approval request retrieved successfully");
        }

        /// <summary>
        /// Approves an approval request
        /// </summary>
        /// <param name="requestId">Approval request ID</param>
        /// <param name="request">Acceptance details</param>
        /// <returns>Updated approval request</returns>
        [HttpPost(ApiRoutes.Admin.ApproveRequest)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<ApprovalRequestResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Approves an approval request",
            Description = "Approves a pending approval request and updates related entities",
            OperationId = "Admin_ApproveRequest",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> ApproveRequest(
            Guid requestId,
            [FromBody] AcceptRequestModel request
        )
        {
            // Get the current admin ID (in a real app, this would come from the authenticated user)
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<ApprovalRequestResponse>("User is not authenticated");
            }

            var command = new ApproveRequestCommand(requestId, adminId.Value, request.Notes);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<ApprovalRequestResponse>(result.Error);
            }

            // Get the updated approval request
            var query = new GetApprovalRequestByIdQuery(requestId);
            var queryResult = await _mediator.Send(query);

            if (queryResult.IsFailure)
            {
                return ErrorResponseFromError<ApprovalRequestResponse>(queryResult.Error);
            }

            return SuccessResponse(queryResult.Value, "Approval request approved successfully");
        }

        /// <summary>
        /// Rejects an approval request
        /// </summary>
        /// <param name="requestId">Approval request ID</param>
        /// <param name="request">Rejection details</param>
        /// <returns>Updated approval request</returns>
        [HttpPost(ApiRoutes.Admin.RejectRequest)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<ApprovalRequestResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Rejects an approval request",
            Description = "Rejects a pending approval request and updates related entities",
            OperationId = "Admin_RejectRequest",
            Tags = new[] { "Admin" }
        )]
        public async Task<IActionResult> RejectRequest(
            Guid requestId,
            [FromBody] RejectRequestModel request
        )
        {
            if (string.IsNullOrWhiteSpace(request.RejectionReason))
            {
                return BadRequest(
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.BadRequest,
                        "Rejection reason is required",
                        null
                    )
                );
            }

            // Get the current admin ID (in a real app, this would come from the authenticated user)
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<ApprovalRequestResponse>("User is not authenticated");
            }

            var command = new RejectRequestCommand(
                requestId,
                adminId.Value,
                request.RejectionReason,
                request.Notes
            );

            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<ApprovalRequestResponse>(result.Error);
            }

            // Get the updated approval request
            var query = new GetApprovalRequestByIdQuery(requestId);
            var queryResult = await _mediator.Send(query);

            if (queryResult.IsFailure)
            {
                return ErrorResponseFromError<ApprovalRequestResponse>(queryResult.Error);
            }

            return SuccessResponse(queryResult.Value, "Approval request rejected successfully");
        }

        /// <summary>
        /// Gets all pending documents awaiting approval
        /// </summary>
        /// <returns>List of pending documents</returns>
        [HttpGet(ApiRoutes.Admin.GetPendingDocuments)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<IEnumerable<DocumentResponse>>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets all pending documents awaiting approval",
            Description = "Returns a list of all documents with pending verification status"
        )]
        public async Task<IActionResult> GetPendingDocuments()
        {
            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<IEnumerable<DocumentResponse>>(
                    "User is not authenticated"
                );
            }

            var query = new GetPendingDocumentsQuery();
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<IEnumerable<DocumentResponse>>(result.Error);
            }

            return SuccessResponse(result.Value, "Pending documents retrieved successfully");
        }

        /// <summary>
        /// Approves a document
        /// </summary>
        /// <param name="documentId">Document ID</param>
        /// <param name="request">Approval details</param>
        /// <returns>Updated document</returns>
        [HttpPost(ApiRoutes.Admin.ApproveDocument)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<DocumentResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Approves a document",
            Description = "Approves a document and updates its verification status"
        )]
        public async Task<IActionResult> ApproveDocument(
            [FromRoute] Guid documentId,
            [FromBody] DocumentApprovalRequest request
        )
        {
            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<DocumentResponse>("User is not authenticated");
            }

            var command = new ApproveDocumentCommand(documentId, adminId.Value);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<DocumentResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Document approved successfully");
        }

        /// <summary>
        /// Rejects a document
        /// </summary>
        /// <param name="documentId">Document ID</param>
        /// <param name="request">Rejection details</param>
        /// <returns>Updated document</returns>
        [HttpPost(ApiRoutes.Admin.RejectDocument)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<DocumentResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Rejects a document",
            Description = "Rejects a document and updates its verification status"
        )]
        public async Task<IActionResult> RejectDocument(
            [FromRoute] Guid documentId,
            [FromBody] DocumentRejectionRequest request
        )
        {
            if (string.IsNullOrWhiteSpace(request.RejectionReason))
            {
                return BadRequest(
                    new ApiResponseModel<object>(
                        ApiResponseStatusEnum.BadRequest,
                        "Rejection reason is required",
                        null
                    )
                );
            }

            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<DocumentResponse>("User is not authenticated");
            }

            var command = new RejectDocumentCommand(
                documentId,
                adminId.Value,
                request.RejectionReason
            );
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<DocumentResponse>(result.Error);
            }

            return SuccessResponse(result.Value, "Document rejected successfully");
        }

        /// <summary>
        /// Suspends a care provider's account
        /// </summary>
        /// <param name="providerId">The ID of the care provider to suspend</param>
        /// <param name="request">Suspension details</param>
        /// <returns>Updated care provider profile</returns>
        [Authorize(Roles = "Admin")]
        [HttpPost(ApiRoutes.Admin.SuspendProvider)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareProviderProfileResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status409Conflict,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Suspends a care provider's account",
            Description = "Updates a care provider's verification status to Suspended",
            OperationId = "Admin_SuspendProvider",
            Tags = new[] { "Admin" }
        )]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AdminExamples))]
        public async Task<IActionResult> SuspendProvider(
            [FromRoute] Guid providerId,
            [FromBody] SuspendProviderRequest request
        )
        {
            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<CareProviderProfileResponse>(
                    "User is not authenticated"
                );
            }

            var command = new SuspendProviderCommand(
                providerId,
                adminId.Value,
                request.SuspensionReason,
                request.Notes
            );

            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareProviderProfileResponse>(result.Error);
            }

            // Map the result to a response
            var response = new CareProviderProfileResponse
            {
                Id = result.Value.Id,
                UserId = result.Value.UserId,
                Name =
                    $"{result.Value.User.UserProfile?.FirstName} {result.Value.User.UserProfile?.LastName}".Trim(),
                Email = result.Value.User.Email ?? string.Empty,
                PhoneNumber = result.Value.User.PhoneNumber ?? string.Empty,
                Gender = result.Value.User.UserProfile?.Gender ?? string.Empty,
                YearsExperience = result.Value.YearsExperience,
                DateOfBirth = result.Value.User.UserProfile?.DateOfBirth,
                VerificationStatus = result.Value.VerificationStatus.GetDescription(),
            };

            return SuccessResponse(response, "Care provider suspended successfully");
        }

        /// <summary>
        /// Removes suspension from a care provider's account
        /// </summary>
        /// <param name="providerId">The ID of the care provider to reinstate</param>
        /// <param name="request">Suspension removal details</param>
        /// <returns>Updated care provider profile</returns>
        [Authorize(Roles = "Admin")]
        [HttpPost(ApiRoutes.Admin.RemoveSuspension)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<CareProviderProfileResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status409Conflict,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Removes suspension from a care provider's account",
            Description = "Updates a care provider's verification status from Suspended back to Verified",
            OperationId = "Admin_RemoveSuspension",
            Tags = new[] { "Admin" }
        )]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AdminExamples))]
        public async Task<IActionResult> RemoveSuspension(
            [FromRoute] Guid providerId,
            [FromBody] RemoveSuspensionRequest request
        )
        {
            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<CareProviderProfileResponse>(
                    "User is not authenticated"
                );
            }

            var command = new RemoveSuspensionCommand(
                providerId,
                adminId.Value,
                request.RemovalReason,
                request.Notes
            );

            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                return ErrorResponseFromError<CareProviderProfileResponse>(result.Error);
            }

            // Map the result to a response
            var response = new CareProviderProfileResponse
            {
                Id = result.Value.Id,
                UserId = result.Value.UserId,
                Name =
                    $"{result.Value.User.UserProfile?.FirstName} {result.Value.User.UserProfile?.LastName}".Trim(),
                Email = result.Value.User.Email ?? string.Empty,
                PhoneNumber = result.Value.User.PhoneNumber ?? string.Empty,
                Gender = result.Value.User.UserProfile?.Gender ?? string.Empty,
                YearsExperience = result.Value.YearsExperience,
                DateOfBirth = result.Value.User.UserProfile?.DateOfBirth,
                VerificationStatus = result.Value.VerificationStatus.GetDescription(),
            };

            return SuccessResponse(response, "Care provider suspension removed successfully");
        }

        /// <summary>
        /// Bulk updates multiple care categories
        /// </summary>
        /// <param name="request">The bulk update request containing multiple category updates</param>
        /// <returns>List of updated care categories</returns>
        [HttpPut(ApiRoutes.Admin.BulkUpdateCareCategories)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<IEnumerable<CareCategoryResponse>>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status422UnprocessableEntity,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Bulk updates multiple care categories",
            Description = "Updates multiple care categories in a single operation. Only provided fields will be updated for each category.",
            OperationId = "Admin_BulkUpdateCareCategories",
            Tags = new[] { "Admin" }
        )]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(CareCategoryExamples))]
        public async Task<IActionResult> BulkUpdateCareCategories(
            [FromBody] BulkUpdateCareCategoriesRequest request
        )
        {
            var validationResult = await _requestValidator.ValidateAsync(
                request,
                new BulkUpdateCareCategoriesRequestValidator()
            );
            if (!validationResult.IsSuccess)
            {
                return ErrorResponseFromError<IEnumerable<CareCategoryResponse>>(
                    Error.Validation("Validation failed", validationResult.Error.ValidationErrors)
                );
            }

            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<IEnumerable<CareCategoryResponse>>(
                    "User is not authenticated"
                );
            }

            _logger.LogInformation(
                "Admin {AdminId} initiated bulk update of {Count} care categories",
                adminId.Value,
                request.Categories.Count
            );

            var command = new BulkUpdateCareCategoriesCommand(request, adminId.Value);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                _logger.LogError(
                    "Bulk update of care categories failed: {Error}",
                    result.Error.Message
                );
                return ErrorResponseFromError<IEnumerable<CareCategoryResponse>>(result.Error);
            }

            _logger.LogInformation(
                "Bulk update completed successfully. Updated {Count} categories",
                result.Value.Count()
            );

            return SuccessResponse(
                result.Value,
                $"Successfully updated {result.Value.Count()} care categories"
            );
        }

        /// <summary>
        /// Gets the current admin's profile information
        /// </summary>
        /// <returns>Admin profile information</returns>
        [HttpGet(ApiRoutes.Admin.GetProfile)]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<GetAdminProfileResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status404NotFound,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets admin profile",
            Description = "Retrieves the current admin's profile information including personal details, roles, and account status",
            OperationId = "Admin_GetProfile",
            Tags = new[] { "Admin" }
        )]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AdminExamples))]
        public async Task<IActionResult> GetProfile()
        {
            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<GetAdminProfileResponse>("User is not authenticated");
            }

            _logger.LogInformation("Admin {AdminId} requested profile information", adminId.Value);

            var query = new GetAdminProfileQuery(adminId.Value);
            var result = await _mediator.Send(query);

            if (result.IsFailure)
            {
                _logger.LogError("Failed to retrieve admin profile: {Error}", result.Error.Message);
                return ErrorResponseFromError<GetAdminProfileResponse>(result.Error);
            }

            _logger.LogInformation(
                "Successfully retrieved admin profile for user {AdminId}",
                adminId.Value
            );

            return SuccessResponse(result.Value, "Admin profile retrieved successfully");
        }

        /// <summary>
        /// Updates the admin's profile information
        /// </summary>
        /// <param name="request">The admin profile update request</param>
        /// <returns>Updated admin profile information</returns>
        [HttpPut(ApiRoutes.Admin.UpdateProfile)]
        [Consumes("multipart/form-data")]
        [ProducesResponseType(
            StatusCodes.Status200OK,
            Type = typeof(ApiResponseModel<UpdateAdminProfileResponse>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status403Forbidden,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status409Conflict,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Updates admin profile",
            Description = "Updates the admin's profile information including name, email, phone, gender, and optional profile picture",
            OperationId = "Admin_UpdateProfile",
            Tags = new[] { "Admin" }
        )]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AdminExamples))]
        public async Task<IActionResult> UpdateProfile([FromForm] UpdateAdminProfileRequest request)
        {
            // Validate the request using RequestValidator
            var validationResult = await _requestValidator.ValidateAsync(
                request,
                new UpdateAdminProfileRequestValidator()
            );
            if (!validationResult.IsSuccess)
            {
                return ErrorResponseFromError<UpdateAdminProfileResponse>(
                    Error.Validation("Validation failed", validationResult.Error.ValidationErrors)
                );
            }

            // Get the current admin ID
            var adminId = _currentUserService.UserId;
            if (adminId == null)
            {
                _logger.LogWarning("User is not authenticated");
                return UnauthorizedResponse<UpdateAdminProfileResponse>(
                    "User is not authenticated"
                );
            }

            _logger.LogInformation("Admin {AdminId} initiated profile update", adminId.Value);

            var command = new UpdateAdminProfileCommand(request, adminId.Value);
            var result = await _mediator.Send(command);

            if (result.IsFailure)
            {
                _logger.LogError("Admin profile update failed: {Error}", result.Error.Message);
                return ErrorResponseFromError<UpdateAdminProfileResponse>(result.Error);
            }

            _logger.LogInformation(
                "Admin profile updated successfully for user {AdminId}",
                adminId.Value
            );

            return SuccessResponse(result.Value, "Admin profile updated successfully");
        }

        #region Statistics Endpoints

        [HttpGet(ApiRoutes.Admin.GetStatistics)]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(StatisticsResponse))]
        [ProducesResponseType(
            StatusCodes.Status401Unauthorized,
            Type = typeof(ApiResponseModel<object>)
        )]
        [ProducesResponseType(
            StatusCodes.Status400BadRequest,
            Type = typeof(ApiResponseModel<object>)
        )]
        [SwaggerOperation(
            Summary = "Gets statistics",
            Description = "Retrieves statistics about clients, bookings, professionals including total counts and confirmation statuses.",
            OperationId = "Statistics",
            Tags = ["Statistics"]
        )]
        public async Task<IActionResult> GetStatistics([FromHeader] string? reportType)
        {
            if (!_currentUserService.IsAuthenticated)
            {
                return ErrorResponseFromError<object>(
                    Error.Custom("400", "Unauthorized error response"),
                    "User is not authenticated."
                );
            }

            if (
                !string.IsNullOrWhiteSpace(reportType)
                && !Enum.TryParse<ReportType>(reportType, true, out var parsedReportType)
            )
            {
                return ErrorResponseFromError<object>(
                    Error.Custom("400", "Invalid report type param"),
                    "The provided report type value is invalid"
                );
            }

            if (Enum.TryParse<ReportType>(reportType, true, out var parsedEnum))
            {
                // Now you can compare parsedEnum with other enum values
                if (parsedEnum == ReportType.CareProviderReport)
                {
                    var query = new GetCareProviderStatisticsQuery();
                    var queryResponse = await _mediator.Send(query);
                    return SuccessResponse<CareProviderStatisticsResponse>(
                        queryResponse.Value,
                        "Statistics retrieved successfully"
                    );
                }

                if (parsedEnum == ReportType.ClientReport)
                {
                    var query = new GetClientStatisticsQuery();
                    var queryResponse = await _mediator.Send(query);
                    return SuccessResponse<ClientStatisticsResponse>(
                        queryResponse.Value,
                        "Statistics retrieved successfully"
                    );
                }

                if (parsedEnum == ReportType.BookReport)
                {
                    var query = new GetBookingStatisticsQuery();
                    var queryResponse = await _mediator.Send(query);
                    return SuccessResponse<BookingStatisticsResponse>(
                        queryResponse.Value,
                        "Statistics retrieved successfully"
                    );
                }
            }

            var careProviderStatisticsQuery = new GetCareProviderStatisticsQuery();
            var careProviderStatisticsResult = await _mediator.Send(careProviderStatisticsQuery);
            var clientStatisticsQuery = new GetClientStatisticsQuery();
            var clientStaticticsResult = await _mediator.Send(clientStatisticsQuery);
            var bookingStatisticsQuery = new GetBookingStatisticsQuery();
            var bookingStatisticsResult = await _mediator.Send(bookingStatisticsQuery);

            var response = new StatisticsResponse
            {
                CareProviderStatistics = careProviderStatisticsResult.Value,
                ClientStatistics = clientStaticticsResult.Value,
                BookingStatisticsResponse = bookingStatisticsResult.Value,
            };

            return SuccessResponse<StatisticsResponse>(
                response,
                "Statistics retrieved successfully"
            );
        }

        #endregion
    }
}
