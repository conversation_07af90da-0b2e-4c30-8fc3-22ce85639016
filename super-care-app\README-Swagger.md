# Swagger Documentation Guide

This document provides information about the Swagger/OpenAPI implementation in the SuperCare application, including how to use it, how it's configured, and how to extend it.

## Table of Contents

1. [Overview](#overview)
2. [Accessing Swagger UI](#accessing-swagger-ui)
3. [Implementation Details](#implementation-details)
4. [Response Examples](#response-examples)
5. [Swagger Annotations](#swagger-annotations)
6. [Extending the Documentation](#extending-the-documentation)
7. [Best Practices](#best-practices)

## Overview

The SuperCare API uses Swagger/OpenAPI for API documentation. Swagger provides a user-friendly interface for exploring and testing the API endpoints, as well as detailed information about request and response models.

Key features of our Swagger implementation:
- Detailed endpoint descriptions
- Request and response examples
- Authentication integration
- Response status codes documentation
- Model schemas with examples

## Accessing Swagger UI

The Swagger UI is available in the development environment at:

```
https://localhost:<port>/swagger
```

The UI provides an interactive interface where you can:
- Browse available endpoints
- See request parameters and response models
- Try out API calls directly from the browser
- View example responses

## Implementation Details

### Configuration

The Swagger configuration is set up in the following files:
- `ServiceExtensions.cs` - Registers Swagger services
- `SwaggerDocumentGenerator.cs` - Configures Swagger document generation
- `Swagger/` directory - Contains custom filters and example providers

### Custom Components

The implementation includes several custom components:

1. **SwaggerDocumentGenerator**
   - Configures the Swagger document generation
   - Sets up security definitions for JWT authentication
   - Registers custom filters

2. **SwaggerExampleFilter**
   - Adds examples to API responses
   - Provides endpoint-specific examples

3. **SwaggerResponseExamplesFilter**
   - Adds detailed response examples to specific endpoints
   - Handles success and error responses

4. **SwaggerModelExampleFilter**
   - Adds examples to model schemas
   - Provides realistic data for request and response models

5. **SwaggerEnumSchemaFilter**
   - Improves the display of enums in the documentation
   - Adds descriptions and values to enum schemas

6. **SwaggerDefaultValuesFilter**
   - Adds default values to model properties
   - Uses DefaultValue attributes to populate default values

7. **ApiResponseExamples**
   - Contains example responses for various scenarios
   - Provides realistic data for success and error responses

## Response Examples

The API documentation includes examples for various response types:

### Success Responses

- Authentication responses (login, register, refresh token)
- User profile responses
- Paginated data responses

### Error Responses

- Validation errors
- Not found errors
- Unauthorized errors
- Forbidden errors
- Internal server errors

Each response example includes:
- Appropriate status code
- Descriptive message
- Realistic payload data

## Swagger Annotations

The API controllers use Swagger annotations to enhance the documentation:

### Controller Annotations

```csharp
[SwaggerTag("Authentication", "Endpoints for user authentication and authorization")]
```

### Operation Annotations

```csharp
[SwaggerOperation(
    Summary = "Authenticates a user",
    Description = "Authenticates a user with email and password and returns JWT tokens",
    OperationId = "Auth_Login",
    Tags = new[] { "Authentication" }
)]
```

### Response Annotations

```csharp
[SwaggerResponse(StatusCodes.Status200OK, "User registered successfully", typeof(ApiResponseModel<AuthResponse>))]
[SwaggerResponse(StatusCodes.Status400BadRequest, "Validation failed or user already exists", typeof(ApiResponseModel<object>))]
```

### Parameter Annotations

```csharp
[SwaggerParameter("User registration details", Required = true)]
```

## Extending the Documentation

### Adding New Endpoints

When adding new endpoints, follow these steps to ensure proper documentation:

1. Add XML documentation comments:
   ```csharp
   /// <summary>
   /// Description of the endpoint
   /// </summary>
   /// <param name="parameter">Description of the parameter</param>
   /// <returns>Description of the return value</returns>
   ```

2. Add ProducesResponseType attributes:
   ```csharp
   [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<YourResponseType>))]
   [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
   ```

3. Add SwaggerOperation attribute:
   ```csharp
   [SwaggerOperation(
       Summary = "Short summary",
       Description = "Detailed description",
       OperationId = "Controller_Action",
       Tags = new[] { "Category" }
   )]
   ```

4. Add response examples:
   - Create example responses in `ApiResponseExamples.cs`
   - Update `SwaggerResponseExamplesFilter.cs` to include your endpoint

### Adding New Models

When adding new models:

1. Add XML documentation comments to properties:
   ```csharp
   /// <summary>
   /// Description of the property
   /// </summary>
   public string PropertyName { get; set; }
   ```

2. Add DefaultValue attributes for default values:
   ```csharp
   [DefaultValue("default value")]
   public string PropertyWithDefault { get; set; }
   ```

3. Add example model in `SwaggerModelExampleFilter.cs`:
   ```csharp
   private void AddYourModelExample(OpenApiDocument swaggerDoc)
   {
       var schemaName = nameof(YourModel);
       if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
           return;

       var example = new YourModel
       {
           Property1 = "Example value",
           Property2 = 123
       };

       swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
   }
   ```

### Adding New Response Examples

To add new response examples:

1. Create a method in `ApiResponseExamples.cs`:
   ```csharp
   public static string YourExampleResponse()
   {
       var data = new YourResponseType
       {
           Property1 = "Example value",
           Property2 = 123
       };

       var response = new ApiResponseModel<YourResponseType>(
           ApiResponseStatusEnum.Success,
           "Operation successful",
           data
       );

       return SerializeToJson(response);
   }
   ```

2. Update `SwaggerResponseExamplesFilter.cs` to use your example:
   ```csharp
   if (controllerName == "YourController" && actionName == "YourAction")
   {
       AddSuccessExample(operation, ApiResponseExamples.YourExampleResponse());
       AddValidationErrorExample(operation);
   }
   ```

## Best Practices

### Documentation Quality

- Write clear, concise descriptions
- Use consistent terminology
- Provide realistic examples
- Document all possible response status codes
- Include validation requirements

### Organization

- Group related endpoints with consistent tags
- Use meaningful operation IDs
- Organize models logically
- Keep examples up-to-date with the actual implementation

### Performance

- Keep example data reasonably sized
- Use lazy loading for large schemas
- Consider caching for production environments

### Security

- Don't include sensitive information in examples
- Use placeholder values for tokens, passwords, etc.
- Document security requirements clearly

## Conclusion

The Swagger documentation is a critical part of the API's usability. By following these guidelines and leveraging the custom components, you can maintain high-quality, helpful documentation that makes it easier for developers to understand and use the API.

For more information about Swagger/OpenAPI, visit the [official documentation](https://swagger.io/docs/).
