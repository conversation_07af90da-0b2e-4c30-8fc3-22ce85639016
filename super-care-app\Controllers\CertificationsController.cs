﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperCareApp.Application.Common.Models.Certifications;
using SuperCareApp.Domain.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace SuperCareApp.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class CertificationsController : ControllerBase
    {
        private readonly ILogger<CertificationsController> _logger;

        public CertificationsController(ILogger<CertificationsController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Gets all available countries for certifications
        /// </summary>
        /// <returns>List of countries</returns>
        [HttpGet("countries")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<CountryResponse>))]
        [SwaggerOperation(
            Summary = "Gets all available countries for certifications",
            Description = "Returns a list of all countries that have certification options",
            OperationId = "Certifications_GetCountries",
            Tags = new[] { "Certifications" }
        )]
        public IActionResult GetCountries()
        {
            try
            {
                var countries = Enum.GetValues(typeof(Country))
                    .Cast<Country>()
                    .Select(c => new CountryResponse
                    {
                        Id = c.ToString(),
                        Name = c.GetDisplayName(),
                    })
                    .ToList();

                return Ok(countries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving countries");
                return StatusCode(500, "Error retrieving countries");
            }
        }

        /// <summary>
        /// Gets certifications available for a specific country
        /// </summary>
        /// <param name="country">Country name or code</param>
        /// <returns>List of certifications for the specified country</returns>
        [HttpGet("by-country/{country}")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<CertificationResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [SwaggerOperation(
            Summary = "Gets certifications by country",
            Description = "Returns a list of certifications available for a specific country",
            OperationId = "Certifications_GetByCountry",
            Tags = new[] { "Certifications" }
        )]
        public IActionResult GetCertificationsByCountry(string country)
        {
            try
            {
                var parsedCountry = CountryExtensions.FromString(country);
                var certifications = CertificationTypeExtensions
                    .GetCertificationsForCountry(parsedCountry)
                    .Select(c => new CertificationResponse
                    {
                        Id = c.ToString(),
                        Name = c.GetDisplayName(),
                        Country = parsedCountry.GetDisplayName(),
                    })
                    .ToList();

                return Ok(certifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving certifications for country {Country}",
                    country
                );
                return StatusCode(500, $"Error retrieving certifications for country {country}");
            }
        }

        /// <summary>
        /// Gets all available certifications across all countries
        /// </summary>
        /// <returns>List of all certifications</returns>
        [HttpGet]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<CertificationResponse>))]
        [SwaggerOperation(
            Summary = "Gets all certifications",
            Description = "Returns a list of all available certifications across all countries",
            OperationId = "Certifications_GetAll",
            Tags = new[] { "Certifications" }
        )]
        public IActionResult GetAllCertifications()
        {
            try
            {
                var certifications = Enum.GetValues(typeof(CertificationType))
                    .Cast<CertificationType>()
                    .Select(c => new CertificationResponse
                    {
                        Id = c.ToString(),
                        Name = c.GetDisplayName(),
                    })
                    .ToList();

                return Ok(certifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all certifications");
                return StatusCode(500, "Error retrieving all certifications");
            }
        }
    }
}
