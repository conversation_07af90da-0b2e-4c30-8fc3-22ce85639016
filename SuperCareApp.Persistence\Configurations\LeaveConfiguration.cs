﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

public class LeaveConfiguration : IEntityTypeConfiguration<Leave>
{
    public void Configure(EntityTypeBuilder<Leave> builder)
    {
        builder.HasKey(l => l.Id);

        builder.Property(l => l.ProviderId).IsRequired();

        builder.Property(l => l.StartDate).IsRequired();

        builder.Property(l => l.EndDate).IsRequired();

        builder.Property(l => l.Reason).HasMaxLength(500);

        // Add a check constraint to ensure EndDate is after or equal to StartDate
        builder.ToTable(t =>
        {
            t.HasCheckConstraint(
                "CK_Leave_EndDateAfterStartDate",
                "\"end_date\" >= \"start_date\""
            );
        });

        // Navigation property
        builder
            .HasOne(l => l.CareProviderProfile)
            .WithMany()
            .HasForeignKey(l => l.ProviderId)
            .OnDelete(DeleteBehavior.Cascade);

        // Create an index for efficient querying
        builder.HasIndex(l => new
        {
            l.ProviderId,
            l.StartDate,
            l.EndDate,
        });
    }
}
