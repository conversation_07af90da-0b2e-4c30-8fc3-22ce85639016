using System.Text.Json;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.User;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Enums;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Provides example responses for specific endpoints
    /// </summary>
    public static class SwaggerResponseExamples
    {
        /// <summary>
        /// Gets an example response for the login endpoint
        /// </summary>
        public static OpenApiExample GetLoginResponseExample()
        {
            var authResponse = new AuthResponse(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                DateTime.UtcNow.AddHours(1),
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                isVerifiedByAdmin: true
            );

            var response = new ApiResponseModel<AuthResponse>(
                ApiResponseStatusEnum.Success,
                "Authentication successful",
                authResponse
            );

            return new OpenApiExample
            {
                Summary = "Successful login response",
                Description = "Example of a successful login response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example response for the register endpoint
        /// </summary>
        public static OpenApiExample GetRegisterResponseExample()
        {
            var authResponse = new AuthResponse(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                DateTime.UtcNow.AddHours(1),
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                isVerifiedByAdmin: false
            );

            var response = new ApiResponseModel<AuthResponse>(
                ApiResponseStatusEnum.Success,
                "User registered successfully",
                authResponse
            );

            return new OpenApiExample
            {
                Summary = "Successful registration response",
                Description = "Example of a successful registration response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example response for the refresh token endpoint
        /// </summary>
        public static OpenApiExample GetRefreshTokenResponseExample()
        {
            var authResponse = new AuthResponse(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                DateTime.UtcNow.AddHours(1),
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                isVerifiedByAdmin: true
            );

            var response = new ApiResponseModel<AuthResponse>(
                ApiResponseStatusEnum.Success,
                "Token refreshed successfully",
                authResponse
            );

            return new OpenApiExample
            {
                Summary = "Successful token refresh response",
                Description = "Example of a successful token refresh response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example response for the get user endpoint
        /// </summary>
        public static OpenApiExample GetUserResponseExample()
        {
            var userDto = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                EmailVerified = true,
                IsActive = true,
                LastLogin = DateTime.UtcNow.AddDays(-1),
                AuthProvider = "local",
                CreatedAt = DateTime.UtcNow.AddMonths(-1),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
                Roles = new List<UserRoleType> { UserRoleType.Client },
                Profile = new UserProfileDto
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    PhoneNumber = "+**********",
                    ProfilePicture = "https://example.com/profile.jpg",
                    Address = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA",
                    Preferences = new Dictionary<string, object>
                    {
                        { "theme", "dark" },
                        { "notifications", true },
                    },
                    CreatedAt = DateTime.UtcNow.AddMonths(-1),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2),
                },
            };

            var response = new ApiResponseModel<UserDto>(
                ApiResponseStatusEnum.Success,
                "User retrieved successfully",
                userDto
            );

            return new OpenApiExample
            {
                Summary = "Successful get user response",
                Description = "Example of a successful get user response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example response for the get users endpoint
        /// </summary>
        public static OpenApiExample GetUsersResponseExample()
        {
            var users = new List<UserDto>
            {
                new UserDto
                {
                    Id = Guid.NewGuid(),
                    Email = "<EMAIL>",
                    EmailVerified = true,
                    IsActive = true,
                    LastLogin = DateTime.UtcNow.AddDays(-1),
                    AuthProvider = "local",
                    CreatedAt = DateTime.UtcNow.AddMonths(-1),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2),
                    Roles = new List<UserRoleType> { UserRoleType.Client },
                    Profile = new UserProfileDto
                    {
                        Id = Guid.NewGuid(),
                        UserId = Guid.NewGuid(),
                        FirstName = "John",
                        LastName = "Doe",
                        PhoneNumber = "+**********",
                        CreatedAt = DateTime.UtcNow.AddMonths(-1),
                        UpdatedAt = DateTime.UtcNow.AddDays(-2),
                    },
                },
                new UserDto
                {
                    Id = Guid.NewGuid(),
                    Email = "<EMAIL>",
                    EmailVerified = true,
                    IsActive = true,
                    LastLogin = DateTime.UtcNow.AddDays(-3),
                    AuthProvider = "local",
                    CreatedAt = DateTime.UtcNow.AddMonths(-2),
                    UpdatedAt = DateTime.UtcNow.AddDays(-4),
                    Roles = new List<UserRoleType> { UserRoleType.CareProvider },
                    Profile = new UserProfileDto
                    {
                        Id = Guid.NewGuid(),
                        UserId = Guid.NewGuid(),
                        FirstName = "Jane",
                        LastName = "Smith",
                        PhoneNumber = "+**********",
                        CreatedAt = DateTime.UtcNow.AddMonths(-2),
                        UpdatedAt = DateTime.UtcNow.AddDays(-4),
                    },
                },
            };

            var response = new PaginatedResponseModel<List<UserDto>>(
                ApiResponseStatusEnum.Success,
                "Users retrieved successfully",
                users,
                currentPage: 1,
                totalPages: 5,
                totalCount: 25,
                pageSize: 10
            );

            return new OpenApiExample
            {
                Summary = "Successful get users response",
                Description = "Example of a successful get users response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example validation error response
        /// </summary>
        public static OpenApiExample GetValidationErrorExample()
        {
            var validationErrors = new Dictionary<string, string[]>
            {
                { "email", new[] { "Email is required", "Email format is invalid" } },
                { "password", new[] { "Password must be at least 8 characters" } },
            };

            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.BadRequest,
                "Validation failed",
                validationErrors
            );

            return new OpenApiExample
            {
                Summary = "Validation error response",
                Description = "Example of a validation error response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example not found error response
        /// </summary>
        public static OpenApiExample GetNotFoundErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.NotFound,
                "The requested resource was not found.",
                null
            );

            return new OpenApiExample
            {
                Summary = "Not found error response",
                Description = "Example of a not found error response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example unauthorized error response
        /// </summary>
        public static OpenApiExample GetUnauthorizedErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Unauthorized,
                "Unauthorized access. Please login or provide a valid authentication token.",
                null
            );

            return new OpenApiExample
            {
                Summary = "Unauthorized error response",
                Description = "Example of an unauthorized error response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example forbidden error response
        /// </summary>
        public static OpenApiExample GetForbiddenErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Forbidden,
                "You do not have permission to access this resource.",
                null
            );

            return new OpenApiExample
            {
                Summary = "Forbidden error response",
                Description = "Example of a forbidden error response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Gets an example internal server error response
        /// </summary>
        public static OpenApiExample GetInternalServerErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.InternalServerError,
                "An internal server error occurred. Please try again later.",
                null
            );

            return new OpenApiExample
            {
                Summary = "Internal server error response",
                Description = "Example of an internal server error response",
                Value = CreateOpenApiObject(response),
            };
        }

        /// <summary>
        /// Creates an OpenApiObject from an object
        /// </summary>
        private static IOpenApiAny CreateOpenApiObject<T>(T obj)
        {
            var json = JsonSerializer.Serialize(
                obj,
                new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true,
                }
            );

            return new OpenApiString(json);
        }
    }
}
