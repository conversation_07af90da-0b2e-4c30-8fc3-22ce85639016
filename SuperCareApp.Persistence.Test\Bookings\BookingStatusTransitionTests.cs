using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.InMemory;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services;
using SuperCareApp.Persistence.Services.Bookings;
using SuperCareApp.Persistence.Services.Bookings.Commands;

namespace SuperCareApp.Persistence.Test.Bookings;

public class BookingStatusTransitionTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BookingManagementService _bookingManagementService;
    private readonly UpdateBookingStatusCommandHandler _statusCommandHandler;
    private readonly CancelBookingCommandHandler _cancelCommandHandler;
    private readonly Guid _userId;
    private readonly Guid _providerId;
    private readonly Guid _categoryId;
    private readonly Guid _clientId;

    public BookingStatusTransitionTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        _context = new ApplicationDbContext(options);

        _bookingManagementService = new BookingManagementService(_context);

        var mockBookingService = new Mock<IBookingService>();
        var mockCurrentUserService = new Mock<ICurrentUserService>();
        mockCurrentUserService.Setup(x => x.UserId).Returns(_userId);
        mockCurrentUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockCurrentUserService.Setup(x => x.IsInRole(It.IsAny<string>())).Returns(false);

        _statusCommandHandler = new UpdateBookingStatusCommandHandler(
            _context,
            NullLogger<UpdateBookingStatusCommandHandler>.Instance,
            mockCurrentUserService.Object
        );
        _cancelCommandHandler = new CancelBookingCommandHandler(
            mockBookingService.Object,
            NullLogger<CancelBookingCommandHandler>.Instance
        );

        _userId = Guid.NewGuid();
        _providerId = Guid.NewGuid();
        _categoryId = Guid.NewGuid();
        _clientId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test users
        var user = new ApplicationUser
        {
            Id = _userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        var client = new ApplicationUser
        {
            Id = _clientId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        // Create provider profile
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create availability for Monday
        var availability = new SuperCareApp.Domain.Entities.Availability
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Monday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        // Create category
        var category = new CareCategory
        {
            Id = _categoryId,
            Name = "Test Category",
            Description = "Test category description",
            IsActive = true,
            PlatformFee = 2.50m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create provider-category relationship with hourly rate
        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            CategoryId = _categoryId,
            HourlyRate = 25.00m,
            ExperienceYears = 5,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Users.AddRange(user, client);
        _context.CareProviderProfiles.Add(providerProfile);
        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);
        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Helper Methods

    private async Task<Booking> CreateTestBooking(
        BookingStatusType status = BookingStatusType.Requested,
        DateTime? bookingDate = null
    )
    {
        var bookingId = Guid.NewGuid();
        var date = bookingDate ?? new DateTime(2025, 7, 21); // Monday

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 8,
            TotalAmount = 200,
            PlatformFee = 20,
            ProviderAmount = 180,
            SpecialInstructions = "Test booking",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _clientId,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = status,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _clientId,
        };

        var bookingWindow = new BookingWindow
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Date = DateOnly.FromDateTime(date),
            StartTime = new TimeOnly(10, 0),
            EndTime = new TimeOnly(11, 0),
            DurationMinutes = 60,
            DailyRate = 200,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _clientId,
        };

        booking.Status = bookingStatus;

        _context.Bookings.Add(booking);
        _context.BookingStatuses.Add(bookingStatus);
        _context.BookingWindows.Add(bookingWindow);
        await _context.SaveChangesAsync();

        return booking;
    }

    #endregion

    #region Valid Status Transitions

    [Fact]
    public async Task HandleBookingAsync_RequestedToConfirmed_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Confirm,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act
        var result = await _bookingManagementService.HandleBookingAsync(request);

        // Assert
        Assert.Equal(booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Confirmed, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_ConfirmedToCompleted_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Confirmed);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Complete,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act
        var result = await _bookingManagementService.HandleBookingAsync(request);

        // Assert
        Assert.Equal(booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Completed, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_RequestedToCancelled_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Cancel,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act
        var result = await _bookingManagementService.HandleBookingAsync(request);

        // Assert
        Assert.Equal(booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Cancelled, updatedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_ConfirmedToCancelled_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Confirmed);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Cancel,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act
        var result = await _bookingManagementService.HandleBookingAsync(request);

        // Assert
        Assert.Equal(booking.Id, result);

        var updatedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Cancelled, updatedBooking.Status.Status);
    }

    #endregion

    #region Invalid Status Transitions

    [Fact]
    public async Task HandleBookingAsync_RequestedToCompleted_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Complete,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _bookingManagementService.HandleBookingAsync(request)
        );

        Assert.Contains("not in Confirmed state", exception.Message);

        // Verify status hasn't changed
        var unchangedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Requested, unchangedBooking.Status.Status);
    }

    [Fact]
    public async Task HandleBookingAsync_CompletedToConfirmed_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Completed);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Confirm,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _bookingManagementService.HandleBookingAsync(request)
        );

        Assert.Contains("not in Requested state", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_CompletedToCancelled_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Completed);
        var request = new BookingRequest(
            booking.Id,
            BookingAction.Cancel,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _bookingManagementService.HandleBookingAsync(request)
        );

        Assert.Contains("Booking is already completed or cancelled", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_CancelledToAnyStatus_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Cancelled);

        var confirmRequest = new BookingRequest(
            booking.Id,
            BookingAction.Confirm,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _bookingManagementService.HandleBookingAsync(confirmRequest)
        );

        Assert.Contains("not in Requested state", exception.Message);
    }

    #endregion


    #region Availability Validation Tests

    [Fact]
    public async Task HandleBookingAsync_ConfirmWithUnavailableSlot_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);

        // Create a conflicting confirmed booking
        var conflictingBooking = await CreateTestBooking(BookingStatusType.Confirmed);

        var confirmRequest = new BookingRequest(
            booking.Id,
            BookingAction.Confirm,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _bookingManagementService.HandleBookingAsync(confirmRequest)
        );

        Assert.Contains("no longer available", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_ConfirmWithAvailableSlot_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(
            BookingStatusType.Requested,
            new DateTime(2025, 7, 21)
        );

        var confirmRequest = new BookingRequest(
            booking.Id,
            BookingAction.Confirm,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act
        var result = await _bookingManagementService.HandleBookingAsync(confirmRequest);

        // Assert
        Assert.Equal(booking.Id, result);

        var confirmedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Confirmed, confirmedBooking.Status.Status);
    }

    #endregion

    #region Transaction Tests

    [Fact]
    public async Task HandleBookingAsync_TransactionRollback_ShouldPreserveOriginalState()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);

        // Create an invalid request that should cause rollback
        var invalidRequest = new BookingRequest(
            booking.Id,
            (BookingAction)999, // Invalid action
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _bookingManagementService.HandleBookingAsync(invalidRequest)
        );

        // Verify original state is preserved
        var unchangedBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.Equal(BookingStatusType.Requested, unchangedBooking.Status.Status);

        // Verify no additional status records were created
        var statusCount = await _context.BookingStatuses.CountAsync(s => s.BookingId == booking.Id);

        Assert.Equal(1, statusCount); // Only original status should exist
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task HandleBookingAsync_NonExistentBooking_ShouldFail()
    {
        // Arrange
        var nonExistentBookingId = Guid.NewGuid();
        var request = new BookingRequest(
            nonExistentBookingId,
            BookingAction.Confirm,
            _providerId,
            _clientId,
            _categoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            200,
            20,
            _clientId
        );

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _bookingManagementService.HandleBookingAsync(request)
        );

        Assert.Contains("Booking not found", exception.Message);
    }

    [Fact]
    public async Task HandleBookingAsync_ConcurrentStatusChanges_ShouldHandleGracefully()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);

        var confirmRequest = new BookingRequest(
            booking.Id,
            BookingAction.Confirm,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        var cancelRequest = new BookingRequest(
            booking.Id,
            BookingAction.Cancel,
            booking.ProviderId,
            booking.ClientId,
            booking.CategoryId,
            new DateOnly(2025, 7, 21),
            new TimeOnly(10, 0),
            new TimeOnly(11, 0),
            null,
            booking.TotalAmount,
            booking.PlatformFee,
            booking.CreatedBy
        );

        // Act - Execute concurrent requests
        var tasks = new[]
        {
            _bookingManagementService.HandleBookingAsync(confirmRequest),
            _bookingManagementService.HandleBookingAsync(cancelRequest),
        };

        var results = await Task.WhenAll(tasks);

        // Assert - One should succeed, the other might fail or both might succeed depending on timing
        Assert.True(results.Any(r => r == booking.Id));

        // Verify final state is consistent
        var finalBooking = await _context
            .Bookings.Include(b => b.Status)
            .FirstAsync(b => b.Id == booking.Id);

        Assert.True(
            finalBooking.Status.Status == BookingStatusType.Confirmed
                || finalBooking.Status.Status == BookingStatusType.Cancelled
        );
    }

    #endregion
}
