using System.Linq;
using System.Reflection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Operation filter to correctly handle file uploads in Swagger UI
    /// </summary>
    public class FileUploadOperationFilter : IOperationFilter
    {
        private const string FormDataMimeType = "multipart/form-data";
        private static readonly string[] FormDataContentTypes = new[] { FormDataMimeType };

        /// <summary>
        /// Applies the filter to the specified operation using the given context.
        /// </summary>
        /// <param name="operation">The operation to apply the filter to.</param>
        /// <param name="context">The current operation filter context.</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (context.MethodInfo == null)
                return;

            var hasFormFileParameter = context
                .MethodInfo.GetParameters()
                .Any(p =>
                    p.ParameterType == typeof(IFormFile)
                    || p.ParameterType == typeof(IFormFileCollection)
                );

            if (!hasFormFileParameter)
                return;

            // Get consumes attribute if present
            var consumes = context
                .MethodInfo.GetCustomAttributes<Microsoft.AspNetCore.Mvc.ConsumesAttribute>()
                .SelectMany(a => a.ContentTypes)
                .Distinct()
                .ToArray();

            // If no consumes attribute, use form data
            if (consumes.Length == 0)
                consumes = FormDataContentTypes;

            // Set up request body
            if (operation.RequestBody == null)
            {
                operation.RequestBody = new OpenApiRequestBody
                {
                    Content = new Dictionary<string, OpenApiMediaType>(),
                };
            }

            foreach (var contentType in consumes)
            {
                if (!operation.RequestBody.Content.ContainsKey(contentType))
                {
                    operation.RequestBody.Content[contentType] = new OpenApiMediaType
                    {
                        Schema = new OpenApiSchema
                        {
                            Type = "object",
                            Properties = new Dictionary<string, OpenApiSchema>(),
                            Required = new HashSet<string>(),
                        },
                    };
                }
            }

            // Store existing parameters
            var existingParameters = operation.Parameters?.ToList() ?? new List<OpenApiParameter>();
            operation.Parameters = new List<OpenApiParameter>();

            // Process each parameter
            foreach (var parameter in context.ApiDescription.ParameterDescriptions)
            {
                // Handle file parameters
                if (
                    parameter.Type == typeof(IFormFile)
                    || parameter.Type == typeof(IFormFileCollection)
                )
                {
                    foreach (var contentType in consumes)
                    {
                        operation.RequestBody.Content[contentType].Schema.Properties[
                            parameter.Name
                        ] = new OpenApiSchema { Type = "string", Format = "binary" };

                        if (parameter.IsRequired)
                        {
                            operation
                                .RequestBody.Content[contentType]
                                .Schema.Required.Add(parameter.Name);
                        }
                    }
                }
                // Handle form parameters
                else if (parameter.Source.Id == "Form")
                {
                    foreach (var contentType in consumes)
                    {
                        var schema = context.SchemaGenerator.GenerateSchema(
                            parameter.Type,
                            context.SchemaRepository
                        );
                        operation.RequestBody.Content[contentType].Schema.Properties[
                            parameter.Name
                        ] = schema;

                        if (parameter.IsRequired)
                        {
                            operation
                                .RequestBody.Content[contentType]
                                .Schema.Required.Add(parameter.Name);
                        }
                    }
                }
                // Handle path and query parameters
                else if (parameter.Source.Id == "Path" || parameter.Source.Id == "Query")
                {
                    var existingParam = existingParameters.FirstOrDefault(p =>
                        p.Name == parameter.Name
                    );
                    if (existingParam != null)
                    {
                        operation.Parameters.Add(existingParam);
                    }
                }
                // Handle other parameters
                else if (parameter.Source.Id != "Body") // Skip body parameters as they're handled by the request body
                {
                    var existingParam = existingParameters.FirstOrDefault(p =>
                        p.Name == parameter.Name
                    );
                    if (existingParam != null)
                    {
                        operation.Parameters.Add(existingParam);
                    }
                }
            }
        }
    }
}
