# Redis Distributed Cache Implementation Plan

## 📋 Overview

This document outlines the comprehensive plan for implementing Redis as a distributed cache in the SuperCare App, replacing the current in-memory caching system with a scalable, distributed solution.

## 🎯 Objectives

- **Replace In-Memory Cache**: Migrate from `IMemoryCache` to Redis distributed cache
- **Improve Scalability**: Enable horizontal scaling across multiple application instances
- **Enhance Performance**: Implement intelligent caching strategies for frequently accessed data
- **Maintain Security**: Ensure cache security with proper authentication and encryption
- **Cache Invalidation**: Implement robust cache invalidation strategies
- **Monitoring**: Add comprehensive cache monitoring and metrics

## 🏗️ Current State Analysis

### Existing Caching Implementation

The application currently uses:

- **In-Memory Cache**: `IMemoryCache` for local caching
- **Cache Behavior**: `CachingBehavior<TRequest, TResponse>` pipeline behavior
- **Cache Attribute**: `[Cacheable]` attribute for marking cacheable queries
- **Cache Settings**: Configurable cache settings in `appsettings.json`

### Current Cache Usage

```csharp
// Example: Document caching
[Cacheable("UserDocuments", 300)] // Cache for 5 minutes
public record GetCachedDocumentsByUserIdQuery(Guid UserId)
    : IQuery<Result<IEnumerable<DocumentResponse>>>;
```

### Limitations of Current System

1. **Single Instance**: Cache is not shared across application instances
2. **Memory Constraints**: Limited by application memory
3. **No Persistence**: Cache lost on application restart
4. **No Distributed Invalidation**: Cannot invalidate cache across instances

## 🚀 Implementation Strategy

### Phase 1: Infrastructure Setup

#### 1.1 Redis Configuration

- Add Redis to Docker Compose
- Configure Redis connection settings
- Set up Redis authentication and security

#### 1.2 NuGet Packages

```xml
<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
<PackageReference Include="StackExchange.Redis" Version="2.7.20" />
<PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
```

### Phase 2: Core Implementation

#### 2.1 Redis Settings Configuration

```csharp
public class RedisSettings
{
    public const string SectionName = "Redis";
    public string ConnectionString { get; set; } = string.Empty;
    public string InstanceName { get; set; } = "SuperCareApp";
    public int Database { get; set; } = 0;
    public bool EnableSsl { get; set; } = false;
    public string Password { get; set; } = string.Empty;
    public int ConnectTimeout { get; set; } = 5000;
    public int SyncTimeout { get; set; } = 5000;
    public bool AbortOnConnectFail { get; set; } = false;
}
```

#### 2.2 Distributed Cache Service Interface

```csharp
public interface IDistributedCacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    Task RefreshAsync(string key, CancellationToken cancellationToken = default);
}
```

#### 2.3 Cache Key Strategy

```csharp
public static class CacheKeys
{
    // User-related caches
    public const string UserProfile = "user:profile:{0}";
    public const string UserDocuments = "user:documents:{0}";
    public const string UserAddresses = "user:addresses:{0}";

    // Provider-related caches
    public const string ProviderProfile = "provider:profile:{0}";
    public const string ProviderAvailability = "provider:availability:{0}:{1}"; // providerId:date
    public const string ProviderCategories = "provider:categories:{0}";

    // Booking-related caches
    public const string UserBookings = "bookings:user:{0}";
    public const string ProviderBookings = "bookings:provider:{0}";
    public const string BookingDetails = "booking:details:{0}";

    // System-wide caches
    public const string CareCategories = "system:categories";
    public const string SystemSettings = "system:settings";

    // Admin dashboard caches
    public const string DashboardStats = "admin:dashboard:stats";
    public const string UserManagement = "admin:users:{0}:{1}"; // page:size
}
```

### Phase 3: Service Implementation

#### 3.1 Distributed Cache Service Implementation

```csharp
public class DistributedCacheService : IDistributedCacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IDatabase _database;
    private readonly ILogger<DistributedCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    // Implementation with Redis-specific features
    // - Pattern-based invalidation using Redis SCAN
    // - Atomic operations using Redis transactions
    // - Compression for large objects
    // - Encryption for sensitive data
}
```

#### 3.2 Enhanced Caching Behavior

```csharp
public class DistributedCachingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IDistributedCacheService _cacheService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<DistributedCachingBehavior<TRequest, TResponse>> _logger;

    // Enhanced implementation with:
    // - User-specific cache keys
    // - Cache tags for invalidation
    // - Conditional caching based on user roles
    // - Cache warming strategies
}
```

## 🔐 Security Implementation

### Authentication & Authorization

```csharp
public class SecureCacheService : IDistributedCacheService
{
    // Features:
    // - Encrypt sensitive data before caching
    // - User-specific cache isolation
    // - Role-based cache access control
    // - Audit logging for cache operations
}
```

### Data Encryption

- Encrypt PII data before storing in cache
- Use AES-256 encryption for sensitive information
- Implement key rotation strategies

## 🔄 Cache Invalidation Strategies

### 1. Time-Based Expiration

```csharp
public static class CacheExpirationTimes
{
    public static readonly TimeSpan UserProfile = TimeSpan.FromMinutes(30);
    public static readonly TimeSpan ProviderAvailability = TimeSpan.FromMinutes(15);
    public static readonly TimeSpan SystemSettings = TimeSpan.FromHours(1);
    public static readonly TimeSpan DashboardStats = TimeSpan.FromMinutes(5);
}
```

### 2. Event-Based Invalidation

```csharp
public interface ICacheInvalidationService
{
    Task InvalidateUserCacheAsync(Guid userId);
    Task InvalidateProviderCacheAsync(Guid providerId);
    Task InvalidateBookingCacheAsync(Guid bookingId);
    Task InvalidateSystemCacheAsync();
}
```

### 3. Tag-Based Invalidation

```csharp
public class CacheTag
{
    public const string User = "user";
    public const string Provider = "provider";
    public const string Booking = "booking";
    public const string System = "system";
    public const string Admin = "admin";
}
```

## 📊 Monitoring & Metrics

### Cache Metrics

```csharp
public interface ICacheMetricsService
{
    void RecordCacheHit(string cacheKey, string operation);
    void RecordCacheMiss(string cacheKey, string operation);
    void RecordCacheInvalidation(string cacheKey, string reason);
    void RecordCacheError(string cacheKey, Exception exception);
}
```

### Health Checks

```csharp
public class RedisHealthCheck : IHealthCheck
{
    // Monitor Redis connectivity
    // Check cache performance metrics
    // Validate cache consistency
}
```

## 🐳 Docker Configuration

### Redis Service Addition

```yaml
# docker-compose.yml
services:
  redis:
    image: redis:7-alpine
    container_name: supercare-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=your_secure_password
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - supercare-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

volumes:
  redis-data:
```

## ⚙️ Configuration Updates

### appsettings.json

```json
{
  "Redis": {
    "ConnectionString": "localhost:6379",
    "InstanceName": "SuperCareApp",
    "Database": 0,
    "EnableSsl": false,
    "Password": "",
    "ConnectTimeout": 5000,
    "SyncTimeout": 5000,
    "AbortOnConnectFail": false
  },
  "CacheSettings": {
    "DefaultExpirationSeconds": 300,
    "EnableCompression": true,
    "EnableEncryption": true,
    "MaxCacheSize": "500MB",
    "CompressionThreshold": 1024
  }
}
```

## 🔧 Migration Strategy

### Phase 1: Parallel Implementation

1. Implement Redis cache alongside existing memory cache
2. Add feature flags to switch between implementations
3. Test Redis implementation in development

### Phase 2: Gradual Migration

1. Migrate non-critical caches first (system settings, categories)
2. Monitor performance and stability
3. Migrate user-specific caches

### Phase 3: Full Migration

1. Replace all memory cache usage with Redis
2. Remove memory cache dependencies
3. Optimize Redis configuration based on usage patterns

## 📋 Implementation Checklist

### Infrastructure

- [ ] Add Redis to Docker Compose
- [ ] Configure Redis authentication
- [ ] Set up Redis persistence
- [ ] Configure Redis networking

### Code Implementation

- [ ] Create Redis settings configuration
- [ ] Implement IDistributedCacheService
- [ ] Update CachingBehavior for Redis
- [ ] Implement cache invalidation service
- [ ] Add cache metrics and monitoring

### Security

- [ ] Implement data encryption for sensitive cache data
- [ ] Add user-specific cache isolation
- [ ] Implement audit logging for cache operations
- [ ] Configure Redis authentication and SSL

### Testing

- [ ] Unit tests for cache service
- [ ] Integration tests with Redis
- [ ] Performance testing
- [ ] Cache invalidation testing

### Monitoring

- [ ] Add Redis health checks
- [ ] Implement cache metrics collection
- [ ] Set up cache performance monitoring
- [ ] Configure alerting for cache issues

## 🎯 Success Criteria

1. **Performance**: Cache hit ratio > 80% for frequently accessed data
2. **Scalability**: Support for multiple application instances
3. **Reliability**: 99.9% cache availability
4. **Security**: All sensitive data encrypted in cache
5. **Monitoring**: Comprehensive cache metrics and alerting

## 📈 Expected Benefits

1. **Improved Performance**: Faster response times for cached data
2. **Better Scalability**: Support for horizontal scaling
3. **Enhanced Reliability**: Persistent cache across application restarts
4. **Reduced Database Load**: Lower database query frequency
5. **Better User Experience**: Faster page loads and API responses

## 🔧 Detailed Service Integration

### Current Services Requiring Cache Integration

#### 1. Authentication Services

```csharp
// Cache user sessions and JWT tokens
public class AuthService : IAuthService
{
    private readonly IDistributedCacheService _cache;

    public async Task<Result<AuthResponse>> LoginAsync(string email, string password)
    {
        // Cache user profile after successful login
        var cacheKey = string.Format(CacheKeys.UserProfile, user.Id);
        await _cache.SetAsync(cacheKey, userProfile, CacheExpirationTimes.UserProfile);
    }

    public async Task InvalidateUserSessionAsync(Guid userId)
    {
        // Invalidate all user-related caches on logout
        await _cache.RemoveByPatternAsync($"user:{userId}:*");
    }
}
```

#### 2. Booking Services

```csharp
public class BookingService : IBookingService
{
    private readonly IDistributedCacheService _cache;
    private readonly ICacheInvalidationService _invalidation;

    public async Task<Result<BookingResponse>> CreateBookingAsync(CreateBookingRequest request)
    {
        var result = await CreateBookingInDatabase(request);

        if (result.IsSuccess)
        {
            // Invalidate provider availability cache
            await _invalidation.InvalidateProviderAvailabilityAsync(request.ProviderId);

            // Invalidate user bookings cache
            await _invalidation.InvalidateUserBookingsAsync(request.ClientId);
        }

        return result;
    }
}
```

#### 3. Provider Services

```csharp
public class CareProviderService : ICareProviderService
{
    public async Task<Result<CareProviderProfileResponse>> GetProfileAsync(Guid userId)
    {
        var cacheKey = string.Format(CacheKeys.ProviderProfile, userId);

        var cached = await _cache.GetAsync<CareProviderProfileResponse>(cacheKey);
        if (cached != null)
        {
            return Result.Success(cached);
        }

        var profile = await GetProfileFromDatabase(userId);
        if (profile.IsSuccess)
        {
            await _cache.SetAsync(cacheKey, profile.Value, CacheExpirationTimes.ProviderProfile);
        }

        return profile;
    }
}
```

### Cache Warming Strategies

#### 1. Application Startup Cache Warming

```csharp
public class CacheWarmupService : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Warm up frequently accessed data
        await WarmupCareCategories();
        await WarmupSystemSettings();
        await WarmupActiveProviders();
    }

    private async Task WarmupCareCategories()
    {
        var categories = await _careCategoryService.GetAllAsync();
        await _cache.SetAsync(CacheKeys.CareCategories, categories, TimeSpan.FromHours(24));
    }
}
```

#### 2. Predictive Cache Loading

```csharp
public class PredictiveCacheService
{
    public async Task WarmupUserRelatedDataAsync(Guid userId)
    {
        // Preload user's frequently accessed data
        _ = Task.Run(async () =>
        {
            await LoadUserProfile(userId);
            await LoadUserBookings(userId);
            await LoadUserAddresses(userId);
        });
    }
}
```

## 🔄 Advanced Cache Invalidation Patterns

### 1. Event-Driven Invalidation

```csharp
public class CacheInvalidationHandler :
    INotificationHandler<UserProfileUpdatedEvent>,
    INotificationHandler<BookingCreatedEvent>,
    INotificationHandler<ProviderAvailabilityChangedEvent>
{
    public async Task Handle(UserProfileUpdatedEvent notification, CancellationToken cancellationToken)
    {
        await _cache.RemoveAsync(string.Format(CacheKeys.UserProfile, notification.UserId));

        // If user is a provider, also invalidate provider cache
        if (notification.IsProvider)
        {
            await _cache.RemoveAsync(string.Format(CacheKeys.ProviderProfile, notification.UserId));
        }
    }

    public async Task Handle(BookingCreatedEvent notification, CancellationToken cancellationToken)
    {
        // Invalidate multiple related caches
        var tasks = new[]
        {
            _cache.RemoveAsync(string.Format(CacheKeys.UserBookings, notification.ClientId)),
            _cache.RemoveAsync(string.Format(CacheKeys.ProviderBookings, notification.ProviderId)),
            _cache.RemoveByPatternAsync($"provider:availability:{notification.ProviderId}:*")
        };

        await Task.WhenAll(tasks);
    }
}
```

### 2. Dependency-Based Invalidation

```csharp
public class CacheDependencyManager
{
    private readonly Dictionary<string, List<string>> _dependencies = new()
    {
        ["user:profile:{0}"] = new() { "user:bookings:{0}", "user:addresses:{0}" },
        ["provider:profile:{0}"] = new() { "provider:availability:{0}:*", "provider:categories:{0}" },
        ["booking:details:{0}"] = new() { "user:bookings:{1}", "provider:bookings:{2}" }
    };

    public async Task InvalidateWithDependenciesAsync(string cacheKey)
    {
        await _cache.RemoveAsync(cacheKey);

        if (_dependencies.TryGetValue(GetKeyPattern(cacheKey), out var dependentKeys))
        {
            var tasks = dependentKeys.Select(key =>
                key.Contains("*") ?
                _cache.RemoveByPatternAsync(key) :
                _cache.RemoveAsync(key));

            await Task.WhenAll(tasks);
        }
    }
}
```

### 3. Time-Based Batch Invalidation

```csharp
public class BatchCacheInvalidationService : IHostedService
{
    private Timer _timer;

    public Task StartAsync(CancellationToken cancellationToken)
    {
        // Run every 5 minutes to clean up expired or stale cache entries
        _timer = new Timer(InvalidateStaleEntries, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
        return Task.CompletedTask;
    }

    private async void InvalidateStaleEntries(object state)
    {
        // Invalidate dashboard statistics cache during low-traffic periods
        if (DateTime.UtcNow.Hour >= 2 && DateTime.UtcNow.Hour <= 4)
        {
            await _cache.RemoveAsync(CacheKeys.DashboardStats);
        }
    }
}
```

## 🛡️ Security Implementation Details

### 1. Data Classification and Encryption

```csharp
public enum CacheDataSensitivity
{
    Public,      // No encryption needed
    Internal,    // Basic encryption
    Sensitive,   // Strong encryption + access control
    Restricted   // Strongest encryption + audit logging
}

public class SecureDistributedCacheService : IDistributedCacheService
{
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null,
        CacheDataSensitivity sensitivity = CacheDataSensitivity.Internal)
    {
        var serializedValue = JsonSerializer.Serialize(value);

        switch (sensitivity)
        {
            case CacheDataSensitivity.Sensitive:
            case CacheDataSensitivity.Restricted:
                serializedValue = await _encryptionService.EncryptAsync(serializedValue);
                await _auditService.LogCacheOperationAsync(key, "SET", sensitivity);
                break;
        }

        await _distributedCache.SetStringAsync(key, serializedValue, GetCacheOptions(expiration));
    }
}
```

### 2. User Context Isolation

```csharp
public class UserContextCacheService
{
    public string GenerateUserSpecificKey(string baseKey, Guid userId, string userRole)
    {
        // Ensure users can only access their own cached data
        var hashedUserId = _hashService.ComputeHash(userId.ToString());
        return $"{baseKey}:user:{hashedUserId}:role:{userRole}";
    }

    public async Task<T?> GetUserSpecificAsync<T>(string baseKey, Guid userId)
    {
        var userKey = GenerateUserSpecificKey(baseKey, userId, _currentUser.Role);
        return await _cache.GetAsync<T>(userKey);
    }
}
```

## 📊 Performance Optimization Strategies

### 1. Cache Compression

```csharp
public class CompressedCacheService
{
    private readonly int _compressionThreshold = 1024; // 1KB

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
    {
        var serialized = JsonSerializer.Serialize(value);
        var bytes = Encoding.UTF8.GetBytes(serialized);

        if (bytes.Length > _compressionThreshold)
        {
            bytes = await _compressionService.CompressAsync(bytes);
            key = $"compressed:{key}";
        }

        await _distributedCache.SetAsync(key, bytes, GetCacheOptions(expiration));
    }
}
```

### 2. Cache Partitioning

```csharp
public class PartitionedCacheService
{
    private readonly Dictionary<string, int> _partitionMap = new()
    {
        ["user:*"] = 0,      // User data in database 0
        ["provider:*"] = 1,   // Provider data in database 1
        ["booking:*"] = 2,    // Booking data in database 2
        ["system:*"] = 3      // System data in database 3
    };

    public IDatabase GetPartitionedDatabase(string key)
    {
        var partition = _partitionMap.FirstOrDefault(p =>
            key.StartsWith(p.Key.Replace("*", ""))).Value;

        return _redis.GetDatabase(partition);
    }
}
```

### 3. Cache Pipeline Operations

```csharp
public class BatchCacheOperations
{
    public async Task SetMultipleAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null)
    {
        var batch = _database.CreateBatch();
        var tasks = new List<Task>();

        foreach (var kvp in keyValuePairs)
        {
            var serialized = JsonSerializer.Serialize(kvp.Value);
            tasks.Add(batch.StringSetAsync(kvp.Key, serialized, expiration));
        }

        batch.Execute();
        await Task.WhenAll(tasks);
    }
}
```

## 🔍 Monitoring and Observability

### 1. Cache Metrics Collection

```csharp
public class CacheMetricsCollector
{
    private readonly IMetrics _metrics;

    public void RecordCacheOperation(string operation, string key, bool success, TimeSpan duration)
    {
        _metrics.Measure.Counter.Increment("cache.operations.total",
            new MetricTags("operation", operation, "success", success.ToString()));

        _metrics.Measure.Timer.Time("cache.operation.duration", duration,
            new MetricTags("operation", operation, "key_prefix", GetKeyPrefix(key)));
    }

    public void RecordCacheHitRatio(string keyPrefix, double hitRatio)
    {
        _metrics.Measure.Gauge.SetValue("cache.hit_ratio", hitRatio,
            new MetricTags("key_prefix", keyPrefix));
    }
}
```

### 2. Cache Health Monitoring

```csharp
public class RedisCacheHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Test basic connectivity
            await _database.PingAsync();

            // Test read/write operations
            var testKey = $"health_check_{Guid.NewGuid()}";
            await _database.StringSetAsync(testKey, "test", TimeSpan.FromSeconds(10));
            var value = await _database.StringGetAsync(testKey);
            await _database.KeyDeleteAsync(testKey);

            stopwatch.Stop();

            var data = new Dictionary<string, object>
            {
                ["response_time_ms"] = stopwatch.ElapsedMilliseconds,
                ["redis_version"] = await GetRedisVersion(),
                ["connected_clients"] = await GetConnectedClients(),
                ["used_memory"] = await GetUsedMemory()
            };

            return HealthCheckResult.Healthy("Redis cache is healthy", data);
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Redis cache is unhealthy", ex);
        }
    }
}
```

---

_This comprehensive implementation plan provides detailed guidance for implementing Redis distributed caching with security, performance optimization, and robust monitoring capabilities._
