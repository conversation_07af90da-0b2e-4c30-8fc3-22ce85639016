using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings;

/// <summary>
/// Request model for bulk updating multiple availability records
/// </summary>
public class BulkUpdateAvailabilityRequest
{
    /// <summary>
    /// List of availability records to update
    /// </summary>
    public List<AvailabilityUpdateItem> Availabilities { get; set; } =
        new List<AvailabilityUpdateItem>();
    public int BufferDuration { get; set; }
    public bool? ProvidesRecurringBooking { get; set; } = false;
    public int? WorkingHoursPerDay { get; set; }
}

/// <summary>
/// Individual availability record to update
/// </summary>
public class AvailabilityUpdateItem
{
    /// <summary>
    /// The day of the week for this availability
    /// </summary>
    public string DayOfWeek { get; set; } = string.Empty;

    /// <summary>
    /// Whether the provider is available during this time slot
    /// </summary>
    public bool IsAvailable { get; set; } = true;

    /// <summary>
    /// The time slots for this availability
    /// </summary>
    public List<CreateAvailabilitySlotRequest> Slots { get; set; } =
        new List<CreateAvailabilitySlotRequest>();
}

public class BulkUpdateAvailabilityRequestValidator
    : AbstractValidator<BulkUpdateAvailabilityRequest>
{
    public BulkUpdateAvailabilityRequestValidator()
    {
        //RuleFor(x => x.Availabilities)
        //    .NotEmpty()
        //    .WithMessage("At least one availability record is required.");

        // Validate each item in the list using its own validator.
        RuleForEach(x => x.Availabilities).SetValidator(new AvailabilityUpdateItemValidator());

        // Add validation for WorkingHoursPerDay
        RuleFor(x => x.WorkingHoursPerDay)
            .InclusiveBetween(0, 12)
            .When(x => x.WorkingHoursPerDay.HasValue)
            .WithMessage("Working hours per day must be between 0 and 12.");

        // Add validation for BufferDuration when Availabilities has values
        RuleFor(x => x.BufferDuration)
            .GreaterThan(0)
            .When(x => x.Availabilities != null && x.Availabilities.Any())
            .WithMessage(
                "Buffer duration must be provided and greater than 0 when availabilities are specified."
            );
    }
}

public class AvailabilityUpdateItemValidator : AbstractValidator<AvailabilityUpdateItem>
{
    public AvailabilityUpdateItemValidator()
    {
        RuleFor(x => x.IsAvailable).NotNull().WithMessage("Availability status is required.");

        When(
            x => x.IsAvailable,
            () =>
            {
                RuleFor(x => x.Slots)
                    .NotEmpty()
                    .WithMessage(
                        "At least one availability slot is required when set to available."
                    );

                RuleForEach(x => x.Slots)
                    .SetValidator(new CreateAvailabilitySlotRequestValidator());
            }
        );

        When(
            x => !x.IsAvailable,
            () =>
            {
                RuleFor(x => x.Slots)
                    .Empty()
                    .WithMessage("Slots must be empty when not available.");
            }
        );
    }
}
