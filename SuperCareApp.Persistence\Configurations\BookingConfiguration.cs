﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class BookingConfiguration : IEntityTypeConfiguration<Booking>
    {
        public void Configure(EntityTypeBuilder<Booking> builder)
        {
            builder.HasKey(b => b.Id);

            builder.Property(b => b.ClientId).IsRequired();

            builder.Property(b => b.ProviderId).IsRequired();

            builder.Property(b => b.CategoryId).IsRequired();

            builder.Property(b => b.SpecialInstructions).HasMaxLength(1000);

            builder.Property(b => b.TotalAmount).IsRequired().HasColumnType("decimal(18,2)");

            builder.Property(b => b.PlatformFee).IsRequired().HasColumnType("decimal(18,2)");

            builder.Property(b => b.ProviderAmount).IsRequired().HasColumnType("decimal(18,2)");

            // Relationships
            builder
                .HasOne(b => b.Client)
                .WithMany()
                .HasForeignKey(b => b.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasOne(b => b.Provider)
                .WithMany()
                .HasForeignKey(b => b.ProviderId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasOne(b => b.Category)
                .WithMany()
                .HasForeignKey(b => b.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
                .HasOne(b => b.Status)
                .WithOne(s => s.Booking)
                .HasForeignKey<BookingStatus>(s => s.BookingId)
                .OnDelete(DeleteBehavior.Cascade);

            builder
                .HasMany(b => b.Reviews)
                .WithOne(r => r.Booking)
                .HasForeignKey(r => r.BookingId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes for efficient querying
            builder.HasIndex(b => b.ClientId);
            builder.HasIndex(b => b.ProviderId);
            builder.HasIndex(b => b.CategoryId);
            builder.HasIndex(b => b.WorkingHours);

            builder
                .HasIndex(b => b.ClientId)
                .HasDatabaseName("IX_Bookings_ClientId")
                .HasFilter("\"is_deleted\" = false");

            builder
                .HasIndex(b => b.ProviderId)
                .HasDatabaseName("IX_Bookings_ProviderId")
                .HasFilter("\"is_deleted\" = false");
        }
    }
}
