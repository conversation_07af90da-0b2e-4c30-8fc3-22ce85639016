using System.Text.Json;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Application.Common.Models.User;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Enums;
using Swashbuckle.AspNetCore.SwaggerGen;
// Alias the ambiguous types
using UserCreateUserRequest = SuperCareApp.Application.Common.Models.User.CreateUserRequest;
using UserUpdateUserRequest = SuperCareApp.Application.Common.Models.User.UpdateUserRequest;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Swagger document filter to add examples to model schemas
    /// </summary>
    public class SwaggerModelExampleFilter : IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            // Add examples to model schemas
            AddModelExamples(swaggerDoc, context);
        }

        private void AddModelExamples(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            // Add examples for common request models
            AddAuthRequestExample(swaggerDoc);
            AddRegisterRequestExample(swaggerDoc);
            AddRefreshTokenRequestExample(swaggerDoc);
            AddCreateUserRequestExample(swaggerDoc);
            AddUpdateUserRequestExample(swaggerDoc);
            AddChangePasswordRequestExample(swaggerDoc);
            AddIdentityCreateUserRequestExample(swaggerDoc);

            // Add examples for common response models
            AddAuthResponseExample(swaggerDoc);
            AddUserDtoExample(swaggerDoc);
            AddDocumentResponseExample(swaggerDoc);
            AddUserProfileDtoExample(swaggerDoc);

            // Add examples for CareProvider models
            AddCareProviderProfileResponseExample(swaggerDoc);
            AddDetailedCareProviderProfileResponseExample(swaggerDoc);
            AddPagedCareProviderListExample(swaggerDoc);
            AddCreateCareProviderProfileRequestExample(swaggerDoc);
            AddUpdateCareProviderProfileRequestExample(swaggerDoc);

            // Add examples for API response models
            AddApiResponseModelExample(swaggerDoc);
            AddPaginatedResponseModelExample(swaggerDoc);
        }

        private void AddAuthRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(AuthRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new AuthRequest { Email = "<EMAIL>", Password = "Password123!" };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddRegisterRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(RegisterRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new RegisterRequest
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                PhoneNumber = "+**********",
                IsCareProvider = true,
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddRefreshTokenRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(RefreshTokenRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new RefreshTokenRequest
            {
                RefreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddCreateUserRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(UserCreateUserRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new UserCreateUserRequest
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                ConfirmPassword = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+**********",
                Role = UserRoleType.Client,
                Address = "123 Main St",
                City = "New York",
                State = "NY",
                PostalCode = "10001",
                Country = "USA",
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddUpdateUserRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(UserUpdateUserRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new UserUpdateUserRequest
            {
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+**********",
                ProfilePicture = "https://example.com/profile.jpg",
                Address = "123 Main St",
                City = "New York",
                State = "NY",
                PostalCode = "10001",
                Country = "USA",
                Preferences = new Dictionary<string, object>
                {
                    { "theme", "dark" },
                    { "notifications", true },
                },
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);

            // Add example for Identity.UpdateUserRequest
            var identitySchemaName = "UpdateUserRequest";
            if (swaggerDoc.Components.Schemas.ContainsKey(identitySchemaName))
            {
                var identityExample = new OpenApiObject
                {
                    ["name"] = new OpenApiString("John Doe"),
                    ["phoneNumber"] = new OpenApiString("+**********"),
                    ["gender"] = new OpenApiString("Male"),
                    ["yearsExperience"] = new OpenApiInteger(5),
                    ["dateOfBirth"] = new OpenApiString("1990-01-01"),
                    ["profilePicture"] = new OpenApiString("(binary)"),
                };

                swaggerDoc.Components.Schemas[identitySchemaName].Example = identityExample;
            }
        }

        private void AddIdentityCreateUserRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = "CreateUserRequest";
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new OpenApiObject
            {
                ["name"] = new OpenApiString("John Doe"),
                ["phoneNumber"] = new OpenApiString("+**********"),
                ["gender"] = new OpenApiString("Male"),
                ["yearsExperience"] = new OpenApiInteger(5),
                ["dateOfBirth"] = new OpenApiString("1990-01-01"),
                ["profilePicture"] = new OpenApiString("(binary)"),
            };

            swaggerDoc.Components.Schemas[schemaName].Example = example;
        }

        private void AddChangePasswordRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(ChangePasswordRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new ChangePasswordRequest
            {
                CurrentPassword = "OldPassword123!",
                NewPassword = "NewPassword123!",
                ConfirmNewPassword = "NewPassword123!",
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddAuthResponseExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(AuthResponse);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new AuthResponse(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                DateTime.UtcNow.AddHours(1),
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                isVerifiedByAdmin: true
            );

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddUserDtoExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(UserDto);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new UserDto
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                EmailVerified = true,
                IsActive = true,
                LastLogin = DateTime.UtcNow.AddDays(-1),
                AuthProvider = "local",
                CreatedAt = DateTime.UtcNow.AddMonths(-1),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
                Roles = new List<UserRoleType> { UserRoleType.Client },
                Profile = new UserProfileDto
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    PhoneNumber = "+**********",
                    ProfilePicture = "https://example.com/profile.jpg",
                    Address = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA",
                    Preferences = new Dictionary<string, object>
                    {
                        { "theme", "dark" },
                        { "notifications", true },
                    },
                    CreatedAt = DateTime.UtcNow.AddMonths(-1),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2),
                },
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddUserProfileDtoExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(UserProfileDto);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new UserProfileDto
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                FirstName = "John",
                LastName = "Doe",
                PhoneNumber = "+**********",
                ProfilePicture = "https://example.com/profile.jpg",
                Address = "123 Main St",
                City = "New York",
                State = "NY",
                PostalCode = "10001",
                Country = "USA",
                Preferences = new Dictionary<string, object>
                {
                    { "theme", "dark" },
                    { "notifications", true },
                },
                CreatedAt = DateTime.UtcNow.AddMonths(-1),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddApiResponseModelExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = "ApiResponseModelOfObject";
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Success,
                "The operation completed successfully.",
                new
                {
                    id = Guid.NewGuid(),
                    name = "Example Data",
                    createdAt = DateTime.UtcNow,
                }
            );

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddPaginatedResponseModelExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = "PaginatedResponseModelOfObject";
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var items = new List<object>
            {
                new
                {
                    id = Guid.NewGuid(),
                    name = "Item 1",
                    createdAt = DateTime.UtcNow,
                },
                new
                {
                    id = Guid.NewGuid(),
                    name = "Item 2",
                    createdAt = DateTime.UtcNow,
                },
                new
                {
                    id = Guid.NewGuid(),
                    name = "Item 3",
                    createdAt = DateTime.UtcNow,
                },
            };

            var example = new PaginatedResponseModel<object>(
                ApiResponseStatusEnum.Success,
                "Data retrieved successfully.",
                items,
                currentPage: 1,
                totalPages: 5,
                totalCount: 25,
                pageSize: 10
            );

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddCareProviderProfileResponseExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(CareProviderProfileResponse);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new CareProviderProfileResponse
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "John Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                DateOfBirth = new DateTime(1985, 6, 15),
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddDetailedCareProviderProfileResponseExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(DetailedCareProviderProfileResponse);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new DetailedCareProviderProfileResponse
            {
                Id = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                Name = "John Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                HourlyRate = 25.50m,
                ProvidesOvernight = true,
                ProvidesLiveIn = false,
                Bio =
                    "Experienced care provider with a background in nursing and elderly care. Specializes in dementia care and physical therapy assistance.",
                Qualifications = JsonSerializer.Serialize(
                    new List<string>
                    {
                        "Certified Nursing Assistant (CNA)",
                        "CPR Certified",
                        "First Aid Certified",
                        "Dementia Care Specialist",
                    }
                ),
                VerificationStatus = VerificationStatus.Verified.GetDescription(),
                Rating = 4.8m,
                RatingCount = 24,
                DateOfBirth = new DateTime(1985, 6, 15),
                CreatedAt = DateTime.UtcNow.AddYears(-2),
                UpdatedAt = DateTime.UtcNow.AddMonths(-1),
                Categories = new List<string>
                {
                    "Elderly Care",
                    "Dementia Care",
                    "Physical Therapy",
                },
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddPagedCareProviderListExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(PagedCareProviderList);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var providers = new List<CareProviderProfileResponse>
            {
                new CareProviderProfileResponse
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    Name = "John Smith",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    YearsExperience = 5,
                    DateOfBirth = new DateTime(1985, 6, 15),
                },
                new CareProviderProfileResponse
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    Name = "Sarah Johnson",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Female",
                    YearsExperience = 8,
                    DateOfBirth = new DateTime(1980, 3, 22),
                },
                new CareProviderProfileResponse
                {
                    Id = Guid.NewGuid(),
                    UserId = Guid.NewGuid(),
                    Name = "Michael Brown",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    YearsExperience = 3,
                    DateOfBirth = new DateTime(1990, 9, 10),
                },
            };

            var example = new PagedCareProviderList
            {
                Providers = providers.ToList(),
                PageNumber = 1,
                PageSize = 10,
                TotalCount = 25,
                TotalPages = 3,
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddCreateCareProviderProfileRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(CreateCareProviderProfileRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new CreateCareProviderProfileRequest
            {
                Name = "John Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                DateOfBirth = new DateTime(1985, 6, 15),
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddUpdateCareProviderProfileRequestExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(UpdateCareProviderProfileRequest);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new UpdateCareProviderProfileRequest
            {
                Name = "John Smith",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                DateOfBirth = new DateTime(1985, 6, 15),
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private void AddDocumentResponseExample(OpenApiDocument swaggerDoc)
        {
            var schemaName = nameof(DocumentResponse);
            if (!swaggerDoc.Components.Schemas.ContainsKey(schemaName))
                return;

            var example = new DocumentResponse
            {
                DocumentId = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                FileName = "passport.pdf",
                MimeType = "application/pdf",
                DocumentUrl = "https://example.com/documents/passport.pdf",
                DocumentType = "ID",
                VerificationStatus = "Verified",
                UploadedAt = DateTime.UtcNow.AddDays(-5),
                VerifiedAt = DateTime.UtcNow.AddDays(-2),
                VerifiedBy = Guid.NewGuid(),
            };

            swaggerDoc.Components.Schemas[schemaName].Example = CreateOpenApiExample(example);
        }

        private IOpenApiAny CreateOpenApiExample<T>(T example)
        {
            var json = JsonSerializer.Serialize(
                example,
                new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true,
                }
            );

            return new OpenApiString(json);
        }
    }
}
