﻿using System.Text.Json;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using super_care_app.Filters;
using super_care_app.Middleware;
using super_care_app.Shared.Configuration;
using super_care_app.Shared.Utility;
using super_care_app.Swagger;
using super_care_app.Utils;
using SuperCareApp.Application;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Persistence;

namespace super_care_app.Extensions
{
    /// <summary>
    /// Extension methods for registering services in the application
    /// </summary>
    public static class ServiceExtensions
    {
        /// <summary>
        /// Registers API and documentation services
        /// </summary>
        public static IServiceCollection AddApiServices(this IServiceCollection services)
        {
            services.AddRouting(options =>
            {
                options.LowercaseUrls = true;
            });
            // API Controllers with filters
            services
                .AddControllers(options =>
                {
                    // Order matters: ValidationFilter first, then ResultActionFilter, then ApiResponseFilter last
                    options.Filters.Add<ValidationFilter>();
                    options.Filters.Add<ResultActionFilter>();

                    // Add the API response filter last to avoid double-wrapping
                    options.Filters.Add<ApiResponseFilter>();
                    //Add lowercase routing
                    options.Conventions.Add(
                        new RouteTokenTransformerConvention(new LowercaseControllerTransformer())
                    );
                })
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.Converters.Add(new TimeOnlyJsonConverter());
                });

            // API Documentation
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen();
            services.ConfigureOptions<SwaggerDocumentGenerator>();

            return services;
        }

        /// <summary>
        /// Registers application layer services
        /// </summary>
        public static IServiceCollection AddApplicationServices(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            services.AddApplication(configuration);
            return services;
        }

        /// <summary>
        /// Registers persistence layer services
        /// </summary>
        public static IServiceCollection AddPersistenceServices(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            services.AddPersistence(configuration);
            return services;
        }

        /// <summary>
        /// Adds health checks to the application
        /// </summary>
        public static IServiceCollection AddHealthChecks(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            services
                .AddHealthChecks()
                .AddDbContextCheck<SuperCareApp.Persistence.Context.ApplicationDbContext>(
                    "Database"
                )
                .AddCheck("API", () => HealthCheckResult.Healthy(), new[] { "api" });

            services.AddSingleton<RequestValidator>();

            return services;
        }

        /// <summary>
        /// Configures health check endpoints
        /// </summary>
        public static WebApplication UseHealthChecks(this WebApplication app)
        {
            app.MapHealthChecks(
                "/health",
                new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
                {
                    ResponseWriter = async (context, report) =>
                    {
                        context.Response.ContentType = "application/json";

                        var response = new
                        {
                            status = report.Status.ToString(),
                            entries = report.Entries.ToDictionary(
                                entry => entry.Key,
                                entry => new
                                {
                                    status = entry.Value.Status.ToString(),
                                    description = entry.Value.Description,
                                    duration = entry.Value.Duration.ToString(),
                                    exception = entry.Value.Exception?.Message,
                                    data = entry.Value.Data,
                                    tags = entry.Value.Tags.ToArray(),
                                }
                            ),
                            totalDuration = report.TotalDuration.ToString(),
                        };

                        var options = new JsonSerializerOptions
                        {
                            WriteIndented = true,
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        };

                        await context.Response.WriteAsync(
                            JsonSerializer.Serialize(response, options)
                        );
                    },
                }
            );

            return app;
        }

        /// <summary>
        /// Configures the application's middleware pipeline
        /// </summary>
        public static WebApplication ConfigureMiddleware(this WebApplication app)
        {
            // Development environment middleware
            if (app.Environment.IsDevelopment())
            {
                app.Use(
                    (context, next) =>
                    {
                        // Apply CORS headers to the "uploads" path
                        if (context.Request.Path.StartsWithSegments("/uploads"))
                        {
                            context.Response.Headers.Append("Access-Control-Allow-Origin", "*");
                        }
                        return next();
                    }
                );
                // Serve static files
                app.UseStaticFiles();

                // Configure Swagger UI
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SuperCare API v1");
                    c.RoutePrefix = "swagger";
                    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List);
                    c.DefaultModelsExpandDepth(0); // Hide schemas section by default
                    c.DisplayRequestDuration();
                    c.EnableDeepLinking();
                    c.EnableFilter();
                    c.ShowExtensions();
                    c.EnableValidator();

                    // Add custom JavaScript for email/password authentication
                    c.InjectJavascript("/swagger-custom.js");
                });
            }

            // Global middleware
            app.UseGlobalExceptionHandler();
            app.UseHttpsRedirection();

            // Authentication and authorization
            app.UseAuthentication();
            app.UseAuthorization();

            // Endpoint routing
            app.MapControllers();

            return app;
        }
    }
}
