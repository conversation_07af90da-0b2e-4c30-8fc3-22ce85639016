﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to remove a care category from a provider
    /// </summary>
    public record RemoveProviderCategoryCommand(
        Guid ProviderId,
        ProviderCategoryRequest Request,
        Guid UserId
    ) : ICommand<Result>;

    /// <summary>
    /// Handler for the RemoveProviderCategoryCommand
    /// </summary>
    internal sealed class RemoveProviderCategoryCommandHandler
        : ICommandHandler<RemoveProviderCategoryCommand, Result>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<RemoveProviderCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public RemoveProviderCategoryCommandHandler(
            ApplicationDbContext dbContext,
            ILogger<RemoveProviderCategoryCommandHandler> logger
        )
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        /// ///
        public async Task<Result> Handle(
            RemoveProviderCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Verify the provider exists
                var provider = await _dbContext
                    .Set<CareProviderProfile>()
                    .FirstOrDefaultAsync(
                        p => p.UserId == request.ProviderId && !p.IsDeleted,
                        cancellationToken
                    );

                if (provider == null)
                {
                    return Result.Failure(
                        Error.NotFound($"Care provider with ID {request.ProviderId} not found.")
                    );
                }

                // Find the provider-category relationship
                var providerCategory = await _dbContext
                    .Set<CareProviderCategory>()
                    .FirstOrDefaultAsync(
                        pc =>
                            pc.ProviderId == provider.Id
                            && pc.CategoryId == request.Request.CategoryId
                            && !pc.IsDeleted,
                        cancellationToken
                    );

                if (providerCategory == null)
                {
                    return Result.Failure(
                        Error.NotFound(
                            $"Provider does not have the category with ID {request.Request.CategoryId}."
                        )
                    );
                }

                // Soft delete the provider-category relationship
                providerCategory.IsDeleted = true;
                providerCategory.DeletedAt = DateTime.UtcNow;
                providerCategory.DeletedBy = request.UserId;

                await _dbContext.SaveChangesAsync(cancellationToken);

                _logger.LogInformation(
                    "Removed category {CategoryId} from provider {ProviderId}",
                    request.Request.CategoryId,
                    provider.Id
                );

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing category from provider");
                return Result.Failure(Error.Internal(ex.Message));
            }
        }
    }
}
