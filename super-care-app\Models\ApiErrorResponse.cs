﻿using System.Text.Json.Serialization;

namespace super_care_app.Models
{
    /// <summary>
    /// Standard error response model for API
    /// </summary>
    public class ApiErrorResponse
    {
        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Detailed errors (for validation errors)
        /// </summary>
        [JsonPropertyName("errors")]
        public IDictionary<string, string[]>? Errors { get; set; }

        /// <summary>
        /// Request ID for tracking
        /// </summary>
        [JsonPropertyName("requestId")]
        public string? RequestId { get; set; }

        /// <summary>
        /// Timestamp of when the error occurred
        /// </summary>
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
