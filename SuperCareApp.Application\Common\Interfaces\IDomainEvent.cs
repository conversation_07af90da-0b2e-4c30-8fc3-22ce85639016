using SuperCareApp.Application.Common.Interfaces.Mediator;

namespace SuperCareApp.Application.Common.Interfaces;

/// <summary>
/// Represents an event that can be published and handled asynchronously.
/// </summary>
/// <remarks>
/// This interface extends INotification to allow for easy integration with the Mediator pattern.
/// </remarks>
public interface IDomainEvent : INotification { }

/// <summary>
/// Represents a handler for processing domain events.
/// </summary>
/// <typeparam name="TDomainEvent"></typeparam>
/// <remarks>
/// This interface extends INotificationHandler to allow for easy integration with the Mediator pattern.
/// </remarks>
public interface IDomainEventHandler<in TDomainEvent> : INotificationHandler<TDomainEvent>
    where TDomainEvent : IDomainEvent { }
