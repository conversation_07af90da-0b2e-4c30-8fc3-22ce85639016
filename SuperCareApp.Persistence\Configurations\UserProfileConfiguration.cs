﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Persistence.Configurations
{
    public class UserProfileConfiguration : IEntityTypeConfiguration<UserProfile>
    {
        public void Configure(EntityTypeBuilder<UserProfile> builder)
        {
            builder.HasKey(p => p.Id);

            builder.Property(p => p.ApplicationUserId).IsRequired();

            builder.Property(p => p.FirstName).HasMaxLength(100);

            builder.Property(p => p.LastName).HasMaxLength(100);

            builder.Property(p => p.Country).HasMaxLength(100);

            builder.Property(p => p.Preferences).HasColumnType("jsonb");

            // Configure the one-to-one relationship with ApplicationUser
            builder
                .HasOne(p => p.User)
                .WithOne(u => u.UserProfile)
                .HasForeignKey<UserProfile>(p => p.ApplicationUserId)
                .IsRequired();

            // Remove the Documents relationship as it should be configured from the Document side
            builder
                .Metadata.FindNavigation(nameof(UserProfile.Documents))
                ?.SetPropertyAccessMode(PropertyAccessMode.Field);
        }
    }
}
