using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Models.Doc;
using super_care_app.Shared.Constants;
using super_care_app.Shared.Utility;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models;
using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Persistence.Services.Address.Commands;
using SuperCareApp.Persistence.Services.Address.Queries;
using SuperCareApp.Persistence.Services.Categories.Commands;
using SuperCareApp.Persistence.Services.Documents.Commands;
using SuperCareApp.Persistence.Services.Documents.Queries;
using SuperCareApp.Persistence.Services.Identity.Commands;
using SuperCareApp.Persistence.Services.Identity.Queries;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.Filters;

namespace super_care_app.Controllers;

/// <summary>
/// Handles user profile management
/// </summary>
[Route("api/v{version:apiVersion}/account")]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<Error>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<Error>))]
public class AccountController : BaseController
{
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;
    private readonly RequestValidator _requestValidator;
    private readonly IPaymentStore _paymentStore;

    public AccountController(
        IMediator mediator,
        ICurrentUserService currentUserService,
        RequestValidator requestValidator,
        IPaymentStore paymentStore
    )
    {
        _mediator = mediator;
        _currentUserService = currentUserService;
        _requestValidator = requestValidator;
        _paymentStore = paymentStore;
    }

    #region Profile Endpoints

    /// <summary>
    /// Gets profiles with pagination support
    /// </summary>
    /// <param name="userTypeHeader">Type of user (Client or CareProvider)</param>
    /// <param name="queryParams">Query parameters for pagination and filtering</param>
    /// <returns>Paginated user profile information with metadata</returns>
    [HttpGet(ApiRoutes.Account.GetProfiles)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(PaginatedResponseModel<List<ProfileResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets profiles with pagination and filtering support",
        Description = "Retrieves profiles with pagination and filtering support",
        OperationId = "Account_GetProfiles",
        Tags = new[] { "Account" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AccountExamples))]
    public async Task<IActionResult> GetProfiles(
        [FromHeader(Name = "Usertype"), Required] string userTypeHeader,
        [FromQuery] ProfileQueryParams queryParams
    )
    {
        if (!Enum.TryParse<UserType>(userTypeHeader, true, out var userType))
        {
            return BadRequestResponse<List<ProfileResponse>>("Invalid user type specified");
        }
        //current user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null)
        {
            return UnauthorizedResponse<List<ProfileResponse>>("User is not authenticated");
        }

        // Map to ProfileListParams
        var paginationParams = new ProfileListParams
        {
            PageNumber = queryParams.PageNumber,
            PageSize = queryParams.PageSize,
            SortBy = queryParams.SortBy,
            SortDescending = queryParams.SortDescending,
            Filters = new CareProviderFilterParams
            {
                Date = queryParams.Date,
                StartTime = queryParams.StartTime,
                EndTime = queryParams.EndTime,
                CategoryIds = queryParams.CategoryIds,
                MinExperience = queryParams.MinExperience,
                MaxExperience = queryParams.MaxExperience,
                LocationLat = queryParams.LocationLat,
                LocationLong = queryParams.LocationLong,
                DistanceRadius = queryParams.DistanceRadius,
                Genders = queryParams.Genders,
                MinAge = queryParams.MinAge,
                MaxAge = queryParams.MaxAge,
                MinPrice = queryParams.MinPrice,
                MaxPrice = queryParams.MaxPrice,
            },
        };

        // Create the query with filtered parameters
        var query = new GetProfilesQuery(userType, paginationParams, queryParams.SearchTerm);

        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<List<ProfileResponse>>(result.Error);
        }

        // Get pagination metadata
        var meta = result.Value.ToMetadata();

        // Return paginated response with metadata
        return PaginatedResponse(result.Value.Profiles, meta, "Profiles retrieved successfully");
    }

    /// <summary>
    /// Gets a user's profile
    /// </summary>
    /// <param name="userId">ID of the user whose profile to retrieve</param>
    /// <param name="userTypeHeader">Type of user (Client or CareProvider)</param>
    /// <returns>User profile information</returns>
    [HttpGet(ApiRoutes.Account.GetProfile)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<ProfileResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets a user's profile",
        Description = "Retrieves the profile of the specified user",
        OperationId = "Account_GetProfile",
        Tags = new[] { "Account" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AccountExamples))]
    public async Task<IActionResult> GetProfile(
        [FromRoute] Guid userId,
        [FromHeader(Name = "Usertype"), Required] string userTypeHeader
    )
    {
        if (!Enum.TryParse<UserType>(userTypeHeader, true, out var userType))
        {
            return BadRequestResponse<ProfileResponse>("Invalid user type specified");
        }
        var query = new GetSingleProfileQuery(userId, userType);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<ProfileResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Profile retrieved successfully");
    }

    /// <summary>
    /// Creates a profile for a user
    /// </summary>
    /// <param name="userId">ID of the user for whom to create a profile</param>
    /// <param name="userTypeHeader">Type of user (Client or CareProvider)</param>
    /// <param name="request">Profile creation details</param>
    /// <returns>Created profile ID</returns>
    [HttpPost(ApiRoutes.Account.CreateProfile)]
    [Authorize]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponseModel<Guid>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Creates a user profile",
        Description = "Creates a profile for the specified user. Can include a profile picture.",
        OperationId = "Account_CreateProfile",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> CreateProfile(
        [FromRoute] Guid userId,
        [FromHeader(Name = "Usertype"), Required] string userTypeHeader,
        [FromForm] CreateUserRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new CreateUserRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        if (!Enum.TryParse<UserType>(userTypeHeader, true, out var userType))
        {
            return BadRequestResponse<Guid>("Invalid user type specified");
        }

        // Check if the current user has permission to create a profile for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<Guid>(
                "You do not have permission to create a profile for this user"
            );
        }

        var command = new CreateProfileCommand(userId, userType, request);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<Guid>(result.Error);
        }

        return SuccessResponse(result.Value, "Profile created successfully");
    }

    /// <summary>
    /// Updates a user's profile
    /// </summary>
    /// <param name="userId">ID of the user whose profile to update</param>
    /// <param name="userTypeHeader">Type of user (Client or CareProvider)</param>
    /// <param name="request">Profile update details</param>
    /// <returns>Success or error response</returns>
    [HttpPut(ApiRoutes.Account.UpdateProfile)]
    [Authorize]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Updates a user profile",
        Description = "Updates the profile for the specified user. Can include a profile picture.",
        OperationId = "Account_UpdateProfile",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> UpdateProfile(
        [FromRoute] Guid userId,
        [FromHeader(Name = "Usertype"), Required] string userTypeHeader,
        [FromForm] UpdateUserRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new UpdateUserRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        if (!Enum.TryParse<UserType>(userTypeHeader, true, out var userType))
        {
            return BadRequestResponse<object>("Invalid user type specified");
        }

        // Check if the current user has permission to update this profile
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>("You do not have permission to update this profile");
        }

        var command = new UpdateProfileCommand(userId, userType, request);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }
        var response = new GenericObjectResponse("Profile updated successfully", Guid.NewGuid());

        return SuccessResponse(response, "Profile updated successfully");
    }

    /// <summary>
    /// Deletes a user's profile
    /// </summary>
    /// <param name="userId">ID of the user whose profile to delete</param>
    /// <param name="userTypeHeader">Type of user (Client or CareProvider)</param>
    /// <returns>Success or error response</returns>
    [HttpDelete(ApiRoutes.Account.DeleteProfile)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Deletes a user's profile",
        Description = "Deletes the profile of the specified user",
        OperationId = "Account_DeleteProfile",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> DeleteProfile(
        [FromRoute] Guid userId,
        [FromHeader(Name = "Usertype"), Required] string userTypeHeader
    )
    {
        if (!Enum.TryParse<UserType>(userTypeHeader, true, out var userType))
        {
            return BadRequestResponse<object>("Invalid user type specified");
        }

        // Check if the current user has permission to delete this profile
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>("You do not have permission to delete this profile");
        }

        var command = new DeleteProfileCommand(userId, userType);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse("Profile deleted successfully");
    }

    #endregion

    #region Address Endpoints

    /// <summary>
    /// Gets all addresses for a user
    /// </summary>
    /// <param name="userId">ID of the user whose addresses to retrieve</param>
    /// <returns>List of user addresses</returns>
    [HttpGet(ApiRoutes.Account.GetAddresses)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<List<AddressResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets all addresses for a user",
        Description = "Retrieves all addresses for the specified user",
        OperationId = "Account_GetAddresses",
        Tags = new[] { "Account" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AccountExamples))]
    public async Task<IActionResult> GetAddresses([FromRoute] Guid userId)
    {
        // Check if the current user has permission to access this user's addresses
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<List<AddressResponse>>(
                "You do not have permission to access this user's addresses"
            );
        }

        var query = new GetUserAddressesQuery(userId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<List<AddressResponse>>(result.Error);
        }

        // Map to response model
        var response = result
            .Value.Select(a => new AddressResponse
            {
                Id = a.Id,
                StreetAddress = a.StreetAddress,
                City = a.City,
                State = a.State,
                PostalCode = a.PostalCode,
                Latitude = a.Latitude,
                Longitude = a.Longitude,
                IsPrimary = a.IsPrimary,
                Label = a.Label,
            })
            .ToList();

        return SuccessResponse(response, "Addresses retrieved successfully");
    }

    /// <summary>
    /// Gets a specific address by ID for a user
    /// </summary>
    /// <param name="userId">ID of the user whose address to retrieve</param>
    /// <param name="addressId">Address ID</param>
    /// <returns>Address details</returns>
    [HttpGet(ApiRoutes.Account.GetAddress)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<AddressResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets an address by ID",
        Description = "Retrieves a specific address by its ID for the specified user",
        OperationId = "Account_GetAddress",
        Tags = new[] { "Account" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AccountExamples))]
    public async Task<IActionResult> GetAddress([FromRoute] Guid userId, [FromRoute] Guid addressId)
    {
        // Check if the current user has permission to access this user's address
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<AddressResponse>(
                "You do not have permission to access this user's address"
            );
        }

        var query = new GetAddressQuery(addressId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<AddressResponse>(result.Error);
        }

        // Map to response model
        var response = new AddressResponse
        {
            Id = result.Value.Id,
            StreetAddress = result.Value.StreetAddress,
            City = result.Value.City,
            State = result.Value.State,
            PostalCode = result.Value.PostalCode,
            Latitude = result.Value.Latitude,
            Longitude = result.Value.Longitude,
            IsPrimary = result.Value.IsPrimary,
            Label = result.Value.Label,
        };

        return SuccessResponse(response, "Address retrieved successfully");
    }

    /// <summary>
    /// Creates a new address for a user
    /// </summary>
    /// <param name="userId">ID of the user for whom to create an address</param>
    /// <param name="request">Address creation details</param>
    /// <returns>Created address ID</returns>
    [HttpPost(ApiRoutes.Account.CreateAddress)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<Guid>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Creates a new address",
        Description = "Creates a new address for the specified user",
        OperationId = "Account_CreateAddress",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> CreateAddress(
        [FromRoute] Guid userId,
        [FromBody] CreateAddressRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new CreateAddressRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        // Check if the current user has permission to create an address for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<Guid>(
                "You do not have permission to create an address for this user"
            );
        }

        // Map to application model
        var appRequest = new CreateAddressRequest
        {
            StreetAddress = request.StreetAddress,
            City = request.City,
            State = request.State,
            PostalCode = request.PostalCode,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            IsPrimary = request.IsPrimary,
            Label = request.Label,
        };

        var command = new CreateAddressCommand(userId, appRequest);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<Guid>(result.Error);
        }

        return SuccessResponse(result.Value, "Address created successfully");
    }

    /// <summary>
    /// Updates an existing address for a user
    /// </summary>
    /// <param name="userId">ID of the user whose address to update</param>
    /// <param name="addressId">Address ID</param>
    /// <param name="request">Address update details</param>
    /// <returns>Success or error response</returns>
    [HttpPut(ApiRoutes.Account.UpdateAddress)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Updates an address",
        Description = "Updates an existing address for the specified user",
        OperationId = "Account_UpdateAddress",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> UpdateAddress(
        [FromRoute] Guid userId,
        [FromRoute] Guid addressId,
        [FromBody] UpdateAddressRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new UpdateAddressRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        // Check if the current user has permission to update this user's address
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to update this user's address"
            );
        }

        // Map to application model
        var appRequest = new UpdateAddressRequest
        {
            AddressId = addressId,
            StreetAddress = request.StreetAddress,
            City = request.City,
            State = request.State,
            PostalCode = request.PostalCode,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            IsPrimary = request.IsPrimary,
            Label = request.Label,
        };

        var command = new UpdateAddressCommand(userId, appRequest);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return NoContent();
    }

    /// <summary>
    /// Deletes an address for a user
    /// </summary>
    /// <param name="userId">ID of the user whose address to delete</param>
    /// <param name="addressId">Address ID</param>
    /// <returns>Success or error response</returns>
    [HttpDelete(ApiRoutes.Account.DeleteAddress)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Deletes an address",
        Description = "Deletes an address for the specified user",
        OperationId = "Account_DeleteAddress",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> DeleteAddress(
        [FromRoute] Guid userId,
        [FromRoute] Guid addressId
    )
    {
        // Check if the current user has permission to delete this user's address
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to delete this user's address"
            );
        }

        var command = new DeleteAddressCommand(userId, addressId);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return NoContent();
    }

    /// <summary>
    /// Sets an address as the primary address for a user
    /// </summary>
    /// <param name="userId">ID of the user whose address to set as primary</param>
    /// <param name="addressId">Address ID</param>
    /// <returns>Success or error response</returns>
    [HttpPut(ApiRoutes.Account.SetPrimaryAddress)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Sets primary address",
        Description = "Sets an address as the primary address for the specified user",
        OperationId = "Account_SetPrimaryAddress",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> SetPrimaryAddress(
        [FromRoute] Guid userId,
        [FromRoute] Guid addressId
    )
    {
        // Check if the current user has permission to set this user's primary address
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to set this user's primary address"
            );
        }

        var command = new SetPrimaryAddressCommand(userId, addressId);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return NoContent();
    }

    #endregion

    #region Care Category Endpoints

    /// <summary>
    /// Adds a care category to a care provider
    /// </summary>
    /// <param name="userId">ID of the user to whom to add the category</param>
    /// <param name="request">Category to add</param>
    /// <returns>Success or error response</returns>
    [HttpPost(ApiRoutes.Account.AddCategory)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Adds a care category to a provider",
        Description = "Adds a care category to the specified care provider's profile",
        OperationId = "Account_AddCategory",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> AddCategory(
        [FromRoute] Guid userId,
        [FromBody] ProviderCategoryRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new ProviderCategoryRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        // Check if the current user has permission to add a category for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to add a category for this user"
            );
        }

        var command = new AddProviderCategoryCommand(userId, request, currentUserId.Value);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse("Category added successfully");
    }

    /// <summary>
    /// Adds multiple care categories to a care provider
    /// </summary>
    /// <param name="userId">ID of the user to whom to add the categories</param>
    /// <param name="request">Categories to add</param>
    /// <returns>Success or error response</returns>
    [HttpPost(ApiRoutes.Account.AddCategories)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Adds multiple care categories to a provider",
        Description = "Adds multiple care categories to the specified care provider's profile",
        OperationId = "Account_AddCategories",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> AddCategories(
        [FromRoute] Guid userId,
        [FromBody] ProviderCategoriesRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new ProviderCategoriesRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        // Check if the current user has permission to add categories for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to add categories for this user"
            );
        }

        var command = new AddProviderCategoriesCommand(userId, request, currentUserId.Value);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse("Categories added successfully");
    }

    /// <summary>
    /// Removes a care category from a care provider
    /// </summary>
    /// <param name="userId">ID of the user from whom to remove the category</param>
    /// <param name="request">Category to remove</param>
    /// <returns>Success or error response</returns>
    [HttpDelete(ApiRoutes.Account.RemoveCategory)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Removes a care category from a provider",
        Description = "Removes a care category from the specified care provider's profile",
        OperationId = "Account_RemoveCategory",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> RemoveCategory(
        [FromRoute] Guid userId,
        [FromBody] ProviderCategoryRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new ProviderCategoryRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        // Check if the current user has permission to remove a category for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to remove a category for this user"
            );
        }

        var command = new RemoveProviderCategoryCommand(userId, request, currentUserId.Value);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse("Category removed successfully");
    }

    #endregion

    #region Document Endpoints

    /// <summary>
    /// Gets all documents for a user
    /// </summary>
    /// <param name="userId">ID of the user whose documents to retrieve</param>
    /// <returns>List of user documents</returns>
    [HttpGet(ApiRoutes.Account.GetDocuments)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<List<DocumentResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets all documents for a user",
        Description = "Retrieves all documents for the specified user",
        OperationId = "Account_GetDocuments",
        Tags = new[] { "Account" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AccountExamples))]
    public async Task<IActionResult> GetDocuments([FromRoute] Guid userId)
    {
        // Check if the current user has permission to access this user's documents
        var query = new GetDocumentsByUserIdQuery(userId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<List<DocumentResponse>>(result.Error);
        }

        return SuccessResponse(result.Value.ToList(), "Documents retrieved successfully");
    }

    /// <summary>
    /// Gets a specific document by ID for a user
    /// </summary>
    /// <param name="userId">ID of the user whose document to retrieve</param>
    /// <param name="documentId">Document ID</param>
    /// <returns>Document details</returns>
    [HttpGet(ApiRoutes.Account.GetDocument)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<DocumentResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets a document by ID",
        Description = "Retrieves a specific document by its ID for the specified user",
        OperationId = "Account_GetDocument",
        Tags = new[] { "Account" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AccountExamples))]
    public async Task<IActionResult> GetDocument(
        [FromRoute] Guid userId,
        [FromRoute] Guid documentId
    )
    {
        // Check if the current user has permission to access this user's document
        var query = new GetDocumentByIdQuery(documentId, userId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<DocumentResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Document retrieved successfully");
    }

    /// <summary>
    /// Uploads a document for a user
    /// </summary>
    /// <param name="userId">ID of the user for whom to upload the document</param>
    /// <param name="request">The upload document request containing the file and document type</param>
    /// <returns>Details of the uploaded document</returns>
    [HttpPost(ApiRoutes.Account.UploadDocument)]
    [Authorize]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<DocumentResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Uploads a document",
        Description = "Uploads a document file for the specified user. Supported formats: JPEG, PNG, PDF. Maximum size: 5MB.",
        OperationId = "Account_UploadDocument",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> UploadDocument(
        [FromRoute] Guid userId,
        [FromForm] UploadDocumentRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new UploadDocumentRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        // Check if the current user has permission to upload a document for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<DocumentResponse>(
                "You do not have permission to upload a document for this user"
            );
        }

        var command = new UploadDocumentCommand(
            request.File,
            request.DocumentType,
            userId,
            request.Issuer,
            request.Country,
            request.CertificationType,
            request.OtherCertificationType,
            request.CertificationNumber,
            request.ExpiryDate
        );
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<DocumentResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Document uploaded successfully");
    }

    /// <summary>
    /// Updates a document for a user
    /// </summary>
    /// <param name="userId">ID of the user whose document to update</param>
    /// <param name="documentId">Document ID</param>
    /// <param name="request">The update document request containing the file</param>
    /// <returns>Success or failure result</returns>
    [HttpPut(ApiRoutes.Account.UpdateDocument)]
    [Authorize]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<DocumentResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Updates a document",
        Description = "Updates an existing document with a new file for the specified user",
        OperationId = "Account_UpdateDocument",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> UpdateDocument(
        [FromRoute] Guid userId,
        [FromRoute] Guid documentId,
        [FromForm] UpdateDocumentRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new UpdateDocumentRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<AuthResponse>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }
        // Check if the current user has permission to update this user's document
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<DocumentResponse>(
                "You do not have permission to update this user's document"
            );
        }

        var command = new UpdateDocumentCommand(
            request.File,
            documentId,
            userId,
            request.DocumentType,
            request.Issuer,
            request.Country,
            request.CertificationType,
            request.OtherCertificationType,
            request.CertificationNumber,
            request.ExpiryDate
        );
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<DocumentResponse>(result.Error);
        }

        return SuccessResponse(result.Value, "Document updated successfully");
    }

    /// <summary>
    /// Deletes a document for a user
    /// </summary>
    /// <param name="userId">ID of the user whose document to delete</param>
    /// <param name="documentId">Document ID</param>
    /// <returns>Success or failure result</returns>
    [HttpDelete(ApiRoutes.Account.DeleteDocument)]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<bool>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Deletes a document",
        Description = "Deletes a document for the specified user",
        OperationId = "Account_DeleteDocument",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> DeleteDocument(
        [FromRoute] Guid userId,
        [FromRoute] Guid documentId
    )
    {
        // Check if the current user has permission to delete this user's document
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId))
        {
            return ForbiddenResponse<bool>(
                "You do not have permission to delete this user's document"
            );
        }

        var command = new DeleteDocumentCommand(documentId, userId);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            return ErrorResponseFromError<bool>(result.Error);
        }

        return SuccessResponse(result.Value, "Document deleted successfully");
    }

    #endregion

    #region Payment Method Endpoints

    [Authorize]
    [HttpGet(ApiRoutes.Account.GetPaymentMethods)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Get User's Payment Methods",
        Description = "Retrieves a list of all saved payment methods for the specified user.",
        OperationId = "Account_GetPaymentMethods",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> GetPaymentMethods([FromRoute] Guid userId)
    {
        if (userId != _currentUserService.UserId)
            return Forbid();

        var methods = await _paymentStore.GetPaymentMethodsForUserAsync(userId);
        return SuccessResponse(methods, "Payment methods retrieved successfully");
    }

    [Authorize]
    [HttpPost(ApiRoutes.Account.AddPaymentMethod)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Add a Payment Method",
        Description = "Creates and saves a new payment method for the specified user.",
        OperationId = "Account_AddPaymentMethod",
        Tags = new[] { "Account" }
    )]
    public async Task<IActionResult> AddPaymentMethod(
        [FromRoute] Guid userId,
        [FromBody] PaymentMethodRequest request
    )
    {
        if (userId != _currentUserService.UserId)
            return Forbid();

        var newMethod = await _paymentStore.AddPaymentMethodAsync(userId, request);
        return SuccessResponse(newMethod, "Payment method added successfully");
    }

    #endregion
}
