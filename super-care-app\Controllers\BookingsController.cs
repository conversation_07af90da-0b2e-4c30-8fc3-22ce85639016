﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using super_care_app.Models.Doc;
using super_care_app.Shared.Constants;
using super_care_app.Shared.Utility;
using SuperCareApp.Application.Common.Interfaces;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Services.Bookings.Commands;
using SuperCareApp.Persistence.Services.Bookings.Queries;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.Filters;

namespace super_care_app.Controllers;

[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<Error>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<Error>))]
public class BookingsController : BaseController
{
    private readonly ILogger<BookingsController> _logger;
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;
    private readonly RequestValidator _requestValidator;

    public BookingsController(
        ILogger<BookingsController> logger,
        IMediator mediator,
        ICurrentUserService currentUserService,
        RequestValidator requestValidator
    )
    {
        _logger = logger;
        _mediator = mediator;
        _currentUserService = currentUserService;
        _requestValidator = requestValidator;
    }

    #region Availability Management

    /// <summary>
    /// Creates an availability for a care provider
    /// </summary>
    /// <param name="userId">ID of the user for whom to create availability</param>
    /// <param name="request">Availability details</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPost(ApiRoutes.Bookings.CreateAvailability)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Creates an availability",
        Description = "Creates a new availability for the specified care provider",
        OperationId = "Bookings_CreateAvailability",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> CreateAvailability(
        [FromRoute] Guid userId,
        [FromBody] CreateAvailabilityRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new CreateAvailabilityRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        // Check if the current user has permission to create availability for this user
        var currentUserId = _currentUserService.UserId;
        if (currentUserId == null || (currentUserId.Value != userId && !User.IsInRole("Admin")))
        {
            return ForbiddenResponse<object>(
                "You do not have permission to create availability for this user"
            );
        }

        var slots = request
            .Slots.Select(s => new AvailabilitySlot
            {
                StartTime = s.StartTime,
                EndTime = s.EndTime,
            })
            .ToList();

        var command = new CreateAvailabilityCommand(
            userId,
            request.DayOfWeek.Trim(),
            request.IsAvailable,
            slots
        );

        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error creating availability: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Availability created successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Updates an availability for a care provider
    /// </summary>
    /// <param name="availabilityId">The ID of the availability to update</param>
    /// <param name="request">The updated availability details</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPut(ApiRoutes.Bookings.UpdateAvailability)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Updates an availability",
        Description = "Updates an existing availability record for a care provider",
        OperationId = "Bookings_UpdateAvailability",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> UpdateAvailability(
        [FromRoute] Guid availabilityId,
        [FromBody] UpdateAvailabilityRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new UpdateAvailabilityRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        // Map request.Slots to List<AvailabilitySlot>
        var slots = request
            .Slots.Select(s => new AvailabilitySlot
            {
                StartTime = s.StartTime,
                EndTime = s.EndTime,
            })
            .ToList();

        var command = new UpdateAvailabilityCommand(
            userId,
            availabilityId,
            request.IsAvailable,
            slots
        );

        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error updating availability: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Availability updated successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Bulk updates multiple availability records in a single request
    /// </summary>
    /// <param name="providerId"></param>
    /// <param name="request">The bulk update request containing multiple availability records to update</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPost(ApiRoutes.Bookings.BulkUpdateAvailability)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Bulk updates availabilities",
        Description = "Updates multiple availability records in a single transaction",
        OperationId = "Bookings_BulkUpdateAvailability",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerRequestExample(typeof(BulkUpdateAvailabilityRequest), typeof(BookingExamples))]
    public async Task<IActionResult> BulkUpdateAvailability(
        [FromRoute] Guid providerId,
        [FromBody] BulkUpdateAvailabilityRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new BulkUpdateAvailabilityRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var availabilityUpdates = request
            .Availabilities.Select(a =>
            {
                var slots = a
                    .Slots.Select(s => new AvailabilitySlot
                    {
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                    })
                    .ToList();

                return (a.DayOfWeek, a.IsAvailable, slots);
            })
            .ToList();

        var command = new BulkUpdateAvailabilityCommand(
            providerId,
            availabilityUpdates,
            request.BufferDuration,
            request.ProvidesRecurringBooking,
            request.WorkingHoursPerDay
        );
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            _logger.LogError("Error bulk updating availabilities: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Availabilities updated successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Deletes an availability for a care provider
    /// </summary>
    /// <param name="availabilityId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpDelete(ApiRoutes.Bookings.DeleteAvailability)]
    public async Task<IActionResult> DeleteAvailability([FromRoute] Guid availabilityId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        var command = new DeleteAvailabilityCommand(userId, availabilityId);
        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error deleting availability: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Availability deleted successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Gets enhanced availability for a specific care provider with monthly calendar view
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <returns>Enhanced availability with available days by month and weekly slots</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetAvailability)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<AvailabilityResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets provider enhanced availability",
        Description = "Retrieves enhanced availability for a specific care provider with monthly calendar view. Returns available slots by day of week and available days by month, considering leave periods and existing bookings.",
        OperationId = "Bookings_GetProviderAvailability",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(BookingExamples))]
    public async Task<IActionResult> GetProviderAvailability([FromRoute] Guid providerId)
    {
        var query = new GetProviderAvailabilityQuery(providerId);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting provider enhanced availability: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            result.Value,
            "Provider enhanced availability retrieved successfully"
        );
    }

    /// <summary>
    /// Gets provider availability for a specific date, considering leave periods and existing bookings
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="date">The specific date to check availability for (YYYY-MM-DD)</param>
    /// <returns>Availability for the specific date with isAvailable false if on leave and no slots if unavailable</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetAvailabilityForDate)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<AvailabilityResponse>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets provider availability for a specific date",
        Description = "Returns provider availability for a specific date, considering leave periods and existing bookings. If provider is on leave, isAvailable will be false with no slots.",
        OperationId = "Bookings_GetProviderAvailabilityForDate",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> GetProviderAvailabilityForDate(
        [FromRoute] Guid providerId,
        [FromRoute] DateOnly date
    )
    {
        var query = new GetProviderAvailabilityForDateQuery(providerId, date);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting provider availability for date: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            result.Value,
            "Provider availability for date retrieved successfully"
        );
    }

    /// <summary>
    /// Gets provider availability template showing all configured slots regardless of bookings.
    /// isAvailable is based only on leave periods and manual availability overrides.
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="checkDate">Optional date to check for leave periods (YYYY-MM-DD)</param>
    /// <returns>Availability template with all configured slots</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetAvailabilityTemplate)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<AvailabilityResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets provider availability template",
        Description = "Returns provider availability template showing all configured slots regardless of bookings. isAvailable is based only on leave periods and manual availability overrides.",
        OperationId = "Bookings_GetProviderAvailabilityTemplate",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> GetProviderAvailabilityTemplate(
        [FromRoute] Guid providerId,
        [FromQuery] DateTime? checkDate = null
    )
    {
        var query = new GetProviderAvailabilityTemplateQuery(providerId, checkDate);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting provider availability template: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            result.Value,
            "Provider availability template retrieved successfully"
        );
    }

    /// <summary>
    /// Updates availability status (manual override) for a specific availability record
    /// </summary>
    /// <param name="availabilityId">The ID of the availability to update</param>
    /// <param name="request">Update details</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPatch(ApiRoutes.Bookings.UpdateAvailabilityStatus)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [SwaggerOperation(
        Summary = "Updates availability status",
        Description = "Updates the availability status (manual override) for a specific availability record. This allows providers to manually enable/disable their availability.",
        OperationId = "Bookings_UpdateAvailabilityStatus",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> UpdateAvailabilityStatus(
        [FromRoute] Guid availabilityId,
        [FromBody] UpdateAvailabilityStatusRequest request
    )
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        var validator = await _requestValidator.ValidateAsync(
            request,
            new UpdateAvailabilityStatusRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        var command = new UpdateAvailabilityStatusCommand(
            userId,
            availabilityId,
            request.IsAvailable,
            request.Reason
        );

        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error updating availability status: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Availability status updated successfully", availabilityId)
        );
    }

    /// <summary>
    /// Gets available time slots for a specific care provider on a given date
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="date">The date to check for availability</param>
    /// <param name="durationMinutes">Duration of each time slot in minutes</param>
    /// <returns>List of available time slots</returns>
    [HttpGet(ApiRoutes.Bookings.GetAvailableTimeSlots)]
    [Authorize]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<TimeSlotResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets available time slots for a care provider",
        Description = "Retrieves all available time slots for a specific care provider on a given date with specified duration",
        OperationId = "Bookings_GetAvailableTimeSlots",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(BookingExamples))]
    public async Task<IActionResult> GetAvailableTimeSlots(
        [FromRoute] Guid providerId,
        [FromQuery] DateTime date,
        [FromQuery] int durationMinutes = 60
    )
    {
        var query = new GetAvailableTimeSlotsQuery(providerId, date, durationMinutes);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting availability: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value);
    }

    #endregion

    #region Booking Management

    /// <summary>
    /// Creates a booking for a client with a care provider
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost(ApiRoutes.Bookings.CreateBooking)]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Creates a booking for a client with a care provider",
        Description = "Creates a booking for a client with a care provider",
        OperationId = "Bookings_CreateBooking",
        Tags = ["Bookings"]
    )]
    public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequest request)
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new CreateBookingRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        Result<Guid> bookingResult;
        if (request.BookingWindows != null && request.BookingWindows.Count > 1)
        {
            var command = new CreateBookingWithWindowsCommand(userId, request);
            bookingResult = await _mediator.Send(command);
        }
        else
        {
            var startDate = request.BookingWindows.First().Date;
            var endDate = request.BookingWindows.First().Date;
            var startTime = request.BookingWindows.First().StartTime;
            var endTime = request.BookingWindows.First().EndTime;
            var command = new CreateBookingCommand(
                userId,
                request.ProviderId,
                request.CategoryId,
                startDate,
                endDate,
                startTime,
                endTime,
                request.SpecialInstructions.Trim()
            );
            bookingResult = await _mediator.Send(command);
        }

        if (bookingResult.IsFailure)
        {
            _logger.LogError("Error creating booking: {Error}", bookingResult.Error);
            return ErrorResponseFromError<object>(bookingResult.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Booking created successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Updates a booking for a client with a care provider
    /// </summary>
    /// <param name="request"></param>
    /// <param name="bookingId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPut(ApiRoutes.Bookings.UpdateBooking)]
    public async Task<IActionResult> UpdateBooking(
        [FromRoute] Guid bookingId,
        [FromBody] UpdateBookingWithWindowsRequest request
    )
    {
        // Validate request using FluentValidation
        var validationResult = await _requestValidator.ValidateAsync(
            request,
            new UpdateBookingWithWindowsRequestValidator()
        );
        if (!validationResult.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validationResult.Error.ValidationErrors)
            );
        }

        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        // Dispatch the command to the handler
        var command = new UpdateBookingWithWindowsCommand(userId, bookingId, request);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            _logger.LogError("Error updating booking: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Booking updated successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Deletes a booking for a client with a care provider
    /// </summary>
    /// <param name="bookingId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpDelete(ApiRoutes.Bookings.DeleteBooking)]
    public async Task<IActionResult> DeleteBooking([FromRoute] Guid bookingId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        var command = new DeleteBookingCommand(userId, bookingId);
        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error deleting booking: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Booking deleted successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Cancels a booking for a client with a care provider
    /// </summary>
    /// <param name="bookingId">The ID of the booking to cancel</param>
    /// <param name="request">Cancellation details</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPost("{bookingId:guid}/cancel")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Cancels a booking",
        Description = "Cancels an existing booking with optional cancellation reason. Handles overlapping bookings gracefully.",
        OperationId = "Bookings_Cancel",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> CancelBooking(
        [FromRoute] Guid bookingId,
        [FromBody] CancelBookingRequest request
    )
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        var validator = await _requestValidator.ValidateAsync(
            request,
            new CancelBookingRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        var command = new CancelBookingCommand(
            userId,
            bookingId,
            request.Reason,
            request.NotifyOtherParty,
            request.ForceCancel
        );

        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error cancelling booking: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Booking cancelled successfully", bookingId)
        );
    }

    [Authorize]
    [HttpPut(ApiRoutes.Bookings.UpdateBookingStatus)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Update Booking Status",
        Description = "Updates the status of a specific booking. "
            + "This action can only be performed by the client or the care provider associated with the booking.<br/><br/>"
            + "**Valid Statuses:** `Requested`, `Accepted`, `Rejected`, `Cancelled`, `Completed`, `InProgress`, `Expired`, `Confirmed`, `Modified`, `NoShow`.<br/><br/>"
            + "A `reason` or `notes` field is required when updating the status to `Cancelled`, `Rejected`, or `NoShow`.",
        OperationId = "Bookings_UpdateBookingStatus",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> UpdateBookingStatus(
        [FromRoute] Guid bookingId,
        [FromBody] UpdateBookingStatusRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(
            request,
            new UpdateBookingStatusRequestValidator()
        );
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        var command = new UpdateBookingStatusCommand(bookingId, request.Status, request.Notes);

        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error updating booking status: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        _logger.LogInformation(
            "Booking status updated successfully for booking {BookingId}",
            bookingId
        );
        var response = new GenericObjectResponse(
            "Booking status updated successfully",
            Guid.NewGuid()
        );
        return SuccessResponse(response);
    }

    /// <summary>
    /// Gets a booking for a client with a care provider
    /// </summary>
    /// <param name="bookingId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetBookingById)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<BookingResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Get bookings by id",
        Description = "Retrieves all bookings for a specific care provider",
        OperationId = "Bookings_GetBookingById",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(BookingExamples))]
    public async Task<IActionResult> GetBookingById([FromRoute] Guid bookingId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        var query = new GetBookingByIdQuery(bookingId);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting booking: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value);
    }

    /// <summary>
    /// Get all the bookings for a client with a care provider
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetAllBookings)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<BookingResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Get all bookings",
        Description = "Retrieves all bookings for a client with a care provider",
        OperationId = "Bookings_GetAllBookings",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(BookingExamples))]
    public async Task<IActionResult> GetAllBookings([FromQuery] BookingsRequest request)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var paginationParams = new BookingListParams
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            SortBy = request.SortBy,
            SortDescending = request.SortDescending,
        };
        var query = new GetBookingsQuery(request, paginationParams);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting booking: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        var meta = result.Value.ToMetadata();
        return PaginatedResponse(result.Value.Bookings, meta, "Bookings retrieved successfully");
    }

    #endregion

    #region Leave Management

    /// <summary>
    /// Creates a leave period for a care provider
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="request">Leave details</param>
    /// <returns>Success or error response</returns>
    [Authorize(Roles = "CareProvider")]
    [HttpPost(ApiRoutes.Bookings.CreateLeave)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Creates a leave period",
        Description = "Creates a new leave period for the specified care provider",
        OperationId = "Bookings_CreateLeave",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> CreateLeave(
        [FromRoute] Guid providerId,
        [FromBody] LeaveRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(request, new LeaveRequestValidator());
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        var command = new AddLeaveCommand(providerId, request);
        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error creating leave: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Leave created successfully", result.Value)
        );
    }

    /// <summary>
    /// Updates a leave period for a care provider
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="leaveId">The ID of the leave period</param>
    /// <param name="request">Updated leave details</param>
    /// <returns>Success or error response</returns>
    [Authorize(Roles = "CareProvider")]
    [HttpPut(ApiRoutes.Bookings.UpdateLeave)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Updates a leave period",
        Description = "Updates an existing leave period for the specified care provider",
        OperationId = "Bookings_UpdateLeave",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> UpdateLeave(
        [FromRoute] Guid providerId,
        [FromRoute] Guid leaveId,
        [FromBody] LeaveRequest request
    )
    {
        var validator = await _requestValidator.ValidateAsync(request, new LeaveRequestValidator());
        if (!validator.IsSuccess)
        {
            return ErrorResponseFromError<object>(
                Error.Validation("Validation failed", validator.Error.ValidationErrors)
            );
        }

        var command = new UpdateLeaveCommand(providerId, leaveId, request);
        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error updating leave: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Leave updated successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Deletes a leave period for a care provider
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="leaveId">The ID of the leave period</param>
    /// <returns>Success or error response</returns>
    [Authorize(Roles = "CareProvider")]
    [HttpDelete(ApiRoutes.Bookings.DeleteLeave)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Deletes a leave period",
        Description = "Deletes an existing leave period for the specified care provider",
        OperationId = "Bookings_DeleteLeave",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> DeleteLeave(
        [FromRoute] Guid providerId,
        [FromRoute] Guid leaveId
    )
    {
        var command = new DeleteLeaveCommand(providerId, leaveId);
        var result = await _mediator.Send(command);
        if (result.IsFailure)
        {
            _logger.LogError("Error deleting leave: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Leave deleted successfully", Guid.NewGuid())
        );
    }

    /// <summary>
    /// Gets a specific leave period for a care provider
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <param name="leaveId">The ID of the leave period</param>
    /// <returns>Leave details</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetLeave)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<LeaveResponse>))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets a leave period",
        Description = "Retrieves a specific leave period for the specified care provider",
        OperationId = "Bookings_GetLeave",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> GetLeave([FromRoute] Guid providerId, [FromRoute] Guid leaveId)
    {
        var query = new GetLeaveQuery(providerId, leaveId);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting leave: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value, "Leave retrieved successfully");
    }

    /// <summary>
    /// Gets all leave periods for a specific care provider
    /// </summary>
    /// <param name="providerId">The ID of the care provider</param>
    /// <returns>List of leave periods</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetProviderLeaves)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<LeaveResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets provider leaves",
        Description = "Retrieves all leave periods for a specific care provider",
        OperationId = "Bookings_GetProviderLeaves",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(BookingExamples))]
    public async Task<IActionResult> GetProviderLeaves([FromRoute] Guid providerId)
    {
        var query = new GetProviderLeavesQuery(providerId);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting provider leaves: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value, "Provider leaves retrieved successfully");
    }

    /// <summary>
    /// Gets all leave periods for the current user
    /// </summary>
    /// <returns>List of leave periods</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetMyLeaves)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(ApiResponseModel<IEnumerable<LeaveResponse>>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Gets current user's leaves",
        Description = "Retrieves all leave periods for the current user",
        OperationId = "Bookings_GetMyLeaves",
        Tags = new[] { "Bookings" }
    )]
    [SwaggerResponseExample(StatusCodes.Status200OK, typeof(BookingExamples))]
    public async Task<IActionResult> GetMyLeaves()
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequest("Unable to retrieve user ID.");
        }

        var query = new GetMyLeavesQuery(userId);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting user leaves: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value, "User leaves retrieved successfully");
    }

    /// <summary>
    /// Gets all leave periods with pagination and filtering
    /// </summary>
    /// <param name="parameters">Filter and pagination parameters</param>
    /// <returns>Paginated list of leave periods</returns>
    [Authorize(Roles = "Admin")]
    [HttpGet(ApiRoutes.Bookings.GetAllLeaves)]
    [ProducesResponseType(
        StatusCodes.Status200OK,
        Type = typeof(PaginatedResponseModel<IEnumerable<LeaveResponse>>)
    )]
    [SwaggerOperation(
        Summary = "Gets all leaves",
        Description = "Retrieves all leave periods with pagination and filtering",
        OperationId = "Bookings_GetAllLeaves",
        Tags = new[] { "Bookings" }
    )]
    public async Task<IActionResult> GetAllLeaves([FromQuery] LeaveListParams parameters)
    {
        var query = new GetAllLeavesQuery(parameters);
        var result = await _mediator.Send(query);
        if (result.IsFailure)
        {
            _logger.LogError("Error getting leaves: {Error}", result.Error);
            return ErrorResponseFromError<object>(result.Error);
        }

        return PaginatedResponse(
            result.Value.Items,
            result.Value.Pagination,
            "Leaves retrieved successfully"
        );
    }

    #endregion

    #region Time Tracking Management
    #endregion

    #region Invoices Management
    /// <summary>
    /// Retrieves an invoice by its unique identifier
    /// </summary>
    /// <param name="invoiceId">The unique identifier of the invoice</param>
    /// <returns>The invoice details or an error response</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetInvoice)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Get invoice by ID",
        Description = "Retrieves the details of a specific invoice by its unique identifier",
        OperationId = "Invoices_GetInvoiceById",
        Tags = new[] { "Invoices" }
    )]
    public async Task<IActionResult> GetInvoice(Guid invoiceId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var query = new GetInvoiceByIdQuery(invoiceId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            _logger.LogError(
                "Error retrieving invoice {InvoiceId}: {Error}",
                invoiceId,
                result.Error
            );
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value);
    }

    /// <summary>
    /// Retrieves all invoices associated with a specific booking
    /// </summary>
    /// <param name="bookingId">The unique identifier of the booking</param>
    /// <returns>A list of invoices for the specified booking or an error response</returns>
    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetInvoicesForBooking)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Get invoices for booking",
        Description = "Retrieves all invoices associated with a specific booking",
        OperationId = "Invoices_GetInvoicesForBooking",
        Tags = new[] { "Invoices" }
    )]
    public async Task<IActionResult> GetInvoicesForBooking(Guid bookingId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var query = new GetInvoicesForBookingQuery(bookingId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            _logger.LogError(
                "Error retrieving invoices for booking {BookingId}: {Error}",
                bookingId,
                result.Error
            );
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value);
    }

    /// <summary>
    /// Generates invoices for a specific booking window
    /// </summary>
    /// <param name="bookingWindowId">The unique identifier of the booking</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPost(ApiRoutes.Bookings.GenerateInvoicesForBooking)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Generate invoices for booking",
        Description = "Generates new invoices for a specific booking",
        OperationId = "Invoices_GenerateInvoicesForBooking",
        Tags = new[] { "Invoices" }
    )]
    public async Task<IActionResult> GenerateInvoicesForBooking(Guid bookingWindowId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var command = new GenerateInvoicesForBookingCommand(bookingWindowId);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            _logger.LogError(
                "Error generating invoice for booking window {BookingWindowId}: {Error}",
                bookingWindowId,
                result.Error
            );
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Invoices generated successfully", Guid.NewGuid())
        );
    }

    [Authorize]
    [HttpGet(ApiRoutes.Bookings.GetUserInvoices)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Get invoices for authenticated user",
        Description = "Gets list of invoices for a authrnticated user",
        OperationId = "Invoices_GetUserInvocies",
        Tags = new[] { "Invoices" }
    )]
    public async Task<IActionResult> GetUserInvoices()
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return UnauthorizedResponse<object>("User is not authenticated.");
        }

        var userId = _currentUserService.UserId ?? Guid.Empty;
        if (userId == Guid.Empty)
        {
            return BadRequestResponse<object>("Unable to retrieve user ID.");
        }

        var query = new GetUserInvoicesQuery(userId);
        var result = await _mediator.Send(query);

        if (result.IsFailure)
        {
            _logger.LogError(
                "Error retrieving invoices for user {UserId}: {Error}",
                userId,
                result.Error
            );
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(result.Value);
    }

    /// <summary>
    /// Marks an invoice as paid
    /// </summary>
    /// <param name="invoiceId">The unique identifier of the invoice to mark as paid</param>
    /// <returns>Success or error response</returns>
    [Authorize]
    [HttpPut(ApiRoutes.Bookings.MarkInvoiceAsPaid)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
    [ProducesResponseType(
        StatusCodes.Status401Unauthorized,
        Type = typeof(ApiResponseModel<object>)
    )]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
    [SwaggerOperation(
        Summary = "Mark invoice as paid",
        Description = "Updates the status of a specific invoice to paid",
        OperationId = "Invoices_MarkInvoiceAsPaid",
        Tags = new[] { "Invoices" }
    )]
    public async Task<IActionResult> MarkInvoiceAsPaid(Guid invoiceId)
    {
        if (!_currentUserService.IsAuthenticated)
        {
            return Unauthorized("User is not authenticated.");
        }

        var command = new MarkInvoiceAsPaidCommand(invoiceId);
        var result = await _mediator.Send(command);

        if (result.IsFailure)
        {
            _logger.LogError(
                "Error marking invoice {InvoiceId} as paid: {Error}",
                invoiceId,
                result.Error
            );
            return ErrorResponseFromError<object>(result.Error);
        }

        return SuccessResponse(
            new GenericObjectResponse("Invoice marked as paid successfully", Guid.NewGuid())
        );
    }
    #endregion
}
