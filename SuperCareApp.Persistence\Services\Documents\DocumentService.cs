﻿using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Documents;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Documents;

public class DocumentService : IDocumentService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<DocumentService> _logger;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IApprovalService _approvalService;
    private readonly IFileStorageService _fileStorageService;
    private readonly string[] _allowedExtensions = [".jpg", ".jpeg", ".png", ".pdf"];
    private readonly int _maxFileSizeMb = 10; // 10MB max file size

    public DocumentService(
        ApplicationDbContext dbContext,
        ILogger<DocumentService> logger,
        IUnitOfWork unitOfWork,
        IApprovalService approvalService,
        IFileStorageService fileStorageService
    )
    {
        _dbContext = dbContext;
        _logger = logger;
        _unitOfWork = unitOfWork;
        _approvalService = approvalService;
        _fileStorageService = fileStorageService;
    }

    public async Task<Result<bool>> DeleteDocumentAsync(Guid documentId)
    {
        try
        {
            var document = await _dbContext.Documents.FirstOrDefaultAsync(d =>
                d.Id == documentId && !d.IsDeleted
            );

            if (document == null)
            {
                return Result.Failure<bool>(Error.NotFound("Document not found"));
            }

            // Soft delete the document in the database
            document.IsDeleted = true;
            document.DeletedAt = DateTime.UtcNow;

            // Try to delete the physical file
            try
            {
                // Delete the file using the file storage service
                await _fileStorageService.DeleteFileAsync(document.DocumentUrl);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to delete physical file for document {DocumentId}",
                    documentId
                );
                // Continue with soft delete even if physical delete fails
            }

            await _dbContext.SaveChangesAsync();
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document {DocumentId}", documentId);
            return Result.Failure<bool>(
                Error.Internal("An error occurred while deleting the document")
            );
        }
    }

    public async Task<Result<IEnumerable<DocumentResponse>>> GetAllDocumentsByUserIdAsync(
        Guid userId,
        bool includeUnapproved = true
    )
    {
        try
        {
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(p =>
                p.ApplicationUserId == userId && !p.IsDeleted
            );
            var documentsResult = await _unitOfWork
                .Repository<Document>()
                .As<IDocumentRepository, Document>()
                .GetDocumentsByUserIdAsync(userProfile.Id, includeUnapproved);

            if (documentsResult.IsFailure)
            {
                return Result.Failure<IEnumerable<DocumentResponse>>(documentsResult.Error);
            }

            // Get all user profiles for the documents
            var userIds = documentsResult.Value.Select(d => d.UserId).Distinct().ToList();
            var userProfiles = await _dbContext
                .UserProfiles.Where(p => userIds.Contains(p.Id) && !p.IsDeleted)
                .ToDictionaryAsync(p => p.Id, p => p.ApplicationUserId);

            // Map documents to responses with correct user IDs and absolute URLs
            var documentResponses = documentsResult.Value.Select(d =>
            {
                // Try to get the ApplicationUserId from the dictionary
                if (!userProfiles.TryGetValue(d.UserId, out var applicationUserId))
                {
                    // If not found, use the document's UserId (this shouldn't happen in normal operation)
                    applicationUserId = d.UserId;
                }

                return new DocumentResponse
                {
                    DocumentId = d.Id,
                    UserId = applicationUserId, // Use the ApplicationUserId, not the UserProfile.Id
                    FileName = Path.GetFileName(d.DocumentUrl),
                    DocumentUrl = _fileStorageService.GetFileUrl(d.DocumentUrl), // Convert to absolute URL
                    DocumentType = d.DocumentType,
                    Issuer = d.Issuer,
                    VerificationStatus = d.VerificationStatus.GetDescription(),
                    UploadedAt = d.UploadedAt,
                    VerifiedAt = d.VerifiedAt,
                    VerifiedBy = d.VerifiedBy,
                    RejectionReason = d.RejectionReason,
                    Country = d.Country.GetDisplayName(),
                    CertificationType = d.CertificationType?.GetDisplayName() ?? string.Empty,
                    OtherCertificationType = d.OtherCertificationType,
                    CertificationNumber = d.CertificationNumber,
                    ExpiryDate = d.ExpiryDate,
                    IsExpired = d.IsExpired,
                };
            });

            return Result.Success<IEnumerable<DocumentResponse>>(documentResponses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving documents for user {UserId}", userId);
            return Result.Failure<IEnumerable<DocumentResponse>>(
                Error.Internal("An error occurred while retrieving the documents")
            );
        }
    }

    public async Task<Result<DocumentResponse>> GetDocumentByIdAsync(
        Guid documentId,
        bool includeUnapproved = true
    )
    {
        try
        {
            var documentResult = await _unitOfWork
                .Repository<Document>()
                .As<IDocumentRepository, Document>()
                .GetDocumentByIdAsync(documentId, includeUnapproved);

            if (documentResult.IsFailure)
            {
                return Result.Failure<DocumentResponse>(documentResult.Error);
            }

            var document = documentResult.Value;
            // Get the UserProfile to find the ApplicationUserId
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(p =>
                p.Id == document.UserId && !p.IsDeleted
            );

            if (userProfile == null)
            {
                return Result.Failure<DocumentResponse>(
                    Error.NotFound($"User profile not found for document ID {documentId}")
                );
            }

            var documentResponse = new DocumentResponse
            {
                DocumentId = document.Id,
                UserId = userProfile.ApplicationUserId, // Use the ApplicationUserId, not the UserProfile.Id
                FileName = Path.GetFileName(document.DocumentUrl),
                DocumentUrl = _fileStorageService.GetFileUrl(document.DocumentUrl), // Convert to absolute URL
                DocumentType = document.DocumentType,
                Issuer = document.Issuer,
                VerificationStatus = document.VerificationStatus.GetDescription(),
                UploadedAt = document.UploadedAt,
                VerifiedAt = document.VerifiedAt,
                VerifiedBy = document.VerifiedBy,
                RejectionReason = document.RejectionReason,
                Country = document.Country.GetDisplayName(),
                CertificationType = document.CertificationType?.GetDisplayName() ?? string.Empty,
                OtherCertificationType = document.OtherCertificationType,
                CertificationNumber = document.CertificationNumber,
                ExpiryDate = document.ExpiryDate,
                IsExpired = document.IsExpired,
            };

            return Result.Success(documentResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document {DocumentId}", documentId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while retrieving the document")
            );
        }
    }

    public async Task<Result<DocumentResponse>> UpdateDocumentAsync(
        IFormFile? file,
        Guid documentId,
        string? documentType = null,
        string? issuer = null,
        string? country = null,
        string? certificationType = null,
        string? otherCertificationType = null,
        string? certificationNumber = null,
        DateTime? expiryDate = null
    )
    {
        try
        {
            var document = await _dbContext.Documents.FirstOrDefaultAsync(d =>
                d.Id == documentId && !d.IsDeleted
            );

            if (document == null)
            {
                return Result.Failure<DocumentResponse>(Error.NotFound("Document not found"));
            }

            // Update file if provided
            if (file != null)
            {
                // Validate the file
                var validateResult = _fileStorageService.ValidateFile(
                    file,
                    _maxFileSizeMb,
                    _allowedExtensions
                );
                if (validateResult.IsFailure)
                {
                    return Result.Failure<DocumentResponse>(validateResult.Error);
                }

                // Store the old file path for deletion after successful database update
                string oldFilePath = document.DocumentUrl;

                // Prepare container name based on document type
                var docType = documentType ?? document.DocumentType;
                var containerName = $"documents/{docType}/{document.UserId}";

                // We'll upload the file after database update is successful
                // Just store the file information for now
                document.DocumentType = docType;
            }

            // Update document type if provided
            if (!string.IsNullOrEmpty(documentType))
            {
                document.DocumentType = documentType;
            }

            // Update certificate-related fields if document type is "Certificate"
            if (document.DocumentType.Equals("Certificate", StringComparison.OrdinalIgnoreCase))
            {
                // Parse country if provided
                if (!string.IsNullOrEmpty(country))
                {
                    document.Country = CountryExtensions.FromString(country);
                }

                // Parse certification type if provided
                if (!string.IsNullOrEmpty(certificationType))
                {
                    if (Enum.TryParse<CertificationType>(certificationType, out var certType))
                    {
                        document.CertificationType = certType;
                    }
                    else if (certificationType.Equals("Other", StringComparison.OrdinalIgnoreCase))
                    {
                        document.CertificationType = CertificationType.OtherCertification;

                        // Validate other certification type is provided when "Other" is selected
                        if (string.IsNullOrEmpty(otherCertificationType))
                        {
                            return Result.Failure<DocumentResponse>(
                                Error.BadRequest(
                                    "Please specify the certification type when 'Other' is selected"
                                )
                            );
                        }
                        document.OtherCertificationType = otherCertificationType;
                    }
                }

                // Update issuer if provided
                if (issuer != null)
                {
                    document.Issuer = issuer;
                }

                // Update certification number if provided
                if (certificationNumber != null)
                {
                    document.CertificationNumber = certificationNumber;
                }

                // Update expiry date if provided
                if (expiryDate.HasValue)
                {
                    // Ensure ExpiryDate is UTC
                    document.ExpiryDate = DateTime.SpecifyKind(expiryDate.Value, DateTimeKind.Utc);
                }
            }

            // Reset verification status when document is updated
            document.VerificationStatus = VerificationStatus.Pending;
            document.VerifiedAt = null;
            document.VerifiedBy = null;
            document.RejectionReason = null;

            // Save to database first
            try
            {
                await _dbContext.SaveChangesAsync();

                // If we have a new file to save, handle file operations after successful database update
                if (file != null)
                {
                    // Prepare container name based on document type
                    var docType = documentType ?? document.DocumentType;
                    var containerName = $"documents/{docType}/{document.UserId}";

                    // Upload the new file
                    var uploadResult = await _fileStorageService.UploadFileAsync(
                        file,
                        containerName
                    );

                    if (uploadResult.IsFailure)
                    {
                        _logger.LogError(
                            "Failed to upload new file for document {DocumentId}: {Error}",
                            documentId,
                            uploadResult.Error
                        );
                        return Result.Failure<DocumentResponse>(uploadResult.Error);
                    }

                    // Update the document URL with the new relative path
                    document.DocumentUrl = uploadResult.Value.RelativePath;

                    // Try to delete the old file
                    try
                    {
                        await _fileStorageService.DeleteFileAsync(document.DocumentUrl);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(
                            ex,
                            "Failed to delete old file for document {DocumentId}",
                            documentId
                        );
                        // Continue even if old file delete fails
                    }

                    // Save the updated document URL
                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating document {DocumentId} in database",
                    documentId
                );
                return Result.Failure<DocumentResponse>(
                    Error.Internal("An error occurred while updating the document in the database")
                );
            }

            // Get the UserProfile to find the ApplicationUserId
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(p =>
                p.Id == document.UserId && !p.IsDeleted
            );

            if (userProfile == null)
            {
                return Result.Failure<DocumentResponse>(
                    Error.NotFound($"User profile not found for document ID {documentId}")
                );
            }

            // Create response
            var response = new DocumentResponse
            {
                DocumentId = document.Id,
                UserId = userProfile.ApplicationUserId,
                FileName = Path.GetFileName(document.DocumentUrl),
                DocumentUrl = _fileStorageService.GetFileUrl(document.DocumentUrl), // Convert to absolute URL
                DocumentType = document.DocumentType,
                VerificationStatus = document.VerificationStatus.GetDescription(),
                UploadedAt = document.UploadedAt,
                Country = document.Country.GetDisplayName(),
                CertificationType = document.CertificationType?.GetDisplayName() ?? string.Empty,
                OtherCertificationType = document.OtherCertificationType,
                CertificationNumber = document.CertificationNumber,
                ExpiryDate = document.ExpiryDate,
                IsExpired = document.IsExpired,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document {DocumentId}", documentId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while updating the document")
            );
        }
    }

    public async Task<Result<DocumentResponse>> UploadDocumentAsync(
        IFormFile file,
        string documentType,
        Guid userId,
        string issuer,
        string country,
        string certificationType,
        string? otherCertificationType,
        string? certificationNumber,
        DateTime? expiryDate
    )
    {
        try
        {
            // Validate the file
            var validateResult = _fileStorageService.ValidateFile(
                file,
                _maxFileSizeMb,
                _allowedExtensions
            );
            if (validateResult.IsFailure)
            {
                return Result.Failure<DocumentResponse>(validateResult.Error);
            }

            // Parse country and certification type
            var parsedCountry = CountryExtensions.FromString(country);
            CertificationType? parsedCertificationType = null;

            if (
                !string.IsNullOrEmpty(certificationType)
                && Enum.TryParse<CertificationType>(certificationType, out var certType)
            )
            {
                parsedCertificationType = certType;

                // Validate that the certification type is valid for the selected country
                var validCertifications = CertificationTypeExtensions.GetCertificationsForCountry(
                    parsedCountry
                );
                if (
                    !validCertifications.Contains(certType)
                    && certType != CertificationType.OtherCertification
                )
                {
                    return Result.Failure<DocumentResponse>(
                        Error.BadRequest(
                            $"The certification type '{certificationType}' is not valid for {parsedCountry.GetDisplayName()}"
                        )
                    );
                }
            }

            // If certification type is "Other", ensure otherCertificationType is provided
            if (
                parsedCertificationType == CertificationType.OtherCertification
                && string.IsNullOrEmpty(otherCertificationType)
            )
            {
                return Result.Failure<DocumentResponse>(
                    Error.BadRequest(
                        "Please specify the certification type when 'Other' is selected"
                    )
                );
            }

            // Check if user profile exists
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(p =>
                p.ApplicationUserId == userId && !p.IsDeleted
            );

            if (userProfile == null)
            {
                return Result.Failure<DocumentResponse>(
                    Error.NotFound($"User profile not found for user ID {userId}")
                );
            }

            // Prepare container name for file storage
            var containerName = $"documents/{documentType}/{userId}";

            // Upload the file using the file storage service
            var uploadResult = await _fileStorageService.UploadFileAsync(file, containerName);

            if (uploadResult.IsFailure)
            {
                return Result.Failure<DocumentResponse>(uploadResult.Error);
            }

            // Create a new document entity with the relative file path
            var document = new Document
            {
                UserId = userProfile.Id,
                DocumentType = documentType,
                DocumentUrl = uploadResult.Value.RelativePath,
                Issuer = issuer,
                VerificationStatus = VerificationStatus.Pending,
                UploadedAt = DateTime.UtcNow,
                Country = parsedCountry,
                CertificationType = parsedCertificationType,
                OtherCertificationType = otherCertificationType,
                CertificationNumber = certificationNumber,
                // Ensure ExpiryDate is UTC if provided
                ExpiryDate = expiryDate.HasValue
                    ? DateTime.SpecifyKind(expiryDate.Value, DateTimeKind.Utc)
                    : null,
            };

            // Save to database
            await _dbContext.Documents.AddAsync(document);
            await _dbContext.SaveChangesAsync();

            //Add to approval directly from here
            var approvalResult = await _approvalService.CreateApprovalAsync(
                userId,
                ApprovalType.DocumentVerification,
                null,
                document.Id
            );

            if (approvalResult.IsFailure)
            {
                return Result.Failure<DocumentResponse>(approvalResult.Error);
            }

            // Create response with absolute URL
            var response = new DocumentResponse
            {
                DocumentId = document.Id,
                UserId = userProfile.ApplicationUserId, // Return the ApplicationUserId for the client
                FileName = uploadResult.Value.OriginalFileName,
                DocumentUrl = uploadResult.Value.FileUrl, // Use the URL from file storage service
                DocumentType = documentType,
                VerificationStatus = VerificationStatus.Pending.GetDescription(),
                UploadedAt = document.UploadedAt,
                Country = parsedCountry.GetDisplayName(),
                CertificationType = parsedCertificationType?.GetDisplayName() ?? string.Empty,
                OtherCertificationType = otherCertificationType,
                CertificationNumber = certificationNumber,
                ExpiryDate = expiryDate,
                IsExpired =
                    expiryDate.HasValue
                    && DateTime.SpecifyKind(expiryDate.Value, DateTimeKind.Utc) < DateTime.UtcNow,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for user {UserId}", userId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while uploading the document")
            );
        }
    }

    public async Task<Result<DocumentResponse>> ApproveDocumentAsync(Guid documentId, Guid adminId)
    {
        try
        {
            // 1. Get approval + document + user in one round-trip
            var data = await (
                from a in _dbContext.Approvals
                join d in _dbContext.Documents on a.RelatedEntityId equals d.Id
                join p in _dbContext.UserProfiles on d.UserId equals p.Id
                where a.RelatedEntityId == documentId && !d.IsDeleted && !p.IsDeleted
                select new
                {
                    Approval = a,
                    Doc = d,
                    User = p,
                }
            ).FirstOrDefaultAsync();

            if (data == null)
                return Result.Failure<DocumentResponse>(
                    Error.NotFound("Approval or document not found")
                );

            // 2. Approve the approval – service persists changes
            var approveResult = await _approvalService.ApproveAsync(
                data.Approval.Id,
                adminId,
                "Document approved"
            );

            data.Doc.VerificationStatus = VerificationStatus.Verified;
            data.Doc.VerifiedAt = DateTime.UtcNow;
            data.Doc.VerifiedBy = adminId;
            await _dbContext.SaveChangesAsync();

            return approveResult.IsFailure
                ? Result.Failure<DocumentResponse>(approveResult.Error)
                : Result.Success(
                    new DocumentResponse
                    {
                        DocumentId = data.Doc.Id,
                        UserId = data.User.ApplicationUserId,
                        FileName = Path.GetFileName(data.Doc.DocumentUrl),
                        DocumentUrl = _fileStorageService.GetFileUrl(data.Doc.DocumentUrl),
                        DocumentType = data.Doc.DocumentType,
                        VerificationStatus = data.Doc.VerificationStatus.GetDescription(),
                        UploadedAt = data.Doc.UploadedAt,
                        VerifiedAt = data.Doc.VerifiedAt,
                        VerifiedBy = data.Doc.VerifiedBy,
                        Country = data.Doc.Country.GetDisplayName(),
                        CertificationType =
                            data.Doc.CertificationType?.GetDisplayName() ?? string.Empty,
                        OtherCertificationType = data.Doc.OtherCertificationType,
                        CertificationNumber = data.Doc.CertificationNumber,
                        ExpiryDate = data.Doc.ExpiryDate,
                        IsExpired = data.Doc.IsExpired,
                    }
                );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving document {DocumentId}", documentId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while approving the document")
            );
        }
    }

    public async Task<Result<DocumentResponse>> RejectDocumentAsync(
        Guid documentId,
        Guid adminId,
        string rejectionReason
    )
    {
        try
        {
            var data = await (
                from a in _dbContext.Approvals
                join d in _dbContext.Documents on a.RelatedEntityId equals d.Id
                join p in _dbContext.UserProfiles on d.UserId equals p.Id
                where a.RelatedEntityId == documentId && !d.IsDeleted && !p.IsDeleted
                select new
                {
                    Approval = a,
                    Doc = d,
                    User = p,
                }
            ).FirstOrDefaultAsync();

            if (data == null)
                return Result.Failure<DocumentResponse>(
                    Error.NotFound("Approval or document not found")
                );

            // Reject the approval record
            var rejectResult = await _approvalService.RejectAsync(
                data.Approval.Id,
                adminId,
                rejectionReason
            );

            if (rejectResult.IsFailure)
            {
                return Result.Failure<DocumentResponse>(rejectResult.Error);
            }

            data.Doc.VerificationStatus = VerificationStatus.Rejected;
            data.Doc.VerifiedAt = DateTime.UtcNow;
            data.Doc.RejectionReason = rejectionReason;
            data.Doc.VerifiedBy = adminId;
            await _dbContext.SaveChangesAsync();
            // Get the UserProfile to find the ApplicationUserId
            var userProfile = await _dbContext.UserProfiles.FirstOrDefaultAsync(p =>
                p.Id == data.User.Id && !p.IsDeleted
            );

            if (userProfile == null)
            {
                return Result.Failure<DocumentResponse>(
                    Error.NotFound($"User profile not found for document ID {documentId}")
                );
            }
            var document = data.Doc;
            // Create response
            var response = new DocumentResponse
            {
                DocumentId = document.Id,
                UserId = userProfile.ApplicationUserId, // Use the ApplicationUserId, not the UserProfile.Id
                FileName = Path.GetFileName(document.DocumentUrl),
                DocumentUrl = _fileStorageService.GetFileUrl(document.DocumentUrl), // Convert to absolute URL
                DocumentType = document.DocumentType,
                VerificationStatus = document.VerificationStatus.GetDescription(),
                UploadedAt = document.UploadedAt,
                VerifiedAt = document.VerifiedAt,
                VerifiedBy = document.VerifiedBy,
                RejectionReason = document.RejectionReason,
                Country = document.Country.GetDisplayName(),
                CertificationType = document.CertificationType?.GetDisplayName() ?? string.Empty,
                OtherCertificationType = document.OtherCertificationType,
                CertificationNumber = document.CertificationNumber,
                ExpiryDate = document.ExpiryDate,
                IsExpired = document.IsExpired,
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Error rejecting document {DocumentId}", documentId);
            return Result.Failure<DocumentResponse>(
                Error.Internal("An error occurred while rejecting the document")
            );
        }
    }

    public async Task<Result<IEnumerable<DocumentResponse>>> GetPendingDocumentsAsync()
    {
        try
        {
            var documentsResult = await _unitOfWork
                .Repository<Document>()
                .As<IDocumentRepository, Document>()
                .GetPendingDocumentsAsync();

            if (documentsResult.IsFailure)
            {
                return Result.Failure<IEnumerable<DocumentResponse>>(documentsResult.Error);
            }

            // Get all user profiles for the documents
            var userIds = documentsResult.Value.Select(d => d.UserId).Distinct().ToList();
            var userProfiles = await _dbContext
                .UserProfiles.Where(p => userIds.Contains(p.Id) && !p.IsDeleted)
                .ToDictionaryAsync(p => p.Id, p => p.ApplicationUserId);

            // Map documents to responses with correct user IDs
            var documentResponses = documentsResult.Value.Select(d =>
            {
                // Try to get the ApplicationUserId from the dictionary
                if (!userProfiles.TryGetValue(d.UserId, out var applicationUserId))
                {
                    // If not found, use the document's UserId (this shouldn't happen in normal operation)
                    applicationUserId = d.UserId;
                }

                return new DocumentResponse
                {
                    DocumentId = d.Id,
                    UserId = applicationUserId, // Use the ApplicationUserId, not the UserProfile.Id
                    FileName = Path.GetFileName(d.DocumentUrl),
                    DocumentUrl = _fileStorageService.GetFileUrl(d.DocumentUrl), // Convert to absolute URL
                    DocumentType = d.DocumentType,
                    VerificationStatus = d.VerificationStatus.GetDescription(),
                    UploadedAt = d.UploadedAt,
                    Country = d.Country.GetDisplayName(),
                    CertificationType = d.CertificationType?.GetDisplayName() ?? string.Empty,
                    OtherCertificationType = d.OtherCertificationType,
                    CertificationNumber = d.CertificationNumber,
                    ExpiryDate = d.ExpiryDate,
                    IsExpired = d.IsExpired,
                };
            });

            return Result.Success(documentResponses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending documents");
            return Result.Failure<IEnumerable<DocumentResponse>>(
                Error.Internal("An error occurred while retrieving pending documents")
            );
        }
    }
}
