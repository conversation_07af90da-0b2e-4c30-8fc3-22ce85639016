﻿using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Application.Shared.Utility;
using static super_care_app.Models.Doc.JsonHelper;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Examples for admin-related API responses
    /// </summary>
    public class AdminExamples
    {
        /// <summary>
        /// Gets an example of a successful get user profiles response
        /// </summary>
        public static string GetUserProfilesExample()
        {
            var profiles = new List<AdminUserProfileResponse>
            {
                new AdminUserProfileResponse
                {
                    UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    ProviderId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    Name = "<PERSON>",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    DateOfBirth = new DateTime(1985, 6, 15),
                    ProfilePictureUrl = "https://example.com/profiles/john-smith.jpg",
                    IsCareProvider = true,
                    YearsExperience = 5,
                    VerificationStatus = "Verified",
                    Roles = new List<string> { "User", "CareProvider" },
                    EmailVerified = true,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddMonths(-6),
                    LastLogin = DateTime.UtcNow.AddDays(-2),
                },
                new AdminUserProfileResponse
                {
                    UserId = Guid.Parse("b67ac10b-58cc-4372-a567-0e02b2c3d456"),
                    ProviderId = Guid.Parse("c77ac10b-58cc-4372-a567-0e02b2c3d789"),
                    Name = "Sarah Johnson",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Female",
                    DateOfBirth = new DateTime(1990, 3, 21),
                    ProfilePictureUrl = "https://example.com/profiles/sarah-johnson.jpg",
                    IsCareProvider = true,
                    YearsExperience = 8,
                    VerificationStatus = "Pending",
                    Roles = new List<string> { "User", "CareProvider" },
                    EmailVerified = true,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddMonths(-3),
                    LastLogin = DateTime.UtcNow.AddDays(-5),
                },
                new AdminUserProfileResponse
                {
                    UserId = Guid.Parse("d87ac10b-58cc-4372-a567-0e02b2c3d012"),
                    Name = "Michael Brown",
                    Email = "<EMAIL>",
                    PhoneNumber = "+**********",
                    Gender = "Male",
                    DateOfBirth = new DateTime(1978, 9, 10),
                    ProfilePictureUrl = "https://example.com/profiles/michael-brown.jpg",
                    IsCareProvider = false,
                    Roles = new List<string> { "User", "Client" },
                    EmailVerified = true,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddMonths(-1),
                    LastLogin = DateTime.UtcNow.AddHours(-12),
                },
            };

            var pagination = new PaginationMetadata(
                currentPage: 1,
                totalPages: 5,
                totalCount: 15,
                pageSize: 3
            );

            var response = new PaginatedResponseModel<IEnumerable<AdminUserProfileResponse>>(
                ApiResponseStatusEnum.Success,
                "User profiles retrieved successfully",
                profiles,
                pagination
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful suspend provider response
        /// </summary>
        public static string SuspendProviderExample()
        {
            var providerProfile = new CareProviderProfileResponse
            {
                Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                Name = "John Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                DateOfBirth = new DateTime(1985, 6, 15),
                VerificationStatus = "Suspended",
            };

            var response = new ApiResponseModel<CareProviderProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Care provider suspended successfully",
                providerProfile
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful suspension removal response
        /// </summary>
        public static string RemoveSuspensionExample()
        {
            var providerProfile = new CareProviderProfileResponse
            {
                Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                Name = "John Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                DateOfBirth = new DateTime(1985, 6, 15),
                VerificationStatus = "Verified",
            };

            var response = new ApiResponseModel<CareProviderProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Care provider suspension removed successfully",
                providerProfile
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful admin profile update response
        /// </summary>
        public static string UpdateAdminProfileExample()
        {
            var adminProfile = new UpdateAdminProfileResponse
            {
                UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                Name = "Jane Admin",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Female",
                ProfilePictureUrl =
                    "https://localhost:5001/uploads/ProfilePictures/f47ac10b-58cc-4372-a567-0e02b2c3d479/profile.jpg",
                UpdatedAt = DateTime.UtcNow,
            };

            var response = new ApiResponseModel<UpdateAdminProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Admin profile updated successfully",
                adminProfile
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful get admin profile response
        /// </summary>
        public static string GetAdminProfileExample()
        {
            var adminProfile = new GetAdminProfileResponse
            {
                UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                FirstName = "Jane",
                LastName = "Admin",
                Name = "Jane Admin",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Female",
                ProfilePictureUrl =
                    "https://localhost:5001/uploads/ProfilePictures/f47ac10b-58cc-4372-a567-0e02b2c3d479/profile.jpg",
                Country = "United States",
                CreatedAt = DateTime.Parse("2023-01-01T00:00:00Z"),
                UpdatedAt = DateTime.Parse("2023-06-15T14:30:45Z"),
                LastLogin = DateTime.Parse("2023-06-15T09:15:30Z"),
                IsActive = true,
                EmailVerified = true,
                Roles = new List<string> { "Admin" },
            };

            var response = new ApiResponseModel<GetAdminProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Admin profile retrieved successfully",
                adminProfile
            );

            return SerializeToJson(response);
        }
    }
}
