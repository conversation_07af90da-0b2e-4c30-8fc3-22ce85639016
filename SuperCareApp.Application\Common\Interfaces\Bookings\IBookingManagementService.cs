﻿using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Common.Settings;

namespace SuperCareApp.Application.Common.Interfaces.Bookings;

public interface IBookingManagementService
{
    /// <summary>
    /// Gets available time slots for a provider on a specific date, accounting for bookings and buffer time.
    /// </summary>
    /// <param name="providerId">The ID of the care provider.</param>
    /// <param name="date">The date to check availability for.</param>
    /// <returns>A list of available time slots as Interval<TimeOnly>.</returns>
    Task<List<Interval<TimeOnly>>> GetAvailableSlotsForDateAsync(Guid providerId, DateOnly date);

    /// <summary>
    /// Handles booking workflow: create, confirm, cancel, or complete a booking.
    /// </summary>
    /// <param name="bookingRequest">Booking details including action type.</param>
    /// <returns>The ID of the affected booking or throws an exception on failure.</returns>
    Task<Guid> HandleBookingAsync(BookingRequest bookingRequest);
}
