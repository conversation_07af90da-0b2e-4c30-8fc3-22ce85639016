﻿using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace SuperCareApp.Application.Common.Models.Documents;

/// <summary>
/// Request model for uploading a document
/// </summary>
public class UploadDocumentRequest
{
    /// <summary>
    /// The document file to upload
    /// </summary>
    public IFormFile File { get; set; }

    /// <summary>
    /// Type of document (e.g., "ID", "Certificate", "License")
    /// </summary>
    public string DocumentType { get; set; }

    /// <summary>
    /// Name of the issuer of the certification
    /// </summary>
    public string Issuer { get; set; }

    /// <summary>
    /// Country associated with the certification
    /// </summary>
    public string Country { get; set; } = "Other";

    /// <summary>
    /// Type of certification
    /// </summary>
    public string CertificationType { get; set; }

    /// <summary>
    /// Custom certification type (if "Other" is selected)
    /// </summary>
    public string? OtherCertificationType { get; set; }

    /// <summary>
    /// Certification number or identifier
    /// </summary>
    public string? CertificationNumber { get; set; }

    /// <summary>
    /// Expiry date of the certification
    /// </summary>
    public DateTime? ExpiryDate { get; set; }
}

public class UploadDocumentRequestValidator : AbstractValidator<UploadDocumentRequest>
{
    public UploadDocumentRequestValidator()
    {
        RuleFor(x => x.File)
            .NotNull()
            .WithMessage("Document file is required.")
            .Must(file => file.Length > 0 && file.Length <= 5 * 1024 * 1024)
            .WithMessage("File must be between 1 byte and 5MB.")
            .Must(file =>
                new[] { ".pdf", ".jpg", ".jpeg", ".png" }.Contains(
                    Path.GetExtension(file.FileName).ToLower()
                )
            )
            .WithMessage("File must be a PDF, JPG, JPEG, or PNG.");

        RuleFor(x => x.DocumentType)
            .NotEmpty()
            .WithMessage("Document type is required.")
            .Must(type => new[] { "ID", "Certificate", "License" }.Contains(type))
            .WithMessage("Document type must be 'ID', 'Certificate', or 'License'.")
            .MaximumLength(50)
            .WithMessage("Document type cannot exceed 50 characters.");

        RuleFor(x => x.Country)
            .NotEmpty()
            .WithMessage("Country is required.")
            .MaximumLength(100)
            .WithMessage("Country cannot exceed 100 characters.");

        RuleFor(x => x.CertificationType)
            .NotEmpty()
            .WithMessage("Certification type is required.")
            .MaximumLength(100)
            .WithMessage("Certification type cannot exceed 100 characters.");

        RuleFor(x => x.OtherCertificationType)
            .NotEmpty()
            .WithMessage("Other certification type is required when 'Other' is selected.")
            .MaximumLength(100)
            .WithMessage("Other certification type cannot exceed 100 characters.")
            .When(x => x.CertificationType == "Other");

        RuleFor(x => x.CertificationNumber)
            .MaximumLength(50)
            .WithMessage("Certification number cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.CertificationNumber));

        RuleFor(x => x.ExpiryDate)
            .Must(date => date == null || date >= DateTime.UtcNow.Date)
            .WithMessage("Expiry date must be today or in the future.")
            .When(x => x.ExpiryDate.HasValue);
    }
}
