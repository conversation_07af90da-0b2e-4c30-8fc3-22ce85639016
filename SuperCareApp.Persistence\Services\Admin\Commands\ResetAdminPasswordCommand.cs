using System.Text.RegularExpressions;
using FluentValidation;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;

namespace SuperCareApp.Persistence.Services.Admin.Commands;

public record ResetAdminPasswordCommand(string Email, string Password)
    : ICommand<Result<ResetPasswordResponse>>;

internal sealed class ResetAdminPasswordCommandHandler
    : ICommandHandler<ResetAdminPasswordCommand, Result<ResetPasswordResponse>>
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<ResetAdminPasswordCommandHandler> _logger;

    public ResetAdminPasswordCommandHandler(
        UserManager<ApplicationUser> userManager,
        ILogger<ResetAdminPasswordCommandHandler> logger
    )
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<Result<ResetPasswordResponse>> Handle(
        ResetAdminPasswordCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var validator = new ResetAdminPasswordCommandValidator();
            var validationResult = await validator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return Result.Failure<ResetPasswordResponse>(
                    Error.Validation(validationResult.Errors.ToString())
                );
            }
            if (
                string.IsNullOrWhiteSpace(request.Email)
                || string.IsNullOrWhiteSpace(request.Password)
            )
            {
                return Result.Failure<ResetPasswordResponse>(
                    Error.Validation("Email and password are required.")
                );
            }

            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null)
            {
                _logger.LogWarning(
                    "Password reset failed: User with email {Email} not found.",
                    request.Email
                );
                return Result.Failure<ResetPasswordResponse>(Error.NotFound("User not found."));
            }

            // Generate a temporary reset token
            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogError(
                    "Failed to generate password reset token for user {UserId}",
                    user.Id
                );
                return Result.Failure<ResetPasswordResponse>(
                    Error.Internal("Failed to generate password reset token.")
                );
            }

            // Reset the password using the temporary token
            var result = await _userManager.ResetPasswordAsync(user, token, request.Password);

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                _logger.LogError(
                    "Password reset failed for user {UserId}. Errors: {Errors}",
                    user.Id,
                    errors
                );
                return Result.Failure<ResetPasswordResponse>(
                    Error.Validation($"Failed to reset password: {errors}")
                );
            }

            _logger.LogInformation("Password reset successfully for user {UserId}", user.Id);
            return Result.Success(
                new ResetPasswordResponse("Password reset successfully.", Guid.NewGuid())
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "An error occurred while resetting password for email {Email}",
                request.Email
            );
            return Result.Failure<ResetPasswordResponse>(
                Error.Internal("An unexpected error occurred while resetting the password.")
            );
        }
    }
}

public class ResetAdminPasswordCommandValidator : AbstractValidator<ResetAdminPasswordCommand>
{
    public ResetAdminPasswordCommandValidator()
    {
        RuleFor(x => x.Email)
            .NotEmpty()
            .WithMessage("Email is required.")
            .EmailAddress(FluentValidation.Validators.EmailValidationMode.AspNetCoreCompatible)
            .WithMessage("Invalid email format.")
            .MaximumLength(254)
            .WithMessage("Email must not exceed 254 characters.")
            .Matches(@"^[^@\s]+@[^@\s]+\.[^@\s]+$")
            .WithMessage("Email must be a valid format with a domain.");

        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("Password is required.")
            .MinimumLength(8)
            .WithMessage("Password must be at least 8 characters long.")
            .MaximumLength(128)
            .WithMessage("Password must not exceed 128 characters.")
            .Matches(@"[A-Z]")
            .WithMessage("Password must contain at least one uppercase letter.")
            .Matches(@"[a-z]")
            .WithMessage("Password must contain at least one lowercase letter.")
            .Matches(@"[0-9]")
            .WithMessage("Password must contain at least one digit.")
            .Matches(@"[!@#$%^&*(),.?""':{}|<>]")
            .WithMessage("Password must contain at least one special character.")
            .Must(password => !Regex.IsMatch(password, @"^\s+|\s+$"))
            .WithMessage("Password must not start or end with whitespace.")
            .Must(password => !password.Contains("password", StringComparison.OrdinalIgnoreCase))
            .WithMessage("Password must not contain the word 'password'.")
            .Must(password => !Regex.IsMatch(password, @"(.)\1{2,}"))
            .WithMessage("Password must not contain three or more repeated characters.");
    }
}
