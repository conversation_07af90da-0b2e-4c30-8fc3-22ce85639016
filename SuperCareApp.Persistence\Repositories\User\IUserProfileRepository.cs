﻿using SuperCareApp.Application.Common.Interfaces.Persistence;
using SuperCareApp.Application.Common.Models.User;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Repositories.User
{
    public interface IUserProfileRepository : IRepository<UserProfile>
    {
        Task<Result<UserProfile>> GetByUserIdAsync(
            Guid userId,
            CancellationToken cancellationToken = default
        );
        Task<Result<UserProfile>> UpdateProfileAsync(
            Guid userId,
            UpdateUserProfileRequest request,
            CancellationToken cancellationToken = default
        );
    }
}
