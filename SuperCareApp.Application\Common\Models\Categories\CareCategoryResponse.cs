﻿using System.Text.Json.Serialization;

namespace SuperCareApp.Application.Common.Models.Categories
{
    /// <summary>
    /// Response model for care categories
    /// </summary>
    public class CareCategoryResponse
    {
        /// <summary>
        /// The category ID
        /// </summary>
        public Guid Id { get; init; }

        /// <summary>
        /// The category name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The category description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Whether the category is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// The platform fee for this care category
        /// </summary>
        public decimal PlatformFee { get; set; }

        /// <summary>
        /// The icon for this care category
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// The color for this care category (hex code)
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// When the category was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the category was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        [JsonPropertyName("hourlyRate")]
        public decimal? HourlyRate { get; set; }

        /// <summary>
        /// The experience level in years for this care category
        /// </summary>
        [JsonPropertyName("experienceLevel")]
        public int? ExperienceYears { get; set; }
    }

    public class CareProviderCategoryResponse
    {
        /// <summary>
        /// The category ID
        /// </summary>
        public Guid Id { get; init; }

        /// <summary>
        /// The category name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The category description
        /// </summary>
        public string? Description { get; set; }

        [JsonIgnore]
        public bool IsActive { get; set; }

        [JsonIgnore]
        public decimal PlatformFee { get; set; }

        /// <summary>
        /// The icon for this care category
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// The color for this care category (hex code)
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// When the category was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the category was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// The hourly rate for this care category
        /// </summary>
        [JsonPropertyName("hourlyRate")]
        public decimal HourlyRate { get; set; }

        /// <summary>
        /// The experience level in years for this care category
        /// </summary>
        [JsonPropertyName("experienceLevel")]
        public int ExperienceYears { get; set; }
    }
}
