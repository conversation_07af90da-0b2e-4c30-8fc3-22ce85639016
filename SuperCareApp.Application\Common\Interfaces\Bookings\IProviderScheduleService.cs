using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Bookings
{
    /// <summary>
    /// Defines the contract for services that handle provider scheduling and availability.
    /// </summary>
    public interface IProviderScheduleService
    {
        Task<Result<ProviderUnavailabilityResponse>> GetUnavailableDays(
            Guid providerId,
            int monthsToCheck = 3,
            DateTime? startDate = null,
            DateTime? endDate = null
        );
    }
}
