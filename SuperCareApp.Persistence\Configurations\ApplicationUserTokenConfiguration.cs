﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace SuperCareApp.Persistence.Configurations
{
    public class ApplicationUserTokenConfiguration : IEntityTypeConfiguration<ApplicationUserToken>
    {
        public void Configure(EntityTypeBuilder<ApplicationUserToken> builder)
        {
            builder.HasIndex(t => t.Token).IsUnique(false);
            builder.HasIndex(t => t.ExpiryDate);
            builder.HasIndex(t => t.IsExpired);
            builder.HasIndex(t => t.IsRevoked);
            builder.HasIndex(t => t.LoginSessionId);
            builder.HasIndex(t => new { t.UserId, t.TokenType });

            // Relationships
            builder
                .HasOne(ut => ut.User)
                .WithMany()
                .HasForeignKey(ut => ut.UserId)
                .HasPrincipalKey(u => u.Id)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
