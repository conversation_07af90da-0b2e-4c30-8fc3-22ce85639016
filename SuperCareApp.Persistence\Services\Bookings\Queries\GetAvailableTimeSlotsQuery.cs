﻿using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

// Query
public record GetAvailableTimeSlotsQuery(Guid ProviderId, DateTime Date, int DurationMinutes = 60)
    : IQuery<Result<IEnumerable<TimeSlotResponse>>>;

// Handler
public class GetAvailableTimeSlotsQueryHandler
    : IQueryHandler<GetAvailableTimeSlotsQuery, Result<IEnumerable<TimeSlotResponse>>>
{
    private readonly IAvailabilityService _availabilityService;

    public GetAvailableTimeSlotsQueryHandler(IAvailabilityService availabilityService)
    {
        _availabilityService = availabilityService;
    }

    public async Task<Result<IEnumerable<TimeSlotResponse>>> Handle(
        GetAvailableTimeSlotsQuery request,
        CancellationToken ct
    )
    {
        return await _availabilityService.GetAvailableTimeSlotsAsync(
            request.ProviderId,
            request.Date,
            request.DurationMinutes
        );
    }
}
