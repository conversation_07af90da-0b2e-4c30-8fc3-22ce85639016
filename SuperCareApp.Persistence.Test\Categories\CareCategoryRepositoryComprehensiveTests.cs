using Microsoft.EntityFrameworkCore;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Repositories;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Comprehensive unit tests for CareCategoryRepository covering all edge cases and error scenarios
/// </summary>
public class CareCategoryRepositoryComprehensiveTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CareCategoryRepository _repository;

    public CareCategoryRepositoryComprehensiveTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _repository = new CareCategoryRepository(_context);
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region GetByNameAsync Tests

    [Fact]
    public async Task GetByNameAsync_WithExistingName_ShouldReturnCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByNameAsync("Test Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(category.Id, result.Value.Id);
        Assert.Equal(category.Name, result.Value.Name);
    }

    [Fact]
    public async Task GetByNameAsync_WithCaseInsensitiveName_ShouldReturnCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Test Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByNameAsync("TEST CATEGORY");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(category.Id, result.Value.Id);
        Assert.Equal(category.Name, result.Value.Name);
    }

    [Fact]
    public async Task GetByNameAsync_WithNonExistentName_ShouldReturnFailure()
    {
        // Act
        var result = await _repository.GetByNameAsync("Non-existent Category");

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Fact]
    public async Task GetByNameAsync_WithDeletedCategory_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByNameAsync("Deleted Category");

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetByNameAsync_WithInvalidName_ShouldReturnFailure(string invalidName)
    {
        // Act
        var result = await _repository.GetByNameAsync(invalidName);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("was not found", result.Error.Message);
    }

    #endregion

    #region GetActiveAsync Tests

    [Fact]
    public async Task GetActiveAsync_WithMixedActiveInactiveCategories_ShouldReturnOnlyActive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var activeCategory2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 20.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory1, activeCategory2, inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());
        Assert.All(result.Value, category => Assert.True(category.IsActive));
        Assert.Contains(result.Value, c => c.Name == "Active Category 1");
        Assert.Contains(result.Value, c => c.Name == "Active Category 2");
        Assert.DoesNotContain(result.Value, c => c.Name == "Inactive Category");
    }

    [Fact]
    public async Task GetActiveAsync_WithDeletedActiveCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedActiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Active Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, deletedActiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetActiveAsync_WithNoActiveCategories_ShouldReturnEmptyList()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveAsync();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Empty(result.Value);
    }

    [Fact]
    public async Task GetActiveAsync_ShouldReturnCategoriesOrderedByName()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categoryZ = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Z Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var categoryA = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "A Category",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var categoryM = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "M Category",
            IsActive = true,
            PlatformFee = 20.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(categoryZ, categoryA, categoryM);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveAsync();

        // Assert
        Assert.True(result.IsSuccess);
        var categories = result.Value.ToList();
        Assert.Equal("A Category", categories[0].Name);
        Assert.Equal("M Category", categories[1].Name);
        Assert.Equal("Z Category", categories[2].Name);
    }

    #endregion

    #region GetByProviderIdAsync Tests

    [Fact]
    public async Task GetByProviderIdAsync_WithValidProviderId_ShouldReturnProviderCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Provider Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategory1 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category1.Id,
            HourlyRate = 50.00m
        };

        var providerCategory2 = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category2.Id,
            HourlyRate = 60.00m
        };

        _context.CareCategories.AddRange(category1, category2);
        _context.CareProviderCategories.AddRange(providerCategory1, providerCategory2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());
        Assert.Contains(result.Value, c => c.Name == "Provider Category 1");
        Assert.Contains(result.Value, c => c.Name == "Provider Category 2");
    }

    [Fact]
    public async Task GetByProviderIdAsync_WithInactiveCategories_ShouldExcludeInactive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var activeProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = activeCategory.Id,
            HourlyRate = 50.00m
        };

        var inactiveProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = inactiveCategory.Id,
            HourlyRate = 60.00m
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        _context.CareProviderCategories.AddRange(activeProviderCategory, inactiveProviderCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetByProviderIdAsync_WithDeletedProviderCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var activeProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 50.00m
        };

        var deletedProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = category.Id,
            HourlyRate = 60.00m,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId
        };

        _context.CareCategories.Add(category);
        _context.CareProviderCategories.AddRange(activeProviderCategory, deletedProviderCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetByProviderIdAsync_WithDeletedCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var activeProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = activeCategory.Id,
            HourlyRate = 50.00m
        };

        var deletedProviderCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = deletedCategory.Id,
            HourlyRate = 60.00m
        };

        _context.CareCategories.AddRange(activeCategory, deletedCategory);
        _context.CareProviderCategories.AddRange(activeProviderCategory, deletedProviderCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal("Active Category", result.Value.First().Name);
    }

    [Fact]
    public async Task GetByProviderIdAsync_WithNonExistentProviderId_ShouldReturnEmptyList()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();

        // Act
        var result = await _repository.GetByProviderIdAsync(nonExistentProviderId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Empty(result.Value);
    }

    [Fact]
    public async Task GetByProviderIdAsync_ShouldReturnCategoriesOrderedByName()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();

        var categoryZ = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Z Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var categoryA = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "A Category",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var providerCategoryZ = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = categoryZ.Id,
            HourlyRate = 50.00m
        };

        var providerCategoryA = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            CategoryId = categoryA.Id,
            HourlyRate = 60.00m
        };

        _context.CareCategories.AddRange(categoryZ, categoryA);
        _context.CareProviderCategories.AddRange(providerCategoryZ, providerCategoryA);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByProviderIdAsync(providerId);

        // Assert
        Assert.True(result.IsSuccess);
        var categories = result.Value.ToList();
        Assert.Equal("A Category", categories[0].Name);
        Assert.Equal("Z Category", categories[1].Name);
    }

    #endregion

    #region GetPaginatedAsync Tests

    [Fact]
    public async Task GetPaginatedAsync_WithValidParameters_ShouldReturnCorrectPage()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categories = Enumerable.Range(1, 10).Select(i => new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i:D2}",
            IsActive = true,
            PlatformFee = i * 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        }).ToArray();

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 2, pageSize: 3);

        // Assert
        Assert.True(result.IsSuccess);
        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(10, totalCount);
        Assert.Equal(3, paginatedCategories.Count());

        // Verify correct page items (ordered by name)
        var categoryList = paginatedCategories.ToList();
        Assert.Equal("Category 04", categoryList[0].Name);
        Assert.Equal("Category 05", categoryList[1].Name);
        Assert.Equal("Category 06", categoryList[2].Name);
    }

    [Fact]
    public async Task GetPaginatedAsync_WithIncludeInactive_ShouldReturnAllCategories()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 1, pageSize: 10, includeInactive: true);

        // Assert
        Assert.True(result.IsSuccess);
        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(2, totalCount);
        Assert.Equal(2, paginatedCategories.Count());
    }

    [Fact]
    public async Task GetPaginatedAsync_WithoutIncludeInactive_ShouldReturnOnlyActive()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var inactiveCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Inactive Category",
            IsActive = false,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, inactiveCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 1, pageSize: 10, includeInactive: false);

        // Assert
        Assert.True(result.IsSuccess);
        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(1, totalCount);
        Assert.Single(paginatedCategories);
        Assert.Equal("Active Category", paginatedCategories.First().Name);
    }

    [Fact]
    public async Task GetPaginatedAsync_WithDeletedCategories_ShouldExcludeDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Active Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(activeCategory, deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 1, pageSize: 10, includeInactive: true);

        // Assert
        Assert.True(result.IsSuccess);
        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(1, totalCount);
        Assert.Single(paginatedCategories);
        Assert.Equal("Active Category", paginatedCategories.First().Name);
    }

    [Fact]
    public async Task GetPaginatedAsync_WithLastPagePartialResults_ShouldReturnCorrectCount()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var categories = Enumerable.Range(1, 7).Select(i => new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i:D2}",
            IsActive = true,
            PlatformFee = i * 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        }).ToArray();

        _context.CareCategories.AddRange(categories);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 3, pageSize: 3);

        // Assert
        Assert.True(result.IsSuccess);
        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(7, totalCount);
        Assert.Single(paginatedCategories); // Last page has only 1 item
        Assert.Equal("Category 07", paginatedCategories.First().Name);
    }

    [Fact]
    public async Task GetPaginatedAsync_WithPageBeyondResults_ShouldReturnEmptyPage()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Single Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPaginatedAsync(pageNumber: 5, pageSize: 10);

        // Assert
        Assert.True(result.IsSuccess);
        var (paginatedCategories, totalCount) = result.Value;
        Assert.Equal(1, totalCount);
        Assert.Empty(paginatedCategories);
    }

    #endregion

    #region ExistsByNameAsync Tests

    [Fact]
    public async Task ExistsByNameAsync_WithExistingName_ShouldReturnTrue()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsByNameAsync("Existing Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value);
    }

    [Fact]
    public async Task ExistsByNameAsync_WithCaseInsensitiveName_ShouldReturnTrue()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsByNameAsync("EXISTING CATEGORY");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value);
    }

    [Fact]
    public async Task ExistsByNameAsync_WithNonExistentName_ShouldReturnFalse()
    {
        // Act
        var result = await _repository.ExistsByNameAsync("Non-existent Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value);
    }

    [Fact]
    public async Task ExistsByNameAsync_WithDeletedCategory_ShouldReturnFalse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var deletedCategory = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Deleted Category",
            IsActive = true,
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = userId,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(deletedCategory);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsByNameAsync("Deleted Category");

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value);
    }

    [Fact]
    public async Task ExistsByNameAsync_WithExcludeId_ShouldIgnoreExcludedCategory()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Existing Category",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.Add(category);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsByNameAsync("Existing Category", category.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value); // Should return false because the category with this name is excluded
    }

    [Fact]
    public async Task ExistsByNameAsync_WithExcludeIdButDifferentName_ShouldReturnTrue()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var category1 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 1",
            IsActive = true,
            PlatformFee = 10.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        var category2 = new CareCategory
        {
            Id = Guid.NewGuid(),
            Name = "Category 2",
            IsActive = true,
            PlatformFee = 15.00m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId
        };

        _context.CareCategories.AddRange(category1, category2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsByNameAsync("Category 1", category2.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value); // Should return true because Category 1 exists and is not excluded
    }

    #endregion
}