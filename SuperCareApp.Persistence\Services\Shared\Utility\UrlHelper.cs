using Microsoft.AspNetCore.Http;

namespace SuperCareApp.Persistence.Services.Shared.Utility;

public static class Url<PERSON>elper
{
    // 1. Keep a single static reference to the *singleton* IHttpContextAccessor
    private static IHttpContextAccessor _httpContextAccessor;

    // 2. Let the Composition Root hand us the singleton once
    public static void Configure(IHttpContextAccessor accessor)
    {
        _httpContextAccessor = accessor ?? throw new ArgumentNullException(nameof(accessor));
    }

    // 3. Your original helper stays static and allocation-free
    public static string BuildImageUrl(string? url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return url;
        var accessor = _httpContextAccessor;
        if (accessor?.HttpContext?.Request is null)
            return url; // no context available – graceful fallback

        var scheme = accessor.HttpContext.Request.Scheme;
        var host = accessor.HttpContext.Request.Host.Value;

        if (string.IsNullOrEmpty(host))
            return url;

        if (
            url.StartsWith("http://", StringComparison.OrdinalIgnoreCase)
            || url.StartsWith("https://", StringComparison.OrdinalIgnoreCase)
        )
            return url;
        if (host == "careappapi.intellexio.com")
            return $"https://{host}{url}";

        return $"{scheme}://{host}{url}";
    }
}
