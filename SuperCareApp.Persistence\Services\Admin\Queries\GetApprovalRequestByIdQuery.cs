﻿using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Models.Admin;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Admin.Queries
{
    public record GetApprovalRequestByIdQuery(Guid RequestId)
        : IRequest<Result<ApprovalRequestResponse>>;

    internal sealed class GetApprovalRequestByIdQueryHandler
        : IRequestHandler<GetApprovalRequestByIdQuery, Result<ApprovalRequestResponse>>
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<GetApprovalRequestByIdQueryHandler> _logger;

        public GetApprovalRequestByIdQueryHandler(
            ApplicationDbContext context,
            ILogger<GetApprovalRequestByIdQueryHandler> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Result<ApprovalRequestResponse>> Handle(
            GetApprovalRequestByIdQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var approval = await _context
                    .Set<Approval>()
                    .Include(a => a.User)
                    .ThenInclude(u => u.UserProfile)
                    .Include(a => a.Processor)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        a => a.Id == request.RequestId && !a.IsDeleted,
                        cancellationToken
                    );

                if (approval == null)
                {
                    return Result.Failure<ApprovalRequestResponse>(
                        Error.NotFound($"Approval request with ID {request.RequestId} not found")
                    );
                }

                var user = approval.User;
                var userProfile = user?.UserProfile;

                var response = new ApprovalRequestResponse
                {
                    Id = approval.Id,
                    UserId = approval.UserId,
                    Email = user?.Email ?? string.Empty,
                    Name =
                        userProfile != null
                            ? $"{userProfile.FirstName} {userProfile.LastName}".Trim()
                            : user?.UserName ?? string.Empty,
                    ApprovalTypeEnum = approval.ApprovalType,
                    ApprovalTypeString = approval.ApprovalType.ToString(),
                    ApprovalTypeDescription = approval.ApprovalType.GetDescription(),
                    IsApproved = approval.IsApproved,
                    RejectionReason = approval.RejectionReason,
                    ApprovalData = approval.ApprovalData,
                    RelatedEntityId = approval.RelatedEntityId,
                    ProcessedBy = approval.ProcessedBy,
                    ProcessedByName = approval.Processor?.UserName,
                    ProcessedAt = approval.ProcessedAt,
                    Notes = approval.Notes,
                    CreatedAt = approval.CreatedAt,
                };

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving approval request {RequestId}",
                    request.RequestId
                );
                return Result.Failure<ApprovalRequestResponse>(Error.Internal(ex.Message));
            }
        }
    }
}
