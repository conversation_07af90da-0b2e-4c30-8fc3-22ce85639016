using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

public class AuditLogEntityTypeConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(
        Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder<AuditLog> builder
    )
    {
        builder.HasKey(a => a.Id);

        builder.Property(a => a.EntityType).HasMaxLength(255).IsRequired();

        builder.Property(a => a.EntityId).IsRequired();

        builder.Property(a => a.Action).HasMaxLength(50);

        builder.Property(a => a.OldValues).HasColumnType("text");

        builder.Property(a => a.NewValues).HasColumnType("text");

        builder.Property(a => a.UserId).IsRequired();

        builder.Property(a => a.Timestamp).IsRequired();
    }
}
