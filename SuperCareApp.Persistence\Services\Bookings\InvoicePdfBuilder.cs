using Microsoft.AspNetCore.Hosting;
using PeachPDF;
using PeachPDF.PdfSharpCore;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Services.Bookings.Commands;

namespace SuperCareApp.Persistence.Services.Bookings;

internal sealed class InvoicePdfBuilder
{
    private readonly IWebHostEnvironment _env;
    private static readonly string[] AllowedFonts = { "Arial", "Helvetica" };

    public InvoicePdfBuilder(IWebHostEnvironment env)
    {
        _env = env;
    }

    public async Task<byte[]?> BuildAsync(Invoice invoice, BookingWindowDto bookingData)
    {
        try
        {
            var html = BuildHtml(invoice, bookingData);
            var gen = new PdfGenerator();
            var cfg = new PdfGenerateConfig
            {
                PageSize = PageSize.A4,
                PageOrientation = PageOrientation.Portrait,
                MarginTop = 20,
                MarginBottom = 20,
                MarginLeft = 20,
                MarginRight = 20,
            };

            gen.AddFontFamilyMapping("Segoe UI", AllowedFonts[1]);

            await using var ms = new MemoryStream();
            var doc = await gen.GeneratePdf(html, cfg);
            doc.Save(ms);
            return ms.ToArray();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(ex.Message);
        }
    }

    private string BuildHtml(Invoice invoice, BookingWindowDto dto)
    {
        var duration = dto.EndTime - dto.StartTime;
        var logoUri = GetLogoDataUri();
        var txnId = $"TXN-{Guid.NewGuid().ToString()[..9]}";

        // Default / fallback values
        var clientName = $"{dto.ClientFirstName ?? "Unknown"} {dto.ClientLastName ?? "Client"}";
        var providerName =
            $"{dto.ProviderFirstName ?? "Unknown"} {dto.ProviderLastName ?? "Provider"}";

        // Build client address string safely
        var clientAddress = "N/A";
        if (dto.ClientPrimaryAddress != null)
        {
            var addressParts = new List<string>();
            if (!string.IsNullOrEmpty(dto.ClientPrimaryAddress.StreetAddress))
                addressParts.Add(dto.ClientPrimaryAddress.StreetAddress);
            if (!string.IsNullOrEmpty(dto.ClientPrimaryAddress.City))
                addressParts.Add(dto.ClientPrimaryAddress.City);
            if (!string.IsNullOrEmpty(dto.ClientPrimaryAddress.State))
                addressParts.Add(dto.ClientPrimaryAddress.State);
            if (!string.IsNullOrEmpty(dto.ClientPrimaryAddress.PostalCode))
                addressParts.Add(dto.ClientPrimaryAddress.PostalCode);

            if (addressParts.Any())
                clientAddress = string.Join(", ", addressParts);
        }

        return $@"
<!DOCTYPE html>
<html lang=""en"">
<head>
  <meta charset=""UTF-8"" />
  <title>Invoice PDF</title>
  <style>
    @page {{
      size: A4;
      margin: 15mm;
    }}
    
    * {{
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }}
    
    body {{
      font-family: 'Arial', sans-serif;
      color: #1a1a1a;
      font-size: 12px;
      line-height: 1.5;
      background-color: #fff;
    }}

    .header {{
      text-align: center;
      margin-bottom: 20px;
    }}

    .header img {{
      max-height: 80px;
      width: auto;
    }}

    .thanks {{
      text-align: center;
      font-size: 14px;
      font-weight: normal;
      color: #035B84;
      margin-bottom: 30px;
    }}

    .section-title {{
      font-size: 14px;
      font-weight: bold;
      color: #035B84;
      margin: 20px 0 10px 0;
      padding-bottom: 5px;
      border-bottom: 1px solid #b4e0ec;
    }}

    .info-table {{
      width: 100%;
      margin-bottom: 20px;
      border-collapse: separate;
      border-spacing: 0;
    }}

    .info-table td {{
      padding: 4px 0;
      vertical-align: top;
    }}

    .info-table .label {{
      width: 120px;
      font-weight: bold;
      padding-right: 10px;
    }}

    .info-table .value {{
      color: #333;
    }}

    .two-column {{
      width: 100%;
      margin-bottom: 20px;
    }}

    .two-column table {{
      width: 100%;
    }}

    .two-column td {{
      width: 50%;
      vertical-align: top;
      padding-right: 20px;
    }}

    .two-column td:last-child {{
      padding-right: 0;
      padding-left: 20px;
    }}

    .column-title {{
      font-size: 13px;
      font-weight: bold;
      color: #035B84;
      margin-bottom: 8px;
    }}

    .cost-table {{
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }}

    .cost-table th, .cost-table td {{
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }}

    .cost-table th {{
      background-color: #E6F7FA;
      color: #035B84;
      font-weight: bold;
    }}

    .cost-table .amount {{
      text-align: right;
    }}

    .cost-table .total-row th, .cost-table .total-row td {{
      font-weight: bold;
      background-color: #f8f9fa;
    }}

    .footer {{
      text-align: center;
      margin-top: 30px;
      font-size: 10px;
      color: #666;
      border-top: 1px solid #eee;
      padding-top: 15px;
    }}

    /* Remove flexbox and use table-based layouts for better PDF compatibility */
    .clearfix::after {{
      content: """";
      display: table;
      clear: both;
    }}
  </style>
</head>
<body>
    <div class=""header"">
        {(string.IsNullOrEmpty(logoUri) ? "" : $@"<img src=""{logoUri}"" alt=""Super Carer App Logo"" />")}
    </div>

    <div class=""thanks"">Thank you for booking with Super Carer App, {clientName}!</div>

    <div class=""section-title"">Invoice Summary</div>
    <table class=""info-table"">
        <tr>
            <td class=""label"">Invoice ID:</td>
            <td class=""value"">{invoice.InvoiceNumber}</td>
        </tr>
        <tr>
            <td class=""label"">Invoice Date:</td>
            <td class=""value"">{invoice.InvoiceDate:MMMM d, yyyy}</td>
        </tr>
        <tr>
            <td class=""label"">Status:</td>
            <td class=""value"">{invoice.Status}</td>
        </tr>
        <tr></tr>
    </table>

    <div class=""section-title"">Client Details</div>
    <table class=""info-table"">
        <tr>
            <td class=""label"">Name:</td>
            <td class=""value"">{clientName}</td>
        </tr>
        <tr>
            <td class=""label"">Email:</td>
            <td class=""value"">{dto.ClientEmail ?? "N/A"}</td>
        </tr>
        <tr>
            <td class=""label"">Phone:</td>
            <td class=""value"">{dto.ClientPhoneNumber ?? "N/A"}</td>
        </tr>
        <tr>
            <td class=""label"">Address:</td>
            <td class=""value"">{clientAddress}</td>
        </tr>
    </table>

    <div class=""section-title"">Service & Payment Information</div>
    <div class=""two-column"">
        <table>
            <tr>
                <td>
                    <div class=""column-title"">Booking Details</div>
                    <table class=""info-table"">
                        <tr>
                            <td class=""label"">Service Type:</td>
                            <td class=""value"">{dto.CategoryName ?? "Care Service"}</td>
                        </tr>
                        <tr>
                            <td class=""label"">Description:</td>
                            <td class=""value"">Personalized care session</td>
                        </tr>
                        <tr>
                            <td class=""label"">Start Date:</td>
                            <td class=""value"">{dto.Date:MMMM d, yyyy}, {dto.StartTime:h:mm tt}</td>
                        </tr>
                        <tr>
                            <td class=""label"">End Date:</td>
                            <td class=""value"">{dto.Date:MMMM d, yyyy}, {dto.EndTime:h:mm tt}</td>
                        </tr>
                        <tr>
                            <td class=""label"">Duration:</td>
                            <td class=""value"">{duration.Hours}h {duration.Minutes}m</td>
                        </tr>
                    </table>
                </td>
                <td>
                    <div class=""column-title"">Payment Details</div>
                    <table class=""info-table"">
                        <tr>
                            <td class=""label"">Method:</td>
                            <td class=""value"">Credit Card</td>
                        </tr>
                        <tr>
                            <td class=""label"">Transaction ID:</td>
                            <td class=""value"">{txnId}</td>
                        </tr>
                        <tr>
                            <td class=""label"">Payment Date:</td>
                            <td class=""value"">{invoice.InvoiceDate:MMMM d, yyyy}</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>

    <div class=""section-title"" style=""page-break-before: always;"">Care Provider</div>
    <table class=""info-table"">
        <tr>
            <td class=""label"">Name:</td>
            <td class=""value"">{providerName}</td>
        </tr>
        <tr>
            <td class=""label"">Email:</td>
            <td class=""value"">{dto.ProviderEmail ?? "N/A"}</td>
        </tr>
        <tr>
            <td class=""label"">Phone:</td>
            <td class=""value"">{dto.ProviderPhoneNumber ?? "N/A"}</td>
        </tr>
        <tr>
            <td class=""label"">Experience:</td>
            <td class=""value"">{dto.ProviderYearsExperience} years</td>
        </tr>
    </table>

    <div class=""section-title"">Cost Breakdown</div>
    <table class=""cost-table"">
        <thead>
        <tr>
            <th>Item</th>
            <th class=""amount"">Amount (USD)</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>Care Service Fee</td>
            <td class=""amount"">${invoice.Amount:F2}</td>
        </tr>
        <tr>
            <td>Service Tax (10%)</td>
            <td class=""amount"">${invoice.Tax:F2}</td>
        </tr>
        <tr class=""total-row"">
            <th>Total</th>
            <th class=""amount"">${invoice.TotalAmount:F2}</th>
        </tr>
        </tbody>
    </table>

    <div class=""footer"">
        This is a computer-generated invoice and does not require a signature.<br/>
        For support, contact <NAME_EMAIL>.
    </div>
</body>
</html>";
    }

    private string GetLogoDataUri()
    {
        try
        {
            var path = Path.Combine(_env.WebRootPath, "images", "care_logo.png");
            return !File.Exists(path)
                ? string.Empty
                : $"data:image/png;base64,{Convert.ToBase64String(File.ReadAllBytes(path))}";
        }
        catch
        {
            return string.Empty;
        }
    }
}
