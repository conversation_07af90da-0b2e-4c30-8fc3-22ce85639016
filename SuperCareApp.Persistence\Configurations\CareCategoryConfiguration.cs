﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class CareCategoryConfiguration : IEntityTypeConfiguration<CareCategory>
    {
        public void Configure(EntityTypeBuilder<CareCategory> builder)
        {
            // Primary Key
            builder.HasKey(c => c.Id);

            // Properties
            builder.Property(c => c.Name).IsRequired().HasMaxLength(100);

            builder.HasIndex(c => c.Name).IsUnique();

            builder.Property(c => c.Description).HasMaxLength(500);

            builder.Property(c => c.IsActive).IsRequired().HasDefaultValue(true);

            builder
                .Property(c => c.PlatformFee)
                .IsRequired()
                .HasColumnType("decimal(18,6)")
                .HasDefaultValue(0m);

            // Icon Property
            builder
                .Property(c => c.Icon)
                .HasMaxLength(255) // e.g., URL or SVG string
                .IsUnicode(false); // Optional: if using ASCII-based icons (e.g., URLs)

            // Color Property
            builder
                .Property(c => c.Color)
                .HasMaxLength(7) // e.g., #FF5733
                .IsUnicode(false) // Hex codes are ASCII-only
                .HasComment("Hex color code for UI representation");

            // Relationships
            builder
                .HasMany(c => c.CareProviderCategories)
                .WithOne(pc => pc.CareCategory)
                .HasForeignKey(pc => pc.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasIndex(c => c.IsActive);
        }
    }
}
