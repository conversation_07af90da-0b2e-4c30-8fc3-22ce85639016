using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.InMemory;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;
using SuperCareApp.Persistence.Services.Bookings.Queries;

namespace SuperCareApp.Persistence.Test.Bookings;

public class GetBookingTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BookingService _bookingService;
    private readonly GetBookingByIdQueryHandler _getByIdHandler;
    private readonly GetBookingsQueryHandler _getBookingsHandler;
    private readonly Guid _userId;
    private readonly Guid _providerId;
    private readonly Guid _categoryId;
    private readonly Guid _clientId;
    private readonly Guid _alternativeClientId;

    public GetBookingTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        _context = new ApplicationDbContext(options);

        var mockScheduleService = new Mock<IBookingManagementService>();
        var mockAvailabilityService = new Mock<IAvailabilityService>();

        _bookingService = new BookingService(
            _context,
            mockScheduleService.Object,
            NullLogger<BookingService>.Instance
        );

        _getByIdHandler = new GetBookingByIdQueryHandler(_bookingService);
        _getBookingsHandler = new GetBookingsQueryHandler(_bookingService);

        _userId = Guid.NewGuid();
        _providerId = Guid.NewGuid();
        _categoryId = Guid.NewGuid();
        _clientId = Guid.NewGuid();
        _alternativeClientId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test users
        var user = new ApplicationUser
        {
            Id = _userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        var client = new ApplicationUser
        {
            Id = _clientId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        var alternativeClient = new ApplicationUser
        {
            Id = _alternativeClientId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        // Create provider profile
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create category
        var category = new CareCategory
        {
            Id = _categoryId,
            Name = "Test Category",
            Description = "Test category description",
            IsActive = true,
            PlatformFee = 2.50m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };
        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            CategoryId = _categoryId,
            HourlyRate = 25.00m,
            ExperienceYears = 5,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Users.AddRange(user, client, alternativeClient);
        _context.CareProviderProfiles.Add(providerProfile);
        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Helper Methods

    private async Task<Booking> CreateTestBooking(
        BookingStatusType status = BookingStatusType.Requested,
        DateTime? bookingDate = null,
        Guid? clientId = null,
        string? specialInstructions = null,
        bool withMultipleWindows = false,
        bool isDeleted = false
    )
    {
        var bookingId = Guid.NewGuid();
        var date = bookingDate ?? DateTime.Today.AddDays(1);
        var client = clientId ?? _clientId;

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = client,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 8,
            TotalAmount = 200,
            PlatformFee = 20,
            ProviderAmount = 180,
            SpecialInstructions = specialInstructions ?? "Test booking",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = client,
            IsDeleted = isDeleted,
            DeletedAt = isDeleted ? DateTime.UtcNow : null,
            DeletedBy = isDeleted ? client : null,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = status,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = client,
        };

        var bookingWindows = new List<BookingWindow>();

        if (withMultipleWindows)
        {
            for (int i = 0; i < 3; i++)
            {
                bookingWindows.Add(
                    new BookingWindow
                    {
                        Id = Guid.NewGuid(),
                        BookingId = bookingId,
                        Date = DateOnly.FromDateTime(date.AddDays(i)),
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                        DurationMinutes = 480,
                        DailyRate = 200,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = client,
                    }
                );
            }
        }
        else
        {
            bookingWindows.Add(
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = bookingId,
                    Date = DateOnly.FromDateTime(date),
                    StartTime = new TimeOnly(9, 0),
                    EndTime = new TimeOnly(17, 0),
                    DurationMinutes = 480,
                    DailyRate = 200,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = client,
                }
            );
        }

        booking.Status = bookingStatus;

        _context.Bookings.Add(booking);
        _context.BookingStatuses.Add(bookingStatus);
        _context.BookingWindows.AddRange(bookingWindows);
        await _context.SaveChangesAsync();

        return booking;
    }

    #endregion

    #region Get Booking By ID Tests

    [Fact]
    public async Task GetBookingByIdAsync_WithValidId_ShouldReturnBooking()
    {
        // Arrange
        var booking = await CreateTestBooking(
            BookingStatusType.Requested,
            specialInstructions: "Detailed instructions"
        );

        // Act
        var result = await _bookingService.GetBookingByIdAsync(booking.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(booking.Id, result.Value.BookingId);
        Assert.Equal("Detailed instructions", result.Value.Description);
        Assert.Equal(BookingStatusType.Requested.ToString(), result.Value.Status);
        Assert.Single(result.Value.BookingWindows);
    }

    [Fact]
    public async Task GetBookingByIdQuery_WithValidId_ShouldReturnBooking()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Confirmed);
        var query = new GetBookingByIdQuery(booking.Id);

        // Act
        var result = await _getByIdHandler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(booking.Id, result.Value.BookingId);
        Assert.Equal(BookingStatusType.Confirmed.ToString(), result.Value.Status);
    }

    [Fact]
    public async Task GetBookingByIdAsync_WithMultipleWindows_ShouldReturnAllWindows()
    {
        // Arrange
        var booking = await CreateTestBooking(withMultipleWindows: true);

        // Act
        var result = await _bookingService.GetBookingByIdAsync(booking.Id);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.NotNull(result.Value);
        Assert.Equal(3, result.Value.BookingWindows.Count);

        var windows = result.Value.BookingWindows.OrderBy(w => w.Date).ToList();
        Assert.Equal(DateTime.Today.AddDays(1).Date, windows[0].Date.Date);
        Assert.Equal(DateTime.Today.AddDays(3).Date, windows[2].Date.Date);
    }

    [Fact]
    public async Task GetBookingByIdAsync_WithNonExistentId_ShouldReturnNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var result = await _bookingService.GetBookingByIdAsync(nonExistentId);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Booking not found", result.Error.Message);
    }

    [Fact]
    public async Task GetBookingByIdAsync_WithDeletedBooking_ShouldReturnNotFound()
    {
        // Arrange
        var booking = await CreateTestBooking(isDeleted: true);

        // Act
        var result = await _bookingService.GetBookingByIdAsync(booking.Id);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Booking not found", result.Error.Message);
    }

    #endregion

    #region Get Bookings (List) Tests

    [Fact]
    public async Task GetAllBookingsAsync_WithNoFilters_ShouldReturnAllBookings()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested, DateTime.Today.AddDays(1));
        await CreateTestBooking(BookingStatusType.Confirmed, DateTime.Today.AddDays(2));
        await CreateTestBooking(BookingStatusType.Completed, DateTime.Today.AddDays(-1));

        var request = new BookingsRequest();
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(3, result.Value.Bookings.Count());
        Assert.Equal(3, result.Value.TotalCount);
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithClientIdFilter_ShouldReturnClientBookings()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested, clientId: _clientId);
        await CreateTestBooking(BookingStatusType.Confirmed, clientId: _clientId);
        await CreateTestBooking(BookingStatusType.Requested, clientId: _alternativeClientId);

        var request = new BookingsRequest { UserId = _clientId };
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Bookings.Count());
        Assert.All(result.Value.Bookings, booking => Assert.NotNull(booking.Clients));
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithProviderIdFilter_ShouldReturnProviderBookings()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested);
        await CreateTestBooking(BookingStatusType.Confirmed);

        var request = new BookingsRequest();
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Bookings.Count());
        Assert.All(result.Value.Bookings, booking => Assert.NotNull(booking.CareProviders));
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithStatusFilter_ShouldReturnFilteredBookings()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested);
        await CreateTestBooking(BookingStatusType.Confirmed);
        await CreateTestBooking(BookingStatusType.Completed);

        var request = new BookingsRequest
        {
            BookingStatus = BookingStatusType.Confirmed.ToString(),
        };
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value.Bookings);
        Assert.Equal(BookingStatusType.Confirmed.ToString(), result.Value.Bookings.First().Status);
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithDateRangeFilter_ShouldReturnBookingsInRange()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested, DateTime.Today.AddDays(1));
        await CreateTestBooking(BookingStatusType.Confirmed, DateTime.Today.AddDays(5));
        await CreateTestBooking(BookingStatusType.Completed, DateTime.Today.AddDays(10));

        var request = new BookingsRequest
        {
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(7),
        };
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Bookings.Count()); // Only first two bookings should be in range
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        for (int i = 0; i < 15; i++)
        {
            await CreateTestBooking(BookingStatusType.Requested, DateTime.Today.AddDays(i + 1));
        }

        var request = new BookingsRequest();
        var parameters = new BookingListParams { PageNumber = 2, PageSize = 5 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(5, result.Value.Bookings.Count());
        Assert.Equal(15, result.Value.TotalCount);
        Assert.Equal(2, result.Value.PageNumber);
        Assert.Equal(3, result.Value.TotalPages);
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithSearchTerm_ShouldReturnMatchingBookings()
    {
        // Arrange
        await CreateTestBooking(specialInstructions: "Need wheelchair accessible vehicle");
        await CreateTestBooking(specialInstructions: "Regular appointment");
        await CreateTestBooking(specialInstructions: "Wheelchair ramp required");

        var request = new BookingsRequest { SearchTerm = "wheelchair" };
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Bookings.Count());
        Assert.All(
            result.Value.Bookings,
            booking =>
                Assert.Contains(
                    "wheelchair",
                    booking.Description,
                    StringComparison.OrdinalIgnoreCase
                )
        );
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithSortByDate_ShouldReturnSortedBookings()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested, DateTime.Today.AddDays(3));
        await CreateTestBooking(BookingStatusType.Confirmed, DateTime.Today.AddDays(1));
        await CreateTestBooking(BookingStatusType.Completed, DateTime.Today.AddDays(2));

        var request = new BookingsRequest { SortBy = "Date", SortDescending = false };
        var parameters = new BookingListParams
        {
            SortBy = "Date",
            SortDescending = false,
            PageNumber = 1,
            PageSize = 10,
        };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        var bookings = result.Value.Bookings.ToList();
        Assert.Equal(3, bookings.Count);

        // Verify ascending date order
        for (int i = 1; i < bookings.Count; i++)
        {
            var prevDate = bookings[i - 1].BookingWindows.First().Date;
            var currentDate = bookings[i].BookingWindows.First().Date;
            Assert.True(prevDate <= currentDate);
        }
    }

    [Fact]
    public async Task GetAllBookingsAsync_ExcludeDeletedBookings_ShouldNotReturnDeleted()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested, isDeleted: false);
        await CreateTestBooking(BookingStatusType.Confirmed, isDeleted: false);
        await CreateTestBooking(BookingStatusType.Completed, isDeleted: true);

        var request = new BookingsRequest();
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Bookings.Count()); // Should exclude deleted booking
        Assert.All(result.Value.Bookings, booking => Assert.NotNull(booking));
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task GetAllBookingsAsync_WithEmptyDatabase_ShouldReturnEmptyResult()
    {
        // Arrange
        var request = new BookingsRequest();
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Empty(result.Value.Bookings);
        Assert.Equal(0, result.Value.TotalCount);
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithInvalidPageNumber_ShouldReturnEmptyResult()
    {
        // Arrange
        await CreateTestBooking(BookingStatusType.Requested);

        var request = new BookingsRequest();
        var parameters = new BookingListParams
        {
            PageNumber = 999, // Page that doesn't exist
            PageSize = 10,
        };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Empty(result.Value.Bookings);
        Assert.Equal(1, result.Value.TotalCount);
        Assert.Equal(1, result.Value.TotalPages);
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithZeroPageSize_ShouldUseDefaultPageSize()
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            await CreateTestBooking(BookingStatusType.Requested, DateTime.Today.AddDays(i + 1));
        }

        var request = new BookingsRequest();
        var parameters = new BookingListParams
        {
            PageNumber = 1,
            PageSize = 0, // Invalid page size
        };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Bookings.Count() > 0); // Should use default page size
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithVeryLargePageSize_ShouldReturnAllBookings()
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            await CreateTestBooking(BookingStatusType.Requested, DateTime.Today.AddDays(i + 1));
        }

        var request = new BookingsRequest();
        var parameters = new BookingListParams
        {
            PageNumber = 1,
            PageSize = 1000, // Very large page size
        };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(5, result.Value.Bookings.Count());
        Assert.Equal(1, result.Value.TotalPages);
    }

    [Fact]
    public async Task GetAllBookingsAsync_WithComplexFilters_ShouldApplyAllFilters()
    {
        // Arrange
        await CreateTestBooking(
            BookingStatusType.Requested,
            DateTime.Today.AddDays(1),
            _clientId,
            "Special request"
        );
        await CreateTestBooking(
            BookingStatusType.Confirmed,
            DateTime.Today.AddDays(2),
            _clientId,
            "Regular booking"
        );
        await CreateTestBooking(
            BookingStatusType.Requested,
            DateTime.Today.AddDays(1),
            _alternativeClientId,
            "Special request"
        );

        var request = new BookingsRequest
        {
            UserId = _clientId,
            BookingStatus = BookingStatusType.Requested.ToString(),
            SearchTerm = "special",
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(5),
        };
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 10 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value.Bookings); // Only one booking should match all filters

        var booking = result.Value.Bookings.First();
        Assert.NotNull(booking.Clients);
        Assert.Equal(BookingStatusType.Requested.ToString(), booking.Status);
        Assert.Contains("special", booking.Description, StringComparison.OrdinalIgnoreCase);
    }

    #endregion

    #region Performance Tests

    [Fact]
    public async Task GetAllBookingsAsync_WithLargeDataset_ShouldPerformWell()
    {
        // Arrange
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Create a larger dataset
        for (int i = 0; i < 100; i++)
        {
            await CreateTestBooking(
                BookingStatusType.Requested,
                DateTime.Today.AddDays(i % 30),
                i % 2 == 0 ? _clientId : _alternativeClientId,
                $"Booking {i}"
            );
        }

        var request = new BookingsRequest();
        var parameters = new BookingListParams { PageNumber = 1, PageSize = 20 };

        // Act
        var result = await _bookingService.GetAllBookingsAsync(
            request,
            parameters,
            CancellationToken.None
        );
        stopwatch.Stop();

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(20, result.Value.Bookings.Count());
        Assert.Equal(100, result.Value.TotalCount);

        // Performance assertion - should complete within reasonable time
        Assert.True(
            stopwatch.ElapsedMilliseconds < 1000,
            $"Query took {stopwatch.ElapsedMilliseconds}ms"
        );
    }

    #endregion
}
