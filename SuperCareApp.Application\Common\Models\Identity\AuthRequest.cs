﻿using System.ComponentModel.DataAnnotations;
using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Identity
{
    /// <summary>
    /// Authentication request model
    /// </summary>
    public class AuthRequest
    {
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class AuthRequestValidator : AbstractValidator<AuthRequest>
    {
        public AuthRequestValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty()
                .WithMessage("Email is required.")
                .EmailAddress()
                .WithMessage("A valid email address is required.")
                .MaximumLength(254)
                .WithMessage("Email cannot exceed 254 characters.")
                .Matches(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
                .WithMessage("Email must contain valid characters and a valid domain.");

            RuleFor(x => x.Password)
                .NotEmpty()
                .WithMessage("Password is required.")
                .MinimumLength(8)
                .WithMessage("Password must be at least 8 characters.")
                .MaximumLength(128)
                .WithMessage("Password cannot exceed 128 characters.")
                .Matches(@"^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d\W_]+$")
                .WithMessage(
                    "Password must contain at least one letter and one number, and can include special characters."
                );
        }
    }
}
