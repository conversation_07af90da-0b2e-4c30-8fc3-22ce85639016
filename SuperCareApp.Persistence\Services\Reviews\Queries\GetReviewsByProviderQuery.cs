using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Reviews;

namespace SuperCareApp.Persistence.Services.Reviews.Queries;

public record GetReviewsByProviderQuery(Guid ProviderId) : IQuery<Result<List<ReviewResponse>>>;

public sealed class GetReviewsByProviderQueryHandler
    : IQueryHandler<GetReviewsByProviderQuery, Result<List<ReviewResponse>>>
{
    private readonly ApplicationDbContext _db;
    private readonly ILogger<GetReviewsByProviderQueryHandler> _logger;
    private readonly ICurrentUserService _currentUserService;

    public GetReviewsByProviderQueryHandler(
        ApplicationDbContext db,
        ILogger<GetReviewsByProviderQueryHandler> logger,
        ICurrentUserService currentUserService
    )
    {
        _db = db;
        _logger = logger;
        _currentUserService = currentUserService;
    }

    public async Task<Result<List<ReviewResponse>>> Handle(
        GetReviewsByProviderQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            _logger.LogInformation(
                "Fetching reviews for provider with ID: {ProviderId}",
                request.ProviderId
            );

            // First verify the provider exists
            var provider = await _db
                .CareProviderProfiles.Include(p => p.User)
                .FirstOrDefaultAsync(
                    p => p.Id == request.ProviderId && !p.IsDeleted,
                    cancellationToken
                );

            if (provider == null)
            {
                _logger.LogWarning("Provider {ProviderId} not found", request.ProviderId);
                return Result.Failure<List<ReviewResponse>>(Error.NotFound("Provider not found"));
            }

            // Get all reviews where this provider is the reviewee (received reviews)
            var reviews = await _db
                .Reviews.Where(r => r.RevieweeId == provider.UserId && !r.IsDeleted)
                .Include(r => r.Reviewer)
                .ThenInclude(u => u.UserProfile)
                .Include(r => r.Reviewee)
                .ThenInclude(u => u.UserProfile)
                .Include(r => r.Booking)
                .ThenInclude(b => b.Category)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync(cancellationToken);

            if (reviews.Count == 0)
            {
                _logger.LogInformation(
                    "No reviews found for provider ID: {ProviderId}",
                    request.ProviderId
                );
            }

            var reviewResponses = reviews
                .Select(review => new ReviewResponse(
                    Id: review.Id.ToString(),
                    Rating: review.Rating,
                    Review: review.Comment ?? string.Empty,
                    Category: new ReviewCategoryResponse(
                        Id: review.Booking.Category.Id.ToString(),
                        Name: review.Booking.Category.Name
                    ),
                    ReviewBy: new ReviewerResponse(
                        Name: GetReviewerName(review.Reviewer),
                        ProfileImage: review.Reviewer.UserProfile?.ImagePath ?? string.Empty
                    ),
                    CreatedAt: review.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                ))
                .ToList();

            _logger.LogInformation(
                "Successfully retrieved {Count} reviews for provider ID: {ProviderId}",
                reviewResponses.Count,
                request.ProviderId
            );

            return Result.Success(reviewResponses);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred while retrieving reviews for provider ID: {ProviderId}",
                request.ProviderId
            );

            return Result.Failure<List<ReviewResponse>>(
                Error.Internal("An error occurred while retrieving reviews.")
            );
        }
    }

    private static string GetReviewerName(ApplicationUser reviewer)
    {
        if (reviewer?.UserProfile != null)
        {
            var firstName = reviewer.UserProfile.FirstName?.Trim();
            var lastName = reviewer.UserProfile.LastName?.Trim();

            if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName))
                return $"{firstName} {lastName}";

            if (!string.IsNullOrEmpty(firstName))
                return firstName;

            if (!string.IsNullOrEmpty(lastName))
                return lastName;
        }

        return reviewer?.Email ?? "Unknown User";
    }
}
