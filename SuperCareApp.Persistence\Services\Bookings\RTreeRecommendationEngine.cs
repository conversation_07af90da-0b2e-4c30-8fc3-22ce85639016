using NetTopologySuite.Geometries;
using NetTopologySuite.Index.Strtree;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Bookings;

public class RTreeRecommendationEngine : IRecommendationEngine
{
    private readonly STRtree<CareProviderProfile> _spatialIndex = new();
    private readonly GeometryFactory _geometryFactory = new();
    private readonly ConcurrentDictionary<Guid, CareProviderProfile> _providerCache = new();
    private readonly object _indexLock = new();

    // Constants for better maintainability
    private const double EARTH_RADIUS_KM = 6371.0;
    private const double DEGREES_PER_KM = 111.0; // Approximate degrees per km
    private const double DEFAULT_POINT_BUFFER = 0.0005; // Buffer around point for envelope
    private const double DEG_TO_RAD = Math.PI / 180.0;

    public void AddCareProfessional(CareProviderProfile provider)
    {
        if (provider?.Id == null)
        {
            throw new ArgumentNullException(nameof(provider));
        }

        var coordinates = GetProviderCoordinates(provider);
        if (coordinates == null)
        {
            // Log warning: Provider has no valid address
            return;
        }

        lock (_indexLock)
        {
            var envelope = BuildEnvelope(coordinates.Value.Latitude, coordinates.Value.Longitude);
            _spatialIndex.Insert(envelope, provider);
            _providerCache.TryAdd(provider.Id, provider);
        }
    }

    public void UpdateCareProfessional(CareProviderProfile provider)
    {
        if (provider?.Id == null)
        {
            throw new ArgumentNullException(nameof(provider));
        }

        // Remove old entry and add new one
        RemoveCareProfessional(provider.Id);
        AddCareProfessional(provider);
    }

    public void RemoveCareProfessional(Guid providerId)
    {
        lock (_indexLock)
        {
            if (_providerCache.TryRemove(providerId, out var provider))
            {
                var coordinates = GetProviderCoordinates(provider);
                if (coordinates != null)
                {
                    var envelope = BuildEnvelope(
                        coordinates.Value.Latitude,
                        coordinates.Value.Longitude
                    );
                    _spatialIndex.Remove(envelope, provider);
                }
            }
        }
    }

    public List<CareProviderProfile> Recommend(
        double clientLat,
        double clientLon,
        string? requiredService,
        TimeOnly? requestedStart,
        TimeOnly? requestedEnd,
        double searchRadiusKm = 5,
        int topN = 5
    )
    {
        if (searchRadiusKm <= 0)
        {
            throw new ArgumentException("Search radius must be positive", nameof(searchRadiusKm));
        }

        if (topN <= 0)
        {
            throw new ArgumentException("topN must be positive", nameof(topN));
        }

        var queryEnvelope = BuildSearchEnvelope(clientLat, clientLon, searchRadiusKm);

        List<CareProviderProfile> candidates;
        lock (_indexLock)
        {
            candidates = _spatialIndex.Query(queryEnvelope).ToList();
        }

        return candidates
            .Select(provider => new ProviderWithDistance(provider, clientLat, clientLon))
            .Where(pwd => pwd.IsValid && pwd.DistanceKm <= searchRadiusKm)
            .Where(pwd => MatchesServiceRequirement(pwd.Provider, requiredService))
            .Where(pwd =>
                MatchesAvailabilityRequirement(pwd.Provider, requestedStart, requestedEnd)
            )
            .OrderBy(pwd => pwd.DistanceKm)
            .ThenByDescending(pwd => pwd.Provider.Rating ?? 0)
            .Take(topN)
            .Select(pwd => pwd.Provider)
            .ToList();
    }

    // Overload for backward compatibility
    public List<CareProviderProfile> Recommend(
        double clientLat,
        double clientLon,
        string requiredService,
        TimeOnly requestedStart,
        TimeOnly requestedEnd
    )
    {
        return Recommend(clientLat, clientLon, requiredService, requestedStart, requestedEnd, 5, 5);
    }

    private static (double Latitude, double Longitude)? GetProviderCoordinates(
        CareProviderProfile provider
    )
    {
        var primaryAddress = provider
            ?.User?.UserAddresses?.FirstOrDefault(ua => ua.IsPrimary)
            ?.Address;

        if (primaryAddress?.Latitude != null && primaryAddress?.Longitude != null)
        {
            return ((double)primaryAddress.Latitude, (double)primaryAddress.Longitude);
        }

        return null;
    }

    private static bool MatchesServiceRequirement(
        CareProviderProfile provider,
        string? requiredService
    )
    {
        if (string.IsNullOrEmpty(requiredService))
        {
            return true; // No service requirement
        }

        return provider.CareProviderCategories?.Any(cpc =>
                cpc.CareCategory?.Name?.Equals(requiredService, StringComparison.OrdinalIgnoreCase)
                == true
            ) == true;
    }

    private static bool MatchesAvailabilityRequirement(
        CareProviderProfile provider,
        TimeOnly? requestedStart,
        TimeOnly? requestedEnd
    )
    {
        if (!requestedStart.HasValue || !requestedEnd.HasValue)
        {
            return true; // No time requirement
        }

        return provider.Availabilities?.Any(availability =>
                availability.IsAvailable
                && availability.AvailabilitySlots?.Any(slot =>
                    slot.StartTime < requestedEnd && slot.EndTime > requestedStart
                ) == true
            ) == true;
    }

    private static Envelope BuildEnvelope(
        double lat,
        double lon,
        double buffer = DEFAULT_POINT_BUFFER
    )
    {
        return new Envelope(lon - buffer, lon + buffer, lat - buffer, lat + buffer);
    }

    private static Envelope BuildSearchEnvelope(double lat, double lon, double radiusKm)
    {
        double delta = radiusKm / DEGREES_PER_KM;
        return new Envelope(lon - delta, lon + delta, lat - delta, lat + delta);
    }

    private static double CalculateHaversineDistance(
        double lat1,
        double lon1,
        double lat2,
        double lon2
    )
    {
        var dLat = (lat2 - lat1) * DEG_TO_RAD;
        var dLon = (lon2 - lon1) * DEG_TO_RAD;

        var a =
            Math.Sin(dLat / 2) * Math.Sin(dLat / 2)
            + Math.Cos(lat1 * DEG_TO_RAD)
                * Math.Cos(lat2 * DEG_TO_RAD)
                * Math.Sin(dLon / 2)
                * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return EARTH_RADIUS_KM * c;
    }

    private readonly struct ProviderWithDistance
    {
        public CareProviderProfile Provider { get; }
        public double DistanceKm { get; }
        public bool IsValid { get; }

        public ProviderWithDistance(
            CareProviderProfile provider,
            double clientLat,
            double clientLon
        )
        {
            Provider = provider;
            var coordinates = GetProviderCoordinates(provider);

            if (coordinates.HasValue)
            {
                DistanceKm = CalculateHaversineDistance(
                    clientLat,
                    clientLon,
                    coordinates.Value.Latitude,
                    coordinates.Value.Longitude
                );
                IsValid = true;
            }
            else
            {
                DistanceKm = double.MaxValue;
                IsValid = false;
            }
        }
    }

    public void Dispose()
    {
        _providerCache?.Clear();
    }
}
