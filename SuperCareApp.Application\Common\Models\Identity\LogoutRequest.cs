﻿using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Identity;

public record LogoutRequest(string token);

public class LogoutRequestValidator : AbstractValidator<LogoutRequest>
{
    public LogoutRequestValidator()
    {
        RuleFor(x => x.token)
            .NotEmpty()
            .WithMessage("Token is required.")
            .Must(token => !string.IsNullOrWhiteSpace(token))
            .WithMessage("Token must be a valid non-whitespace string.");
    }
}
