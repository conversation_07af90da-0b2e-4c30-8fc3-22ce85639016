namespace SuperCareApp.Shared.Utility
{
    /// <summary>
    /// Extension methods for creating standardized API responses
    /// </summary>
    public static class ApiResponseExtension
    {
        private const string GenericSuccessMessage = "The operation completed successfully.";
        private const string GenericWarningMessage = "The operation completed with warnings.";
        private const string GenericErrorMessage = "The operation completed with errors.";
        private const string GenericNotFoundMessage = "The requested resource was not found.";
        private const string GenericUnauthorizedMessage = "Unauthorized access.";
        private const string GenericForbiddenMessage = "Access to the resource is forbidden.";
        private const string GenericInternalServerErrorMessage =
            "An internal server error occurred.";
        private const string GenericBadRequestMessage = "The request is invalid.";
        private const string GenericInfoMessage = "Information response.";

        /// <summary>
        /// Creates a success API response
        /// </summary>
        public static IResult ToSuccessApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Ok(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Success,
                    message ?? GenericSuccessMessage,
                    payload,
                    statusCode
                )
            );

        /// <summary>
        /// Creates a warning API response
        /// </summary>
        public static IResult ToWarningApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Warning,
                    message ?? GenericWarningMessage,
                    payload,
                    statusCode
                ),
                statusCode: 200
            );

        /// <summary>
        /// Creates an info API response
        /// </summary>
        public static IResult ToInfoApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Ok(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Info,
                    message ?? GenericInfoMessage,
                    payload,
                    statusCode
                )
            );

        /// <summary>
        /// Creates an error API response
        /// </summary>
        public static IResult ToErrorApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Error,
                    message ?? GenericErrorMessage,
                    payload,
                    statusCode
                ),
                statusCode: 400
            );

        /// <summary>
        /// Creates a not found API response
        /// </summary>
        public static IResult ToNotFoundApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.NotFound,
                    message ?? GenericNotFoundMessage,
                    payload,
                    statusCode
                ),
                statusCode: 404
            );

        /// <summary>
        /// Creates an unauthorized API response
        /// </summary>
        public static IResult ToUnauthorizedApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Unauthorized,
                    message ?? GenericUnauthorizedMessage,
                    payload,
                    statusCode
                ),
                statusCode: 401
            );

        /// <summary>
        /// Creates a forbidden API response
        /// </summary>
        public static IResult ToForbiddenApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.Forbidden,
                    message ?? GenericForbiddenMessage,
                    payload,
                    statusCode
                ),
                statusCode: 403
            );

        /// <summary>
        /// Creates a bad request API response
        /// </summary>
        public static IResult ToBadRequestApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.BadRequest,
                    message ?? GenericBadRequestMessage,
                    payload,
                    statusCode
                ),
                statusCode: 400
            );

        /// <summary>
        /// Creates an internal server error API response
        /// </summary>
        public static IResult ToInternalServerErrorApiResult(
            object? payload = null,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new ApiResponseModel<object>(
                    ApiResponseStatusEnum.InternalServerError,
                    message ?? GenericInternalServerErrorMessage,
                    payload,
                    statusCode
                ),
                statusCode: 500
            );

        /// <summary>
        /// Creates a paginated API response
        /// </summary>
        public static IResult ToPaginatedApiResult(
            object payload,
            int currentPage,
            int totalPages,
            int totalCount,
            int pageSize,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new PaginatedResponseModel<object>(
                    ApiResponseStatusEnum.Success,
                    message ?? GenericSuccessMessage,
                    payload,
                    currentPage,
                    totalPages,
                    totalCount,
                    pageSize,
                    statusCode
                )
            );

        /// <summary>
        /// Creates a paginated API response using a PaginationMetadata object
        /// </summary>
        public static IResult ToPaginatedApiResult(
            object payload,
            PaginationMetadata meta,
            string? message = null,
            string? statusCode = null
        ) =>
            Results.Json(
                new PaginatedResponseModel<object>(
                    ApiResponseStatusEnum.Success,
                    message ?? GenericSuccessMessage,
                    payload,
                    meta,
                    statusCode
                )
            );

        /// <summary>
        /// Creates an API response with the specified status
        /// </summary>
        public static IResult ToApiResult(
            ApiResponseStatusEnum status,
            object? payload = null,
            string? message = null,
            string? statusCode = null
        )
        {
            var response = new ApiResponseModel<object>(
                status,
                message ?? GetDefaultMessage(status),
                payload,
                statusCode
            );

            return Results.Json(response, statusCode: response.StatusCode);
        }

        private static string GetDefaultMessage(ApiResponseStatusEnum status) =>
            status switch
            {
                ApiResponseStatusEnum.Success => GenericSuccessMessage,
                ApiResponseStatusEnum.Warning => GenericWarningMessage,
                ApiResponseStatusEnum.Info => GenericInfoMessage,
                ApiResponseStatusEnum.Error => GenericErrorMessage,
                ApiResponseStatusEnum.NotFound => GenericNotFoundMessage,
                ApiResponseStatusEnum.BadRequest => GenericBadRequestMessage,
                ApiResponseStatusEnum.Unauthorized => GenericUnauthorizedMessage,
                ApiResponseStatusEnum.Forbidden => GenericForbiddenMessage,
                ApiResponseStatusEnum.InternalServerError => GenericInternalServerErrorMessage,
                _ => string.Empty,
            };
    }
}
