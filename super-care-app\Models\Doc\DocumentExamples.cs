﻿using System.Text.Json;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Shared.Utility;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Example response models for Document API documentation
    /// </summary>
    public static class DocumentExamples
    {
        // Reusable JsonSerializerOptions
        private static readonly JsonSerializerOptions _jsonOptions = new() { WriteIndented = true };

        /// <summary>
        /// Gets an example of a successful document upload response
        /// </summary>
        public static string UploadDocumentExample()
        {
            var documentResponse = new DocumentResponse
            {
                DocumentId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                FileName = "passport.pdf",
                MimeType = "application/pdf",
                DocumentUrl = "/Documents/ID/a57ac10b-58cc-4372-a567-0e02b2c3d123/passport.pdf",
                DocumentType = "ID",
                VerificationStatus = "Pending",
                UploadedAt = DateTime.UtcNow.AddDays(-1),
                Country = "United Kingdom",
                CertificationType = "",
                OtherCertificationType = null,
                CertificationNumber = null,
                ExpiryDate = null,
                IsExpired = false,
            };

            var response = new ApiResponseModel<DocumentResponse>(
                ApiResponseStatusEnum.Success,
                "Document uploaded successfully",
                documentResponse
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful certificate upload response
        /// </summary>
        public static string UploadCertificateExample()
        {
            var documentResponse = new DocumentResponse
            {
                DocumentId = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d480"),
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                FileName = "nursing_certificate.pdf",
                MimeType = "application/pdf",
                DocumentUrl =
                    "/Documents/Certificate/a57ac10b-58cc-4372-a567-0e02b2c3d123/nursing_certificate.pdf",
                DocumentType = "Certificate",
                VerificationStatus = "Pending",
                UploadedAt = DateTime.UtcNow.AddDays(-1),
                Country = "United Kingdom",
                CertificationType = "Level 3 Diploma in Adult Care",
                OtherCertificationType = null,
                CertificationNumber = "CERT-12345",
                ExpiryDate = DateTime.UtcNow.AddYears(2),
                IsExpired = false,
            };

            var response = new ApiResponseModel<DocumentResponse>(
                ApiResponseStatusEnum.Success,
                "Certificate uploaded successfully",
                documentResponse
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful get document response
        /// </summary>
        public static string GetDocumentExample()
        {
            var documentResponse = new DocumentResponse
            {
                DocumentId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                FileName = "passport.pdf",
                MimeType = "application/pdf",
                DocumentUrl = "/Documents/ID/a57ac10b-58cc-4372-a567-0e02b2c3d123/passport.pdf",
                DocumentType = "ID",
                VerificationStatus = "Verified",
                UploadedAt = DateTime.UtcNow.AddDays(-5),
                VerifiedAt = DateTime.UtcNow.AddDays(-2),
                VerifiedBy = Guid.Parse("b57ac10b-58cc-4372-a567-0e02b2c3d456"),
                Country = "United Kingdom",
                CertificationType = "",
                OtherCertificationType = null,
                CertificationNumber = null,
                ExpiryDate = null,
                IsExpired = false,
            };

            var response = new ApiResponseModel<DocumentResponse>(
                ApiResponseStatusEnum.Success,
                "Document retrieved successfully",
                documentResponse
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful get all documents response
        /// </summary>
        public static string GetAllDocumentsExample()
        {
            var documents = new List<DocumentResponse>
            {
                new DocumentResponse
                {
                    DocumentId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    FileName = "passport.pdf",
                    MimeType = "application/pdf",
                    DocumentUrl = "/Documents/ID/a57ac10b-58cc-4372-a567-0e02b2c3d123/passport.pdf",
                    DocumentType = "ID",
                    VerificationStatus = "Verified",
                    UploadedAt = DateTime.UtcNow.AddDays(-5),
                    VerifiedAt = DateTime.UtcNow.AddDays(-2),
                    VerifiedBy = Guid.Parse("b57ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Country = "United Kingdom",
                    CertificationType = "",
                    OtherCertificationType = null,
                    CertificationNumber = null,
                    ExpiryDate = null,
                    IsExpired = false,
                },
                new DocumentResponse
                {
                    DocumentId = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d480"),
                    UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    FileName = "nursing_certificate.pdf",
                    MimeType = "application/pdf",
                    DocumentUrl =
                        "/Documents/Certificate/a57ac10b-58cc-4372-a567-0e02b2c3d123/nursing_certificate.pdf",
                    DocumentType = "Certificate",
                    VerificationStatus = "Pending",
                    UploadedAt = DateTime.UtcNow.AddDays(-1),
                    Country = "United Kingdom",
                    CertificationType = "Level 3 Diploma in Adult Care",
                    OtherCertificationType = null,
                    CertificationNumber = "CERT-12345",
                    ExpiryDate = DateTime.UtcNow.AddYears(2),
                    IsExpired = false,
                },
            };

            var response = new ApiResponseModel<IEnumerable<DocumentResponse>>(
                ApiResponseStatusEnum.Success,
                "Documents retrieved successfully",
                documents
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }

        /// <summary>
        /// Gets an example of a successful update document response
        /// </summary>
        public static string UpdateDocumentExample()
        {
            var documentResponse = new DocumentResponse
            {
                DocumentId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                FileName = "updated_passport.pdf",
                MimeType = "application/pdf",
                DocumentUrl =
                    "/Documents/ID/a57ac10b-58cc-4372-a567-0e02b2c3d123/updated_passport.pdf",
                DocumentType = "ID",
                VerificationStatus = "Pending",
                UploadedAt = DateTime.UtcNow.AddDays(-5),
                Country = "United Kingdom",
                CertificationType = "",
                OtherCertificationType = null,
                CertificationNumber = null,
                ExpiryDate = null,
                IsExpired = false,
            };

            var response = new ApiResponseModel<DocumentResponse>(
                ApiResponseStatusEnum.Success,
                "Document updated successfully",
                documentResponse
            );

            return JsonSerializer.Serialize(response, _jsonOptions);
        }
    }
}
