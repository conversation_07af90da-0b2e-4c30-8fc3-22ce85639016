﻿namespace SuperCareApp.Application.Common.Models.Identity
{
    /// <summary>
    /// Authentication response model
    /// </summary>
    public record AuthResponse(
        string accessToken,
        DateTime expiresIn,
        string refreshToken,
        bool? isVerifiedByAdmin = null,
        AuthUserResponse? user = null
    );

    public record AuthUserResponse(
        string userId,
        string email,
        string phoneNumber,
        string providerId = null!
    );
}
