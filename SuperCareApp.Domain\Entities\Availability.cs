﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Domain.Entities;

public sealed class Availability : BaseEntity
{
    public Guid ProviderId { get; set; } // Changed from ProviderUserId to ProviderId
    public string DayOfWeek { get; set; }

    public bool IsAvailable { get; set; }

    // Navigation property
    public CareProviderProfile CareProviderProfile { get; set; } = null!;
    public ICollection<AvailabilitySlot> AvailabilitySlots { get; set; } =
        new List<AvailabilitySlot>();
}
