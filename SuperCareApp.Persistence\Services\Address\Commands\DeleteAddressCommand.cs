﻿using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Address.Commands
{
    public record DeleteAddressCommand(Guid UserId, Guid AddressId) : ICommand<Result<bool>>;

    internal sealed class DeleteAddressCommandHandler
        : ICommandHandler<DeleteAddressCommand, Result<bool>>
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<DeleteAddressCommandHandler> _logger;

        public DeleteAddressCommandHandler(
            IAddressService addressService,
            ILogger<DeleteAddressCommandHandler> logger
        )
        {
            _addressService = addressService;
            _logger = logger;
        }

        public async Task<Result<bool>> Handle(
            DeleteAddressCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _addressService.DeleteAddressAsync(request.UserId, request.AddressId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error deleting address {AddressId} for user {UserId}",
                    request.AddressId,
                    request.UserId
                );
                return Result.Failure<bool>(
                    Error.Internal($"Error deleting address: {ex.Message}")
                );
            }
        }
    }
}
