using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Queries
{
    /// <summary>
    /// Query to get paginated care categories
    /// </summary>
    public record GetPaginatedCategoriesQuery(
        int PageNumber = 1,
        int PageSize = 10,
        bool IncludeInactive = false
    ) : IQuery<Result<PagedCategoryList>>;

    /// <summary>
    /// Handler for the GetPaginatedCategoriesQuery
    /// </summary>
    public sealed class GetPaginatedCategoriesQueryHandler
        : IQueryHandler<GetPaginatedCategoriesQuery, Result<PagedCategoryList>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<GetPaginatedCategoriesQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetPaginatedCategoriesQueryHandler(
            ICareCategoryService categoryService,
            ILogger<GetPaginatedCategoriesQueryHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        public async Task<Result<PagedCategoryList>> Handle(
            GetPaginatedCategoriesQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _categoryService.GetPaginatedCategoriesAsync(
                    request.PageNumber,
                    request.PageSize,
                    request.IncludeInactive,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paginated care categories");
                return Result.Failure<PagedCategoryList>(Error.Internal(ex.Message));
            }
        }
    }
}
