using Microsoft.EntityFrameworkCore;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Application.Common.Settings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using BookingStatus = SuperCareApp.Domain.Entities.BookingStatus;

namespace SuperCareApp.Persistence.Services;

public class BookingManagementService : IBookingManagementService
{
    private readonly ApplicationDbContext _context;
    private readonly TimeSpan _minimumSlotDuration = TimeSpan.FromMinutes(30);

    public BookingManagementService(ApplicationDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Gets available time slots for a provider on a specific date, accounting for bookings and buffer time.
    /// </summary>
    /// <param name="providerId">The ID of the care provider.</param>
    /// <param name="date">The date to check availability for.</param>
    /// <returns>A list of available time slots as Interval<TimeOnly>.</returns>
    public async Task<List<Interval<TimeOnly>>> GetAvailableSlotsForDateAsync(
        Guid providerId,
        DateOnly date
    )
    {
        var provider = await _context
            .CareProviderProfiles.AsSplitQuery()
            .Include(p => p.Availabilities)
            .ThenInclude(a => a.AvailabilitySlots)
            .Include(cpc => cpc.CareProviderCategories)
            .ThenInclude(cpc => cpc.CareCategory)
            .AsSplitQuery()
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == providerId);

        if (provider is null)
            return new List<Interval<TimeOnly>>();

        var dayOfWeek = date.DayOfWeek.ToString();

        var dailyAvailabilities = provider
            .Availabilities.Where(a =>
                a.DayOfWeek.Equals(dayOfWeek, StringComparison.OrdinalIgnoreCase) && a.IsAvailable
            )
            .ToList();

        if (!dailyAvailabilities.Any())
            return new List<Interval<TimeOnly>>();

        var baseAvailableIntervals = dailyAvailabilities
            .SelectMany(a => a.AvailabilitySlots)
            .Select(slot => new Interval<TimeOnly>(slot.StartTime, slot.EndTime))
            .OrderBy(slot => slot.Start)
            .ToList();

        if (!baseAvailableIntervals.Any())
            return new List<Interval<TimeOnly>>();

        // Validate and merge availability slots
        foreach (var slot in baseAvailableIntervals)
            slot.Validate();

        var mergedAvailability = IntervalUtils.Merge(baseAvailableIntervals);

        var rangeStart = date.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
        var rangeEnd = date.ToDateTime(TimeOnly.MaxValue, DateTimeKind.Utc);

        var leaves = await _context
            .Leaves.Where(l =>
                l.ProviderId == providerId && l.StartDate <= rangeEnd && l.EndDate >= rangeStart
            )
            .AsNoTracking()
            .ToListAsync();

        var bookings = await _context
            .Bookings.Include(b => b.Status)
            .Include(b => b.BookingWindows)
            .Where(b => b.ProviderId == providerId && b.BookingWindows.Any(w => w.Date == date))
            .AsNoTracking()
            .ToListAsync();

        var blockages = GetBlockagesForDate(date, leaves, bookings, provider.BufferDuration);

        var availableSlots = new List<Interval<TimeOnly>>();
        foreach (var slot in mergedAvailability)
        {
            var remaining = IntervalUtils.Subtract(slot, blockages);
            availableSlots.AddRange(remaining);
        }

        return availableSlots
            .Where(slot => (slot.End - slot.Start) >= _minimumSlotDuration)
            .OrderBy(slot => slot.Start)
            .ToList();
    }

    /// <summary>
    /// Handles booking workflow: create, confirm, cancel, or complete a booking.
    /// </summary>
    /// <param name="bookingRequest">Booking details including action type.</param>
    /// <returns>Booking ID or throws an exception on failure.</returns>
    public async Task<Guid> HandleBookingAsync(BookingRequest bookingRequest)
    {
        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            Guid bookingId;
            switch (bookingRequest.Action)
            {
                //case BookingAction.Create:
                //    bookingId = await CreateBookingAsync(bookingRequest);
                //    break;
                case BookingAction.Confirm:
                    await ConfirmBookingAsync(bookingRequest.BookingId);
                    bookingId = bookingRequest.BookingId;
                    break;
                case BookingAction.Cancel:
                    await CancelBookingAsync(bookingRequest.BookingId);
                    bookingId = bookingRequest.BookingId;
                    break;
                case BookingAction.Complete:
                    await CompleteBookingAsync(bookingRequest.BookingId);
                    bookingId = bookingRequest.BookingId;
                    break;
                default:
                    throw new ArgumentException("Invalid booking action.");
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return bookingId;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    private async Task ConfirmBookingAsync(Guid bookingId)
    {
        var booking =
            await _context
                .Bookings.Include(b => b.Status)
                .Include(b => b.BookingWindows)
                .FirstOrDefaultAsync(b => b.Id == bookingId)
            ?? throw new InvalidOperationException("Booking not found.");

        if (booking.Status.Status != BookingStatusType.Requested)
            throw new InvalidOperationException("Booking is not in Requested state.");

        foreach (var window in booking.BookingWindows)
        {
            var requestedSlot = new Interval<TimeOnly>(window.StartTime, window.EndTime);
            var availableSlots = await GetAvailableSlotsForDateAsync(
                booking.ProviderId,
                window.Date
            );

            if (!availableSlots.Any(slot => IntervalUtils.IsContainedWithin(requestedSlot, slot)))
                throw new InvalidOperationException(
                    $"Slot for {window.Date} is no longer available."
                );
        }

        var newStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Confirmed,
            CreatedBy = booking.Status.CreatedBy,
            CreatedAt = DateTime.UtcNow,
        };

        _context.BookingStatuses.Add(newStatus);
        booking.Status = newStatus; // Add this line to update the reference
    }

    private async Task CancelBookingAsync(Guid bookingId)
    {
        var booking =
            await _context
                .Bookings.Include(b => b.Status)
                .FirstOrDefaultAsync(b => b.Id == bookingId)
            ?? throw new InvalidOperationException("Booking not found.");

        if (
            booking.Status.Status == BookingStatusType.Completed
            || booking.Status.Status == BookingStatusType.Cancelled
        )
            throw new InvalidOperationException("Booking is already completed or cancelled.");

        var newStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Cancelled,
            CreatedBy = booking.Status.CreatedBy,
            CreatedAt = DateTime.UtcNow,
        };

        booking.Status = newStatus;
        _context.BookingStatuses.Add(newStatus);
        // Slot merging is handled by GetAvailableSlotsForDateAsync թ
    }

    private async Task CompleteBookingAsync(Guid bookingId)
    {
        var booking =
            await _context
                .Bookings.Include(b => b.Status)
                .FirstOrDefaultAsync(b => b.Id == bookingId)
            ?? throw new InvalidOperationException("Booking not found.");

        if (booking.Status.Status != BookingStatusType.Confirmed)
            throw new InvalidOperationException("Booking is not in Confirmed state.");

        var newStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Completed,
            CreatedBy = booking.Status.CreatedBy,
            CreatedAt = DateTime.UtcNow,
        };

        booking.Status = newStatus;
        _context.BookingStatuses.Add(newStatus);
    }

    private List<Interval<TimeOnly>> GetBlockagesForDate(
        DateOnly date,
        IEnumerable<Leave> leaves,
        IEnumerable<Booking> bookings,
        int bufferDurationMinutes
    )
    {
        var blockages = new List<Interval<TimeOnly>>();
        var buffer = TimeSpan.FromMinutes(bufferDurationMinutes);

        foreach (var leave in leaves)
        {
            var leaveStartDate = DateOnly.FromDateTime(leave.StartDate);
            var leaveEndDate = DateOnly.FromDateTime(leave.EndDate);
            if (leaveStartDate > date || leaveEndDate < date)
                continue;

            var start =
                leaveStartDate < date ? TimeOnly.MinValue : TimeOnly.FromDateTime(leave.StartDate);
            var end =
                leaveEndDate > date ? TimeOnly.MaxValue : TimeOnly.FromDateTime(leave.EndDate);
            blockages.Add(new Interval<TimeOnly>(start, end));
        }

        foreach (var booking in bookings)
        {
            foreach (var window in booking.BookingWindows.Where(w => w.Date == date))
            {
                // Apply buffer only for confirmed bookings
                if (booking.Status.Status != BookingStatusType.Confirmed)
                    continue;

                var start = window.StartTime.Add(-buffer);
                var end = window.EndTime.Add(buffer);

                start = start < TimeOnly.MinValue ? TimeOnly.MinValue : start;
                end = end > TimeOnly.MaxValue ? TimeOnly.MaxValue : end;

                var interval = new Interval<TimeOnly>(start, end);
                interval.Validate();
                blockages.Add(interval);
            }
        }

        return IntervalUtils.Merge(blockages);
    }

    private List<DateOnly> GetAllDaysInRange(DateOnly startDate, DateOnly endDate)
    {
        var allDays = new List<DateOnly>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            allDays.Add(date);
        }
        return allDays;
    }
}
