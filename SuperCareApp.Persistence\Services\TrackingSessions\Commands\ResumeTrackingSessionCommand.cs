﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.TrackingSessions.Commands;

public sealed record ResumeTrackingSessionCommand(Guid SessionId, Guid ProviderId)
    : ICommand<Result>;

internal sealed class ResumeTrackingSessionHandler(ApplicationDbContext db, TimeProvider time)
    : ICommandHandler<ResumeTrackingSessionCommand, Result>
{
    private readonly ApplicationDbContext _db = db;
    private readonly TimeProvider _time = time;

    public async Task<Result> Handle(ResumeTrackingSessionCommand req, CancellationToken ct)
    {
        try
        {
            var session = await _db.TrackingSessions.SingleOrDefaultAsync(
                s => s.Id == req.SessionId && s.ProviderId == req.ProviderId,
                ct
            );

            if (session is null)
                return Result.Failure(Error.NotFound("Session not found."));

            if (session.Status != TrackingSessionStatus.Paused || session.PausedAt is null)
                return Result.Failure(Error.BadRequest("Session not paused."));

            var pausedSpan = _time.GetUtcNow() - session.PausedAt.Value;
            session.TotalPausedDuration += pausedSpan;
            session.PausedAt = null;
            session.Status = TrackingSessionStatus.Running;

            await _db.SaveChangesAsync(ct);
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure(Error.BadRequest(ex.Message + "" + ex.StackTrace));
        }
    }
}
