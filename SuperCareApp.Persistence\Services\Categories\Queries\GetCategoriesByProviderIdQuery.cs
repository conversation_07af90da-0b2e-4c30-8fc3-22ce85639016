﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Queries
{
    /// <summary>
    /// Query to get care categories by provider ID
    /// </summary>
    public record GetCategoriesByProviderIdQuery(Guid ProviderId)
        : IQuery<Result<IEnumerable<CareProviderCategoryResponse>>>;

    /// <summary>
    /// Handler for the GetCategoriesByProviderIdQuery
    /// </summary>
    public sealed class GetCategoriesByProviderIdQueryHandler
        : IQueryHandler<
            GetCategoriesByProviderIdQuery,
            Result<IEnumerable<CareProviderCategoryResponse>>
        >
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<GetCategoriesByProviderIdQueryHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetCategoriesByProviderIdQueryHandler(
            ICareCategoryService categoryService,
            ILogger<GetCategoriesByProviderIdQueryHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        public async Task<Result<IEnumerable<CareProviderCategoryResponse>>> Handle(
            GetCategoriesByProviderIdQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _categoryService.GetCategoriesByProviderIdAsync(
                    request.ProviderId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting care categories for provider with ID {ProviderId}",
                    request.ProviderId
                );
                return Result.Failure<IEnumerable<CareProviderCategoryResponse>>(
                    Error.Internal(ex.Message)
                );
            }
        }
    }
}
