using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Admin;

namespace SuperCareApp.Persistence.Services.Admin.Queries
{
    /// <summary>
    /// Query to get admin profile information
    /// </summary>
    public record GetAdminProfileQuery(Guid AdminId) : IQuery<Result<GetAdminProfileResponse>>;

    /// <summary>
    /// Handler for the GetAdminProfileQuery
    /// </summary>
    internal sealed class GetAdminProfileQueryHandler
        : IQueryHandler<GetAdminProfileQuery, Result<GetAdminProfileResponse>>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<GetAdminProfileQueryHandler> _logger;
        private readonly UserManager<ApplicationUser> _userManager;

        /// <summary>
        /// Constructor
        /// </summary>
        public GetAdminProfileQueryHandler(
            ApplicationDbContext dbContext,
            IFileStorageService fileStorageService,
            ILogger<GetAdminProfileQueryHandler> logger,
            UserManager<ApplicationUser> userManager
        )
        {
            _dbContext = dbContext;
            _fileStorageService = fileStorageService;
            _logger = logger;
            _userManager = userManager;
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        public async Task<Result<GetAdminProfileResponse>> Handle(
            GetAdminProfileQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                _logger.LogInformation(
                    "Retrieving admin profile for user {AdminId}",
                    request.AdminId
                );

                // Get the admin user with profile and roles
                var adminUser = await _dbContext
                    .Users.Include(u => u.UserProfile)
                    .FirstOrDefaultAsync(
                        u => u.Id == request.AdminId && !u.IsDeleted,
                        cancellationToken
                    );

                if (adminUser == null)
                {
                    _logger.LogWarning("Admin user not found: {AdminId}", request.AdminId);
                    return Result.Failure<GetAdminProfileResponse>(
                        Error.NotFound("Admin user not found")
                    );
                }

                var roles = await _userManager.GetRolesAsync(adminUser);

                // Check if user has admin role
                var hasAdminRole = roles.Any(role => role == "Admin");
                if (!hasAdminRole)
                {
                    _logger.LogWarning("User {AdminId} does not have admin role", request.AdminId);
                    return Result.Failure<GetAdminProfileResponse>(
                        Error.Forbidden("User does not have admin privileges")
                    );
                }

                // Get profile picture URL if exists
                string? profilePictureUrl = null;
                if (adminUser.UserProfile?.ImagePath != null)
                {
                    profilePictureUrl = _fileStorageService.GetFileUrl(
                        adminUser.UserProfile.ImagePath
                    );
                }

                // Create response
                var response = new GetAdminProfileResponse
                {
                    UserId = adminUser.Id,
                    FirstName = adminUser.UserProfile?.FirstName ?? string.Empty,
                    LastName = adminUser.UserProfile?.LastName ?? string.Empty,
                    Name =
                        $"{adminUser.UserProfile?.FirstName} {adminUser.UserProfile?.LastName}".Trim(),
                    Email = adminUser.Email ?? string.Empty,
                    PhoneNumber = adminUser.UserProfile?.PhoneNumber ?? string.Empty,
                    Gender = adminUser.UserProfile?.Gender ?? string.Empty,
                    ProfilePictureUrl = profilePictureUrl,
                    Country = adminUser.UserProfile?.Country,
                    CreatedAt = adminUser.CreatedAt,
                    UpdatedAt = adminUser.UpdatedAt,
                    LastLogin = adminUser.LastLogin,
                    IsActive = adminUser.IsActive,
                    EmailVerified = adminUser.EmailConfirmed,
                    Roles = roles.ToList(),
                };

                _logger.LogInformation(
                    "Successfully retrieved admin profile for user {AdminId}",
                    request.AdminId
                );

                return Result.Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving admin profile for user {AdminId}",
                    request.AdminId
                );
                return Result.Failure<GetAdminProfileResponse>(
                    Error.Internal("An error occurred while retrieving the admin profile")
                );
            }
        }
    }
}
