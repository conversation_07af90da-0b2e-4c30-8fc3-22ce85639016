﻿namespace SuperCareApp.Domain.Enums;

public enum InvoiceStatus
{
    Draft,
    Pending,
    Paid,
    Partial,
    Overdue,
    Cancelled,
}

public static class InvoiceStatusExtensions
{
    public static string ToString(this InvoiceStatus status)
    {
        return status switch
        {
            InvoiceStatus.Draft => "Draft",
            InvoiceStatus.Pending => "Pending",
            InvoiceStatus.Paid => "Paid",
            InvoiceStatus.Partial => "Partial",
            InvoiceStatus.Overdue => "Overdue",
            InvoiceStatus.Cancelled => "Cancelled",
            _ => throw new ArgumentOutOfRangeException(nameof(status), status, null),
        };
    }

    public static InvoiceStatus FromString(string statusString)
    {
        return statusString?.Trim() switch
        {
            "Draft" => InvoiceStatus.Draft,
            "Pending" => InvoiceStatus.Pending,
            "Paid" => InvoiceStatus.Paid,
            "Partial" => InvoiceStatus.Partial,
            "Overdue" => InvoiceStatus.Overdue,
            "Cancelled" => InvoiceStatus.Cancelled,
            _ => throw new ArgumentException(
                $"Invalid invoice status: '{statusString}'",
                nameof(statusString)
            ),
        };
    }
}
