# API Response Model

This document explains the standardized API response model implemented in the application.

## Overview

The API response model provides a consistent structure for all API responses, making it easier for clients to handle responses from the API. The model includes:

- `apiResponseId`: A unique identifier for the response
- `success`: A boolean indicating whether the operation was successful (true or false)
- `statusCode`: The HTTP status code
- `message`: A human-readable message
- `payload`: The actual data returned by the API
- `timestamp`: The time when the response was created

## Response Structure

```json
{
  "apiResponseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "success": true,
  "statusCode": 200,
  "message": "The operation completed successfully.",
  "payload": {
    // The actual data returned by the API
  },
  "timestamp": "2023-06-15T14:30:45.123Z"
}
```

For paginated responses, additional fields are included:

```json
{
  "apiResponseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "success": true,
  "statusCode": 200,
  "message": "The operation completed successfully.",
  "payload": {
    // The paginated data
  },
  "timestamp": "2023-06-15T14:30:45.123Z",
  "currentPage": 1,
  "totalPages": 5
}
```

## Status Types

The API response model supports the following status types:

- `success`: The operation completed successfully
- `warning`: The operation completed with warnings
- `info`: Information response
- `error`: The operation completed with errors
- `notFound`: The requested resource was not found
- `badRequest`: The request is invalid
- `unauthorized`: Unauthorized access
- `forbidden`: Access to the resource is forbidden
- `internalServerError`: An internal server error occurred

## Using the API Response Model in Controllers

### Using Controller Extensions

The easiest way to use the API response model is through the controller extension methods:

```csharp
// Return a success response
return this.Success(data);

// Return a success response with a custom message
return this.Success(data, "Custom success message");

// Return an error response
return this.Error<object>("Error message");

// Return a not found response
return this.NotFound<object>("Resource not found");

// Return a bad request response
return this.BadRequest<object>("Invalid request");

// Return an unauthorized response
return this.Unauthorized<object>("Unauthorized access");

// Return a forbidden response
return this.Forbidden<object>("Access forbidden");

// Return an internal server error response
return this.InternalServerError<object>("Internal server error");

// Return a paginated response
return this.Paginated(data, currentPage, totalPages);
```

### Using the API Response Model Directly

You can also create the API response model directly:

```csharp
var response = new ApiResponseModel<MyDataType>(
    ApiResponseStatusEnum.Success,
    "Operation completed successfully",
    myData);

return Ok(response);
```

### Using Minimal API Style

For minimal APIs, you can use the `ApiResponseExtension` methods:

```csharp
// Return a success response
return ApiResponseExtension.ToSuccessApiResult(data);

// Return an error response
return ApiResponseExtension.ToErrorApiResult(data, "Error message");

// Return a not found response
return ApiResponseExtension.ToNotFoundApiResult(data, "Resource not found");

// And so on...
```

## Automatic Response Wrapping

The `ApiResponseFilter` automatically wraps all controller responses in the API response model format. This means you don't have to explicitly use the API response model in your controllers - the filter will do it for you.

For example, if you return:

```csharp
return Ok(myData);
```

The filter will automatically wrap it in an API response model:

```json
{
  "apiResponseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "success",
  "statusCode": 200,
  "message": "The operation completed successfully.",
  "payload": {
    // myData
  },
  "timestamp": "2023-06-15T14:30:45.123Z"
}
```

## Handling Validation Errors

The `ApiResponseFilter` also handles model validation errors automatically. If a request fails validation, the filter will return a bad request response with the validation errors in the payload:

```json
{
  "apiResponseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "badRequest",
  "statusCode": 400,
  "message": "Validation failed",
  "payload": {
    "email": ["Email is required", "Email format is invalid"],
    "password": ["Password must be at least 8 characters"]
  },
  "timestamp": "2023-06-15T14:30:45.123Z"
}
```

## Handling Exceptions

The `ApiResponseFilter` also handles exceptions automatically. If an exception occurs during the execution of a controller action, the filter will return an internal server error response:

```json
{
  "apiResponseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "status": "internalServerError",
  "statusCode": 500,
  "message": "An internal server error occurred",
  "payload": null,
  "timestamp": "2023-06-15T14:30:45.123Z"
}
```

In development mode, the exception message will be included in the response.
