using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Operation filter to correctly handle file uploads in Swagger UI
    /// </summary>
    public class FormFileOperationFilter : IOperationFilter
    {
        /// <summary>
        /// Applies the filter to the specified operation using the given context.
        /// </summary>
        /// <param name="operation">The operation to apply the filter to.</param>
        /// <param name="context">The current operation filter context.</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            var formFileParameterNames = context
                .MethodInfo.GetParameters()
                .Where(p =>
                    p.ParameterType.GetProperties()
                        .Any(prop => prop.PropertyType == typeof(IFormFile))
                )
                .Select(p => p.Name.ToLower())
                .ToList();

            if (formFileParameterNames.Count == 0)
                return;

            var formFileProperties = context
                .SchemaRepository.Schemas.SelectMany(schema =>
                    schema
                        .Value.Properties.Where(prop => prop.Value.Format == "binary")
                        .Select(prop => prop.Key.ToLower())
                )
                .ToList();

            if (
                operation.RequestBody != null
                && operation.RequestBody.Content.TryGetValue(
                    "multipart/form-data",
                    out var mediaType
                )
            )
            {
                var schema = mediaType.Schema;

                if (schema.Properties == null)
                    schema.Properties = new Dictionary<string, OpenApiSchema>();

                foreach (var formFileProperty in formFileProperties)
                {
                    if (schema.Properties.ContainsKey(formFileProperty))
                    {
                        schema.Properties[formFileProperty] = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary",
                        };
                    }
                }

                foreach (var formFileParam in formFileParameterNames)
                {
                    if (schema.Properties.ContainsKey(formFileParam))
                    {
                        schema.Properties[formFileParam] = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary",
                        };
                    }
                }
            }
        }
    }
}
