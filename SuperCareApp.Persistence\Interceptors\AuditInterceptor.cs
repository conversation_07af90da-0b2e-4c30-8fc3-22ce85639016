using System.Text.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Interceptors;

/// <summary>
/// Interceptor that automatically creates audit logs for all entity changes
/// </summary>
public class AuditInterceptor : SaveChangesInterceptor
{
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditConfiguration _auditConfiguration;
    private readonly List<AuditLog> _auditLogs = new();

    public AuditInterceptor(
        ICurrentUserService currentUserService,
        IAuditConfiguration auditConfiguration
    )
    {
        _currentUserService = currentUserService;
        _auditConfiguration = auditConfiguration;
    }

    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result
    )
    {
        CreateAuditLogs(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default
    )
    {
        CreateAuditLogs(eventData.Context);
        return await base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    public override int SavedChanges(SaveChangesCompletedEventData eventData, int result)
    {
        SaveAuditLogs(eventData.Context);
        return base.SavedChanges(eventData, result);
    }

    public override async ValueTask<int> SavedChangesAsync(
        SaveChangesCompletedEventData eventData,
        int result,
        CancellationToken cancellationToken = default
    )
    {
        await SaveAuditLogsAsync(eventData.Context, cancellationToken);
        return await base.SavedChangesAsync(eventData, result, cancellationToken);
    }

    private void CreateAuditLogs(DbContext? context)
    {
        if (context == null)
            return;

        _auditLogs.Clear();
        var currentUserId = _currentUserService.UserId ?? Guid.Empty;

        foreach (var entry in context.ChangeTracker.Entries())
        {
            // Skip audit logs themselves to prevent infinite loops
            if (entry.Entity is AuditLog)
                continue;

            // Check if this entity type should be audited
            var entityType = entry.Entity.GetType().Name;
            if (!_auditConfiguration.ShouldAuditEntity(entityType))
                continue;

            // Only audit entities that have an Id property (primary key)
            if (!HasIdProperty(entry.Entity))
                continue;

            var auditLog = CreateAuditLogEntry(entry, currentUserId);
            if (auditLog != null)
            {
                _auditLogs.Add(auditLog);
            }
        }
    }

    private AuditLog? CreateAuditLogEntry(EntityEntry entry, Guid userId)
    {
        var entityId = GetEntityId(entry.Entity);
        if (entityId == Guid.Empty)
            return null;

        var entityType = entry.Entity.GetType().Name;
        var action = GetActionType(entry.State);

        if (string.IsNullOrEmpty(action))
            return null;

        var auditLog = new AuditLog
        {
            Id = Guid.NewGuid(),
            EntityType = entityType,
            EntityId = entityId,
            Action = action,
            UserId = userId,
            Timestamp = DateTime.UtcNow,
        };

        switch (entry.State)
        {
            case EntityState.Added:
                auditLog.NewValues = SerializeEntity(entry, EntityState.Added);
                break;

            case EntityState.Modified:
                auditLog.OldValues = SerializeEntity(entry, EntityState.Modified, true);
                auditLog.NewValues = SerializeEntity(entry, EntityState.Modified, false);
                break;

            case EntityState.Deleted:
                auditLog.OldValues = SerializeEntity(entry, EntityState.Deleted);
                break;
        }

        return auditLog;
    }

    private static string GetActionType(EntityState state) =>
        state switch
        {
            EntityState.Added => "INSERT",
            EntityState.Modified => "UPDATE",
            EntityState.Deleted => "DELETE",
            _ => string.Empty,
        };

    private static bool HasIdProperty(object entity)
    {
        var entityType = entity.GetType();
        return entityType.GetProperty("Id") != null;
    }

    private static Guid GetEntityId(object entity)
    {
        var idProperty = entity.GetType().GetProperty("Id");
        if (idProperty?.GetValue(entity) is Guid id)
        {
            return id;
        }
        return Guid.Empty;
    }

    private string SerializeEntity(
        EntityEntry entry,
        EntityState state,
        bool useOriginalValues = false
    )
    {
        var properties = new Dictionary<string, object?>();

        var entityTypeName = entry.Entity.GetType().Name;

        foreach (var property in entry.Properties)
        {
            // Skip properties based on audit configuration
            if (!_auditConfiguration.ShouldAuditProperty(entityTypeName, property.Metadata.Name))
                continue;

            // Skip navigation properties and complex types
            if (property.Metadata.IsForeignKey() && property.Metadata.Name.EndsWith("Id"))
                continue;

            object? value = null;

            try
            {
                value = state switch
                {
                    EntityState.Added => property.CurrentValue,
                    EntityState.Modified when useOriginalValues => property.OriginalValue,
                    EntityState.Modified when !useOriginalValues => property.CurrentValue,
                    EntityState.Deleted => property.OriginalValue,
                    _ => property.CurrentValue,
                };

                // Only include modified properties for updates
                if (state == EntityState.Modified && !useOriginalValues)
                {
                    if (!property.IsModified)
                        continue;
                }

                properties[property.Metadata.Name] = value;
            }
            catch
            {
                // Skip properties that can't be accessed
                continue;
            }
        }

        return JsonSerializer.Serialize(
            properties,
            new JsonSerializerOptions
            {
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            }
        );
    }

    private void SaveAuditLogs(DbContext? context)
    {
        if (context == null || !_auditLogs.Any())
            return;

        try
        {
            context.Set<AuditLog>().AddRange(_auditLogs);
            context.SaveChanges();
        }
        catch (Exception)
        {
            // Log the error but don't fail the main operation
            // In a real application, you might want to log this to a separate logging system
        }
        finally
        {
            _auditLogs.Clear();
        }
    }

    private async Task SaveAuditLogsAsync(DbContext? context, CancellationToken cancellationToken)
    {
        if (context == null || !_auditLogs.Any())
            return;

        try
        {
            context.Set<AuditLog>().AddRange(_auditLogs);
            await context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception)
        {
            // Log the error but don't fail the main operation
            // In a real application, you might want to log this to a separate logging system
        }
        finally
        {
            _auditLogs.Clear();
        }
    }
}
