# 📊 OpenTelemetry Observability Implementation Plan

## Overview

This document outlines the implementation plan for comprehensive observability using OpenTelemetry, Grafana, and Prometheus in the SuperCare application. This will provide distributed tracing, metrics collection, and monitoring capabilities.

## 🎯 Objectives

- **Distributed Tracing**: Track requests across microservices and external dependencies
- **Metrics Collection**: Gather application and infrastructure metrics
- **Performance Monitoring**: Monitor application performance and identify bottlenecks
- **Error Tracking**: Comprehensive error monitoring and alerting
- **Business Metrics**: Track key business indicators and user behavior
- **Infrastructure Monitoring**: Monitor system resources and health

## 🏗️ Architecture Overview

```
Application → OpenTelemetry SDK → OTLP Exporter → Collector → Storage & Visualization
                                                      ↓
                                              Prometheus (Metrics)
                                                      ↓
                                              Grafana (Dashboards)
                                                      ↓
                                              <PERSON><PERSON><PERSON> (Traces)
```

## 📋 Implementation Phases

### Phase 1: OpenTelemetry Foundation (Week 1-2)

#### 1.1 Package Installation
```xml
<!-- SuperCareApp.API.csproj -->
<PackageReference Include="OpenTelemetry" Version="1.6.0" />
<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.6.0" />
<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.5.1-beta.1" />
<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.5.1-beta.1" />
<PackageReference Include="OpenTelemetry.Instrumentation.SqlClient" Version="1.5.1-beta.1" />
<PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.0.0-beta.7" />
<PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.5.0-rc.1" />
<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.6.0" />
<PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
```

#### 1.2 Configuration Setup
```csharp
// Program.cs
public static void Main(string[] args)
{
    var builder = WebApplication.CreateBuilder(args);
    
    // Configure OpenTelemetry
    builder.Services.AddOpenTelemetry()
        .WithTracing(tracerProviderBuilder =>
        {
            tracerProviderBuilder
                .AddSource("SuperCareApp")
                .SetSampler(new TraceIdRatioBasedSampler(1.0))
                .AddAspNetCoreInstrumentation(options =>
                {
                    options.RecordException = true;
                    options.Filter = httpContext =>
                    {
                        // Filter out health check endpoints
                        return !httpContext.Request.Path.Value?.Contains("/health") ?? true;
                    };
                })
                .AddHttpClientInstrumentation()
                .AddSqlClientInstrumentation(options =>
                {
                    options.SetDbStatementForText = true;
                    options.RecordException = true;
                })
                .AddEntityFrameworkCoreInstrumentation()
                .AddJaegerExporter()
                .AddOtlpExporter();
        })
        .WithMetrics(meterProviderBuilder =>
        {
            meterProviderBuilder
                .AddMeter("SuperCareApp")
                .AddAspNetCoreInstrumentation()
                .AddHttpClientInstrumentation()
                .AddRuntimeInstrumentation()
                .AddPrometheusExporter();
        });

    var app = builder.Build();
    
    // Add Prometheus metrics endpoint
    app.MapPrometheusScrapingEndpoint();
    
    app.Run();
}
```

#### 1.3 Custom Instrumentation
```csharp
public class SuperCareInstrumentation
{
    public static readonly ActivitySource ActivitySource = new("SuperCareApp");
    public static readonly Meter Meter = new("SuperCareApp");
    
    // Custom counters
    public static readonly Counter<long> BookingCreatedCounter = 
        Meter.CreateCounter<long>("supercare_bookings_created_total", "Total number of bookings created");
    
    public static readonly Counter<long> PaymentProcessedCounter = 
        Meter.CreateCounter<long>("supercare_payments_processed_total", "Total number of payments processed");
    
    public static readonly Counter<long> DocumentUploadedCounter = 
        Meter.CreateCounter<long>("supercare_documents_uploaded_total", "Total number of documents uploaded");
    
    // Custom histograms
    public static readonly Histogram<double> BookingProcessingDuration = 
        Meter.CreateHistogram<double>("supercare_booking_processing_duration_seconds", "Booking processing duration");
    
    public static readonly Histogram<double> PaymentProcessingDuration = 
        Meter.CreateHistogram<double>("supercare_payment_processing_duration_seconds", "Payment processing duration");
    
    // Custom gauges
    public static readonly ObservableGauge<int> ActiveUsersGauge = 
        Meter.CreateObservableGauge<int>("supercare_active_users", "Number of active users");
    
    public static readonly ObservableGauge<int> PendingBookingsGauge = 
        Meter.CreateObservableGauge<int>("supercare_pending_bookings", "Number of pending bookings");
}
```

### Phase 2: Custom Metrics & Tracing (Week 3-4)

#### 2.1 Service-Level Instrumentation
```csharp
public class BookingService : IBookingService
{
    private readonly IBookingRepository _repository;
    private readonly ILogger<BookingService> _logger;

    public async Task<Result<BookingResponse>> CreateBookingAsync(CreateBookingRequest request)
    {
        using var activity = SuperCareInstrumentation.ActivitySource.StartActivity("BookingService.CreateBooking");
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Add tags to the activity
            activity?.SetTag("booking.category_id", request.CategoryId.ToString());
            activity?.SetTag("booking.provider_id", request.ProviderId.ToString());
            activity?.SetTag("booking.client_id", request.ClientId.ToString());
            
            // Business logic here
            var result = await ProcessBookingCreationAsync(request);
            
            if (result.IsSuccess)
            {
                // Record successful booking creation
                SuperCareInstrumentation.BookingCreatedCounter.Add(1, new TagList
                {
                    ["category"] = request.CategoryId.ToString(),
                    ["status"] = "success"
                });
                
                activity?.SetStatus(ActivityStatusCode.Ok);
                activity?.SetTag("booking.id", result.Value.Id.ToString());
            }
            else
            {
                activity?.SetStatus(ActivityStatusCode.Error, result.Error.Message);
                activity?.SetTag("error.type", result.Error.Type.ToString());
            }
            
            return result;
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.RecordException(ex);
            
            SuperCareInstrumentation.BookingCreatedCounter.Add(1, new TagList
            {
                ["category"] = request.CategoryId.ToString(),
                ["status"] = "error"
            });
            
            throw;
        }
        finally
        {
            stopwatch.Stop();
            SuperCareInstrumentation.BookingProcessingDuration.Record(
                stopwatch.Elapsed.TotalSeconds,
                new TagList { ["operation"] = "create" });
        }
    }
}
```

#### 2.2 Middleware for Request Tracking
```csharp
public class TelemetryMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TelemetryMiddleware> _logger;

    public TelemetryMiddleware(RequestDelegate next, ILogger<TelemetryMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        using var activity = SuperCareInstrumentation.ActivitySource.StartActivity("HTTP Request");
        
        // Add custom tags
        activity?.SetTag("http.user_id", context.User?.FindFirst("sub")?.Value);
        activity?.SetTag("http.user_agent", context.Request.Headers["User-Agent"].ToString());
        activity?.SetTag("http.client_ip", context.Connection.RemoteIpAddress?.ToString());
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _next(context);
            
            // Record response metrics
            activity?.SetTag("http.status_code", context.Response.StatusCode);
            
            if (context.Response.StatusCode >= 400)
            {
                activity?.SetStatus(ActivityStatusCode.Error, $"HTTP {context.Response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.RecordException(ex);
            throw;
        }
        finally
        {
            stopwatch.Stop();
            
            // Record request duration
            var duration = stopwatch.Elapsed.TotalSeconds;
            var tags = new TagList
            {
                ["method"] = context.Request.Method,
                ["endpoint"] = context.Request.Path,
                ["status_code"] = context.Response.StatusCode.ToString()
            };
            
            SuperCareInstrumentation.Meter
                .CreateHistogram<double>("http_request_duration_seconds")
                .Record(duration, tags);
        }
    }
}
```

### Phase 3: Prometheus & Grafana Setup (Week 5-6)

#### 3.1 Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "supercare_rules.yml"

scrape_configs:
  - job_name: 'supercare-api'
    static_configs:
      - targets: ['localhost:5000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  - job_name: 'supercare-database'
    static_configs:
      - targets: ['localhost:9187']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 3.2 Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "SuperCare Application Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status_code=~\"4..|5..\"}[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      },
      {
        "title": "Business Metrics",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(supercare_bookings_created_total[5m])",
            "legendFormat": "Bookings Created/sec"
          },
          {
            "expr": "rate(supercare_payments_processed_total[5m])",
            "legendFormat": "Payments Processed/sec"
          }
        ]
      }
    ]
  }
}
```

## 🔧 Advanced Monitoring Features

### 1. Custom Business Metrics
```csharp
public class BusinessMetricsService : IHostedService
{
    private readonly Timer _timer;
    private readonly IServiceProvider _serviceProvider;

    public BusinessMetricsService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _timer = new Timer(CollectMetrics, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
    }

    private async void CollectMetrics(object state)
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Collect active users
        var activeUsers = await dbContext.Users
            .CountAsync(u => u.LastLogin >= DateTime.UtcNow.AddMinutes(-30));
        
        SuperCareInstrumentation.ActiveUsersGauge.Record(activeUsers);
        
        // Collect pending bookings
        var pendingBookings = await dbContext.Bookings
            .CountAsync(b => b.Status.Status == BookingStatusType.Pending);
        
        SuperCareInstrumentation.PendingBookingsGauge.Record(pendingBookings);
        
        // Collect revenue metrics
        var todayRevenue = await dbContext.Payments
            .Where(p => p.CreatedAt.Date == DateTime.UtcNow.Date && p.Status == PaymentStatus.Completed)
            .SumAsync(p => p.Amount);
        
        SuperCareInstrumentation.Meter
            .CreateObservableGauge<decimal>("supercare_daily_revenue")
            .Record(todayRevenue);
    }

    public Task StartAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _timer?.Dispose();
        return Task.CompletedTask;
    }
}
```

### 2. Error Tracking and Alerting
```csharp
public class ErrorTrackingService
{
    private readonly ILogger<ErrorTrackingService> _logger;
    
    public void TrackError(Exception exception, string operation, Dictionary<string, object> context = null)
    {
        using var activity = SuperCareInstrumentation.ActivitySource.StartActivity("Error.Track");
        
        activity?.SetTag("error.type", exception.GetType().Name);
        activity?.SetTag("error.message", exception.Message);
        activity?.SetTag("operation", operation);
        activity?.RecordException(exception);
        
        // Add context tags
        if (context != null)
        {
            foreach (var kvp in context)
            {
                activity?.SetTag($"context.{kvp.Key}", kvp.Value?.ToString());
            }
        }
        
        // Record error metric
        SuperCareInstrumentation.Meter
            .CreateCounter<long>("supercare_errors_total")
            .Add(1, new TagList
            {
                ["error_type"] = exception.GetType().Name,
                ["operation"] = operation
            });
        
        _logger.LogError(exception, "Error in operation {Operation} with context {@Context}", 
            operation, context);
    }
}
```

## 🚨 Alerting Rules

### Prometheus Alerting Rules
```yaml
# supercare_rules.yml
groups:
  - name: supercare_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"
          
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"
          
      - alert: DatabaseConnectionFailure
        expr: up{job="supercare-database"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "Database is not responding"
          
      - alert: LowBookingRate
        expr: rate(supercare_bookings_created_total[1h]) < 0.01
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Low booking creation rate"
          description: "Booking creation rate is unusually low"
```

## 📊 Key Dashboards

### 1. Application Performance Dashboard
- **Request Rate**: Requests per second by endpoint
- **Response Time**: P50, P95, P99 response times
- **Error Rate**: Error percentage by endpoint
- **Throughput**: Overall application throughput

### 2. Business Metrics Dashboard
- **User Activity**: Active users, new registrations
- **Booking Metrics**: Bookings created, completion rates
- **Revenue Metrics**: Daily/monthly revenue, payment success rates
- **Provider Metrics**: Active providers, booking distribution

### 3. Infrastructure Dashboard
- **System Resources**: CPU, memory, disk usage
- **Database Performance**: Query performance, connection pool
- **External Dependencies**: API response times, availability
- **Network Metrics**: Bandwidth usage, connection counts

## 🔧 Implementation Best Practices

### 1. Sampling Strategy
```csharp
// Configure sampling based on environment
builder.Services.AddOpenTelemetry()
    .WithTracing(tracerProviderBuilder =>
    {
        var samplingRatio = builder.Environment.IsProduction() ? 0.1 : 1.0;
        tracerProviderBuilder.SetSampler(new TraceIdRatioBasedSampler(samplingRatio));
    });
```

### 2. Resource Attribution
```csharp
// Add resource attributes for better identification
builder.Services.AddOpenTelemetry()
    .ConfigureResource(resource =>
    {
        resource.AddService(
            serviceName: "SuperCareApp",
            serviceVersion: Assembly.GetExecutingAssembly().GetName().Version?.ToString(),
            serviceInstanceId: Environment.MachineName);
        
        resource.AddAttributes(new[]
        {
            new KeyValuePair<string, object>("deployment.environment", builder.Environment.EnvironmentName),
            new KeyValuePair<string, object>("service.namespace", "SuperCare"),
        });
    });
```

### 3. Performance Optimization
```csharp
// Batch export for better performance
builder.Services.AddOpenTelemetry()
    .WithTracing(tracerProviderBuilder =>
    {
        tracerProviderBuilder.AddOtlpExporter(options =>
        {
            options.ExportProcessorType = ExportProcessorType.Batch;
            options.BatchExportProcessorOptions = new BatchExportProcessorOptions<Activity>
            {
                MaxExportBatchSize = 512,
                ScheduledDelayMilliseconds = 5000,
                ExporterTimeoutMilliseconds = 30000,
                MaxQueueSize = 2048
            };
        });
    });
```

## 🚀 Deployment Configuration

### Docker Compose for Local Development
```yaml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true

volumes:
  grafana-storage:
```

## 📈 Future Enhancements

1. **Distributed Tracing**: Implement cross-service tracing
2. **Log Correlation**: Correlate logs with traces and metrics
3. **Anomaly Detection**: AI-powered anomaly detection
4. **Capacity Planning**: Predictive scaling based on metrics
5. **User Experience Monitoring**: Real user monitoring (RUM)
6. **Security Monitoring**: Security-focused metrics and alerts

## 📚 Resources

- [OpenTelemetry .NET Documentation](https://opentelemetry.io/docs/instrumentation/net/)
- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Jaeger Documentation](https://www.jaegertracing.io/docs/)
