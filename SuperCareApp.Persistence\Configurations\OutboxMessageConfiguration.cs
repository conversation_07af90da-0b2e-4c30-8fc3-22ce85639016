using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations;

public class OutboxMessageConfiguration : IEntityTypeConfiguration<OutboxMessage>
{
    public void Configure(EntityTypeBuilder<OutboxMessage> builder)
    {
        builder.ToTable("_outbox_messages");

        // Primary key
        builder.HasKey(x => x.Id);

        // Properties configuration
        builder
            .Property(x => x.Id)
            .IsRequired()
            .HasColumnName("id")
            .HasColumnType("uuid")
            .HasDefaultValueSql("gen_random_uuid()");

        builder
            .Property(x => x.OccurredOn)
            .IsRequired()
            .HasColumnName("occurred_on")
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("NOW()");

        builder
            .Property(x => x.Type)
            .IsRequired()
            .HasColumnName("type")
            .HasColumnType("varchar(255)")
            .HasMaxLength(255);

        builder
            .Property(x => x.Content)
            .IsRequired()
            .HasColumnName("content")
            .HasColumnType("jsonb"); // Using JSONB for better performance and querying

        builder
            .Property(x => x.Processed)
            .IsRequired()
            .HasColumnName("processed")
            .HasColumnType("boolean")
            .HasDefaultValue(false);

        builder
            .Property(x => x.ProcessedAt)
            .HasColumnName("processed_at")
            .HasColumnType("timestamp with time zone");

        // Indexes for enterprise-level performance

        // Primary composite index for unprocessed messages (most common query)
        builder
            .HasIndex(x => new { x.Processed, x.OccurredOn })
            .HasDatabaseName("ix_outbox_messages_processed_occurred_on")
            .HasFilter("NOT processed") // Partial index for better performance
            .IncludeProperties(x => new { x.Type, x.Content }); // Covering index

        // Index for processing status queries
        builder.HasIndex(x => x.Processed).HasDatabaseName("ix_outbox_messages_processed");

        // Index for chronological queries and cleanup operations
        builder.HasIndex(x => x.OccurredOn).HasDatabaseName("ix_outbox_messages_occurred_on");

        // Index for processed messages cleanup (with partial index)
        builder
            .HasIndex(x => x.ProcessedAt)
            .HasDatabaseName("ix_outbox_messages_processed_at")
            .HasFilter("processed = true");

        // Index for message type filtering
        builder.HasIndex(x => x.Type).HasDatabaseName("ix_outbox_messages_type");

        // Composite index for type-based processing
        builder
            .HasIndex(x => new
            {
                x.Type,
                x.Processed,
                x.OccurredOn,
            })
            .HasDatabaseName("ix_outbox_messages_type_processed_occurred_on")
            .HasFilter("NOT processed");

        // Index for audit and monitoring queries
        builder
            .HasIndex(x => new { x.Processed, x.ProcessedAt })
            .HasDatabaseName("ix_outbox_messages_processed_processed_at")
            .HasFilter("processed = true");

        // Constraints
        builder.ToTable(t =>
        {
            t.HasCheckConstraint(
                "ck_outbox_messages_processed_at",
                "(processed = false AND processed_at IS NULL) OR (processed = true AND processed_at IS NOT NULL)"
            );
        });

        // Add comment for documentation
        builder.ToTable(t => t.HasComment("Outbox table to store outgoing messages"));
    }
}
