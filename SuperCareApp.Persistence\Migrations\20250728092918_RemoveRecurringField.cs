﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SuperCareApp.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RemoveRecurringField : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "provides_recurring_booking",
                table: "care_provider_profiles"
            );
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "provides_recurring_booking",
                table: "care_provider_profiles",
                type: "boolean",
                nullable: false,
                defaultValue: false
            );
        }
    }
}
