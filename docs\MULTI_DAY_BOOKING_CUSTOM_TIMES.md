# Multi-Day Booking Custom Times & Tracking Validation Implementation Plan

**Version**: 1.0  
**Date**: December 2024  
**Status**: Planning Phase

## Table of Contents

- [Overview](#overview)
- [Current System Analysis](#current-system-analysis)
- [New Requirements](#new-requirements)
- [Implementation Strategy](#implementation-strategy)
- [Domain Layer Changes](#domain-layer-changes)
- [Application Layer Changes](#application-layer-changes)
- [Persistence Layer Changes](#persistence-layer-changes)
- [API Layer Changes](#api-layer-changes)
- [Testing Strategy](#testing-strategy)
- [Migration Plan](#migration-plan)
- [Risk Assessment](#risk-assessment)
- [Success Criteria](#success-criteria)

## Overview

This document outlines the implementation plan for enhancing the SuperCare booking system to support:

1. **Custom start and end times for multi-day bookings**
2. **Enhanced tracking data validation with structured GPS coordinates**

The current system only allows custom time windows for single-day bookings, while multi-day bookings use default provider working hours. Additionally, tracking data validation is minimal, accepting unstructured object data.

## Current System Analysis

### Current Booking Flow

```mermaid
graph TD
    A[Create Booking Request] --> B{Single Day?}
    B -->|Yes| C[Validate StartTime/EndTime Required]
    B -->|No| D[Use Default Working Hours]
    C --> E[Check Single Day Availability]
    D --> F[Calculate Total Days × Working Hours]
    E --> G[Create Booking]
    F --> G

```

### Enhanced Booking Flow (New Implementation)

```mermaid
graph TD
    A[Create Booking Request] --> B[Validate StartTime/EndTime Required]
    B --> C{Single Day?}
    C -->|Yes| D[Check Single Day Availability]
    C -->|No| E[Check Multi-Day Availability]

    D --> F[Validate Time Slot Available]
    E --> G[Loop Through Each Day]
    G --> H[Validate Time Slot for Each Day]
    H --> I{All Days Available?}
    I -->|No| J[Return Unavailable Dates Error]
    I -->|Yes| K[Calculate Custom Hours × Days]

    F --> L[Calculate Single Day Hours]
    K --> M[Create Booking with Custom Times]
    L --> M
    J --> N[Booking Failed]
    M --> O[Create Tracking Session Template]
    O --> P[Booking Created Successfully]

```

### Enhanced Tracking Flow (New Implementation)

```mermaid
graph TD
    A[Start Tracking Request] --> B[Validate GPS Coordinates]
    B --> C{Valid Coordinates?}
    C -->|No| D[Return Validation Error]
    C -->|Yes| E[Create GeoPoint Object]
    E --> F[Initialize Tracking Session]
    F --> G[Store Structured GPS Data]

    H[Update Tracking Request] --> I[Validate New GPS Coordinates]
    I --> J{Valid Coordinates?}
    J -->|No| K[Return Validation Error]
    J -->|Yes| L[Create New GeoPoint]
    L --> M[Append to Existing GPS Array]
    M --> N[Update Tracking Session]

    O[End Tracking Request] --> P[Validate Final GPS Coordinates]
    P --> Q{Valid Coordinates?}
    Q -->|No| R[Log Warning, End Without GPS]
    Q -->|Yes| S[Create Final GeoPoint]
    S --> T[Calculate Total Hours]
    T --> U[Complete Tracking Session]

```

### Current Limitations

1. **Multi-day bookings**: No custom time validation per day
2. **Availability checking**: Only validates single-day slots
3. **Tracking data**: Accepts unstructured `object` without validation
4. **Time calculations**: Inconsistent between single and multi-day bookings

## New Requirements

### Requirement 1: Multi-Day Custom Times

- Allow clients to specify custom `StartTime` and `EndTime` for multi-day bookings
- Apply the same time window consistently across all booking days
- Validate provider availability for specified times on each day

### Requirement 2: Enhanced Tracking Validation

- Implement structured GPS coordinate validation
- Ensure latitude/longitude values are within valid ranges
- Provide clear error messages for invalid tracking data

## Implementation Strategy

### Approach: Incremental Enhancement

- Maintain backward compatibility with existing single-day bookings
- Extend current validation logic rather than replacing it
- Use existing database schema where possible
- Implement comprehensive validation at domain level

### Alternative Approaches Considered

#### Option A: Per-Day Booking Windows (Rejected)

```sql
CREATE TABLE BookingWindows (
    Id GUID PRIMARY KEY,
    BookingId GUID,
    Date DATE,
    StartTime TIME,
    EndTime TIME
);
```

**Rejected**: Adds complexity without clear business value

#### Option B: JSON Column for Windows (Rejected)

```json
{
  "windows": [
    { "date": "2024-01-15", "start": "09:00", "end": "17:00" },
    { "date": "2024-01-16", "start": "09:00", "end": "17:00" }
  ]
}
```

**Rejected**: Limited SQL query capabilities

#### Option C: Single Window Applied Daily (Selected)

- Reuse existing `StartTime`/`EndTime` columns
- Apply same window to all days in booking range
- Simple and meets current business requirements

### Proposed Multi-Day Booking Flow

```mermaid
graph TD
    A[Create Booking Request (Incl. Start/End Dates and Times)] --> B{Multi-Day Booking?};
    B -- Yes --> C[Validate Custom Start/End Times];
    C --> D{Check Availability for Each Day};
    D --> E{All Days Available?};
    E -- Yes --> F[Calculate Total Hours (Duration x Days)];
    E -- No --> G[Return Availability Error];
    B -- No --> H[Validate Custom Start/End Times];
    H --> I{Check Availability for Single Day};
    I --> J{Day Available?};
    J -- Yes --> K[Calculate Duration];
    J -- No --> L[Return Availability Error];
    F --> M[Create Booking Entity];
    K --> M;
    G --> Z[Booking Creation Failed];
    L --> Z;
    M --> Y[Booking Created Successfully];
```

## Domain Layer Changes

### 1. Booking Entity Enhancements

**File**: `SuperCareApp.Domain/Entities/Booking.cs`

**Changes**:

- No database schema changes required
- Add business rule validation for multi-day time windows
- Update computed properties for duration calculations

**New Validation Method**:

```csharp
public void ValidateMultiDayTimeWindow()
{
    if (StartDate != EndDate && (StartTime == default || EndTime == default))
        throw new DomainException("StartTime and EndTime are required for multi-day bookings");

    if (EndTime <= StartTime)
        throw new DomainException("EndTime must be after StartTime");
}
```

### 2. TrackingSession Entity Enhancements

**File**: `SuperCareApp.Domain/Entities/TrackingSession.cs`

**Changes**:

- Replace generic `object` tracking data with structured GPS data
- Add validation for GPS coordinates

### 3. New Value Objects

**File**: `SuperCareApp.Domain/ValueObjects/GeoPoint.cs`

```csharp
public class GeoPoint : ValueObject
{
    public double Latitude { get; private set; }
    public double Longitude { get; private set; }
    public DateTime Timestamp { get; private set; }
    public double? Accuracy { get; private set; }
    public string? Address { get; private set; }

    public GeoPoint(double latitude, double longitude, DateTime? timestamp = null,
                   double? accuracy = null, string? address = null)
    {
        ValidateCoordinates(latitude, longitude);

        Latitude = latitude;
        Longitude = longitude;
        Timestamp = timestamp ?? DateTime.UtcNow;
        Accuracy = accuracy;
        Address = address;
    }

    private static void ValidateCoordinates(double latitude, double longitude)
    {
        if (latitude < -90 || latitude > 90)
            throw new ArgumentException($"Latitude must be between -90 and 90. Got: {latitude}");

        if (longitude < -180 || longitude > 180)
            throw new ArgumentException($"Longitude must be between -180 and 180. Got: {longitude}");
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Latitude;
        yield return Longitude;
        yield return Timestamp;
    }
}
```

### 4. Domain Validators

**File**: `SuperCareApp.Domain/Validation/DomainValidators.cs`

```csharp
public static class BookingValidators
{
    public static void ValidateMultiDayBooking(Booking booking)
    {
        if (booking.StartDate != booking.EndDate)
        {
            if (booking.StartTime == default)
                throw new DomainException("StartTime is required for multi-day bookings");

            if (booking.EndTime == default)
                throw new DomainException("EndTime is required for multi-day bookings");

            if (booking.EndTime <= booking.StartTime)
                throw new DomainException("EndTime must be after StartTime");
        }
    }
}

public static class TrackingValidators
{
    public static void ValidateTrackingData(List<GeoPoint> trackingData)
    {
        if (trackingData?.Any() == true)
        {
            foreach (var point in trackingData)
            {
                // Validation is handled in GeoPoint constructor
                // Additional business rules can be added here
            }
        }
    }
}
```

## Application Layer Changes

### 1. Booking Request Models

**File**: `SuperCareApp.Application/Common/Models/Bookings/CreateBookingRequest.cs`

**Enhanced Validator**:

```csharp
public class CreateBookingRequestValidator : AbstractValidator<CreateBookingRequest>
{
    public CreateBookingRequestValidator()
    {
        // Existing validations...

        // Enhanced multi-day validation
        When(x => x.StartDate != x.EndDate, () =>
        {
            RuleFor(x => x.StartTime)
                .NotNull()
                .WithMessage("Start time is required for multi-day bookings");

            RuleFor(x => x.EndTime)
                .NotNull()
                .WithMessage("End time is required for multi-day bookings")
                .GreaterThan(x => x.StartTime)
                .WithMessage("End time must be after start time");

            RuleFor(x => x)
                .Must(HaveValidTimeWindow)
                .WithMessage("Time window must be within provider's availability");
        });
    }

    private bool HaveValidTimeWindow(CreateBookingRequest request)
    {
        if (request.StartTime == null || request.EndTime == null)
            return false;

        var duration = request.EndTime.Value - request.StartTime.Value;
        return duration.TotalHours >= 1 && duration.TotalHours <= 12;
    }
}
```

### 2. Tracking Session Models

**File**: `SuperCareApp.Application/Common/Models/TrackingSession/TrackingSessionRequest.cs`

**Enhanced Models**:

```csharp
public class StartTrackingSessionRequest
{
    [Required]
    public Guid BookingId { get; set; }

    public string? Notes { get; set; }

    public GeoPointRequest? InitialLocation { get; set; }
}

public class UpdateTrackingSessionRequest
{
    public TrackingSessionStatus? Status { get; set; }

    public string? Notes { get; set; }

    public GeoPointRequest? LocationData { get; set; }
}

public class GeoPointRequest
{
    [Required]
    [Range(-90, 90)]
    public double Latitude { get; set; }

    [Required]
    [Range(-180, 180)]
    public double Longitude { get; set; }

    public DateTime? Timestamp { get; set; }

    [Range(0, double.MaxValue)]
    public double? Accuracy { get; set; }

    public string? Address { get; set; }
}
```

**Validators**:

```csharp
public class GeoPointRequestValidator : AbstractValidator<GeoPointRequest>
{
    public GeoPointRequestValidator()
    {
        RuleFor(x => x.Latitude)
            .InclusiveBetween(-90, 90)
            .WithMessage("Latitude must be between -90 and 90 degrees");

        RuleFor(x => x.Longitude)
            .InclusiveBetween(-180, 180)
            .WithMessage("Longitude must be between -180 and 180 degrees");

        RuleFor(x => x.Accuracy)
            .GreaterThanOrEqualTo(0)
            .When(x => x.Accuracy.HasValue)
            .WithMessage("Accuracy must be non-negative");

        RuleFor(x => x.Timestamp)
            .LessThanOrEqualTo(DateTime.UtcNow.AddMinutes(5))
            .When(x => x.Timestamp.HasValue)
            .WithMessage("Timestamp cannot be in the future");
    }
}
```

### 3. Service Interfaces

**File**: `SuperCareApp.Application/Common/Interfaces/Bookings/IBookingService.cs`

**Enhanced Interface**:

```csharp
public interface IBookingService
{
    // Existing methods...

    Task<Result<Guid>> CreateMultiDayBookingAsync(
        Guid userId,
        Guid providerId,
        Guid categoryId,
        DateTime startDate,
        DateTime endDate,
        TimeOnly startTime,
        TimeOnly endTime,
        bool isRecurring,
        string? specialInstructions);

    Task<Result<List<DateOnly>>> ValidateMultiDayAvailabilityAsync(
        Guid providerId,
        DateTime startDate,
        DateTime endDate,
        TimeOnly startTime,
        TimeOnly endTime);
}
```

## Persistence Layer Changes

### 1. BookingService Enhancements

**File**: `SuperCareApp.Persistence/Services/Bookings/BookingService.cs`

**Key Changes**:

#### Enhanced CreateBookingAsync Method

```csharp
public async Task<Result<Guid>> CreateBookingAsync(
    Guid userId,
    Guid providerId,
    Guid categoryId,
    DateTime startDate,
    DateTime endDate,
    bool isRecurring,
    TimeOnly? startTime,
    TimeOnly? endTime,
    string? specialInstructions,
    int workingHours = 8)
{
    try
    {
        // Validate entities exist
        var validationResult = await ValidateBookingEntitiesAsync(userId, providerId, categoryId);
        if (!validationResult.IsSuccess)
            return Result.Failure<Guid>(validationResult.Error);

        var (client, providerProfile, category) = validationResult.Value;

        // Enhanced validation for both single and multi-day bookings
        if (startTime == null || endTime == null)
            return Result.Failure<Guid>(
                Error.Validation("Start time and end time are required for all bookings"));

        var timeValidation = ValidateTimeRange((TimeOnly)startTime, (TimeOnly)endTime);
        if (!timeValidation.IsSuccess)
            return Result.Failure<Guid>(timeValidation.Error);

        var duration = timeValidation.Value;

        // Multi-day availability validation
        if (startDate != endDate)
        {
            var multiDayValidation = await ValidateMultiDayAvailabilityAsync(
                providerId, startDate, endDate, (TimeOnly)startTime, (TimeOnly)endTime);

            if (!multiDayValidation.IsSuccess)
                return Result.Failure<Guid>(multiDayValidation.Error);

            // Calculate working hours for multi-day booking
            var totalDays = (endDate - startDate).Days + 1;
            workingHours = (int)(duration * totalDays);
        }
        else
        {
            // Single-day availability validation
            var availabilityResult = await ValidateAvailabilityAsync(
                providerId, startDate, (TimeOnly)startTime, (TimeOnly)endTime);

            if (!availabilityResult.IsSuccess)
                return Result.Failure<Guid>(availabilityResult.Error);

            workingHours = (int)Math.Ceiling(duration);
        }

        // Calculate amounts and create booking
        var (providerAmount, serviceFee, totalAmount) = CalculateBookingAmounts(
            providerProfile, category, workingHours,
            days: startDate == endDate ? 1 : (endDate - startDate).Days + 1);

        var booking = CreateBookingEntity(
            client.Id, providerId, categoryId, startDate, endDate,
            startTime, endTime, specialInstructions, totalAmount,
            serviceFee, providerAmount, userId, isRecurring, workingHours);

        var bookingStatus = CreateBookingStatusEntity(booking.Id, userId, "Booking created");

        await SaveBookingAsync(booking, bookingStatus);

        _logger.LogInformation(
            "Booking {BookingId} created successfully for {Days} days with custom times {StartTime}-{EndTime}",
            booking.Id, (endDate - startDate).Days + 1, startTime, endTime);

        return Result.Success(booking.Id);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error creating booking for user {UserId}", userId);
        return Result.Failure<Guid>(Error.Internal("Error creating booking"));
    }
}
```

#### New Multi-Day Validation Method

```csharp
private async Task<Result> ValidateMultiDayAvailabilityAsync(
    Guid providerId,
    DateTime startDate,
    DateTime endDate,
    TimeOnly startTime,
    TimeOnly endTime)
{
    try
    {
        var unavailableDates = new List<DateOnly>();

        for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
        {
            var dateOnly = DateOnly.FromDateTime(date);
            var requestedSlot = new Interval<TimeOnly>(startTime, endTime);
            requestedSlot.Validate();

            var availableSlots = await _scheduleService.GetAvailableSlotsForDateAsync(
                providerId, dateOnly);

            if (!availableSlots.Any(slot => IntervalUtils.IsContainedWithin(requestedSlot, slot)))
            {
                unavailableDates.Add(dateOnly);
            }
        }

        if (unavailableDates.Any())
        {
            var datesString = string.Join(", ", unavailableDates.Select(d => d.ToString("yyyy-MM-dd")));
            return Result.Failure(Error.Conflict(
                $"Requested time slot is not available on the following dates: {datesString}"));
        }

        return Result.Success();
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error validating multi-day availability");
        return Result.Failure(Error.Internal("Error validating availability"));
    }
}
```

### 2. TrackingSessionService Enhancements

**File**: `SuperCareApp.Persistence/Services/TrackingSessions/TrackingSessionService.cs`

**Key Changes**:

#### Enhanced StartTrackingSessionAsync

```csharp
public async Task<Result<TrackingSessionResponse>> StartTrackingSessionAsync(
    Guid bookingId,
    Guid providerId,
    string? notes = null,
    GeoPointRequest? initialLocationData = null)
{
    try
    {
        // Existing validation...

        var trackingData = new List<GeoPoint>();
        if (initialLocationData != null)
        {
            try
            {
                var geoPoint = new GeoPoint(
                    initialLocationData.Latitude,
                    initialLocationData.Longitude,
                    initialLocationData.Timestamp,
                    initialLocationData.Accuracy,
                    initialLocationData.Address);

                trackingData.Add(geoPoint);
            }
            catch (ArgumentException ex)
            {
                return Result.Failure<TrackingSessionResponse>(
                    Error.Validation($"Invalid GPS coordinates: {ex.Message}"));
            }
        }

        var trackingSession = new TrackingSession
        {
            BookingId = bookingId,
            ProviderId = providerId,
            StartTime = DateTime.UtcNow,
            Status = TrackingSessionStatus.Active,
            Notes = notes,
            TrackingData = JsonConvert.SerializeObject(trackingData)
        };

        var result = await _trackingSessionRepository.AddAsync(trackingSession);
        if (result.IsFailure)
            return Result.Failure<TrackingSessionResponse>(result.Error);

        return Result.Success(MapToResponse(result.Value));
    }
    catch (Exception ex)
    {
        return Result.Failure<TrackingSessionResponse>(
            Error.Internal($"Failed to start tracking session: {ex.Message}"));
    }
}
```

#### Enhanced UpdateTrackingSessionAsync

```csharp
public async Task<Result<TrackingSessionResponse>> UpdateTrackingSessionAsync(
    Guid sessionId,
    Guid providerId,
    UpdateTrackingSessionRequest request)
{
    try
    {
        var sessionResult = await _trackingSessionRepository.GetByIdAsync(sessionId);
        if (sessionResult.IsFailure)
            return Result.Failure<TrackingSessionResponse>(sessionResult.Error);

        var session = sessionResult.Value;

        // Existing validation...

        // Handle location data update
        if (request.LocationData != null)
        {
            try
            {
                var existingData = !string.IsNullOrEmpty(session.TrackingData)
                    ? JsonConvert.DeserializeObject<List<GeoPoint>>(session.TrackingData) ?? new List<GeoPoint>()
                    : new List<GeoPoint>();

                var newGeoPoint = new GeoPoint(
                    request.LocationData.Latitude,
                    request.LocationData.Longitude,
                    request.LocationData.Timestamp,
                    request.LocationData.Accuracy,
                    request.LocationData.Address);

                existingData.Add(newGeoPoint);
                session.TrackingData = JsonConvert.SerializeObject(existingData);
            }
            catch (ArgumentException ex)
            {
                return Result.Failure<TrackingSessionResponse>(
                    Error.Validation($"Invalid GPS coordinates: {ex.Message}"));
            }
        }

        // Rest of the method...
    }
    catch (Exception ex)
    {
        return Result.Failure<TrackingSessionResponse>(
            Error.Internal($"Failed to update tracking session: {ex.Message}"));
    }
}
```

## API Layer Changes

### 1. BookingsController Updates

**File**: `super-care-app/Controllers/BookingsController.cs`

**Enhanced Endpoints**:

```csharp
[HttpPost]
[Authorize(Roles = "Client")]
public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequest request)
{
    var userId = _currentUserService.UserId ?? Guid.Empty;

    var result = await _bookingService.CreateBookingAsync(
        userId,
        request.ProviderId,
        request.CategoryId,
        request.StartDate,
        request.EndDate,
        request.IsRecurring,
        request.StartTime,
        request.EndTime,
        request.SpecialInstructions);

    return result.IsFailure
        ? ErrorResponseFromError<Guid>(result.Error)
        : SuccessResponse(result.Value, "Booking created successfully");
}

[HttpPost("validate-multi-day-availability")]
[Authorize]
public async Task<IActionResult> ValidateMultiDayAvailability([FromBody] ValidateMultiDayAvailabilityRequest request)
{
    var result = await _bookingService.ValidateMultiDayAvailabilityAsync(
        request.ProviderId,
        request.StartDate,
        request.EndDate,
        request.StartTime,
        request.EndTime);

    return result.IsFailure
        ? ErrorResponseFromError<List<DateOnly>>(result.Error)
        : SuccessResponse(result.Value, "Availability validated successfully");
}
```

### 2. TrackingSessionsController Updates

**File**: `super-care-app/Controllers/TrackingSessionsController.cs`

**Enhanced Endpoints**:

```csharp
[HttpPost("bookings/{bookingId:guid}/start")]
[Authorize(Roles = "CareProvider")]
public async Task<IActionResult> StartTrackingSession(
    [FromRoute] Guid bookingId,
    [FromBody] StartTrackingSessionRequest request)
{
    var providerId = _currentUserService.UserId ?? Guid.Empty;

    var result = await _trackingSessionService.StartTrackingSessionAsync(
        bookingId,
        providerId,
        request.Notes,
        request.InitialLocation);

    return result.IsFailure
        ? ErrorResponseFromError<TrackingSessionResponse>(result.Error)
        : SuccessResponse(result.Value, "Tracking session started successfully");
}

[HttpPut("{sessionId:guid}")]
[Authorize(Roles = "CareProvider")]
public async Task<IActionResult> UpdateTrackingSession(
    [FromRoute] Guid sessionId,
    [FromBody] UpdateTrackingSessionRequest request)
{
    var providerId = _currentUserService.UserId ?? Guid.Empty;

    var result = await _trackingSessionService.UpdateTrackingSessionAsync(
        sessionId,
        providerId,
        request);

    return result.IsFailure
        ? ErrorResponseFromError<TrackingSessionResponse>(result.Error)
        : SuccessResponse(result.Value, "Tracking session updated successfully");
}
```

## Testing Strategy

### 1. Unit Tests

#### Domain Layer Tests

```csharp
[TestFixture]
public class GeoPointTests
{
    [Test]
    public void Constructor_ValidCoordinates_ShouldCreateGeoPoint()
    {
        // Arrange
        var latitude = 51.5074;
        var longitude = -0.1278;

        // Act
        var geoPoint = new GeoPoint(latitude, longitude);

        // Assert
        Assert.That(geoPoint.Latitude, Is.EqualTo(latitude));
        Assert.That(geoPoint.Longitude, Is.EqualTo(longitude));
    }

    [TestCase(-91, 0)]
    [TestCase(91, 0)]
    [TestCase(0, -181)]
    [TestCase(0, 181)]
    public void Constructor_InvalidCoordinates_ShouldThrowArgumentException(double lat, double lng)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => new GeoPoint(lat, lng));
    }
}

[TestFixture]
public class BookingValidatorTests
{
    [Test]
    public void ValidateMultiDayBooking_ValidTimeWindow_ShouldNotThrow()
    {
        // Arrange
        var booking = new Booking
        {
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(2),
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0)
        };

        // Act & Assert
        Assert.DoesNotThrow(() => BookingValidators.ValidateMultiDayBooking(booking));
    }

    [Test]
    public void ValidateMultiDayBooking_MissingStartTime_ShouldThrowDomainException()
    {
        // Arrange
        var booking = new Booking
        {
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(2),
            StartTime = default,
            EndTime = new TimeOnly(17, 0)
        };

        // Act & Assert
        Assert.Throws<DomainException>(() => BookingValidators.ValidateMultiDayBooking(booking));
    }
}
```

#### Application Layer Tests

```csharp
[TestFixture]
public class CreateBookingRequestValidatorTests
{
    private CreateBookingRequestValidator _validator;

    [SetUp]
    public void SetUp()
    {
        _validator = new CreateBookingRequestValidator();
    }

    [Test]
    public void Validate_MultiDayBookingWithValidTimes_ShouldBeValid()
    {
        // Arrange
        var request = new CreateBookingRequest
        {
            ProviderId = Guid.NewGuid(),
            CategoryId = Guid.NewGuid(),
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(2),
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
            IsRecurring = false
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.That(result.IsValid, Is.True);
    }

    [Test]
    public void Validate_MultiDayBookingWithoutStartTime_ShouldBeInvalid()
    {
        // Arrange
        var request = new CreateBookingRequest
        {
            ProviderId = Guid.NewGuid(),
            CategoryId = Guid.NewGuid(),
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(2),
            StartTime = null,
            EndTime = new TimeOnly(17, 0),
            IsRecurring = false
        };

        // Act
        var result = _validator.Validate(request);

        // Assert
        Assert.That(result.IsValid, Is.False);
        Assert.That(result.Errors.Any(e => e.ErrorMessage.Contains("Start time is required")), Is.True);
    }
}
```

#### Persistence Layer Tests

```csharp
[TestFixture]
public class BookingServiceTests
{
    private BookingService _bookingService;
    private Mock<IBookingManagementService> _mockScheduleService;
    private Mock<IAvailabilityService> _mockAvailabilityService;

    [SetUp]
    public void SetUp()
    {
        // Setup mocks and service
    }

    [Test]
    public async Task CreateBookingAsync_MultiDayWithValidAvailability_ShouldSucceed()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();
        var startDate = DateTime.Today;
        var endDate = DateTime.Today.AddDays(2);
        var startTime = new TimeOnly(9, 0);
        var endTime = new TimeOnly(17, 0);

        // Setup mocks to return available slots for all days
        _mockScheduleService.Setup(s => s.GetAvailableSlotsForDateAsync(It.IsAny<Guid>(), It.IsAny<DateOnly>()))
            .ReturnsAsync(new List<Interval<TimeOnly>>
            {
                new Interval<TimeOnly>(new TimeOnly(8, 0), new TimeOnly(18, 0))
            });

        // Act
        var result = await _bookingService.CreateBookingAsync(
            userId, providerId, categoryId, startDate, endDate, false, startTime, endTime, null);

        // Assert
        Assert.That(result.IsSuccess, Is.True);
    }

    [Test]
    public async Task CreateBookingAsync_MultiDayWithUnavailableDay_ShouldFail()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var categoryId = Guid.NewGuid();
        var startDate = DateTime.Today;
        var endDate = DateTime.Today.AddDays(2);
        var startTime = new TimeOnly(9, 0);
        var endTime = new TimeOnly(17, 0);

        // Setup mock to return no available slots for one day
        _mockScheduleService.SetupSequence(s => s.GetAvailableSlotsForDateAsync(It.IsAny<Guid>(), It.IsAny<DateOnly>()))
            .ReturnsAsync(new List<Interval<TimeOnly>>
            {
                new Interval<TimeOnly>(new TimeOnly(8, 0), new TimeOnly(18, 0))
            })
            .ReturnsAsync(new List<Interval<TimeOnly>>()) // No slots available
            .ReturnsAsync(new List<Interval<TimeOnly>>
            {
                new Interval<TimeOnly>(new TimeOnly(8, 0), new TimeOnly(18, 0))
            });

        // Act
        var result = await _bookingService.CreateBookingAsync(
            userId, providerId, categoryId, startDate, endDate, false, startTime, endTime, null);

        // Assert
        Assert.That(result.IsFailure, Is.True);
        Assert.That(result.Error.Message, Does.Contain("not available"));
    }
}
```

### 2. Integration Tests

```csharp
[TestFixture]
public class BookingIntegrationTests : IntegrationTestBase
{
    [Test]
    public async Task CreateMultiDayBooking_EndToEnd_ShouldWork()
    {
        // Arrange
        var client = await CreateTestClient();
        var provider = await CreateTestProvider();
        var category = await CreateTestCategory();

        await SetupProviderAvailability(provider.Id, new TimeOnly(8, 0), new TimeOnly(18, 0));

        var request = new CreateBookingRequest
        {
            ProviderId = provider.Id,
            CategoryId = category.Id,
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
            IsRecurring = false,
            SpecialInstructions = "Multi-day care needed"
        };

        // Act
        var response = await PostAsync("/api/bookings", request, client.Token);

        // Assert
        Assert.That(response.IsSuccessStatusCode, Is.True);

        var booking = await GetBookingFromDatabase(response.BookingId);
        Assert.That(booking.StartTime, Is.EqualTo(new TimeOnly(9, 0)));
        Assert.That(booking.EndTime, Is.EqualTo(new TimeOnly(17, 0)));
        Assert.That(booking.WorkingHours, Is.EqualTo(24)); // 8 hours × 3 days
    }

    [Test]
    public async Task TrackingSession_WithValidGPS_ShouldWork()
    {
        // Arrange
        var booking = await CreateTestBooking();
        var provider = await GetTestProvider();

        var startRequest = new StartTrackingSessionRequest
        {
            BookingId = booking.Id,
            Notes = "Starting care session",
            InitialLocation = new GeoPointRequest
            {
                Latitude = 51.5074,
                Longitude = -0.1278,
                Accuracy = 5.0,
                Address = "London, UK"
            }
        };

        // Act
        var response = await PostAsync($"/api/tracking-sessions/bookings/{booking.Id}/start",
            startRequest, provider.Token);

        // Assert
        Assert.That(response.IsSuccessStatusCode, Is.True);

        var session = await GetTrackingSessionFromDatabase(response.SessionId);
        var trackingData = JsonConvert.DeserializeObject<List<GeoPoint>>(session.TrackingData);

        Assert.That(trackingData, Has.Count.EqualTo(1));
        Assert.That(trackingData[0].Latitude, Is.EqualTo(51.5074));
        Assert.That(trackingData[0].Longitude, Is.EqualTo(-0.1278));
    }
}
```

### 3. Performance Tests

```csharp
[TestFixture]
public class BookingPerformanceTests
{
    [Test]
    public async Task CreateMultiDayBooking_30Days_ShouldCompleteWithin5Seconds()
    {
        // Arrange
        var stopwatch = Stopwatch.StartNew();
        var request = CreateMultiDayBookingRequest(30); // 30-day booking

        // Act
        var result = await _bookingService.CreateBookingAsync(/* parameters */);
        stopwatch.Stop();

        // Assert
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(5000));
    }

    [Test]
    public async Task ValidateMultiDayAvailability_100Providers_ShouldCompleteWithin10Seconds()
    {
        // Test bulk availability validation performance
    }
}
```

## Migration Plan with Rollback Capability

### Phase 1: Foundation Layer (Week 1) - **ROLLBACK SAFE**

**Scope**: Domain layer enhancements with backward compatibility

**Tasks**:

1. **Day 1-2**: Create `GeoPoint` value object with validation
   - ✅ **Rollback**: Remove new files, no existing code affected
2. **Day 3-4**: Add domain validators (`BookingValidators`, `TrackingValidators`)
   - ✅ **Rollback**: Remove validator classes, existing validation unchanged
3. **Day 5**: Create comprehensive unit tests for new domain components
   - ✅ **Rollback**: Remove test files

**Rollback Strategy**:

- Delete new domain files
- No database changes
- No API changes
- **Risk Level**: LOW

**Deployment**: Feature branch only, no production deployment

---

### Phase 2: Application Layer Enhancement (Week 2) - **ROLLBACK SAFE**

**Scope**: Request/Response models and service interfaces

**Tasks**:

1. **Day 1-2**: Create enhanced request models (`GeoPointRequest`, updated validators)
   - ✅ **Rollback**: Remove new model files, existing models unchanged
2. **Day 3-4**: Update service interfaces with new methods (additive only)
   - ✅ **Rollback**: Remove new interface methods, existing methods unchanged
3. **Day 5**: Add application layer unit tests
   - ✅ **Rollback**: Remove test files

**Rollback Strategy**:

- Remove new application model files
- Revert interface additions (backward compatible)
- No breaking changes to existing contracts
- **Risk Level**: LOW

**Deployment**: Feature branch only, comprehensive testing

---

### Phase 3A: Persistence Layer - Tracking Enhancement (Week 3) - **FEATURE FLAG PROTECTED**

**Scope**: TrackingSessionService GPS validation (isolated feature)

**Tasks**:

1. **Day 1-2**: Implement GPS validation in `TrackingSessionService`
   - 🔄 **Rollback**: Feature flag `ENABLE_GPS_VALIDATION` = false
2. **Day 3**: Add backward compatibility for existing tracking data
   - 🔄 **Rollback**: Graceful degradation to old format
3. **Day 4-5**: Integration tests for tracking validation
   - ✅ **Rollback**: Remove test files

**Feature Flag Implementation**:

```csharp
public async Task<Result<TrackingSessionResponse>> StartTrackingSessionAsync(...)
{
    if (_featureFlags.IsEnabled("ENABLE_GPS_VALIDATION"))
    {
        // New GPS validation logic
        return await StartTrackingSessionWithGPSValidation(...);
    }
    else
    {
        // Existing logic (fallback)
        return await StartTrackingSessionLegacy(...);
    }
}
```

**Rollback Strategy**:

- Set feature flag to `false`
- System reverts to existing tracking behavior
- No data loss or corruption
- **Risk Level**: MEDIUM

**Deployment**: Staging with feature flag OFF, production ready

---

### Phase 3B: Persistence Layer - Booking Enhancement (Week 4) - **FEATURE FLAG PROTECTED**

**Scope**: BookingService multi-day validation (core feature)

**Tasks**:

1. **Day 1-3**: Implement multi-day availability validation
   - 🔄 **Rollback**: Feature flag `ENABLE_MULTIDAY_CUSTOM_TIMES` = false
2. **Day 4**: Add performance optimizations and caching
   - 🔄 **Rollback**: Feature flag disables optimization
3. **Day 5**: Comprehensive integration testing
   - ✅ **Rollback**: Remove test files

**Feature Flag Implementation**:

```csharp
public async Task<Result<Guid>> CreateBookingAsync(...)
{
    if (_featureFlags.IsEnabled("ENABLE_MULTIDAY_CUSTOM_TIMES") && startDate != endDate)
    {
        // New multi-day validation logic
        return await CreateMultiDayBookingWithCustomTimes(...);
    }
    else
    {
        // Existing logic (fallback)
        return await CreateBookingLegacy(...);
    }
}
```

**Rollback Strategy**:

- Set feature flag to `false`
- Multi-day bookings revert to default working hours
- Single-day bookings unaffected
- **Risk Level**: MEDIUM-HIGH

**Deployment**: Staging with feature flag OFF, production ready

---

### Phase 4: API Layer Enhancement (Week 5) - **BACKWARD COMPATIBLE**

**Scope**: Controller updates with version compatibility

**Tasks**:

1. **Day 1-2**: Update controller endpoints with new validation
   - 🔄 **Rollback**: API versioning maintains old endpoints
2. **Day 3**: Add new validation endpoints (additive)
   - ✅ **Rollback**: Remove new endpoints, existing ones unchanged
3. **Day 4**: Update Swagger documentation
   - ✅ **Rollback**: Revert documentation changes
4. **Day 5**: End-to-end API testing
   - ✅ **Rollback**: Remove test files

**API Versioning Strategy**:

```csharp
[ApiVersion("1.0")] // Existing behavior
[ApiVersion("2.0")] // New enhanced behavior
[HttpPost("bookings")]
public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequest request)
{
    if (HttpContext.GetRequestedApiVersion().MajorVersion >= 2)
    {
        // Enhanced validation with custom times
        return await CreateBookingV2(request);
    }
    else
    {
        // Legacy behavior
        return await CreateBookingV1(request);
    }
}
```

**Rollback Strategy**:

- Default API version remains 1.0
- Clients must explicitly request v2.0 for new features
- Zero impact on existing API consumers
- **Risk Level**: LOW

**Deployment**: Production with v1.0 as default

---

### Phase 5: Production Rollout (Week 6) - **GRADUAL ROLLOUT**

**Scope**: Feature flag activation with monitoring

**Tasks**:

1. **Day 1**: Deploy to production with all feature flags OFF
   - 🔄 **Rollback**: Already in rollback state
2. **Day 2**: Enable GPS validation for 10% of tracking sessions
   - 🔄 **Rollback**: Set flag to 0% traffic
3. **Day 3**: Enable multi-day custom times for 25% of bookings
   - 🔄 **Rollback**: Set flag to 0% traffic
4. **Day 4**: Monitor metrics, increase to 50% if stable
   - 🔄 **Rollback**: Reduce percentage or disable
5. **Day 5**: Full rollout (100%) if all metrics green
   - 🔄 **Rollback**: Immediate flag disable

**Monitoring Dashboard**:

- API response times
- Validation failure rates
- Database query performance
- Error rates by feature flag
- User experience metrics

**Rollback Triggers**:

- Response time > 5 seconds
- Error rate > 1%
- Database CPU > 80%
- User complaints > threshold

**Rollback Strategy**:

- **Immediate**: Feature flag to 0% (< 30 seconds)
- **Quick**: API version default to v1.0 (< 5 minutes)
- **Full**: Code rollback to previous release (< 30 minutes)
- **Risk Level**: LOW (with proper monitoring)

---

### Emergency Rollback Procedures

#### Level 1: Feature Flag Rollback (30 seconds)

```bash
# Disable specific features immediately
curl -X POST /admin/feature-flags \
  -d '{"ENABLE_GPS_VALIDATION": false, "ENABLE_MULTIDAY_CUSTOM_TIMES": false}'
```

#### Level 2: API Version Rollback (5 minutes)

```csharp
// Update default API version in Startup.cs
services.AddApiVersioning(options => {
    options.DefaultApiVersion = new ApiVersion(1, 0); // Force v1.0
});
```

#### Level 3: Code Rollback (30 minutes)

```bash
# Rollback to previous release
kubectl rollout undo deployment/supercare-api
# Or
docker-compose up -d --scale api=3 supercare-api:previous-tag
```

#### Level 4: Database Rollback (60 minutes)

```sql
-- No schema changes required, but if needed:
-- Restore from backup or run reverse migration scripts
```

### Rollback Testing Strategy

**Pre-deployment Testing**:

1. Test feature flag disable/enable cycles
2. Verify API version switching
3. Load test with flags in different states
4. Validate backward compatibility

**Production Rollback Drills**:

1. Monthly feature flag rollback exercises
2. Quarterly full rollback simulations
3. Performance impact assessments
4. Documentation updates

### Success Metrics by Phase

| Phase        | Success Criteria                                       | Rollback Criteria    |
| ------------ | ------------------------------------------------------ | -------------------- |
| **Phase 1**  | All unit tests pass, no breaking changes               | N/A (no deployment)  |
| **Phase 2**  | Integration tests pass, interfaces backward compatible | N/A (no deployment)  |
| **Phase 3A** | GPS validation works, existing tracking unaffected     | Error rate > 0.5%    |
| **Phase 3B** | Multi-day validation works, single-day unaffected      | Response time > 3s   |
| **Phase 4**  | API v2.0 works, v1.0 unchanged                         | API error rate > 1%  |
| **Phase 5**  | Full rollout stable, all metrics green                 | Any rollback trigger |

### Database Migration Strategy

**No schema changes required** - the implementation reuses existing columns:

- `Booking.StartTime` and `Booking.EndTime` (already exist)
- `TrackingSession.TrackingData` (already exists as JSON string)

**Optional optimizations**:

```sql
-- Add index for multi-day booking queries
CREATE INDEX IX_Bookings_ProviderId_DateRange
ON Bookings(ProviderId, StartDate, EndDate)
WHERE IsDeleted = 0;

-- Add index for tracking session GPS queries (if needed)
CREATE INDEX IX_TrackingSessions_BookingId_StartTime
ON TrackingSessions(BookingId, StartTime)
WHERE IsDeleted = 0;
```

## Risk Assessment

### High Risk

1. **Performance Impact**: Multi-day availability validation requires checking multiple dates

   - **Mitigation**: Implement efficient batch queries and caching
   - **Monitoring**: Track API response times and database query performance

2. **Data Migration**: Existing tracking sessions with unstructured data
   - **Mitigation**: Implement backward compatibility in deserialization
   - **Monitoring**: Log deserialization errors and handle gracefully

### Medium Risk

1. **Validation Complexity**: Multiple validation layers may cause confusion

   - **Mitigation**: Clear error messages and comprehensive documentation
   - **Monitoring**: Track validation failure rates and common error patterns

2. **API Breaking Changes**: Enhanced validation may break existing clients
   - **Mitigation**: Maintain backward compatibility where possible
   - **Monitoring**: Monitor API error rates after deployment

### Low Risk

1. **GPS Coordinate Edge Cases**: Handling coordinates near poles or date line
   - **Mitigation**: Use standard GPS validation ranges
   - **Monitoring**: Log coordinate validation failures

## Success Criteria

### Functional Requirements

- ✅ Multi-day bookings accept custom start/end times
- ✅ System validates provider availability for each day in booking range
- ✅ Tracking sessions only accept valid GPS coordinates
- ✅ Clear error messages for validation failures
- ✅ Backward compatibility with existing single-day bookings

### Performance Requirements

- ✅ Multi-day booking creation completes within 5 seconds (up to 30 days)
- ✅ Availability validation scales linearly with number of days
- ✅ GPS validation adds less than 100ms to tracking operations
- ✅ No degradation in existing single-day booking performance

### Quality Requirements

- ✅ 95%+ test coverage for new functionality
- ✅ Zero critical bugs in production
- ✅ API documentation updated and accurate
- ✅ Monitoring and alerting in place

### Business Requirements

- ✅ Clients can book multi-day care with specific hours
- ✅ Providers see accurate availability across multiple days
- ✅ Tracking data is reliable and structured
- ✅ System prevents double-booking across date ranges

---

**Next Steps:**

1. Review and approve this implementation plan
2. Create feature branch: `feature/multi-day-custom-times`
3. Begin implementation starting with Domain layer
4. Set up monitoring and alerting for new functionality
5. Schedule user acceptance testing sessions

**Estimated Timeline:** 4 weeks  
**Team Size:** 2-3 developers  
**Dependencies:** None (uses existing infrastructure)
