﻿using SuperCareApp.Application.Common.Interfaces.Identity;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Identity.Queries;

public record ValidateOtpQuery(string Email, string Otp) : IRequest<Result<string>>;

public class ValidateOtpQueryHandler : IRequestHandler<ValidateOtpQuery, Result<string>>
{
    private readonly IAuthService _authService;

    public ValidateOtpQueryHandler(IAuthService authService)
    {
        _authService = authService;
    }

    public async Task<Result<string>> Handle(
        ValidateOtpQuery request,
        CancellationToken cancellationToken
    )
    {
        var resetTokenResult = await _authService.VerifyOtpForgetPasswordAsync(
            request.Email,
            request.Otp
        );

        if (resetTokenResult.IsFailure)
        {
            return Result.Failure<string>(resetTokenResult.Error);
        }
        return Result.Success(resetTokenResult.Value);
    }
}
