using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.InMemory;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;
using SuperCareApp.Domain.Identity;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;
using SuperCareApp.Persistence.Services.Bookings.Commands;

namespace SuperCareApp.Persistence.Test.Bookings;

public class DeleteBookingTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly BookingService _bookingService;
    private readonly DeleteBookingCommandHandler _commandHandler;
    private readonly Guid _userId;
    private readonly Guid _providerId;
    private readonly Guid _categoryId;
    private readonly Guid _clientId;

    public DeleteBookingTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        _context = new ApplicationDbContext(options);

        var mockScheduleService = new Mock<IBookingManagementService>();
        var mockAvailabilityService = new Mock<IAvailabilityService>();

        _bookingService = new BookingService(
            _context,
            mockScheduleService.Object,
            NullLogger<BookingService>.Instance
        );

        _commandHandler = new DeleteBookingCommandHandler(_bookingService);

        _userId = Guid.NewGuid();
        _providerId = Guid.NewGuid();
        _categoryId = Guid.NewGuid();
        _clientId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test users
        var user = new ApplicationUser
        {
            Id = _userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        var client = new ApplicationUser
        {
            Id = _clientId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            EmailConfirmed = true,
        };

        // Create provider profile
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create category
        var category = new CareCategory
        {
            Id = _categoryId,
            Name = "Test Category",
            Description = "Test category description",
            IsActive = true,
            PlatformFee = 2.50m,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Create provider-category relationship with hourly rate
        var providerCategory = new CareProviderCategory
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            CategoryId = _categoryId,
            HourlyRate = 25.00m,
            ExperienceYears = 5,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Users.AddRange(user, client);
        _context.CareProviderProfiles.Add(providerProfile);
        _context.CareCategories.Add(category);
        _context.CareProviderCategories.Add(providerCategory);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Helper Methods

    private async Task<Booking> CreateTestBooking(
        BookingStatusType status = BookingStatusType.Requested,
        DateTime? bookingDate = null,
        bool withMultipleWindows = false
    )
    {
        var bookingId = Guid.NewGuid();
        var date = bookingDate ?? DateTime.Today.AddDays(1);

        var booking = new Booking
        {
            Id = bookingId,
            ClientId = _clientId,
            ProviderId = _providerId,
            CategoryId = _categoryId,
            WorkingHours = 8,
            TotalAmount = 200,
            PlatformFee = 20,
            ProviderAmount = 180,
            SpecialInstructions = "Test booking",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _clientId,
        };

        var bookingStatus = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = status,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _clientId,
        };

        var bookingWindows = new List<BookingWindow>();

        if (withMultipleWindows)
        {
            // Create 3 booking windows for multi-day booking
            for (int i = 0; i < 3; i++)
            {
                bookingWindows.Add(
                    new BookingWindow
                    {
                        Id = Guid.NewGuid(),
                        BookingId = bookingId,
                        Date = DateOnly.FromDateTime(date.AddDays(i)),
                        StartTime = new TimeOnly(9, 0),
                        EndTime = new TimeOnly(17, 0),
                        DurationMinutes = 480,
                        DailyRate = 200,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = _clientId,
                    }
                );
            }
        }
        else
        {
            bookingWindows.Add(
                new BookingWindow
                {
                    Id = Guid.NewGuid(),
                    BookingId = bookingId,
                    Date = DateOnly.FromDateTime(date),
                    StartTime = new TimeOnly(9, 0),
                    EndTime = new TimeOnly(17, 0),
                    DurationMinutes = 480,
                    DailyRate = 200,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = _clientId,
                }
            );
        }

        booking.Status = bookingStatus;

        _context.Bookings.Add(booking);
        _context.BookingStatuses.Add(bookingStatus);
        _context.BookingWindows.AddRange(bookingWindows);
        await _context.SaveChangesAsync();

        return booking;
    }

    private async Task<Review> CreateTestReview(Guid bookingId)
    {
        var review = new Review
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            ReviewerId = _clientId,
            RevieweeId = _userId, // Provider being reviewed
            Rating = 5,
            Comment = "Great service!",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _clientId,
        };

        _context.Reviews.Add(review);
        await _context.SaveChangesAsync();
        return review;
    }

    #endregion

    #region Successful Deletion Tests

    [Fact]
    public async Task DeleteBookingAsync_WithValidRequestedBooking_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
        Assert.NotNull(deletedBooking.DeletedAt);
        Assert.Equal(_clientId, deletedBooking.DeletedBy);
    }

    [Fact]
    public async Task DeleteBookingCommand_WithValidData_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var command = new DeleteBookingCommand(_clientId, booking.Id);

        // Act
        var result = await _commandHandler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
    }

    [Fact]
    public async Task DeleteBookingAsync_WithMultipleWindows_ShouldDeleteAllWindows()
    {
        // Arrange
        var booking = await CreateTestBooking(
            BookingStatusType.Requested,
            withMultipleWindows: true
        );

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedBooking = await _context
            .Bookings.Include(b => b.BookingWindows)
            .FirstOrDefaultAsync(b => b.Id == booking.Id);

        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);

        // Verify all booking windows are marked as deleted
        Assert.All(
            deletedBooking.BookingWindows,
            w =>
            {
                Assert.True(w.IsDeleted);
                Assert.NotNull(w.DeletedAt);
                Assert.Equal(_clientId, w.DeletedBy);
            }
        );
    }

    [Fact]
    public async Task DeleteBookingAsync_WithRelatedReviews_ShouldPreserveReviews()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Completed);
        var review = await CreateTestReview(booking.Id);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess);

        // Verify booking is deleted
        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);

        // Verify review is preserved (soft delete doesn't cascade to reviews)
        var preservedReview = await _context.Reviews.FirstOrDefaultAsync(r => r.Id == review.Id);
        Assert.NotNull(preservedReview);
        Assert.False(preservedReview.IsDeleted);
    }

    #endregion

    #region Authorization Tests

    [Fact]
    public async Task DeleteBookingAsync_WithUnauthorizedUser_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var unauthorizedUserId = Guid.NewGuid();

        // Act
        var result = await _bookingService.DeleteBookingAsync(unauthorizedUserId, booking.Id);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not authorized", result.Error.Message);

        // Verify booking is not deleted
        var unchangedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(unchangedBooking);
        Assert.False(unchangedBooking.IsDeleted);
    }

    [Fact]
    public async Task DeleteBookingAsync_WithProviderAsDeleter_ShouldFail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);

        // Act - Provider trying to delete client's booking
        var result = await _bookingService.DeleteBookingAsync(_userId, booking.Id);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("not authorized", result.Error.Message);

        // Verify booking is not deleted
        var unchangedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(unchangedBooking);
        Assert.False(unchangedBooking.IsDeleted);
    }

    #endregion

    #region Validation Tests

    [Fact]
    public async Task DeleteBookingAsync_WithNonExistentBooking_ShouldFail()
    {
        // Arrange
        var nonExistentBookingId = Guid.NewGuid();

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, nonExistentBookingId);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Booking not found", result.Error.Message);
    }

    [Fact]
    public async Task DeleteBookingAsync_WithCompletedBooking_ShouldAllowDeletion()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Completed);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess); // Completed bookings can be deleted for record keeping

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
    }

    [Fact]
    public async Task DeleteBookingAsync_WithCancelledBooking_ShouldAllowDeletion()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Cancelled);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess); // Cancelled bookings can be deleted

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task DeleteBookingAsync_WithPastBooking_ShouldSucceed()
    {
        // Arrange
        var pastDate = DateTime.Today.AddDays(-5);
        var booking = await CreateTestBooking(BookingStatusType.Requested, pastDate);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess); // Past bookings can be deleted

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
    }

    [Fact]
    public async Task DeleteBookingAsync_WithFutureBooking_ShouldSucceed()
    {
        // Arrange
        var futureDate = DateTime.Today.AddDays(30);
        var booking = await CreateTestBooking(BookingStatusType.Requested, futureDate);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
    }

    [Fact]
    public async Task DeleteBookingAsync_WithTodayBooking_ShouldSucceed()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested, DateTime.Today);

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
    }

    #endregion

    #region Concurrent Deletion Tests

    [Fact]
    public async Task DeleteBookingAsync_WithConcurrentDeletions_ShouldHandleGracefully()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var tasks = new List<Task<Result>>();

        for (int i = 0; i < 3; i++)
        {
            var task = _bookingService.DeleteBookingAsync(_clientId, booking.Id);
            tasks.Add(task);
        }

        // Act
        var results = await Task.WhenAll(tasks);

        // Assert
        // All should succeed (idempotent operation)
        Assert.All(results, r => Assert.True(r.IsSuccess));

        // Verify booking is deleted only once
        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);
        Assert.NotNull(deletedBooking.DeletedAt);
    }

    #endregion

    #region Data Integrity Tests

    [Fact]
    public async Task DeleteBookingAsync_ShouldPreserveAuditTrail()
    {
        // Arrange
        var booking = await CreateTestBooking(BookingStatusType.Requested);
        var originalCreatedAt = booking.CreatedAt;
        var originalCreatedBy = booking.CreatedBy;

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking.Id);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking.Id);
        Assert.NotNull(deletedBooking);

        // Verify audit trail is preserved
        Assert.Equal(originalCreatedAt, deletedBooking.CreatedAt);
        Assert.Equal(originalCreatedBy, deletedBooking.CreatedBy);
        Assert.NotNull(deletedBooking.DeletedAt);
        Assert.Equal(_clientId, deletedBooking.DeletedBy);
        Assert.True(deletedBooking.IsDeleted);
    }

    [Fact]
    public async Task DeleteBookingAsync_ShouldNotAffectOtherBookings()
    {
        // Arrange
        var booking1 = await CreateTestBooking(BookingStatusType.Requested);
        var booking2 = await CreateTestBooking(
            BookingStatusType.Requested,
            DateTime.Today.AddDays(2)
        );

        // Act
        var result = await _bookingService.DeleteBookingAsync(_clientId, booking1.Id);

        // Assert
        Assert.True(result.IsSuccess);

        // Verify only the target booking is deleted
        var deletedBooking = await _context.Bookings.FirstOrDefaultAsync(b => b.Id == booking1.Id);
        var unaffectedBooking = await _context.Bookings.FirstOrDefaultAsync(b =>
            b.Id == booking2.Id
        );

        Assert.NotNull(deletedBooking);
        Assert.True(deletedBooking.IsDeleted);

        Assert.NotNull(unaffectedBooking);
        Assert.False(unaffectedBooking.IsDeleted);
    }

    #endregion
}
