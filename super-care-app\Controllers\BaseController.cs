﻿using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Common.Results;

namespace super_care_app.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Consumes("application/json")]
[Produces("application/json")]
[Route("api/v{version:apiVersion}/[controller]")]
[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponseModel<object>))]
[ProducesResponseType(
    StatusCodes.Status500InternalServerError,
    Type = typeof(ApiResponseModel<object>)
)]
public abstract class BaseController : ControllerBase
{
    /// <summary>
    /// Creates a successful response with the specified payload
    /// </summary>
    protected IActionResult SuccessResponse<T>(
        T payload,
        string message = "Operation completed successfully"
    )
    {
        var response = new ApiResponseModel<T>(ApiResponseStatusEnum.Success, message, payload);
        return Ok(response);
    }

    /// <summary>
    /// Creates a response from a Result object
    /// </summary>
    protected IActionResult FromResult<T>(
        Result<T> result,
        string successMessage = "Operation completed successfully"
    )
    {
        if (result.IsSuccess)
        {
            return SuccessResponse(result.Value, successMessage);
        }

        return ErrorResponseFromError<T>(result.Error);
    }

    /// <summary>
    /// Creates a paginated successful response
    /// </summary>
    protected IActionResult PaginatedResponse<T>(
        T payload,
        int currentPage,
        int totalPages,
        int totalCount,
        int pageSize,
        string message = "Data retrieved successfully"
    )
    {
        var response = new PaginatedResponseModel<T>(
            ApiResponseStatusEnum.Success,
            message,
            payload,
            currentPage,
            totalPages,
            totalCount,
            pageSize
        );
        return Ok(response);
    }

    /// <summary>
    /// Creates a paginated successful response using a PaginationMetadata object
    /// </summary>
    protected IActionResult PaginatedResponse<T>(
        T payload,
        PaginationMetadata meta,
        string message = "Data retrieved successfully"
    )
    {
        var response = new PaginatedResponseModel<T>(
            ApiResponseStatusEnum.Success,
            message,
            payload,
            meta
        );
        return Ok(response);
    }

    /// <summary>
    /// Creates an error response from an Error object
    /// </summary>
    protected IActionResult ErrorResponseFromError<T>(Error error, T? payload = default)
    {
        var status = error.Code switch
        {
            "NotFound" => ApiResponseStatusEnum.NotFound,
            "Validation" => ApiResponseStatusEnum.BadRequest,
            "BadRequest" => ApiResponseStatusEnum.BadRequest,
            "Conflict" => ApiResponseStatusEnum.BadRequest,
            "Unauthorized" => ApiResponseStatusEnum.Unauthorized,
            "Forbidden" => ApiResponseStatusEnum.Forbidden,
            "Internal" => ApiResponseStatusEnum.InternalServerError,
            "ExternalService" => ApiResponseStatusEnum.InternalServerError,
            _ => ApiResponseStatusEnum.Error,
        };

        if (error.ValidationErrors != null)
        {
            return ErrorResponse(error.Message, status, error.ValidationErrors);
        }

        return ErrorResponse(error.Message, status, payload);
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    protected IActionResult ErrorResponse<T>(
        string message,
        ApiResponseStatusEnum status = ApiResponseStatusEnum.Error,
        T? payload = default
    )
    {
        var response = new ApiResponseModel<T>(status, message, payload);

        return status switch
        {
            ApiResponseStatusEnum.BadRequest => BadRequest(response),
            ApiResponseStatusEnum.NotFound => NotFound(response),
            ApiResponseStatusEnum.Unauthorized => Unauthorized(response),
            ApiResponseStatusEnum.Forbidden => StatusCode(StatusCodes.Status403Forbidden, response),
            ApiResponseStatusEnum.InternalServerError => StatusCode(
                StatusCodes.Status500InternalServerError,
                response
            ),
            _ => StatusCode(StatusCodes.Status500InternalServerError, response),
        };
    }

    /// <summary>
    /// Creates a bad request response
    /// </summary>
    protected IActionResult BadRequestResponse<T>(
        string message = "Bad request",
        T? payload = default
    )
    {
        return ErrorResponse(message, ApiResponseStatusEnum.BadRequest, payload);
    }

    /// <summary>
    /// Creates a not found response
    /// </summary>
    protected IActionResult NotFoundResponse<T>(
        string message = "Resource not found",
        T? payload = default
    )
    {
        return ErrorResponse(message, ApiResponseStatusEnum.NotFound, payload);
    }

    /// <summary>
    /// Creates an unauthorized response
    /// </summary>
    protected IActionResult UnauthorizedResponse<T>(
        string message = "Unauthorized",
        T? payload = default
    )
    {
        return ErrorResponse(message, ApiResponseStatusEnum.Unauthorized, payload);
    }

    /// <summary>
    /// Creates a forbidden response
    /// </summary>
    protected IActionResult ForbiddenResponse<T>(string message = "Forbidden", T? payload = default)
    {
        return ErrorResponse(message, ApiResponseStatusEnum.Forbidden, payload);
    }

    /// <summary>
    /// Creates an internal server error response
    /// </summary>
    protected IActionResult InternalServerErrorResponse<T>(
        string message = "Internal server error",
        T? payload = default
    )
    {
        return ErrorResponse(message, ApiResponseStatusEnum.InternalServerError, payload);
    }
}
