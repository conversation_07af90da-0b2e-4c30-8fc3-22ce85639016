﻿namespace SuperCareApp.Domain.Entities.ValueObjects;

/// <summary>
/// Flat, immutable snapshot returned by the read-model to the application layer.
/// All time values are expressed in the provider’s configured time zone (UTC assumed here).
/// </summary>
public sealed record ProviderAvailabilitySnapshot
{
    /// <summary>
    /// True if the provider has an approved leave that covers the requested date.
    /// </summary>
    public required bool IsOnLeave { get; init; }

    /// <summary>
    /// True if an Availability aggregate exists for the requested weekday
    /// and the provider has marked themselves as available.
    /// </summary>
    public required bool IsAvailableOnWeekday { get; init; }

    /// <summary>
    /// All contiguous ranges the provider has declared as available for the weekday.
    /// Empty when <see cref="IsAvailableOnWeekday"/> is false.
    /// </summary>
    public required IReadOnlyList<TimeRange> AvailableRanges { get; init; }

    /// <summary>
    /// All bookings already committed for the requested date.
    /// Excludes cancelled bookings.
    /// </summary>
    public required IReadOnlyList<TimeRange> BookedSlots { get; init; }

    /// <summary>
    /// Buffer in minutes that must surround each booked slot.
    /// Sourced from <see cref="CareProviderProfile.BufferDuration"/>.
    /// </summary>
    public required int BufferMinutes { get; init; }

    /// <summary>
    /// Convenience factory for the default “unavailable” snapshot.
    /// </summary>
    public static ProviderAvailabilitySnapshot Unavailable { get; } =
        new()
        {
            IsOnLeave = false,
            IsAvailableOnWeekday = false,
            AvailableRanges = new List<TimeRange>(),
            BookedSlots = new List<TimeRange>(),
            BufferMinutes = 0,
        };
}
