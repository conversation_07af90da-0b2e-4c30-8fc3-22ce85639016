using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Operation filter to add email/password authentication to Swagger UI
    /// </summary>
    public class SwaggerEmailPasswordAuthFilter : IOperationFilter
    {
        /// <summary>
        /// Applies the filter to the specified operation using the given context.
        /// </summary>
        /// <param name="operation">The operation to apply the filter to.</param>
        /// <param name="context">The current operation filter context.</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // No additional logic needed here as we're using the global security requirement
            // This filter is just a placeholder to register our custom JS
        }
    }
}
