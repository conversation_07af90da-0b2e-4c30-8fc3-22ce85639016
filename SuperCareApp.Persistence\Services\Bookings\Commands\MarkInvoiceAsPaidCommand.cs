﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

public record MarkInvoiceAsPaidCommand(Guid InvoiceId) : ICommand<Result>;

public class MarkInvoiceAsPaidCommandHandler : ICommandHandler<MarkInvoiceAsPaidCommand, Result>
{
    private readonly ApplicationDbContext _context;

    public MarkInvoiceAsPaidCommandHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result> Handle(
        MarkInvoiceAsPaidCommand request,
        CancellationToken cancellationToken
    )
    {
        var invoice = await _context.Invoices.FirstOrDefaultAsync(
            i => i.Id == request.InvoiceId,
            cancellationToken
        );

        if (invoice == null)
            return Result.Failure(Error.NotFound("Invoice not found."));

        invoice.Status = InvoiceStatus.Paid;
        _context.Invoices.Update(invoice);
        await _context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
