using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to bulk update care categories
    /// </summary>
    public record BulkUpdateCareCategoriesCommand(
        BulkUpdateCareCategoriesRequest Request,
        Guid UserId
    ) : ICommand<Result<IEnumerable<CareCategoryResponse>>>;

    /// <summary>
    /// Handler for the BulkUpdateCareCategoriesCommand
    /// </summary>
    public sealed class BulkUpdateCareCategoriesCommandHandler
        : ICommandHandler<
            BulkUpdateCareCategoriesCommand,
            Result<IEnumerable<CareCategoryResponse>>
        >
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<BulkUpdateCareCategoriesCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public BulkUpdateCareCategoriesCommandHandler(
            ICareCategoryService categoryService,
            ILogger<BulkUpdateCareCategoriesCommandHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result<IEnumerable<CareCategoryResponse>>> Handle(
            BulkUpdateCareCategoriesCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                _logger.LogInformation(
                    "Starting bulk update of {Count} care categories by user {UserId}",
                    request.Request.Categories.Count,
                    request.UserId
                );

                return await _categoryService.BulkUpdateCategoriesAsync(
                    request.Request,
                    request.UserId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk update of care categories");
                return Result.Failure<IEnumerable<CareCategoryResponse>>(
                    Error.Internal(ex.Message)
                );
            }
        }
    }
}
