﻿using Microsoft.AspNetCore.Http;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Storage;

/// <summary>
/// Interface for file storage operations
/// </summary>
public interface IFileStorageService
{
    /// <summary>
    /// Uploads a file to storage
    /// </summary>
    /// <param name="file">The file to upload</param>
    /// <param name="containerName">The container/folder name to store the file in</param>
    /// <param name="fileName">Optional custom file name (if null, a unique name will be generated)</param>
    /// <returns>Result containing the file metadata</returns>
    Task<Result<FileMetadata>> UploadFileAsync(
        IFormFile file,
        string containerName,
        string? fileName = null
    );

    /// <summary>
    /// Uploads a file to local storage
    /// </summary>
    /// <param name="fileStream">The file stream to store</param>
    /// <param name="containerName">The folder name to store the file in</param>
    /// <param name="fileName">The file name for the stored file</param>
    /// <param name="contentType">The content type of the file</param>
    /// <returns>Result containing the file metadata</returns>
    Task<Result<FileMetadata>> UploadFileToLocalStorageAsync(
        Stream fileStream,
        string containerName,
        string fileName,
        string contentType = "application/octet-stream"
    );

    /// <summary>
    /// Deletes a file from storage
    /// </summary>
    /// <param name="filePath">The path to the file to delete</param>
    /// <returns>Result indicating success or failure</returns>
    Task<Result<bool>> DeleteFileAsync(string filePath);

    /// <summary>
    /// Gets the URL for a file
    /// </summary>
    /// <param name="filePath">The path to the file</param>
    /// <returns>The URL to access the file</returns>
    string GetFileUrl(string filePath);

    /// <summary>
    /// Validates if a file meets the requirements
    /// </summary>
    /// <param name="file">The file to validate</param>
    /// <param name="maxSizeInMb">Maximum file size in MB</param>
    /// <param name="allowedExtensions">List of allowed file extensions</param>
    /// <returns>Result indicating if the file is valid</returns>
    Result<bool> ValidateFile(IFormFile file, int maxSizeInMb, string[] allowedExtensions);
}

/// <summary>
/// File metadata returned after upload
/// </summary>
public class FileMetadata
{
    /// <summary>
    /// Original file name
    /// </summary>
    public string OriginalFileName { get; set; } = string.Empty;

    /// <summary>
    /// Stored file name (maybe different from original if a unique name was generated)
    /// </summary>
    public string StoredFileName { get; set; } = string.Empty;

    /// <summary>
    /// Absolute file path in the storage system
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// Relative file path for storage in database
    /// </summary>
    public string RelativePath { get; set; } = string.Empty;

    /// <summary>
    /// File URL for access
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// File content type
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// Upload date and time
    /// </summary>
    public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
}
