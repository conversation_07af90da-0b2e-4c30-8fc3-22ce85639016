﻿using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Interfaces.Persistence
{
    public interface IApprovalRepository : IRepository<Approval>
    {
        Task<Result<IEnumerable<Approval>>> GetPendingApprovalsAsync(
            CancellationToken cancellationToken = default
        );
        Task<Result<IEnumerable<Approval>>> GetApprovalsByUserIdAsync(
            Guid userId,
            CancellationToken cancellationToken = default
        );
        Task<Result<IEnumerable<Approval>>> GetApprovalsByTypeAsync(
            ApprovalType type,
            CancellationToken cancellationToken = default
        );
        Task<Result<Approval>> ApproveAsync(
            Guid approvalId,
            Guid adminId,
            string? notes = null,
            CancellationToken cancellationToken = default
        );
        Task<Result<Approval>> RejectAsync(
            Guid approvalId,
            Guid adminId,
            string rejectionReason,
            string? notes = null,
            CancellationToken cancellationToken = default
        );
    }
}
