using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Identity;

public interface IUserProfileService
{
    Task<Result<Guid>> CreateAsync(UserProfile profile);
    Task<Result<UserProfile?>> GetByUserIdAsync(Guid userId);
    Task<Result<Guid>> UpdateAsync(Guid userId, UserProfile profile);
    Task<Result> DeleteAsync(Guid userId);
}
