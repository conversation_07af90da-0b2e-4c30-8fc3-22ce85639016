using SuperCareApp.Application.Common.Models.Provider;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Provider
{
    /// <summary>
    /// Service interface for managing availability templates for care providers
    /// </summary>
    public interface IAvailabilityTemplateService
    {
        /// <summary>
        /// Creates a default availability template for a new care provider
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <returns>Result indicating success or failure</returns>
        Task<Result> CreateDefaultAvailabilityTemplateAsync(Guid providerId);

        /// <summary>
        /// Gets the availability template for a care provider
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <returns>Result containing the availability template</returns>
        Task<Result<AvailabilityTemplateDto>> GetAvailabilityTemplateAsync(Guid providerId);

        /// <summary>
        /// Updates the availability template for a care provider
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <param name="template">The updated availability template</param>
        /// <returns>Result indicating success or failure</returns>
        Task<Result> UpdateAvailabilityTemplateAsync(
            Guid providerId,
            AvailabilityTemplateDto template
        );

        /// <summary>
        /// Resets the availability template to default (all days unavailable)
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <returns>Result indicating success or failure</returns>
        Task<Result> ResetToDefaultTemplateAsync(Guid providerId);

        /// <summary>
        /// Bulk updates multiple days availability status
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <param name="dayUpdates">Dictionary of day names and their availability status</param>
        /// <returns>Result indicating success or failure</returns>
        Task<Result> BulkUpdateDaysAvailabilityAsync(
            Guid providerId,
            Dictionary<string, bool> dayUpdates
        );

        /// <summary>
        /// Sets weekdays (Monday-Friday) availability status
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <param name="isAvailable">Availability status for weekdays</param>
        /// <returns>Result indicating success or failure</returns>
        Task<Result> SetWeekdaysAvailabilityAsync(Guid providerId, bool isAvailable);

        /// <summary>
        /// Sets weekend (Saturday-Sunday) availability status
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <param name="isAvailable">Availability status for weekend</param>
        /// <returns>Result indicating success or failure</returns>
        Task<Result> SetWeekendAvailabilityAsync(Guid providerId, bool isAvailable);

        /// <summary>
        /// Validates if a care provider has a complete availability template
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <returns>Result containing validation status</returns>
        Task<Result<bool>> ValidateAvailabilityTemplateAsync(Guid providerId);

        /// <summary>
        /// Checks if a care provider has any availability set up
        /// </summary>
        /// <param name="providerId">The care provider's user ID</param>
        /// <returns>Result containing whether provider has any available days</returns>
        Task<Result<bool>> HasAnyAvailabilityAsync(Guid providerId);
    }
}
