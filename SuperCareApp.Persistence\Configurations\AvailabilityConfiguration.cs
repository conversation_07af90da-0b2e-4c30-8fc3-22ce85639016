﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Configurations
{
    public class AvailabilityConfiguration : IEntityTypeConfiguration<Availability>
    {
        public void Configure(EntityTypeBuilder<Availability> builder)
        {
            builder.HasKey(a => a.Id);

            builder.Property(a => a.ProviderId).IsRequired();

            builder
                .Property(a => a.DayOfWeek)
                .HasColumnType("character varying")
                .HasMaxLength(120)
                .IsRequired();

            builder.Property(a => a.IsAvailable).IsRequired().HasDefaultValue(true);

            // Navigation property
            builder
                .HasOne(a => a.CareProviderProfile)
                .WithMany(p => p.Availabilities)
                .HasForeignKey(a => a.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);

            // Relationships with AvailabilitySlots
            builder
                .HasMany(a => a.AvailabilitySlots)
                .WithOne(slot => slot.Availability)
                .HasForeignKey(slot => slot.AvailabilityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes for efficient querying
            builder.HasIndex(a => new { a.ProviderId, a.DayOfWeek });
        }
    }
}
