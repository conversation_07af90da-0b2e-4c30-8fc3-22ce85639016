﻿using Microsoft.Extensions.Logging;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Persistence.Services.Address.Commands
{
    public record CreateAddressCommand(Guid UserId, CreateAddressRequest Request)
        : ICommand<Result<Guid>>;

    internal sealed class CreateAddressCommandHandler
        : ICommandHandler<CreateAddressCommand, Result<Guid>>
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<CreateAddressCommandHandler> _logger;

        public CreateAddressCommandHandler(
            IAddressService addressService,
            ILogger<CreateAddressCommandHandler> logger
        )
        {
            _addressService = addressService;
            _logger = logger;
        }

        public async Task<Result<Guid>> Handle(
            CreateAddressCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _addressService.CreateAddressAsync(request.UserId, request.Request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating address for user {UserId}", request.UserId);
                return Result.Failure<Guid>(
                    Error.Internal($"Error creating address: {ex.Message}")
                );
            }
        }
    }
}
