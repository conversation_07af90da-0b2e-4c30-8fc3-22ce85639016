using System.ComponentModel;
using System.Reflection;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace super_care_app.Swagger
{
    /// <summary>
    /// Swagger schema filter to add default values to model properties
    /// </summary>
    public class SwaggerDefaultValuesFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (schema == null || context == null || context.Type == null)
                return;

            // Get all properties with a DefaultValue attribute
            var properties = context
                .Type.GetProperties()
                .Where(p => p.GetCustomAttribute<DefaultValueAttribute>() != null);

            foreach (var property in properties)
            {
                // Get the property name in camelCase (as used by Swagger)
                var propertyName =
                    char.ToLowerInvariant(property.Name[0]) + property.Name.Substring(1);

                // Skip if the property is not in the schema
                if (!schema.Properties.ContainsKey(propertyName))
                    continue;

                // Get the default value
                var defaultAttribute = property.GetCustomAttribute<DefaultValueAttribute>();
                if (defaultAttribute?.Value == null)
                    continue;

                // Set the default value based on the property type
                schema.Properties[propertyName].Default = GetOpenApiValue(defaultAttribute.Value);
            }
        }

        private static IOpenApiAny GetOpenApiValue(object value)
        {
            return value switch
            {
                bool boolValue => new OpenApiBoolean(boolValue),
                int intValue => new OpenApiInteger(intValue),
                long longValue => new OpenApiLong(longValue),
                float floatValue => new OpenApiFloat(floatValue),
                double doubleValue => new OpenApiDouble(doubleValue),
                string stringValue => new OpenApiString(stringValue),
                DateTime dateTimeValue => new OpenApiDateTime(dateTimeValue),
                Guid guidValue => new OpenApiString(guidValue.ToString()),
                Enum enumValue => new OpenApiString(enumValue.ToString()),
                _ => new OpenApiString(value.ToString()),
            };
        }
    }
}
