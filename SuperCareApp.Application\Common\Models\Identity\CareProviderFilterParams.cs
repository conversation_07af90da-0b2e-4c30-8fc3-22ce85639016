﻿namespace SuperCareApp.Application.Common.Models.Identity;

public class CareProviderFilterParams
{
    public string? Date { get; set; }
    public TimeOnly? StartTime { get; set; }
    public TimeOnly? EndTime { get; set; }
    public List<Guid>? CategoryIds { get; set; }
    public int? MinExperience { get; set; }
    public int? MaxExperience { get; set; }
    public double? LocationLat { get; set; }
    public double? LocationLong { get; set; }
    public double? DistanceRadius { get; set; }
    public List<string>? Genders { get; set; }
    public int? MinAge { get; set; }
    public int? MaxAge { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
}
