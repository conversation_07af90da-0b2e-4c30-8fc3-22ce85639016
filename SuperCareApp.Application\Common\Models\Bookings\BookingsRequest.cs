﻿using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace SuperCareApp.Application.Common.Models.Bookings;

public class BookingsRequest
{
    [FromQuery]
    public Guid? UserId { get; set; }

    [FromQuery]
    [SwaggerParameter(
        "Filter for specific care service. Use comma separated values for multiple services"
    )]
    public string? CareServices { get; set; }

    [FromQuery]
    [SwaggerParameter("Filter for specific booking status")]
    public string? BookingStatus { get; set; }

    [FromQuery]
    [SwaggerParameter("Filter for specific start date")]
    public DateTime? StartDate { get; set; }

    [FromQuery]
    [SwaggerParameter("Filter for specific end date")]
    public DateTime? EndDate { get; set; }

    [FromQuery]
    [SwaggerParameter("Filter for specific start time")]
    public TimeOnly? StartTime { get; set; }

    [FromQuery]
    [SwaggerParameter("Filter for specific end time")]
    public TimeOnly? EndTime { get; set; }

    [FromQuery]
    [SwaggerParameter("Page number (1-based)", Required = false)]
    public int PageNumber { get; set; } = 1;

    [FromQuery]
    [SwaggerParameter("Number of items per page (max 50)", Required = false)]
    public int PageSize { get; set; } = 10;

    [FromQuery]
    [SwaggerParameter(
        "Field to sort by (startDate, totalAmount, status, clientName, providerName, categoryName)",
        Required = false
    )]
    public string? SortBy { get; set; }

    [FromQuery]
    [SwaggerParameter(
        "Sort direction (true for descending, false for ascending)",
        Required = false
    )]
    public bool SortDescending { get; set; }

    [FromQuery]
    [SwaggerParameter(
        "Search term to filter bookings (e.g., client name, provider name, category name)",
        Required = false
    )]
    public string? SearchTerm { get; set; }
}
