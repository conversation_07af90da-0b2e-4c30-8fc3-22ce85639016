﻿using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace super_care_app.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly HealthCheckService _healthCheckService;
        private readonly ILogger<HealthController> _logger;

        public HealthController(
            HealthCheckService healthCheckService,
            ILogger<HealthController> logger
        )
        {
            _healthCheckService = healthCheckService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var report = await _healthCheckService.CheckHealthAsync();

            _logger.LogInformation("Health check executed with status {Status}", report.Status);

            var response = new
            {
                status = report.Status.ToString(),
                entries = report.Entries.ToDictionary(
                    entry => entry.Key,
                    entry => new
                    {
                        status = entry.Value.Status.ToString(),
                        description = entry.Value.Description,
                        duration = entry.Value.Duration.ToString(),
                        exception = entry.Value.Exception?.Message,
                        data = entry.Value.Data,
                        tags = entry.Value.Tags.ToArray(),
                    }
                ),
                totalDuration = report.TotalDuration.ToString(),
            };

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            };

            var jsonResponse = JsonSerializer.Serialize(response, options);

            return report.Status == HealthStatus.Healthy
                ? Content(jsonResponse, "application/json")
                : StatusCode((int)HttpStatusCode.ServiceUnavailable, jsonResponse);
        }
    }
}
