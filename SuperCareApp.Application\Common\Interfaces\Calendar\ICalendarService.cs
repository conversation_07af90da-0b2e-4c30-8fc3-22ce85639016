using SuperCareApp.Application.Common.Models.Calendar;
using SuperCareApp.Domain.Common.Results;

namespace SuperCareApp.Application.Common.Interfaces.Calendar
{
    /// <summary>
    /// Interface for calendar-related operations
    /// </summary>
    public interface ICalendarService
    {
        /// <summary>
        /// Gets monthly calendar view for current user (provider or client)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="year">Year</param>
        /// <param name="month">Month</param>
        /// <returns>Monthly calendar data</returns>
        Task<Result<MonthlyCalendarResponse>> GetMonthlyCalendarAsync(
            Guid userId,
            int year,
            int month
        );

        /// <summary>
        /// Gets calendar data for a specific date range and provider
        /// </summary>
        /// /// <param name="providerId">Provider ID</param>
        /// <param name="parameters">Range parameters</param>
        /// <returns>Calendar range data</returns>
        Task<Result<CalendarRangeResponse>> GetCalendarRangeAsync(
            Guid providerId,
            CalendarRangeParams parameters
        );

        /// <summary>
        /// Gets detailed availability for a specific day and provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="date">Date to check</param>
        /// <returns>Day availability details</returns>
        Task<Result<CalendarDayResponse>> GetDayAvailabilityAsync(Guid providerId, DateTime date);

        /// <summary>
        /// Gets next available time slots for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="parameters">Search parameters</param>
        /// <returns>Next available slots</returns>
        Task<Result<NextAvailableSlotsResponse>> GetNextAvailableSlotsAsync(
            Guid providerId,
            NextAvailableSlotsParams parameters
        );

        /// <summary>
        /// Checks if a specific time slot is available for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="parameters">Availability check parameters</param>
        /// <returns>Availability check result</returns>
        Task<Result<CheckAvailabilityResponse>> CheckAvailabilityAsync(
            Guid providerId,
            CheckAvailabilityParams parameters
        );

        /// <summary>
        /// Gets calendar summary with statistics for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="year">Year</param>
        /// <param name="month">Month</param>
        /// <returns>Calendar summary with statistics</returns>
        Task<Result<MonthlyCalendarResponse>> GetCalendarSummaryAsync(
            Guid providerId,
            int year,
            int month
        );

        /// <summary>
        /// Gets available providers for a specific date and time
        /// </summary>
        /// <param name="parameters">Search parameters</param>
        /// <returns>Available providers</returns>
        Task<Result<AvailableProvidersResponse>> GetAvailableProvidersAsync(
            AvailableProvidersParams parameters
        );

        /// <summary>
        /// Gets filtered calendar view for a provider
        /// </summary>
        /// <param name="providerId">Provider ID</param>
        /// <param name="year">Year</param>
        /// <param name="month">Month</param>
        /// <param name="filters">Filter parameters</param>
        /// <returns>Filtered calendar data</returns>
        Task<Result<MonthlyCalendarResponse>> GetFilteredCalendarAsync(
            Guid providerId,
            int year,
            int month,
            FilteredCalendarParams filters
        );
    }
}
