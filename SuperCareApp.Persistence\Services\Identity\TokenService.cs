using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using SuperCareApp.Application.Common.Models.Identity;

namespace SuperCareApp.Persistence.Services.Identity;

public class TokenService : ITokenService
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager; // Inject UserManager her
    private readonly ILogger<TokenService> _logger;
    private readonly JwtSettings _jwtSettings;

    public TokenService(
        ApplicationDbContext context,
        UserManager<ApplicationUser> userManager,
        ILogger<TokenService> logger,
        IOptions<JwtSettings> jwtSettings
    )
    {
        _context = context;
        _userManager = userManager;
        _logger = logger;
        _jwtSettings = jwtSettings.Value;
    }

    public async Task<Result<string>> GenerateAccessTokenAsync(
        ApplicationUser user,
        Guid? loginSessionId = null
    )
    {
        try
        {
            // Create claims for the JWT token
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Name, user.UserName),
                new(ClaimTypes.Email, user.Email),
            };

            var roles = await _userManager.GetRolesAsync(user);

            // Add roles as claims
            if (roles != null)
            {
                foreach (var role in roles)
                {
                    claims.Add(new Claim(ClaimTypes.Role, role));
                }
            }
            // Create signing credentials
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            // Create JWT token
            var token = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: claims,
                expires: DateTime.UtcNow.AddHours(1), // Token expires in 1 hour
                signingCredentials: credentials
            );

            // Generate token string
            var tokenString = await Task.FromResult(
                new JwtSecurityTokenHandler().WriteToken(token)
            );

            // Save the token
            var accessTokenEntity = new ApplicationUserToken
            {
                UserId = user.Id,
                Name = Guid.NewGuid().ToString(),
                LoginProvider = "CareApp",
                Token = tokenString,
                TokenType = "Access",
                ExpiryDate = DateTime.UtcNow.AddHours(1),
                IsExpired = false,
                IsRevoked = false,
                LoginSessionId = loginSessionId.Value,
            };

            await SaveTokenAsync(accessTokenEntity);

            return Result.Success(tokenString);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating access token for user {UserId}", user.Id);
            return Result.Failure<string>(Error.Internal("Error generating access token"));
        }
    }

    public async Task<Result<string>> GenerateRefreshTokenAsync(
        ApplicationUser user,
        string accessToken,
        Guid? loginSessionId = null
    )
    {
        try
        {
            // Step 1: Validate the access token
            try
            {
                await ValidateAccessTokenAsync(accessToken);
            }
            catch (SecurityTokenExpiredException)
            {
                _logger.LogWarning("Access token expired during refresh token generation");
                return Result.Failure<string>(Error.Unauthorized("Access token has expired"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Invalid access token during refresh token generation");
                return Result.Failure<string>(Error.Validation("Invalid access token"));
            }

            // Step 2: Generate a JWT-based refresh token
            var refreshToken = GenerateJwtRefreshToken(user.Id.ToString());
            //Save the token
            var refreshTokenEntity = new ApplicationUserToken
            {
                UserId = user.Id,
                Name = Guid.NewGuid().ToString(),
                LoginProvider = "CareApp",
                Token = refreshToken,
                TokenType = "Refresh",
                ExpiryDate = DateTime.UtcNow.AddMonths(6),
                IsExpired = false,
                IsRevoked = false,
                LoginSessionId = loginSessionId.Value,
            };
            await SaveTokenAsync(refreshTokenEntity);
            return Result.Success(refreshToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating refresh token");
            return Result.Failure<string>(Error.Internal("Error generating refresh token"));
        }
    }

    public Task<Result<bool>> RemoveTokenAsync(string token)
    {
        throw new NotImplementedException();
    }

    public async Task<Result<bool>> RevokeAllTokensAsync(Guid userId)
    {
        try
        {
            var userTokens = await _context
                .UserTokens.AsNoTracking()
                .Where(t => t.UserId == userId)
                .ToListAsync();
            foreach (var userToken in userTokens)
            {
                userToken.IsRevoked = true;
                userToken.IsExpired = true;
                _context.UserTokens.Update(userToken);
            }
            await _context.SaveChangesAsync();
            return Result.Success(true);
        }
        catch (Exception ex)
        {
            return Result.Failure<bool>(
                Error.Internal($"Failed to revoke all tokens for user {userId}: {ex.Message}")
            );
        }
    }

    private async Task<Result<bool>> RevokeAllTokensForLoginSessionAsync(Guid loginSessionId)
    {
        if (loginSessionId == Guid.Empty)
        {
            return Result.Failure<bool>(Error.BadRequest("Invalid login session ID"));
        }

        try
        {
            var tokensToUpdate = await _context
                .UserTokens.Where(t =>
                    t.LoginSessionId == loginSessionId && !t.IsRevoked && !t.IsExpired
                )
                .ToListAsync();

            if (tokensToUpdate.Count == 0)
            {
                return Result.Success(true);
            }

            var now = DateTime.UtcNow;
            foreach (var token in tokensToUpdate)
            {
                token.IsRevoked = true;
                token.IsExpired = true;
            }

            await _context.SaveChangesAsync();

            _logger?.LogInformation(
                "Revoked {Count} tokens for login session {LoginSessionId}",
                tokensToUpdate.Count,
                loginSessionId
            );

            return Result.Success(true);
        }
        catch (Exception ex)
        {
            _logger?.LogError(
                ex,
                "Failed to revoke tokens for login session {LoginSessionId}",
                loginSessionId
            );
            return Result.Failure<bool>(
                Error.Internal($"Failed to revoke tokens for login session: {ex.Message}")
            );
        }
    }

    public async Task<Result<bool>> RevokeTokenAsync(string token)
    {
        var userToken = await _context
            .UserTokens.AsNoTracking()
            .FirstOrDefaultAsync(t => t.Token == token);
        if (userToken == null)
        {
            return Result.Failure<bool>(Error.Validation("Invalid token"));
        }
        userToken.IsRevoked = true;
        userToken.IsExpired = true;
        await _context.SaveChangesAsync();
        return Result.Success(true);
    }

    public async Task<Result> SaveTokenAsync(ApplicationUserToken userToken)
    {
        try
        {
            await _context.UserTokens.AddAsync(userToken);
            await _context.SaveChangesAsync();
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save user token reason: {Reason}", ex.Message);
            _logger.LogError(ex, "Stack trace: {StackTrace}", ex.StackTrace);
            _logger.LogError(ex, "Inner exception: {InnerException}", ex.InnerException?.Message);
            _logger.LogError(ex, "Source: {Source}", ex.Source);
            return Result.Failure(Error.Internal("Failed to save user token"));
        }
    }

    public async Task<Result> SaveTokensAsync(IEnumerable<ApplicationUserToken> userTokens)
    {
        try
        {
            await _context.UserTokens.AddRangeAsync(userTokens);
            await _context.SaveChangesAsync();
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save user tokens reason: {Reason}", ex.Message);
            _logger.LogError(ex, "Stack trace: {StackTrace}", ex.StackTrace);
            _logger.LogError(ex, "Inner exception: {InnerException}", ex.InnerException?.Message);
            _logger.LogError(ex, "Source: {Source}", ex.Source);
            return Result.Failure(Error.Internal("Failed to save user tokens"));
        }
    }

    public async Task<Result<Guid>> ValidateToken(string token)
    {
        Guid userId;
        var userToken = await _context
            .UserTokens.AsNoTracking()
            .FirstOrDefaultAsync(t => t.Token == token);
        if (userToken == null)
        {
            userId = Guid.Empty;
            return Result.Failure<Guid>(Error.Unauthorized("Invalid token"));
        }
        if (userToken.IsRevoked || userToken.IsExpired)
        {
            userId = Guid.Empty;
            return Result.Failure<Guid>(Error.Unauthorized("Invalid token"));
        }
        userId = userToken.UserId;
        return Result.Success(userId);
    }

    private async Task<Result<Guid>> ValidateRefreshToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
        {
            _logger?.LogWarning("Refresh token validation failed: Token is null or empty");
            return Result.Failure<Guid>(Error.Unauthorized("Invalid refresh token"));
        }

        var userToken = await _context
            .UserTokens.AsNoTracking()
            .Where(t => t.Token == token && t.TokenType == "Refresh")
            .Select(t => new
            {
                t.UserId,
                t.IsRevoked,
                t.IsExpired,
            })
            .FirstOrDefaultAsync();

        if (userToken == null)
        {
            _logger?.LogWarning("Refresh token validation failed: Token not found in database");
            return Result.Failure<Guid>(Error.Unauthorized("Invalid refresh token"));
        }

        if (userToken.IsRevoked)
        {
            _logger?.LogWarning(
                "Refresh token validation failed: Token is revoked for user {UserId}",
                userToken.UserId
            );
            return Result.Failure<Guid>(Error.Unauthorized("Refresh token has been revoked"));
        }

        if (userToken.IsExpired)
        {
            _logger?.LogWarning(
                "Refresh token validation failed: Token expired for user {UserId}",
                userToken.UserId
            );
            return Result.Failure<Guid>(Error.Unauthorized("Refresh token has expired"));
        }

        _logger?.LogInformation(
            "Refresh token validated successfully for user {UserId}",
            userToken.UserId
        );
        return Result.Success(userToken.UserId);
    }

    public async Task<Result<AuthResponse>> ExchangeTokenAsync(string refreshToken)
    {
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            return Result.Failure<AuthResponse>(Error.Unauthorized("Invalid refresh token"));
        }

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var userIdResult = await ValidateRefreshToken(refreshToken);
            if (userIdResult.IsFailure)
            {
                return Result.Failure<AuthResponse>(userIdResult.Error);
            }

            var tokenInfo = await _context
                .UserTokens.AsNoTracking()
                .Where(t => t.Token == refreshToken && t.TokenType == "Refresh")
                .Select(t => new { t.LoginSessionId, t.UserId })
                .FirstOrDefaultAsync();

            if (tokenInfo == null)
            {
                return Result.Failure<AuthResponse>(Error.Unauthorized("Invalid refresh token"));
            }

            var revokeResult = await RevokeAllTokensForLoginSessionAsync(tokenInfo.LoginSessionId);
            if (revokeResult.IsFailure)
            {
                await transaction.RollbackAsync();
                return Result.Failure<AuthResponse>(revokeResult.Error);
            }

            var user = await _userManager.FindByIdAsync(userIdResult.Value.ToString());
            if (user == null)
            {
                await transaction.RollbackAsync();
                _logger.LogError("User not found: {UserId}", userIdResult.Value);
                return Result.Failure<AuthResponse>(Error.Unauthorized("User not found"));
            }

            var newLoginSessionId = Guid.NewGuid();

            var accessTokenResult = await GenerateAccessTokenAsync(user, newLoginSessionId);
            if (accessTokenResult.IsFailure)
            {
                await transaction.RollbackAsync();
                return Result.Failure<AuthResponse>(accessTokenResult.Error);
            }

            var refreshTokenResult = await GenerateRefreshTokenAsync(
                user,
                accessTokenResult.Value,
                newLoginSessionId
            );
            if (refreshTokenResult.IsFailure)
            {
                await transaction.RollbackAsync();
                return Result.Failure<AuthResponse>(refreshTokenResult.Error);
            }

            bool? isVerifiedByAdmin = null;
            var careProviderProfile = await _context
                .CareProviderProfiles.AsNoTracking()
                .Where(cp => cp.UserId == user.Id && !cp.IsDeleted)
                .Select(cp => cp.VerificationStatus)
                .FirstOrDefaultAsync();

            isVerifiedByAdmin = careProviderProfile == Domain.Enums.VerificationStatus.Verified;

            await transaction.CommitAsync();

            var authResponse = new AuthResponse(
                accessTokenResult.Value,
                DateTime.UtcNow.AddHours(1),
                refreshTokenResult.Value,
                isVerifiedByAdmin
            );

            _logger.LogInformation("Token exchange completed for user {UserId}", user.Id);
            return Result.Success(authResponse);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(
                ex,
                "Failed to exchange token for user {UserId}",
                refreshToken.GetHashCode()
            ); // Avoid logging actual token
            return Result.Failure<AuthResponse>(Error.Internal("Failed to process token exchange"));
        }
    }

    private async Task<JwtSecurityToken> ValidateAccessTokenAsync(string accessToken)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));

        tokenHandler.ValidateToken(
            accessToken,
            new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = key,
                ValidateIssuer = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtSettings.Audience,
                ValidateLifetime = true, // Ensure the token is not expired
            },
            out var validatedToken
        );

        return await Task.FromResult((JwtSecurityToken)validatedToken);
    }

    private string GenerateJwtRefreshToken(string userId)
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, userId),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim("TokenType", "Refresh"),
        };

        var token = new JwtSecurityToken(
            issuer: _jwtSettings.Issuer,
            audience: _jwtSettings.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMonths(6), // Long-lived refresh token
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}
