using System.Text.Json.Serialization;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Identity;

public class ProfileResponse
{
    // Common properties
    [JsonIgnore]
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    /// <summary>
    /// The ID of the CareProviderProfile when UserType is CareProvider
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public Guid? ProviderId { get; set; }

    // Main properties as per required format
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int YearsExperience { get; set; }
    public bool? ProvidesRecurringBooking { get; set; }
    public int? WorkingHours { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<CategoryInfo>? Categories { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? HourlyRate { get; set; }
    public DateTime? DateOfBirth { get; set; }

    // Profile picture URL
    public string? ProfilePictureUrl { get; set; }

    // Address information
    public AddressInfo? PrimaryAddress { get; set; }

    // Documents/Certifications
    public List<DocumentResponse>? Documents { get; set; }

    [JsonIgnore]
    public string? Address { get; set; }

    [JsonIgnore]
    public string? City { get; set; }

    [JsonIgnore]
    public string? State { get; set; }

    [JsonIgnore]
    public string? PostalCode { get; set; }

    [JsonIgnore]
    public string? Country { get; set; }

    // CareProvider specific properties - only included for CareProvider UserType
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public string? Bio { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public bool? ProvidesOvernight { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public bool? ProvidesLiveIn { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public string? Qualifications { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public decimal? Rating { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int? RatingCount { get; set; }

    // Travel experience details (if available)
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public TravelExperienceInfo? TravelExperience { get; set; }

    // Used internally to determine which fields to include
    [JsonIgnore]
    public UserType UserType { get; set; }
}

/// <summary>
/// Address information for a user
/// </summary>
public class AddressInfo
{
    public string StreetAddress { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? Label { get; set; }
}

/// <summary>
/// Travel experience information for a user
/// </summary>
public class TravelExperienceInfo
{
    public bool WillingToTravel { get; set; }
    public int? MaxTravelDistance { get; set; }
    public string? PreferredTransportation { get; set; }
    public List<string>? TravelLocations { get; set; }
}

public class CategoryInfo
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal? HourlyRate { get; set; }
    public int? ExperienceLevel { get; set; }
}
