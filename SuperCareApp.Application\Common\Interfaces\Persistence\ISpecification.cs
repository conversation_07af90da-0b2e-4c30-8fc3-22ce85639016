﻿using System.Linq.Expressions;

namespace SuperCareApp.Application.Common.Interfaces.Persistence
{
    /// <summary>
    /// Specification pattern interface for query filtering
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    public interface ISpecification<T>
    {
        /// <summary>
        /// The filter criteria
        /// </summary>
        Expression<Func<T, bool>>? Criteria { get; }

        /// <summary>
        /// Include expressions for eager loading
        /// </summary>
        List<Expression<Func<T, object>>> Includes { get; }

        /// <summary>
        /// Ordering expressions
        /// </summary>
        List<OrderingExpression<T>> OrderingExpressions { get; }

        /// <summary>
        /// Number of entities to take (for pagination)
        /// </summary>
        int? Take { get; }

        /// <summary>
        /// Number of entities to skip (for pagination)
        /// </summary>
        int? Skip { get; }
    }

    /// <summary>
    /// Ordering direction enum
    /// </summary>
    public enum OrderingDirection
    {
        /// <summary>
        /// Ascending order
        /// </summary>
        Ascending,

        /// <summary>
        /// Descending order
        /// </summary>
        Descending,
    }

    /// <summary>
    /// Ordering expression for specifications
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    public class OrderingExpression<T>
    {
        /// <summary>
        /// The ordering key selector
        /// </summary>
        public Expression<Func<T, object>>? OrderingKeySelector { get; set; }

        /// <summary>
        /// The ordering direction
        /// </summary>
        public OrderingDirection Direction { get; set; }
    }
}
