﻿using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to update a care category
    /// </summary>
    public record UpdateCareCategoryCommand(
        Guid CategoryId,
        UpdateCareCategoryRequest Request,
        Guid UserId
    ) : ICommand<Result<CareCategoryResponse>>;

    /// <summary>
    /// Handler for the UpdateCareCategoryCommand
    /// </summary>
    public sealed class UpdateCareCategoryCommandHandler
        : ICommandHandler<UpdateCareCategoryCommand, Result<CareCategoryResponse>>
    {
        private readonly ICareCategoryService _categoryService;
        private readonly ILogger<UpdateCareCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public UpdateCareCategoryCommandHandler(
            ICareCategoryService categoryService,
            ILogger<UpdateCareCategoryCommandHandler> logger
        )
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result<CareCategoryResponse>> Handle(
            UpdateCareCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                return await _categoryService.UpdateCategoryAsync(
                    request.CategoryId,
                    request.Request,
                    request.UserId,
                    cancellationToken
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error updating care category with ID {CategoryId}",
                    request.CategoryId
                );
                return Result.Failure<CareCategoryResponse>(Error.Internal(ex.Message));
            }
        }
    }
}
