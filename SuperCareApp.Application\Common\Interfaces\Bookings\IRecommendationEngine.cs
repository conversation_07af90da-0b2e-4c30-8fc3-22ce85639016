using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Bookings;

public interface IRecommendationEngine : IDisposable
{
    void AddCareProfessional(CareProviderProfile provider);

    void UpdateCareProfessional(CareProviderProfile provider);

    void RemoveCareProfessional(Guid providerId);

    List<CareProviderProfile> Recommend(
        double clientLat,
        double clientLon,
        string? requiredService,
        TimeOnly? requestedStart,
        TimeOnly? requestedEnd,
        double searchRadiusKm = 5,
        int topN = 5
    );
}
