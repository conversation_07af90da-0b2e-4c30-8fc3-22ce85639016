﻿using SuperCareApp.Application.Common.Models.Address;
using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Application.Common.Interfaces.Address
{
    public interface IAddressService
    {
        Task<Result<Guid>> CreateAddressAsync(Guid userId, CreateAddressRequest request);
        Task<Result<AddressDto>> GetAddressByIdAsync(Guid addressId);
        Task<Result<List<AddressDto>>> GetAddressesByUserIdAsync(Guid userId);
        Task<Result<Guid>> UpdateAddressAsync(Guid userId, UpdateAddressRequest request);
        Task<Result<bool>> DeleteAddressAsync(Guid userId, Guid addressId);
        Task<Result<Guid>> SetPrimaryAddressAsync(Guid userId, Guid addressId);
    }
}
