namespace super_care_app.Models.Address
{
    /// <summary>
    /// Response model for address data
    /// </summary>
    public class AddressResponse
    {
        /// <summary>
        /// Unique identifier for the address
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Street address
        /// </summary>
        public string StreetAddress { get; set; } = string.Empty;

        /// <summary>
        /// City
        /// </summary>
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// State or province
        /// </summary>
        public string State { get; set; } = string.Empty;

        /// <summary>
        /// Postal code or ZIP code
        /// </summary>
        public string PostalCode { get; set; } = string.Empty;

        /// <summary>
        /// Latitude coordinate
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// Longitude coordinate
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Whether this is the primary address for the user
        /// </summary>
        public bool IsPrimary { get; set; }

        /// <summary>
        /// Label for the address (e.g., "Home", "Work")
        /// </summary>
        public string? Label { get; set; }
    }
}
