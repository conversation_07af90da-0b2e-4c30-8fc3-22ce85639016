# Dashboard API Implementation Plan

## 📋 Overview

This document outlines the comprehensive implementation plan for dashboard APIs serving care professionals and admin views in the SuperCare App frontend. The plan builds upon existing dashboard infrastructure while adding specialized endpoints for different user roles.

## 🎯 Objectives

- **Care Professional Dashboard**: Provide comprehensive analytics for care providers
- **Enhanced Admin Dashboard**: Extend existing admin dashboard with additional insights
- **Real-time Data**: Implement real-time updates for critical metrics
- **Performance Optimization**: Ensure fast loading times with efficient queries
- **Role-based Access**: Secure endpoints based on user roles
- **Frontend Integration**: Design APIs optimized for frontend consumption

## 🏗️ Current State Analysis

### Existing Dashboard Infrastructure

The application currently has:

- **Admin Dashboard Service**: `DashboardStatisticsService` with comprehensive statistics
- **Admin Controller**: `DashboardController` with multiple endpoints
- **Statistics Models**: Rich data models for various analytics
- **Basic Statistics**: User, booking, financial, and notification analytics

### Current Endpoints

- `GET /api/v1/admin/dashboard/statistics` - Complete dashboard statistics
- `GET /api/v1/admin/dashboard/system-overview` - System overview
- `GET /api/v1/admin/dashboard/user-analytics` - User analytics
- `GET /api/v1/admin/dashboard/booking-analytics` - Booking analytics
- `GET /api/v1/admin/dashboard/financial-analytics` - Financial analytics

## 🚀 Implementation Strategy

### Phase 1: Care Professional Dashboard APIs

#### 1.1 Provider Dashboard Service

```csharp
public interface ICareProviderDashboardService
{
    Task<Result<ProviderDashboardResponse>> GetDashboardAsync(
        Guid providerId,
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<ProviderEarningsResponse>> GetEarningsAsync(
        Guid providerId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    Task<Result<ProviderBookingAnalyticsResponse>> GetBookingAnalyticsAsync(
        Guid providerId,
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<ProviderPerformanceResponse>> GetPerformanceMetricsAsync(
        Guid providerId,
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<ProviderAvailabilityInsightsResponse>> GetAvailabilityInsightsAsync(
        Guid providerId,
        int monthsAhead = 3,
        CancellationToken cancellationToken = default);

    Task<Result<List<RecentActivityResponse>>> GetRecentActivityAsync(
        Guid providerId,
        int limit = 10,
        CancellationToken cancellationToken = default);
}
```

#### 1.2 Provider Dashboard Models

```csharp
public class ProviderDashboardResponse
{
    public ProviderOverviewStats Overview { get; set; } = new();
    public ProviderEarningsStats Earnings { get; set; } = new();
    public ProviderBookingStats Bookings { get; set; } = new();
    public ProviderPerformanceStats Performance { get; set; } = new();
    public List<RecentActivityResponse> RecentActivity { get; set; } = new();
    public ProviderAvailabilityStats Availability { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public DashboardTimeRange TimeRange { get; set; } = new();
}

public class ProviderOverviewStats
{
    public int TotalBookings { get; set; }
    public int CompletedBookings { get; set; }
    public int PendingBookings { get; set; }
    public int CancelledBookings { get; set; }
    public decimal TotalEarnings { get; set; }
    public decimal CurrentMonthEarnings { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalReviews { get; set; }
    public double CompletionRate { get; set; }
    public double ResponseRate { get; set; }
    public int ActiveClients { get; set; }
    public int RepeatClients { get; set; }
}

public class ProviderEarningsStats
{
    public decimal TotalEarnings { get; set; }
    public decimal CurrentMonthEarnings { get; set; }
    public decimal LastMonthEarnings { get; set; }
    public double GrowthRate { get; set; }
    public decimal AverageBookingValue { get; set; }
    public decimal PendingPayments { get; set; }
    public List<DailyEarningsData> EarningsByDay { get; set; } = new();
    public List<CategoryEarningsData> EarningsByCategory { get; set; } = new();
    public List<MonthlyEarningsData> MonthlyTrend { get; set; } = new();
}

public class ProviderBookingStats
{
    public int TotalBookings { get; set; }
    public int NewBookings { get; set; }
    public int CompletedBookings { get; set; }
    public int CancelledBookings { get; set; }
    public double BookingGrowthRate { get; set; }
    public Dictionary<string, int> BookingsByStatus { get; set; } = new();
    public Dictionary<string, int> BookingsByCategory { get; set; } = new();
    public List<DailyBookingData> BookingsByDay { get; set; } = new();
    public Dictionary<int, int> BookingsByHour { get; set; } = new();
    public List<TopClientData> TopClients { get; set; } = new();
}

public class ProviderPerformanceStats
{
    public decimal AverageRating { get; set; }
    public int TotalReviews { get; set; }
    public double CompletionRate { get; set; }
    public double CancellationRate { get; set; }
    public double ResponseRate { get; set; }
    public double OnTimeRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public List<RatingTrendData> RatingTrend { get; set; } = new();
    public List<RecentReviewData> RecentReviews { get; set; } = new();
    public Dictionary<int, int> RatingDistribution { get; set; } = new();
}
```

#### 1.3 Provider Dashboard Controller

```csharp
[Authorize(Roles = "CareProvider")]
[Route("api/v{version:apiVersion}/provider/dashboard")]
[SwaggerTag("Provider Dashboard - Analytics and insights for care providers")]
public class ProviderDashboardController : BaseController
{
    private readonly ICareProviderDashboardService _dashboardService;
    private readonly ICurrentUserService _currentUserService;

    [HttpGet]
    [SwaggerOperation(Summary = "Get provider dashboard overview")]
    public async Task<ActionResult<ApiResponseModel<ProviderDashboardResponse>>> GetDashboard(
        [FromQuery] string timeRange = "30days",
        CancellationToken cancellationToken = default)
    {
        var providerId = _currentUserService.UserId!.Value;
        var range = ParseTimeRange(timeRange);

        var result = await _dashboardService.GetDashboardAsync(providerId, range, cancellationToken);
        return FromResult(result, "Provider dashboard retrieved successfully");
    }

    [HttpGet("earnings")]
    [SwaggerOperation(Summary = "Get provider earnings analytics")]
    public async Task<ActionResult<ApiResponseModel<ProviderEarningsResponse>>> GetEarnings(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        var providerId = _currentUserService.UserId!.Value;
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;

        var result = await _dashboardService.GetEarningsAsync(providerId, start, end, cancellationToken);
        return FromResult(result, "Provider earnings retrieved successfully");
    }

    [HttpGet("bookings")]
    [SwaggerOperation(Summary = "Get provider booking analytics")]
    public async Task<ActionResult<ApiResponseModel<ProviderBookingAnalyticsResponse>>> GetBookingAnalytics(
        [FromQuery] string timeRange = "30days",
        CancellationToken cancellationToken = default)
    {
        var providerId = _currentUserService.UserId!.Value;
        var range = ParseTimeRange(timeRange);

        var result = await _dashboardService.GetBookingAnalyticsAsync(providerId, range, cancellationToken);
        return FromResult(result, "Provider booking analytics retrieved successfully");
    }

    [HttpGet("performance")]
    [SwaggerOperation(Summary = "Get provider performance metrics")]
    public async Task<ActionResult<ApiResponseModel<ProviderPerformanceResponse>>> GetPerformance(
        [FromQuery] string timeRange = "30days",
        CancellationToken cancellationToken = default)
    {
        var providerId = _currentUserService.UserId!.Value;
        var range = ParseTimeRange(timeRange);

        var result = await _dashboardService.GetPerformanceMetricsAsync(providerId, range, cancellationToken);
        return FromResult(result, "Provider performance metrics retrieved successfully");
    }

    [HttpGet("availability-insights")]
    [SwaggerOperation(Summary = "Get provider availability insights")]
    public async Task<ActionResult<ApiResponseModel<ProviderAvailabilityInsightsResponse>>> GetAvailabilityInsights(
        [FromQuery] int monthsAhead = 3,
        CancellationToken cancellationToken = default)
    {
        var providerId = _currentUserService.UserId!.Value;

        var result = await _dashboardService.GetAvailabilityInsightsAsync(providerId, monthsAhead, cancellationToken);
        return FromResult(result, "Provider availability insights retrieved successfully");
    }

    [HttpGet("recent-activity")]
    [SwaggerOperation(Summary = "Get provider recent activity")]
    public async Task<ActionResult<ApiResponseModel<List<RecentActivityResponse>>>> GetRecentActivity(
        [FromQuery] int limit = 10,
        CancellationToken cancellationToken = default)
    {
        var providerId = _currentUserService.UserId!.Value;

        var result = await _dashboardService.GetRecentActivityAsync(providerId, limit, cancellationToken);
        return FromResult(result, "Provider recent activity retrieved successfully");
    }
}
```

### Phase 2: Enhanced Admin Dashboard APIs

#### 2.1 Enhanced Admin Dashboard Service

```csharp
public interface IEnhancedAdminDashboardService : IDashboardStatisticsService
{
    Task<Result<AdminRealTimeStatsResponse>> GetRealTimeStatsAsync(
        CancellationToken cancellationToken = default);

    Task<Result<AdminProviderInsightsResponse>> GetProviderInsightsAsync(
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<AdminClientInsightsResponse>> GetClientInsightsAsync(
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<AdminRevenueInsightsResponse>> GetRevenueInsightsAsync(
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<AdminOperationalInsightsResponse>> GetOperationalInsightsAsync(
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default);

    Task<Result<List<AdminAlertResponse>>> GetSystemAlertsAsync(
        CancellationToken cancellationToken = default);
}
```

#### 2.2 Enhanced Admin Models

```csharp
public class AdminRealTimeStatsResponse
{
    public int ActiveUsers { get; set; }
    public int OnlineProviders { get; set; }
    public int PendingBookings { get; set; }
    public int ActiveBookings { get; set; }
    public decimal TodayRevenue { get; set; }
    public int NewRegistrations { get; set; }
    public int PendingApprovals { get; set; }
    public int SystemAlerts { get; set; }
    public double SystemLoad { get; set; }
    public double ResponseTime { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class AdminProviderInsightsResponse
{
    public int TotalProviders { get; set; }
    public int ActiveProviders { get; set; }
    public int NewProviders { get; set; }
    public int VerifiedProviders { get; set; }
    public int PendingVerification { get; set; }
    public double AverageRating { get; set; }
    public List<TopPerformingProviderData> TopPerformers { get; set; } = new();
    public List<ProviderCategoryDistribution> CategoryDistribution { get; set; } = new();
    public List<ProviderLocationData> LocationDistribution { get; set; } = new();
    public List<ProviderRegistrationTrend> RegistrationTrend { get; set; } = new();
}

public class AdminClientInsightsResponse
{
    public int TotalClients { get; set; }
    public int ActiveClients { get; set; }
    public int NewClients { get; set; }
    public int VerifiedClients { get; set; }
    public double RetentionRate { get; set; }
    public double AverageBookingsPerClient { get; set; }
    public List<ClientSegmentData> ClientSegments { get; set; } = new();
    public List<ClientLocationData> LocationDistribution { get; set; } = new();
    public List<ClientActivityTrend> ActivityTrend { get; set; } = new();
    public List<TopClientData> TopClients { get; set; } = new();
}

public class AdminRevenueInsightsResponse
{
    public decimal TotalRevenue { get; set; }
    public decimal PlatformRevenue { get; set; }
    public decimal ProviderRevenue { get; set; }
    public double GrowthRate { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public List<RevenueByPeriodData> RevenueTrend { get; set; } = new();
    public List<RevenueByServiceData> RevenueByService { get; set; } = new();
    public List<RevenueByLocationData> RevenueByLocation { get; set; } = new();
    public List<PaymentMethodData> PaymentMethods { get; set; } = new();
}

public class AdminOperationalInsightsResponse
{
    public double BookingCompletionRate { get; set; }
    public double BookingCancellationRate { get; set; }
    public double AverageResponseTime { get; set; }
    public double CustomerSatisfactionScore { get; set; }
    public int PendingApprovals { get; set; }
    public int PendingDocuments { get; set; }
    public int SupportTickets { get; set; }
    public List<OperationalMetricTrend> MetricsTrend { get; set; } = new();
    public List<ServiceQualityData> ServiceQuality { get; set; } = new();
}
```

### Phase 3: Service Implementation Details

#### 3.1 Care Provider Dashboard Service Implementation

```csharp
public class CareProviderDashboardService : ICareProviderDashboardService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CareProviderDashboardService> _logger;

    public async Task<Result<ProviderDashboardResponse>> GetDashboardAsync(
        Guid providerId,
        DashboardTimeRange timeRange,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var (startDate, endDate) = GetDateRange(timeRange);

            // Execute multiple queries in parallel for better performance
            var overviewTask = GetProviderOverviewAsync(providerId, startDate, endDate, cancellationToken);
            var earningsTask = GetProviderEarningsStatsAsync(providerId, startDate, endDate, cancellationToken);
            var bookingsTask = GetProviderBookingStatsAsync(providerId, startDate, endDate, cancellationToken);
            var performanceTask = GetProviderPerformanceStatsAsync(providerId, startDate, endDate, cancellationToken);
            var activityTask = GetRecentActivityAsync(providerId, 5, cancellationToken);
            var availabilityTask = GetProviderAvailabilityStatsAsync(providerId, cancellationToken);

            await Task.WhenAll(overviewTask, earningsTask, bookingsTask, performanceTask, activityTask, availabilityTask);

            var response = new ProviderDashboardResponse
            {
                Overview = overviewTask.Result.Value,
                Earnings = earningsTask.Result.Value,
                Bookings = bookingsTask.Result.Value,
                Performance = performanceTask.Result.Value,
                RecentActivity = activityTask.Result.Value,
                Availability = availabilityTask.Result.Value,
                GeneratedAt = DateTime.UtcNow,
                TimeRange = timeRange
            };

            return Result.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating provider dashboard for {ProviderId}", providerId);
            return Result.Failure<ProviderDashboardResponse>(
                Error.Internal("Failed to generate provider dashboard"));
        }
    }

    private async Task<Result<ProviderOverviewStats>> GetProviderOverviewAsync(
        Guid providerId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var bookings = await _context.Bookings
            .Where(b => b.ProviderId == providerId && !b.IsDeleted)
            .Where(b => b.CreatedAt >= startDate && b.CreatedAt <= endDate)
            .Include(b => b.Status)
            .ToListAsync(cancellationToken);

        var allTimeBookings = await _context.Bookings
            .Where(b => b.ProviderId == providerId && !b.IsDeleted)
            .Include(b => b.Status)
            .ToListAsync(cancellationToken);

        var completedBookings = bookings.Where(b => b.Status.Status == BookingStatusType.Completed).ToList();
        var pendingBookings = bookings.Where(b => b.Status.Status == BookingStatusType.Requested).ToList();
        var cancelledBookings = bookings.Where(b => b.Status.Status == BookingStatusType.Cancelled).ToList();

        // Get provider profile for rating
        var providerProfile = await _context.CareProviderProfiles
            .FirstOrDefaultAsync(p => p.Id == providerId, cancellationToken);

        // Calculate earnings
        var totalEarnings = completedBookings.Sum(b => b.ProviderAmount);
        var currentMonthStart = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);
        var currentMonthEarnings = allTimeBookings
            .Where(b => b.CreatedAt >= currentMonthStart && b.Status.Status == BookingStatusType.Completed)
            .Sum(b => b.ProviderAmount);

        // Calculate rates
        var totalBookingsCount = allTimeBookings.Count;
        var completedCount = allTimeBookings.Count(b => b.Status.Status == BookingStatusType.Completed);
        var completionRate = totalBookingsCount > 0 ? (double)completedCount / totalBookingsCount * 100 : 0;

        // Get unique clients
        var activeClients = bookings.Select(b => b.ClientId).Distinct().Count();
        var repeatClients = allTimeBookings
            .GroupBy(b => b.ClientId)
            .Count(g => g.Count() > 1);

        var stats = new ProviderOverviewStats
        {
            TotalBookings = bookings.Count,
            CompletedBookings = completedBookings.Count,
            PendingBookings = pendingBookings.Count,
            CancelledBookings = cancelledBookings.Count,
            TotalEarnings = totalEarnings,
            CurrentMonthEarnings = currentMonthEarnings,
            AverageRating = providerProfile?.Rating ?? 0,
            TotalReviews = providerProfile?.ReviewCount ?? 0,
            CompletionRate = completionRate,
            ResponseRate = 95.0, // Mock data - would come from tracking
            ActiveClients = activeClients,
            RepeatClients = repeatClients
        };

        return Result.Success(stats);
    }

    private async Task<Result<ProviderEarningsStats>> GetProviderEarningsStatsAsync(
        Guid providerId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var completedBookings = await _context.Bookings
            .Where(b => b.ProviderId == providerId && !b.IsDeleted)
            .Where(b => b.Status.Status == BookingStatusType.Completed)
            .Where(b => b.CreatedAt >= startDate && b.CreatedAt <= endDate)
            .Include(b => b.Category)
            .ToListAsync(cancellationToken);

        var totalEarnings = completedBookings.Sum(b => b.ProviderAmount);

        // Current and last month earnings
        var currentMonthStart = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);
        var lastMonthStart = currentMonthStart.AddMonths(-1);

        var currentMonthEarnings = await _context.Bookings
            .Where(b => b.ProviderId == providerId && !b.IsDeleted)
            .Where(b => b.Status.Status == BookingStatusType.Completed)
            .Where(b => b.CreatedAt >= currentMonthStart)
            .SumAsync(b => b.ProviderAmount, cancellationToken);

        var lastMonthEarnings = await _context.Bookings
            .Where(b => b.ProviderId == providerId && !b.IsDeleted)
            .Where(b => b.Status.Status == BookingStatusType.Completed)
            .Where(b => b.CreatedAt >= lastMonthStart && b.CreatedAt < currentMonthStart)
            .SumAsync(b => b.ProviderAmount, cancellationToken);

        var growthRate = lastMonthEarnings > 0
            ? (double)((currentMonthEarnings - lastMonthEarnings) / lastMonthEarnings * 100)
            : 0;

        var averageBookingValue = completedBookings.Any()
            ? completedBookings.Average(b => b.ProviderAmount)
            : 0;

        // Pending payments (mock data - would come from payment service)
        var pendingPayments = await _context.Bookings
            .Where(b => b.ProviderId == providerId && !b.IsDeleted)
            .Where(b => b.Status.Status == BookingStatusType.Completed)
            .Where(b => b.CreatedAt >= DateTime.UtcNow.AddDays(-7)) // Last 7 days
            .SumAsync(b => b.ProviderAmount, cancellationToken);

        // Daily earnings
        var earningsByDay = completedBookings
            .GroupBy(b => b.CreatedAt.Date)
            .Select(g => new DailyEarningsData
            {
                Date = g.Key,
                Amount = g.Sum(b => b.ProviderAmount),
                BookingCount = g.Count()
            })
            .OrderBy(d => d.Date)
            .ToList();

        // Earnings by category
        var earningsByCategory = completedBookings
            .GroupBy(b => b.Category.Name)
            .Select(g => new CategoryEarningsData
            {
                CategoryName = g.Key,
                Amount = g.Sum(b => b.ProviderAmount),
                BookingCount = g.Count(),
                Percentage = totalEarnings > 0 ? (double)(g.Sum(b => b.ProviderAmount) / totalEarnings * 100) : 0
            })
            .OrderByDescending(c => c.Amount)
            .ToList();

        var stats = new ProviderEarningsStats
        {
            TotalEarnings = totalEarnings,
            CurrentMonthEarnings = currentMonthEarnings,
            LastMonthEarnings = lastMonthEarnings,
            GrowthRate = growthRate,
            AverageBookingValue = averageBookingValue,
            PendingPayments = pendingPayments,
            EarningsByDay = earningsByDay,
            EarningsByCategory = earningsByCategory
        };

        return Result.Success(stats);
    }
}
```

### Phase 4: API Endpoints Specification

#### 4.1 Care Provider Dashboard Endpoints

| Method | Endpoint                                           | Description                     | Response Model                         |
| ------ | -------------------------------------------------- | ------------------------------- | -------------------------------------- |
| GET    | `/api/v1/provider/dashboard`                       | Get complete provider dashboard | `ProviderDashboardResponse`            |
| GET    | `/api/v1/provider/dashboard/earnings`              | Get earnings analytics          | `ProviderEarningsResponse`             |
| GET    | `/api/v1/provider/dashboard/bookings`              | Get booking analytics           | `ProviderBookingAnalyticsResponse`     |
| GET    | `/api/v1/provider/dashboard/performance`           | Get performance metrics         | `ProviderPerformanceResponse`          |
| GET    | `/api/v1/provider/dashboard/availability-insights` | Get availability insights       | `ProviderAvailabilityInsightsResponse` |
| GET    | `/api/v1/provider/dashboard/recent-activity`       | Get recent activity             | `List<RecentActivityResponse>`         |

#### 4.2 Enhanced Admin Dashboard Endpoints

| Method | Endpoint                                       | Description              | Response Model                     |
| ------ | ---------------------------------------------- | ------------------------ | ---------------------------------- |
| GET    | `/api/v1/admin/dashboard/real-time`            | Get real-time statistics | `AdminRealTimeStatsResponse`       |
| GET    | `/api/v1/admin/dashboard/provider-insights`    | Get provider insights    | `AdminProviderInsightsResponse`    |
| GET    | `/api/v1/admin/dashboard/client-insights`      | Get client insights      | `AdminClientInsightsResponse`      |
| GET    | `/api/v1/admin/dashboard/revenue-insights`     | Get revenue insights     | `AdminRevenueInsightsResponse`     |
| GET    | `/api/v1/admin/dashboard/operational-insights` | Get operational insights | `AdminOperationalInsightsResponse` |
| GET    | `/api/v1/admin/dashboard/alerts`               | Get system alerts        | `List<AdminAlertResponse>`         |

#### 4.3 Query Parameters

```csharp
public class DashboardQueryParameters
{
    /// <summary>
    /// Time range: 7days, 30days, 90days, 1year, custom
    /// </summary>
    public string TimeRange { get; set; } = "30days";

    /// <summary>
    /// Start date for custom range
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date for custom range
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Include detailed breakdowns
    /// </summary>
    public bool IncludeDetails { get; set; } = true;

    /// <summary>
    /// Data granularity: daily, weekly, monthly
    /// </summary>
    public string Granularity { get; set; } = "daily";
}
```

### Phase 5: Supporting Data Models

#### 5.1 Common Data Models

```csharp
public class DashboardTimeRange
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public string Granularity { get; set; } = "daily";
}

public class DailyEarningsData
{
    public DateTime Date { get; set; }
    public decimal Amount { get; set; }
    public int BookingCount { get; set; }
}

public class CategoryEarningsData
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public int BookingCount { get; set; }
    public double Percentage { get; set; }
}

public class MonthlyEarningsData
{
    public int Year { get; set; }
    public int Month { get; set; }
    public string MonthName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public int BookingCount { get; set; }
    public double GrowthRate { get; set; }
}

public class DailyBookingData
{
    public DateTime Date { get; set; }
    public int BookingCount { get; set; }
    public int CompletedCount { get; set; }
    public int CancelledCount { get; set; }
    public decimal Revenue { get; set; }
}

public class TopClientData
{
    public Guid ClientId { get; set; }
    public string ClientName { get; set; } = string.Empty;
    public int BookingCount { get; set; }
    public decimal TotalSpent { get; set; }
    public DateTime LastBooking { get; set; }
    public double AverageRating { get; set; }
}

public class RatingTrendData
{
    public DateTime Date { get; set; }
    public decimal AverageRating { get; set; }
    public int ReviewCount { get; set; }
}

public class RecentReviewData
{
    public Guid ReviewId { get; set; }
    public string ClientName { get; set; } = string.Empty;
    public int Rating { get; set; }
    public string Comment { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string ServiceCategory { get; set; } = string.Empty;
}

public class RecentActivityResponse
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "booking", "review", "payment", etc.
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}
```

#### 5.2 Provider-Specific Models

```csharp
public class ProviderAvailabilityStats
{
    public int TotalAvailableHours { get; set; }
    public int BookedHours { get; set; }
    public double UtilizationRate { get; set; }
    public int AvailableDays { get; set; }
    public int BookedDays { get; set; }
    public List<AvailabilityTrendData> AvailabilityTrend { get; set; } = new();
    public Dictionary<string, int> AvailabilityByDay { get; set; } = new();
    public List<PeakHoursData> PeakHours { get; set; } = new();
}

public class AvailabilityTrendData
{
    public DateTime Date { get; set; }
    public int AvailableHours { get; set; }
    public int BookedHours { get; set; }
    public double UtilizationRate { get; set; }
}

public class PeakHoursData
{
    public int Hour { get; set; }
    public string TimeSlot { get; set; } = string.Empty;
    public int BookingCount { get; set; }
    public double DemandLevel { get; set; }
}
```

#### 5.3 Admin-Specific Models

```csharp
public class TopPerformingProviderData
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public decimal TotalEarnings { get; set; }
    public int BookingCount { get; set; }
    public decimal AverageRating { get; set; }
    public double CompletionRate { get; set; }
    public string Specialization { get; set; } = string.Empty;
}

public class ProviderCategoryDistribution
{
    public string CategoryName { get; set; } = string.Empty;
    public int ProviderCount { get; set; }
    public double Percentage { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalBookings { get; set; }
}

public class ProviderLocationData
{
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public int ProviderCount { get; set; }
    public double Percentage { get; set; }
}

public class ClientSegmentData
{
    public string SegmentName { get; set; } = string.Empty;
    public int ClientCount { get; set; }
    public double Percentage { get; set; }
    public decimal AverageSpending { get; set; }
    public double RetentionRate { get; set; }
}

public class AdminAlertResponse
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty; // "error", "warning", "info"
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // "low", "medium", "high", "critical"
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
}
```

### Phase 6: Performance Optimization & Caching

#### 6.1 Caching Strategy

```csharp
public class DashboardCacheService
{
    private readonly IDistributedCacheService _cache;
    private readonly ILogger<DashboardCacheService> _logger;

    // Cache keys for different dashboard components
    public static class CacheKeys
    {
        public const string ProviderDashboard = "provider:dashboard:{0}:{1}"; // providerId:timeRange
        public const string ProviderEarnings = "provider:earnings:{0}:{1}:{2}"; // providerId:startDate:endDate
        public const string AdminRealTime = "admin:realtime";
        public const string AdminProviderInsights = "admin:provider-insights:{0}"; // timeRange
        public const string SystemAlerts = "admin:alerts";
    }

    // Cache expiration times
    public static class CacheExpiration
    {
        public static readonly TimeSpan ProviderDashboard = TimeSpan.FromMinutes(15);
        public static readonly TimeSpan ProviderEarnings = TimeSpan.FromMinutes(30);
        public static readonly TimeSpan AdminRealTime = TimeSpan.FromMinutes(2);
        public static readonly TimeSpan AdminInsights = TimeSpan.FromMinutes(10);
        public static readonly TimeSpan SystemAlerts = TimeSpan.FromMinutes(5);
    }

    public async Task<T?> GetCachedDashboardDataAsync<T>(string cacheKey)
    {
        try
        {
            return await _cache.GetAsync<T>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve cached dashboard data for key {CacheKey}", cacheKey);
            return default;
        }
    }

    public async Task SetCachedDashboardDataAsync<T>(string cacheKey, T data, TimeSpan expiration)
    {
        try
        {
            await _cache.SetAsync(cacheKey, data, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache dashboard data for key {CacheKey}", cacheKey);
        }
    }
}
```

#### 6.2 Database Query Optimization

```csharp
public class OptimizedDashboardQueries
{
    // Use compiled queries for frequently executed dashboard queries
    private static readonly Func<ApplicationDbContext, Guid, DateTime, DateTime, Task<ProviderStatsDto>>
        GetProviderStatsQuery = EF.CompileAsyncQuery(
            (ApplicationDbContext context, Guid providerId, DateTime startDate, DateTime endDate) =>
                context.Bookings
                    .Where(b => b.ProviderId == providerId && !b.IsDeleted)
                    .Where(b => b.CreatedAt >= startDate && b.CreatedAt <= endDate)
                    .GroupBy(b => 1)
                    .Select(g => new ProviderStatsDto
                    {
                        TotalBookings = g.Count(),
                        CompletedBookings = g.Count(b => b.Status.Status == BookingStatusType.Completed),
                        TotalEarnings = g.Where(b => b.Status.Status == BookingStatusType.Completed)
                                        .Sum(b => b.ProviderAmount),
                        AverageBookingValue = g.Where(b => b.Status.Status == BookingStatusType.Completed)
                                             .Average(b => (decimal?)b.ProviderAmount) ?? 0
                    })
                    .FirstOrDefault());

    // Batch queries for better performance
    public async Task<ProviderDashboardData> GetProviderDashboardDataAsync(
        Guid providerId, DateTime startDate, DateTime endDate)
    {
        // Execute multiple related queries in a single database round trip
        var tasks = new[]
        {
            GetProviderStatsQuery(context, providerId, startDate, endDate),
            GetProviderBookingsByStatusAsync(providerId, startDate, endDate),
            GetProviderEarningsByCategoryAsync(providerId, startDate, endDate),
            GetProviderRecentActivityAsync(providerId, 10)
        };

        await Task.WhenAll(tasks);

        return new ProviderDashboardData
        {
            Stats = tasks[0].Result,
            BookingsByStatus = tasks[1].Result,
            EarningsByCategory = tasks[2].Result,
            RecentActivity = tasks[3].Result
        };
    }
}
```

### Phase 7: Frontend Integration Guidelines

#### 7.1 API Response Format

```typescript
// TypeScript interfaces for frontend consumption
interface ApiResponse<T> {
  apiResponseId: string;
  status: "success" | "error" | "notFound";
  statusCode: number;
  message: string;
  payload: T;
  timestamp: string;
}

interface ProviderDashboardResponse {
  overview: ProviderOverviewStats;
  earnings: ProviderEarningsStats;
  bookings: ProviderBookingStats;
  performance: ProviderPerformanceStats;
  recentActivity: RecentActivityResponse[];
  availability: ProviderAvailabilityStats;
  generatedAt: string;
  timeRange: DashboardTimeRange;
}

interface DashboardTimeRange {
  startDate: string;
  endDate: string;
  period: string;
  granularity: string;
}
```

#### 7.2 Frontend Service Implementation

```typescript
// Dashboard API service for frontend
export class DashboardApiService {
  private baseUrl = "/api/v1";

  async getProviderDashboard(
    timeRange: string = "30days"
  ): Promise<ProviderDashboardResponse> {
    const response = await fetch(
      `${this.baseUrl}/provider/dashboard?timeRange=${timeRange}`,
      {
        headers: {
          Authorization: `Bearer ${this.getAuthToken()}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Dashboard API error: ${response.statusText}`);
    }

    const apiResponse: ApiResponse<ProviderDashboardResponse> =
      await response.json();
    return apiResponse.payload;
  }

  async getProviderEarnings(
    startDate?: string,
    endDate?: string
  ): Promise<ProviderEarningsResponse> {
    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    if (endDate) params.append("endDate", endDate);

    const response = await fetch(
      `${this.baseUrl}/provider/dashboard/earnings?${params}`,
      {
        headers: {
          Authorization: `Bearer ${this.getAuthToken()}`,
          "Content-Type": "application/json",
        },
      }
    );

    const apiResponse: ApiResponse<ProviderEarningsResponse> =
      await response.json();
    return apiResponse.payload;
  }

  async getAdminRealTimeStats(): Promise<AdminRealTimeStatsResponse> {
    const response = await fetch(`${this.baseUrl}/admin/dashboard/real-time`, {
      headers: {
        Authorization: `Bearer ${this.getAuthToken()}`,
        "Content-Type": "application/json",
      },
    });

    const apiResponse: ApiResponse<AdminRealTimeStatsResponse> =
      await response.json();
    return apiResponse.payload;
  }
}
```

#### 7.3 Real-time Updates with SignalR

```csharp
// SignalR Hub for real-time dashboard updates
[Authorize]
public class DashboardHub : Hub
{
    public async Task JoinProviderDashboard(string providerId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"provider-{providerId}");
    }

    public async Task JoinAdminDashboard()
    {
        if (Context.User.IsInRole("Admin"))
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "admin-dashboard");
        }
    }

    public async Task LeaveProviderDashboard(string providerId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"provider-{providerId}");
    }

    public async Task LeaveAdminDashboard()
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, "admin-dashboard");
    }
}

// Service to send real-time updates
public class DashboardNotificationService
{
    private readonly IHubContext<DashboardHub> _hubContext;

    public async Task NotifyProviderDashboardUpdate(Guid providerId, object updateData)
    {
        await _hubContext.Clients.Group($"provider-{providerId}")
            .SendAsync("DashboardUpdate", updateData);
    }

    public async Task NotifyAdminDashboardUpdate(object updateData)
    {
        await _hubContext.Clients.Group("admin-dashboard")
            .SendAsync("AdminDashboardUpdate", updateData);
    }
}
```

### Phase 8: Security & Authorization

#### 8.1 Role-based Access Control

```csharp
// Custom authorization policies for dashboard access
public static class DashboardPolicies
{
    public const string ProviderDashboardAccess = "ProviderDashboardAccess";
    public const string AdminDashboardAccess = "AdminDashboardAccess";
    public const string AdvancedAnalyticsAccess = "AdvancedAnalyticsAccess";
}

// Policy configuration
services.AddAuthorization(options =>
{
    options.AddPolicy(DashboardPolicies.ProviderDashboardAccess, policy =>
        policy.RequireRole("CareProvider")
              .RequireClaim("dashboard_access", "provider"));

    options.AddPolicy(DashboardPolicies.AdminDashboardAccess, policy =>
        policy.RequireRole("Admin")
              .RequireClaim("dashboard_access", "admin"));

    options.AddPolicy(DashboardPolicies.AdvancedAnalyticsAccess, policy =>
        policy.RequireRole("Admin")
              .RequireClaim("analytics_access", "advanced"));
});
```

#### 8.2 Data Privacy & Filtering

```csharp
public class DashboardDataFilter
{
    public static ProviderDashboardResponse FilterProviderData(
        ProviderDashboardResponse data,
        ClaimsPrincipal user)
    {
        // Ensure providers can only see their own data
        var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        // Remove sensitive information based on user role
        if (!user.IsInRole("Admin"))
        {
            // Hide detailed client information for privacy
            data.Bookings.TopClients = data.Bookings.TopClients
                .Select(c => new TopClientData
                {
                    ClientId = c.ClientId,
                    ClientName = MaskClientName(c.ClientName),
                    BookingCount = c.BookingCount,
                    TotalSpent = c.TotalSpent,
                    LastBooking = c.LastBooking,
                    AverageRating = c.AverageRating
                }).ToList();
        }

        return data;
    }

    private static string MaskClientName(string name)
    {
        if (string.IsNullOrEmpty(name) || name.Length <= 2)
            return name;

        return $"{name[0]}***{name[^1]}";
    }
}
```

## 📋 Implementation Checklist

### Phase 1: Core Infrastructure

- [ ] Create `ICareProviderDashboardService` interface and implementation
- [ ] Create `IEnhancedAdminDashboardService` interface extending existing service
- [ ] Implement dashboard data models and DTOs
- [ ] Set up dependency injection for new services
- [ ] Create database query optimization utilities

### Phase 2: API Controllers

- [ ] Implement `ProviderDashboardController` with all endpoints
- [ ] Extend existing `DashboardController` with new admin endpoints
- [ ] Add proper authorization attributes and policies
- [ ] Implement query parameter validation
- [ ] Add comprehensive API documentation with Swagger

### Phase 3: Data Services

- [ ] Implement provider overview statistics service
- [ ] Implement provider earnings analytics service
- [ ] Implement provider booking analytics service
- [ ] Implement provider performance metrics service
- [ ] Implement provider availability insights service
- [ ] Implement enhanced admin insights services

### Phase 4: Performance & Caching

- [ ] Implement dashboard-specific caching service
- [ ] Add compiled queries for frequently executed operations
- [ ] Implement cache invalidation strategies
- [ ] Add database query optimization
- [ ] Implement batch query operations

### Phase 5: Real-time Features

- [ ] Create `DashboardHub` for SignalR connections
- [ ] Implement `DashboardNotificationService`
- [ ] Add real-time update triggers for dashboard data
- [ ] Configure SignalR groups for different user types
- [ ] Test real-time functionality

### Phase 6: Security & Authorization

- [ ] Implement role-based access control policies
- [ ] Add data privacy filtering mechanisms
- [ ] Implement user context validation
- [ ] Add audit logging for dashboard access
- [ ] Test security measures

### Phase 7: Frontend Integration

- [ ] Create TypeScript interfaces for API responses
- [ ] Implement frontend dashboard API service
- [ ] Add error handling and retry mechanisms
- [ ] Implement real-time connection management
- [ ] Create dashboard component integration guides

### Phase 8: Testing & Validation

- [ ] Write unit tests for dashboard services
- [ ] Write integration tests for API endpoints
- [ ] Write performance tests for query optimization
- [ ] Test caching mechanisms
- [ ] Test real-time functionality
- [ ] Test security and authorization

## 🧪 Testing Strategy

### Unit Tests

```csharp
[TestClass]
public class CareProviderDashboardServiceTests
{
    private Mock<ApplicationDbContext> _mockContext;
    private Mock<ILogger<CareProviderDashboardService>> _mockLogger;
    private CareProviderDashboardService _service;

    [TestInitialize]
    public void Setup()
    {
        _mockContext = new Mock<ApplicationDbContext>();
        _mockLogger = new Mock<ILogger<CareProviderDashboardService>>();
        _service = new CareProviderDashboardService(_mockContext.Object, _mockLogger.Object);
    }

    [TestMethod]
    public async Task GetDashboardAsync_ValidProvider_ReturnsCorrectData()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var timeRange = new DashboardTimeRange
        {
            StartDate = DateTime.UtcNow.AddDays(-30),
            EndDate = DateTime.UtcNow
        };

        // Mock data setup
        var mockBookings = CreateMockBookings(providerId);
        _mockContext.Setup(c => c.Bookings).Returns(mockBookings);

        // Act
        var result = await _service.GetDashboardAsync(providerId, timeRange);

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsNotNull(result.Value);
        Assert.AreEqual(providerId, result.Value.Overview.ProviderId);
    }

    [TestMethod]
    public async Task GetEarningsAsync_ValidDateRange_ReturnsCorrectEarnings()
    {
        // Arrange & Act & Assert
        // Test earnings calculation logic
    }
}
```

### Integration Tests

```csharp
[TestClass]
public class ProviderDashboardControllerIntegrationTests : IntegrationTestBase
{
    [TestMethod]
    public async Task GetDashboard_AuthenticatedProvider_ReturnsOk()
    {
        // Arrange
        var client = CreateAuthenticatedClient("CareProvider");

        // Act
        var response = await client.GetAsync("/api/v1/provider/dashboard?timeRange=30days");

        // Assert
        Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);

        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponseModel<ProviderDashboardResponse>>(content);

        Assert.AreEqual("success", apiResponse.Status);
        Assert.IsNotNull(apiResponse.Payload);
    }

    [TestMethod]
    public async Task GetDashboard_UnauthorizedUser_ReturnsUnauthorized()
    {
        // Arrange
        var client = CreateUnauthenticatedClient();

        // Act
        var response = await client.GetAsync("/api/v1/provider/dashboard");

        // Assert
        Assert.AreEqual(HttpStatusCode.Unauthorized, response.StatusCode);
    }
}
```

### Performance Tests

```csharp
[TestClass]
public class DashboardPerformanceTests
{
    [TestMethod]
    public async Task GetProviderDashboard_LargeDataset_CompletesWithinTimeLimit()
    {
        // Arrange
        var providerId = Guid.NewGuid();
        var service = CreateServiceWithLargeDataset();
        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await service.GetDashboardAsync(providerId, GetDefaultTimeRange());
        stopwatch.Stop();

        // Assert
        Assert.IsTrue(result.IsSuccess);
        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 2000, "Dashboard should load within 2 seconds");
    }
}
```

## 🎯 Success Criteria

### Performance Metrics

1. **Response Time**: Dashboard APIs respond within 2 seconds for 95% of requests
2. **Throughput**: Support 100+ concurrent dashboard requests
3. **Cache Hit Rate**: Achieve 80%+ cache hit rate for dashboard data
4. **Database Efficiency**: Reduce database queries by 60% through optimization

### Functional Requirements

1. **Data Accuracy**: All dashboard metrics match source data with 100% accuracy
2. **Real-time Updates**: Dashboard updates within 30 seconds of data changes
3. **Role-based Access**: Proper authorization for all dashboard endpoints
4. **Data Privacy**: Sensitive information properly masked for non-admin users

### User Experience

1. **Load Time**: Dashboard loads within 3 seconds on average
2. **Responsiveness**: Real-time updates work seamlessly
3. **Error Handling**: Graceful error handling with meaningful messages
4. **Mobile Compatibility**: Dashboard APIs support mobile frontend consumption

### Security & Compliance

1. **Authentication**: All endpoints properly secured with JWT
2. **Authorization**: Role-based access control implemented correctly
3. **Data Protection**: PII data properly protected and masked
4. **Audit Trail**: All dashboard access logged for compliance

## 📈 Expected Benefits

### For Care Providers

1. **Business Insights**: Comprehensive view of earnings, bookings, and performance
2. **Performance Tracking**: Monitor ratings, completion rates, and client satisfaction
3. **Financial Management**: Track earnings trends and payment status
4. **Availability Optimization**: Insights into booking patterns and peak hours

### For Administrators

1. **System Overview**: Real-time monitoring of platform health and usage
2. **Business Analytics**: Revenue trends, user growth, and operational metrics
3. **Provider Management**: Insights into provider performance and distribution
4. **Decision Support**: Data-driven insights for business decisions

### Technical Benefits

1. **Scalability**: Optimized queries and caching for better performance
2. **Maintainability**: Clean architecture with separation of concerns
3. **Extensibility**: Easy to add new dashboard features and metrics
4. **Monitoring**: Comprehensive logging and error tracking

## 🔄 Migration & Deployment Strategy

### Phase 1: Development (Week 1-2)

- Implement core dashboard services and models
- Create API controllers and endpoints
- Set up basic caching and optimization

### Phase 2: Testing (Week 3)

- Comprehensive testing of all endpoints
- Performance testing and optimization
- Security testing and validation

### Phase 3: Integration (Week 4)

- Frontend integration and testing
- Real-time features implementation
- End-to-end testing

### Phase 4: Deployment (Week 5)

- Production deployment with feature flags
- Monitoring and performance validation
- User acceptance testing

### Phase 5: Optimization (Week 6)

- Performance tuning based on real usage
- Additional features based on feedback
- Documentation and training

---

_This comprehensive dashboard API implementation plan provides a robust foundation for delivering powerful analytics and insights to both care professionals and administrators in the SuperCare healthcare platform._
