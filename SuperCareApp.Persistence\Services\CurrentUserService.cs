﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;

namespace SuperCareApp.Persistence.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public Guid? UserId
        {
            get
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(
                    ClaimTypes.NameIdentifier
                );
                if (userIdClaim == null)
                    return null;

                if (Guid.TryParse(userIdClaim.Value, out Guid parsedId))
                    return parsedId;

                return null;
            }
        }

        public bool IsAuthenticated =>
            _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

        public void Clear()
        {
            _httpContextAccessor.HttpContext.SignOutAsync();
            _httpContextAccessor.HttpContext.User = new ClaimsPrincipal();
            _httpContextAccessor.HttpContext.Items.Clear();
        }

        public bool IsInRole(string role)
        {
            return _httpContextAccessor.HttpContext.User.IsInRole(role);
        }
    }
}
