using FluentValidation;

namespace SuperCareApp.Application.Common.Models.Bookings;

public class BookingWindowRequest
{
    public DateTime Date { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }

    public BookingWindowRequest(DateTime date, TimeOnly startTime, TimeOnly endTime)
    {
        Date = date;
        StartTime = startTime;
        EndTime = endTime;
    }

    public BookingWindowRequest() { }
}

public class BookingWindowRequestValidator : AbstractValidator<BookingWindowRequest>
{
    public BookingWindowRequestValidator()
    {
        RuleFor(x => x.Date)
            .NotEmpty()
            .WithMessage("Date is required for each window.")
            .Must(BeAValidDate)
            .WithMessage("Invalid date format.")
            .Must(BeTodayOrInFuture)
            .WithMessage("Date must be today or in the future.");

        RuleFor(x => x.StartTime).NotEmpty().WithMessage("Start time is required for each window.");

        RuleFor(x => x.EndTime).NotEmpty().WithMessage("End time is required for each window.");

        RuleFor(x => x)
            .Must(x => x.EndTime > x.StartTime)
            .WithMessage("End time must be after start time.");

        RuleFor(x => x)
            .Must(HaveValidDateTimeRange)
            .WithMessage("Booking window must have valid combined Date and Time range.");
    }

    private bool BeAValidDate(DateTime date)
    {
        return date != default;
    }

    private bool BeTodayOrInFuture(DateTime date)
    {
        return date.Date >= DateTime.Today;
    }

    private bool HaveValidDateTimeRange(BookingWindowRequest x)
    {
        var startDateTime = x.Date.Date + x.StartTime.ToTimeSpan();
        var endDateTime = x.Date.Date + x.EndTime.ToTimeSpan();
        return endDateTime > startDateTime;
    }
}
