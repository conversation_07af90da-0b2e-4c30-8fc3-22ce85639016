using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Persistence.Context;
using SuperCareApp.Persistence.Services.Bookings;
using AvailabilityEntity = SuperCareApp.Domain.Entities.Availability;

namespace SuperCareApp.Persistence.Test.Services.BookingAvailabilityWorkflow;

public class AvailabilityServiceLeaveWorkflowTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly AvailabilityService _service;
    private readonly Mock<IBookingManagementService> _mockBookingService;
    private readonly Guid _providerId;
    private readonly Guid _userId;

    public AvailabilityServiceLeaveWorkflowTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);
        _mockBookingService = new Mock<IBookingManagementService>();
        _service = new AvailabilityService(
            _context,
            _mockBookingService.Object,
            NullLogger<AvailabilityService>.Instance
        );

        _providerId = Guid.NewGuid();
        _userId = Guid.NewGuid();

        SeedTestData();
    }

    private void SeedTestData()
    {
        var providerProfile = new CareProviderProfile
        {
            Id = _providerId,
            UserId = _userId,
            BufferDuration = 30,
            WorkingHours = 8,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.CareProviderProfiles.Add(providerProfile);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Leave Management Tests

    [Fact]
    public async Task AddLeaveAsync_WithValidRequest_ShouldSucceed()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Vacation",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.True(result.IsSuccess);

        var leave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == result.Value);
        Assert.NotNull(leave);
        Assert.Equal(_providerId, leave.ProviderId);
        Assert.Equal(leaveRequest.StartDate, leave.StartDate);
        Assert.Equal(leaveRequest.EndDate, leave.EndDate);
        Assert.Equal(leaveRequest.Reason, leave.Reason);
    }

    [Fact]
    public async Task AddLeaveAsync_WithInvalidDateRange_ShouldFail()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(3),
            EndDate = DateTime.Today.AddDays(1), // End before start
            Reason = "Invalid leave",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("End date must be after start date", result.Error.Message);
    }

    [Fact]
    public async Task AddLeaveAsync_WithPastStartDate_ShouldFail()
    {
        // Arrange
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(-1), // Past date
            EndDate = DateTime.Today.AddDays(1),
            Reason = "Past leave",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, leaveRequest);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Start date cannot be in the past", result.Error.Message);
    }

    [Fact]
    public async Task AddLeaveAsync_WithNonExistentProvider_ShouldFail()
    {
        // Arrange
        var nonExistentProviderId = Guid.NewGuid();
        var leaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Vacation",
        };

        // Act
        var result = await _service.AddLeaveAsync(nonExistentProviderId, leaveRequest);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Provider profile not found", result.Error.Message);
    }

    [Fact]
    public async Task UpdateLeaveAsync_WithValidRequest_ShouldSucceed()
    {
        // Arrange
        var originalLeave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Original reason",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(originalLeave);
        await _context.SaveChangesAsync();

        var updateRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(2),
            EndDate = DateTime.Today.AddDays(4),
            Reason = "Updated reason",
        };

        // Act
        var result = await _service.UpdateLeaveAsync(_providerId, originalLeave.Id, updateRequest);

        // Assert
        Assert.True(result.IsSuccess);

        var updatedLeave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == originalLeave.Id);
        Assert.NotNull(updatedLeave);
        Assert.Equal(updateRequest.StartDate, updatedLeave.StartDate);
        Assert.Equal(updateRequest.EndDate, updatedLeave.EndDate);
        Assert.Equal(updateRequest.Reason, updatedLeave.Reason);
    }

    [Fact]
    public async Task UpdateLeaveAsync_WithNonExistentLeave_ShouldFail()
    {
        // Arrange
        var nonExistentLeaveId = Guid.NewGuid();
        var updateRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Updated reason",
        };

        // Act
        var result = await _service.UpdateLeaveAsync(
            _providerId,
            nonExistentLeaveId,
            updateRequest
        );

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("Leave not found", result.Error.Message);
    }

    [Fact]
    public async Task DeleteLeaveAsync_WithValidLeave_ShouldSucceed()
    {
        // Arrange
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "To be deleted",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.DeleteLeaveAsync(_providerId, leave.Id);

        // Assert
        Assert.True(result.IsSuccess);

        var deletedLeave = await _context.Leaves.FirstOrDefaultAsync(l => l.Id == leave.Id);
        Assert.NotNull(deletedLeave);
        Assert.True(deletedLeave.IsDeleted);
        Assert.NotNull(deletedLeave.DeletedAt);
        Assert.Equal(_providerId, deletedLeave.DeletedBy);
    }

    [Fact]
    public async Task GetMyLeavesAsync_WithExistingLeaves_ShouldReturnCorrectLeaves()
    {
        // Arrange
        var leave1 = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Vacation 1",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var leave2 = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(5),
            EndDate = DateTime.Today.AddDays(7),
            Reason = "Vacation 2",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        // Leave for different provider (should not be returned)
        var otherProviderLeave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = Guid.NewGuid(),
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Other provider leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.NewGuid(),
        };

        _context.Leaves.AddRange(leave1, leave2, otherProviderLeave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetMyLeavesAsync(_userId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(2, result.Value.Count());

        var leaves = result.Value.ToList();
        Assert.Contains(leaves, l => l.Id == leave1.Id);
        Assert.Contains(leaves, l => l.Id == leave2.Id);
        Assert.DoesNotContain(leaves, l => l.Id == otherProviderLeave.Id);
    }

    #endregion

    #region Availability and Leave Interaction Tests

    [Fact]
    public async Task GetProviderAvailabilityAsync_WhenProviderIsOnLeave_ShouldShowUnavailable()
    {
        // Arrange
        // Create availability for Monday
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = "Monday",
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);

        // Create leave that covers next Monday
        var nextMonday = DateTime.Today.AddDays(
            (7 + (int)DayOfWeek.Monday - (int)DateTime.Today.DayOfWeek) % 7
        );
        if (nextMonday == DateTime.Today)
            nextMonday = nextMonday.AddDays(7);

        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = nextMonday,
            EndDate = nextMonday,
            Reason = "Sick leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetProviderAvailabilityAsync(_providerId);

        // Assert
        Assert.True(result.IsSuccess);
        var mondayAvailability = result.Value.FirstOrDefault(a => a.Day == "Monday");
        Assert.NotNull(mondayAvailability);
        Assert.False(mondayAvailability.Available); // Should be false due to leave
        Assert.Empty(mondayAvailability.Slots); // Should have no slots due to leave
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateAsync_WhenProviderIsOnLeave_ShouldReturnUnavailable()
    {
        // Arrange
        var testDate = DateTime.Today.AddDays(1);
        var dayOfWeek = testDate.DayOfWeek.ToString();

        // Create availability for the test day
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = dayOfWeek,
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);

        // Create leave for the test date
        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = testDate,
            EndDate = testDate,
            Reason = "Personal leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetProviderAvailabilityForDateAsync(_providerId, testDate);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.False(result.Value.Available);
        Assert.Empty(result.Value.Slots);
    }

    [Fact]
    public async Task GetProviderAvailabilityForDateAsync_WhenProviderIsAvailableAndNotOnLeave_ShouldReturnAvailableSlots()
    {
        // Arrange
        var testDate = DateTime.Today.AddDays(1);
        var dayOfWeek = testDate.DayOfWeek.ToString();

        // Create availability for the test day
        var availability = new AvailabilityEntity
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            DayOfWeek = dayOfWeek,
            IsAvailable = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var availabilitySlot = new AvailabilitySlot
        {
            Id = Guid.NewGuid(),
            AvailabilityId = availability.Id,
            StartTime = new TimeOnly(9, 0),
            EndTime = new TimeOnly(17, 0),
        };

        _context.Availabilities.Add(availability);
        _context.AvailabilitySlots.Add(availabilitySlot);
        await _context.SaveChangesAsync();

        // Mock the booking management service to return available slots
        _mockBookingService
            .Setup(s =>
                s.GetAvailableSlotsForDateAsync(_providerId, DateOnly.FromDateTime(testDate))
            )
            .ReturnsAsync(
                new List<Application.Common.Settings.Interval<TimeOnly>>
                {
                    new(new TimeOnly(9, 0), new TimeOnly(17, 0)),
                }
            );

        // Act
        var result = await _service.GetProviderAvailabilityForDateAsync(_providerId, testDate);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value.Available);
        Assert.Single(result.Value.Slots);
        Assert.Equal("09:00", result.Value.Slots.First().StartTime);
        Assert.Equal("17:00", result.Value.Slots.First().EndTime);
    }

    [Fact]
    public async Task GetProviderAvailabilityTemplateAsync_WithActiveLeavePeriods_ShouldReflectLeaveImpact()
    {
        // Arrange
        var checkDate = DateTime.Today;

        // Create availability for multiple days
        var availabilities = new[]
        {
            new { Day = "Monday", Available = true },
            new { Day = "Tuesday", Available = true },
            new { Day = "Wednesday", Available = true },
        };

        foreach (var avail in availabilities)
        {
            var availability = new AvailabilityEntity
            {
                Id = Guid.NewGuid(),
                ProviderId = _providerId,
                DayOfWeek = avail.Day,
                IsAvailable = avail.Available,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = _userId,
            };

            var availabilitySlot = new AvailabilitySlot
            {
                Id = Guid.NewGuid(),
                AvailabilityId = availability.Id,
                StartTime = new TimeOnly(9, 0),
                EndTime = new TimeOnly(17, 0),
            };

            _context.Availabilities.Add(availability);
            _context.AvailabilitySlots.Add(availabilitySlot);
        }

        // Create leave that affects Tuesday within the check period
        var nextTuesday = checkDate.AddDays(
            (7 + (int)DayOfWeek.Tuesday - (int)checkDate.DayOfWeek) % 7
        );
        if (nextTuesday == checkDate)
            nextTuesday = nextTuesday.AddDays(7);
        if (nextTuesday > checkDate.AddDays(7))
            nextTuesday = nextTuesday.AddDays(-7);

        var leave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = nextTuesday,
            EndDate = nextTuesday,
            Reason = "Medical appointment",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(leave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetProviderAvailabilityTemplateAsync(_providerId, checkDate);

        // Assert
        Assert.True(result.IsSuccess);

        var mondayAvailability = result.Value.Availabilities.FirstOrDefault(a => a.Day == "Monday");
        var tuesdayAvailability = result.Value.Availabilities.FirstOrDefault(a =>
            a.Day == "Tuesday"
        );
        var wednesdayAvailability = result.Value.Availabilities.FirstOrDefault(a =>
            a.Day == "Wednesday"
        );

        Assert.NotNull(mondayAvailability);
        Assert.True(mondayAvailability.Available); // Not affected by leave

        Assert.NotNull(tuesdayAvailability);
        Assert.False(tuesdayAvailability.Available); // Affected by leave

        Assert.NotNull(wednesdayAvailability);
        Assert.True(wednesdayAvailability.Available); // Not affected by leave
    }

    #endregion

    #region Edge Cases and Error Handling

    [Fact]
    public async Task AddLeaveAsync_WithOverlappingLeaves_ShouldAllowOverlap()
    {
        // Arrange
        // Create first leave
        var firstLeave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "First leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.Add(firstLeave);
        await _context.SaveChangesAsync();

        // Create overlapping leave request
        var overlappingLeaveRequest = new LeaveRequest
        {
            StartDate = DateTime.Today.AddDays(2),
            EndDate = DateTime.Today.AddDays(4),
            Reason = "Overlapping leave",
        };

        // Act
        var result = await _service.AddLeaveAsync(_providerId, overlappingLeaveRequest);

        // Assert
        // The system should allow overlapping leaves (business decision)
        Assert.True(result.IsSuccess);

        var leaves = await _context.Leaves.Where(l => l.ProviderId == _providerId).ToListAsync();
        Assert.Equal(2, leaves.Count);
    }

    [Fact]
    public async Task GetProviderLeavesAsync_WithDeletedLeaves_ShouldNotReturnDeletedLeaves()
    {
        // Arrange
        var activeLeave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(1),
            EndDate = DateTime.Today.AddDays(3),
            Reason = "Active leave",
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        var deletedLeave = new Leave
        {
            Id = Guid.NewGuid(),
            ProviderId = _providerId,
            StartDate = DateTime.Today.AddDays(5),
            EndDate = DateTime.Today.AddDays(7),
            Reason = "Deleted leave",
            IsDeleted = true,
            DeletedAt = DateTime.UtcNow,
            DeletedBy = _userId,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = _userId,
        };

        _context.Leaves.AddRange(activeLeave, deletedLeave);
        await _context.SaveChangesAsync();

        // Act
        var result = await _service.GetProviderLeavesAsync(_providerId);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Single(result.Value);
        Assert.Equal(activeLeave.Id, result.Value.First().Id);
    }

    #endregion
}
