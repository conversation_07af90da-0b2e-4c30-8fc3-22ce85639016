﻿using SuperCareApp.Domain.Common.Results;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Interfaces.Persistence
{
    public interface IDocumentRepository : IRepository<Document>
    {
        /// <summary>
        /// Gets all documents for a user, optionally including unapproved documents
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="includeUnapproved">Whether to include unapproved documents</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A collection of documents</returns>
        Task<Result<IEnumerable<Document>>> GetDocumentsByUserIdAsync(
            Guid userId,
            bool includeUnapproved = false,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets a document by ID, optionally including unapproved documents
        /// </summary>
        /// <param name="documentId">The document ID</param>
        /// <param name="includeUnapproved">Whether to include unapproved documents</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The document if found</returns>
        Task<Result<Document>> GetDocumentByIdAsync(
            Guid documentId,
            bool includeUnapproved = false,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Gets all pending documents awaiting approval
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A collection of pending documents</returns>
        Task<Result<IEnumerable<Document>>> GetPendingDocumentsAsync(
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Approves a document
        /// </summary>
        /// <param name="documentId">The document ID</param>
        /// <param name="adminId">The admin ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated document</returns>
        Task<Result<Document>> ApproveDocumentAsync(
            Guid documentId,
            Guid adminId,
            CancellationToken cancellationToken = default
        );

        /// <summary>
        /// Rejects a document
        /// </summary>
        /// <param name="documentId">The document ID</param>
        /// <param name="adminId">The admin ID</param>
        /// <param name="rejectionReason">The reason for rejection</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated document</returns>
        Task<Result<Document>> RejectDocumentAsync(
            Guid documentId,
            Guid adminId,
            string rejectionReason,
            CancellationToken cancellationToken = default
        );
    }
}
