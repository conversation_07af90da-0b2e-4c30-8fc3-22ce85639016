﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to add multiple care categories to a provider
    /// </summary>
    public record AddProviderCategoriesCommand(
        Guid ProviderId,
        ProviderCategoriesRequest Request,
        Guid UserId
    ) : ICommand<Result>;

    /// <summary>
    /// Handler for the AddProviderCategoriesCommand
    /// </summary>
    internal sealed class AddProviderCategoriesCommandHandler
        : ICommandHandler<AddProviderCategoriesCommand, Result>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<AddProviderCategoriesCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public AddProviderCategoriesCommandHandler(
            ApplicationDbContext dbContext,
            ILogger<AddProviderCategoriesCommandHandler> logger
        )
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result> Handle(
            AddProviderCategoriesCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Verify the provider exists
                var provider = await _dbContext
                    .Set<CareProviderProfile>()
                    .FirstOrDefaultAsync(
                        p => p.UserId == request.ProviderId && !p.IsDeleted,
                        cancellationToken
                    );

                if (provider == null)
                {
                    return Result.Failure(
                        Error.NotFound($"Care provider with ID {request.ProviderId} not found.")
                    );
                }

                // Get all active categories
                var activeCategories = await _dbContext
                    .Set<CareCategory>()
                    .Where(c => c.IsActive && !c.IsDeleted)
                    .Select(c => c.Id)
                    .ToListAsync(cancellationToken);

                // Verify all requested categories exist and are active
                var invalidCategories = request
                    .Request.CategoryIds.Where(id => !activeCategories.Contains(id))
                    .ToList();

                if (invalidCategories.Any())
                {
                    return Result.Failure(
                        Error.Validation(
                            $"The following categories are invalid or inactive: {string.Join(", ", invalidCategories)}"
                        )
                    );
                }

                // Get existing provider categories
                var existingCategories = await _dbContext
                    .Set<CareProviderCategory>()
                    .Where(pc => pc.ProviderId == provider.Id && !pc.IsDeleted)
                    .Select(pc => pc.CategoryId)
                    .ToListAsync(cancellationToken);

                // Filter out categories that the provider already has
                var newCategoryIds = request
                    .Request.CategoryIds.Where(id => !existingCategories.Contains(id))
                    .ToList();

                if (!newCategoryIds.Any())
                {
                    return Result.Success(); // No new categories to add
                }

                // Add the new categories to the provider
                var providerCategories = newCategoryIds
                    .Select(categoryId => new CareProviderCategory
                    {
                        Id = Guid.NewGuid(),
                        ProviderId = provider.Id,
                        CategoryId = categoryId,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = request.UserId,
                    })
                    .ToList();

                await _dbContext
                    .Set<CareProviderCategory>()
                    .AddRangeAsync(providerCategories, cancellationToken);
                await _dbContext.SaveChangesAsync(cancellationToken);

                _logger.LogInformation(
                    "Added {Count} categories to provider {ProviderId}",
                    newCategoryIds.Count,
                    provider.Id
                );

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding categories to provider");
                return Result.Failure(Error.Internal(ex.Message));
            }
        }
    }
}
