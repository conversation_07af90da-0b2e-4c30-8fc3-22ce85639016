﻿using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Application.Common.Models.Documents;

/// <summary>
/// Response model for document operations
/// </summary>
public class DocumentResponse
{
    /// <summary>
    /// ID of the document
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// ID of the user who owns the document
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Name of the file
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// MIME type of the file
    /// </summary>
    public string MimeType { get; set; } = string.Empty;

    /// <summary>
    /// URL or path to the document
    /// </summary>
    public string DocumentUrl { get; set; } = string.Empty;

    /// <summary>
    /// Type of document (e.g., "ID", "Certificate", "License")
    /// </summary>
    public string DocumentType { get; set; } = string.Empty;

    /// <summary>
    /// Issuer of the document
    /// </summary>
    public string Issuer { get; set; } = string.Empty;

    /// <summary>
    /// Verification status of the document
    /// </summary>
    public string VerificationStatus { get; set; } = string.Empty;

    /// <summary>
    /// Date and time when the document was uploaded
    /// </summary>
    public DateTime UploadedAt { get; set; }

    /// <summary>
    /// Date and time when the document was verified (if applicable)
    /// </summary>
    public DateTime? VerifiedAt { get; set; }

    /// <summary>
    /// ID of the admin who verified the document (if applicable)
    /// </summary>
    public Guid? VerifiedBy { get; set; }

    /// <summary>
    /// Reason for rejection (if applicable)
    /// </summary>
    public string? RejectionReason { get; set; }

    /// <summary>
    /// Country associated with the certification
    /// </summary>
    public string Country { get; set; } = string.Empty;

    /// <summary>
    /// Type of certification
    /// </summary>
    public string CertificationType { get; set; } = string.Empty;

    /// <summary>
    /// Custom certification type (if "Other" is selected)
    /// </summary>
    public string? OtherCertificationType { get; set; }

    /// <summary>
    /// Certification number or identifier
    /// </summary>
    public string? CertificationNumber { get; set; }

    /// <summary>
    /// Expiry date of the certification
    /// </summary>
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// Whether the certification is expired
    /// </summary>
    public bool IsExpired { get; set; }
}
