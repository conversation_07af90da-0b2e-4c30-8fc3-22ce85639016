namespace SuperCareApp.Application.Common.Models.Bookings
{
    /// <summary>
    /// Parameters for filtering and pagination of availability lists
    /// </summary>
    public class AvailabilityListParams
    {
        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }

        /// <summary>
        /// Filter by provider ID
        /// </summary>
        public Guid? ProviderId { get; set; }

        /// <summary>
        /// Filter by day of week (e.g., "Monday", "Tuesday", etc.)
        /// </summary>
        public string? DayOfWeek { get; set; }

        /// <summary>
        /// Filter by availability status
        /// </summary>
        public bool? IsAvailable { get; set; }

        /// <summary>
        /// Check availability for a specific date (considers leave periods)
        /// </summary>
        public DateTime? CheckDate { get; set; }

        /// <summary>
        /// Check availability for a date range (considers leave periods)
        /// </summary>
        public DateTime? CheckDateFrom { get; set; }

        /// <summary>
        /// Check availability for a date range (considers leave periods)
        /// </summary>
        public DateTime? CheckDateTo { get; set; }

        /// <summary>
        /// Sort by field name
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort in descending order if true
        /// </summary>
        public bool SortDescending { get; set; } = false;
    }
}
