using System.Text.Json;
using SuperCareApp.Application.Common.Models.Documents;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Application.Common.Models.User;
using SuperCareApp.Application.Shared.Utility;
using SuperCareApp.Domain.Enums;

namespace super_care_app.Models.Doc
{
    /// <summary>
    /// Example response models for API documentation
    /// </summary>
    public static class ApiResponseExamples
    {
        /// <summary>
        /// Gets an example of a successful login response
        /// </summary>
        public static string LoginSuccessExample()
        {
            var authResponse = new AuthResponse(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                DateTime.UtcNow.AddHours(1),
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            );

            var response = new ApiResponseModel<AuthResponse>(
                ApiResponseStatusEnum.Success,
                "Authentication successful",
                authResponse
            );

            return SerializeTo<PERSON><PERSON>(response);
        }

        /// <summary>
        /// Gets an example of a successful registration response
        /// </summary>
        public static string RegisterSuccessExample()
        {
            var authResponse = new AuthResponse(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                DateTime.UtcNow.AddHours(1),
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            );

            var response = new ApiResponseModel<AuthResponse>(
                ApiResponseStatusEnum.Success,
                "User registered successfully",
                authResponse
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful user profile response
        /// </summary>
        public static string UserProfileSuccessExample()
        {
            var userDto = new UserDto
            {
                Id = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                Email = "<EMAIL>",
                EmailVerified = true,
                IsActive = true,
                LastLogin = DateTime.Parse("2023-05-15T10:30:00Z"),
                AuthProvider = "local",
                CreatedAt = DateTime.Parse("2023-01-01T00:00:00Z"),
                UpdatedAt = DateTime.Parse("2023-05-10T15:45:00Z"),
                Roles = new List<UserRoleType> { UserRoleType.Client },
                Profile = new UserProfileDto
                {
                    Id = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    FirstName = "John",
                    LastName = "Doe",
                    PhoneNumber = "+**********",
                    ProfilePicture =
                        "https://example.com/uploads/ProfilePictures/f47ac10b-58cc-4372-a567-0e02b2c3d479/profile_20230510154500_a1b2c3d4.jpg",
                    Address = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Country = "USA",
                    Preferences = new Dictionary<string, object>
                    {
                        { "theme", "dark" },
                        { "notifications", true },
                    },
                    CreatedAt = DateTime.Parse("2023-01-01T00:00:00Z"),
                    UpdatedAt = DateTime.Parse("2023-05-10T15:45:00Z"),
                },
            };

            var response = new ApiResponseModel<UserDto>(
                ApiResponseStatusEnum.Success,
                "User profile retrieved successfully",
                userDto
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful profile picture upload response
        /// </summary>
        public static string ProfilePictureUploadExample()
        {
            var profilePictureResponse = new ProfilePictureResponse
            {
                ImageUrl =
                    "https://example.com/uploads/ProfilePictures/f47ac10b-58cc-4372-a567-0e02b2c3d479/profile_20230510154500_a1b2c3d4.jpg",
                FileName = "profile_20230510154500_a1b2c3d4.jpg",
                UploadedAt = DateTime.UtcNow,
            };

            var response = new ApiResponseModel<ProfilePictureResponse>(
                ApiResponseStatusEnum.Success,
                "Profile picture uploaded successfully",
                profilePictureResponse
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful profile response for a care provider
        /// </summary>
        public static string ProfileResponseExample()
        {
            var documentResponses = new List<DocumentResponse>
            {
                new DocumentResponse
                {
                    DocumentId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    FileName = "passport.pdf",
                    MimeType = "application/pdf",
                    DocumentUrl = "/Documents/ID/f47ac10b-58cc-4372-a567-0e02b2c3d479/passport.pdf",
                    DocumentType = "ID",
                    VerificationStatus = "Verified",
                    UploadedAt = DateTime.UtcNow.AddDays(-5),
                    VerifiedAt = DateTime.UtcNow.AddDays(-2),
                    VerifiedBy = Guid.Parse("b57ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Country = "United Kingdom",
                    CertificationType = "",
                    OtherCertificationType = null,
                    CertificationNumber = null,
                    ExpiryDate = null,
                    IsExpired = false,
                },
                new DocumentResponse
                {
                    DocumentId = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d480"),
                    UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    FileName = "nursing_certificate.pdf",
                    MimeType = "application/pdf",
                    DocumentUrl =
                        "/Documents/Certificate/f47ac10b-58cc-4372-a567-0e02b2c3d479/nursing_certificate.pdf",
                    DocumentType = "Certificate",
                    VerificationStatus = "Pending",
                    UploadedAt = DateTime.UtcNow.AddDays(-1),
                    Country = "United Kingdom",
                    CertificationType = "Level 3 Diploma in Adult Care",
                    OtherCertificationType = null,
                    CertificationNumber = "CERT-12345",
                    ExpiryDate = DateTime.UtcNow.AddYears(2),
                    IsExpired = false,
                },
            };

            // Example for CareProvider profile
            var careProviderProfileResponse = new ProfileResponse
            {
                UserId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                ProviderId = Guid.Parse("e47ac10b-58cc-4372-a567-0e02b2c3d479"),
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Male",
                YearsExperience = 5,
                DateOfBirth = DateTime.Parse("1985-06-15"),
                ProfilePictureUrl =
                    "https://example.com/uploads/ProfilePictures/f47ac10b-58cc-4372-a567-0e02b2c3d479/profile.jpg",
                PrimaryAddress = new AddressInfo
                {
                    StreetAddress = "123 Main St",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10001",
                    Latitude = 40.7128m,
                    Longitude = -74.0060m,
                    Label = "Home",
                },
                Documents = documentResponses,
                TravelExperience = new TravelExperienceInfo
                {
                    WillingToTravel = true,
                    MaxTravelDistance = 25,
                    PreferredTransportation = "Car",
                    TravelLocations = new List<string> { "Manhattan", "Brooklyn", "Queens" },
                },
                // Care provider specific fields
                Bio = "Experienced care provider with a background in nursing and elderly care.",
                HourlyRate = 25.50m,
                ProvidesOvernight = true,
                ProvidesLiveIn = false,
                Qualifications = "Certified Nursing Assistant (CNA), CPR Certified",
                Rating = 4.8m,
                RatingCount = 24,
                UserType = UserType.CareProvider,
            };

            // Example for Client profile
            var clientProfileResponse = new ProfileResponse
            {
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                FirstName = "Jane",
                LastName = "Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Female",
                YearsExperience = 0,
                DateOfBirth = DateTime.Parse("1990-03-15"),
                ProfilePictureUrl =
                    "https://example.com/uploads/ProfilePictures/a57ac10b-58cc-4372-a567-0e02b2c3d123/profile.jpg",
                PrimaryAddress = new AddressInfo
                {
                    StreetAddress = "456 Park Ave",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10022",
                    Latitude = 40.7592m,
                    Longitude = -73.9730m,
                    Label = "Home",
                },
                Documents = documentResponses.Take(1).ToList(),
                UserType = UserType.Client,
                // Note: Care provider specific fields are not included for Client profiles
            };

            // Use the care provider profile for the example
            // (You can switch to clientProfileResponse to show a client example)
            var profileResponse = careProviderProfileResponse;

            var response = new ApiResponseModel<ProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Profile retrieved successfully",
                profileResponse
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a successful profile response for a client
        /// </summary>
        public static string ClientProfileResponseExample()
        {
            var documentResponses = new List<DocumentResponse>
            {
                new DocumentResponse
                {
                    DocumentId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                    UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                    FileName = "passport.pdf",
                    MimeType = "application/pdf",
                    DocumentUrl = "/Documents/ID/a57ac10b-58cc-4372-a567-0e02b2c3d123/passport.pdf",
                    DocumentType = "ID",
                    VerificationStatus = "Verified",
                    UploadedAt = DateTime.UtcNow.AddDays(-5),
                    VerifiedAt = DateTime.UtcNow.AddDays(-2),
                    VerifiedBy = Guid.Parse("b57ac10b-58cc-4372-a567-0e02b2c3d456"),
                    Country = "United Kingdom",
                    CertificationType = "",
                    OtherCertificationType = null,
                    CertificationNumber = null,
                    ExpiryDate = null,
                    IsExpired = false,
                },
            };

            var clientProfileResponse = new ProfileResponse
            {
                UserId = Guid.Parse("a57ac10b-58cc-4372-a567-0e02b2c3d123"),
                FirstName = "Jane",
                LastName = "Smith",
                Email = "<EMAIL>",
                PhoneNumber = "+**********",
                Gender = "Female",
                YearsExperience = 0,
                DateOfBirth = DateTime.Parse("1990-03-15"),
                ProfilePictureUrl =
                    "https://example.com/uploads/ProfilePictures/a57ac10b-58cc-4372-a567-0e02b2c3d123/profile.jpg",
                PrimaryAddress = new AddressInfo
                {
                    StreetAddress = "456 Park Ave",
                    City = "New York",
                    State = "NY",
                    PostalCode = "10022",
                    Latitude = 40.7592m,
                    Longitude = -73.9730m,
                    Label = "Home",
                },
                Documents = documentResponses,
                UserType = UserType.Client,
                // Note: Care provider specific fields are not included for Client profiles
            };

            var response = new ApiResponseModel<ProfileResponse>(
                ApiResponseStatusEnum.Success,
                "Profile retrieved successfully",
                clientProfileResponse
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a validation error response
        /// </summary>
        public static string ValidationErrorExample()
        {
            var validationErrors = new Dictionary<string, string[]>
            {
                { "email", new[] { "Email is required", "Email format is invalid" } },
                {
                    "password",
                    new[]
                    {
                        "Password must be at least 8 characters",
                        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
                    }
                },
            };

            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.BadRequest,
                "Validation failed",
                validationErrors
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a not found error response
        /// </summary>
        public static string NotFoundErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.NotFound,
                "The requested resource was not found",
                null
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of an unauthorized error response
        /// </summary>
        public static string UnauthorizedErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Unauthorized,
                "You are not authorized to access this resource. Please login or provide a valid authentication token.",
                null
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of a forbidden error response
        /// </summary>
        public static string ForbiddenErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.Forbidden,
                "You do not have permission to access this resource.",
                null
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Gets an example of an internal server error response
        /// </summary>
        public static string InternalServerErrorExample()
        {
            var response = new ApiResponseModel<object>(
                ApiResponseStatusEnum.InternalServerError,
                "An unexpected error occurred. Please try again later.",
                null
            );

            return SerializeToJson(response);
        }

        /// <summary>
        /// Serializes an object to JSON
        /// </summary>
        private static string SerializeToJson<T>(T obj)
        {
            return JsonSerializer.Serialize(
                obj,
                new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true,
                }
            );
        }
    }
}
