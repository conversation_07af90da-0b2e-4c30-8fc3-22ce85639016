﻿using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace super_care_app.Utils;

public class TimeOnlyJsonConverter : JsonConverter<TimeOnly>
{
    private const string TimeFormat = "HH:mm";

    public override TimeOnly Read(
        ref Utf8JsonReader reader,
        Type typeToConvert,
        JsonSerializerOptions options
    )
    {
        var timeString = reader.GetString();
        if (string.IsNullOrEmpty(timeString))
        {
            return default;
        }

        // Try to parse using the exact format first
        if (
            TimeOnly.TryParseExact(
                timeString,
                TimeFormat,
                CultureInfo.InvariantCulture,
                DateTimeStyles.None,
                out var result
            )
        )
        {
            return result;
        }

        // Fall back to standard parsing if the exact format fails
        return TimeOnly.Parse(timeString, CultureInfo.InvariantCulture);
    }

    public override void Write(Utf8JsonWriter writer, TimeOnly value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString(TimeFormat, CultureInfo.InvariantCulture));
    }
}
